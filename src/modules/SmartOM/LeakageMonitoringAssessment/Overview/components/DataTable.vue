<template>
  <div class="data-table">
    <fks-table
      :data="tableData"
      height="250px"
      border
      stripe
      size="small"
      :header-cell-style="{background: '#F6F8FD !important'}"
      @row-click="handleRowClick"
    >
      <fks-table-column
        type="index"
        prop="index"
        label="#"
        width="38"
        align="center"
      />
      
      <!-- 站点模式表格列 -->
      <template v-if="viewMode === 'station'">
        <fks-table-column
          prop="管线名称"
          label="管线名称"
          min-width="113"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="桩号"
          label="桩号"
          min-width="120"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点编号"
          label="站点编号"
          width="101"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点类型"
          label="站点类型"
          min-width="129"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="状态"
          label="状态"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.status)">
              {{ scope.row.状态 }}
            </span>
          </template>
        </fks-table-column>
        <fks-table-column
          prop="报警类型"
          label="报警类型"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="流量 (m³/s)"
          label="流量 (m³/s)"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="压力 (MPa)"
          label="压力 (MPa)"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="声能"
          label="声能"
          width="80"
          align="center"
        />
        <fks-table-column
          prop="最后上线时间"
          label="最后上线时间"
          min-width="140"
          show-overflow-tooltip
          show-tooltip-when-overflow
        >
          <template slot-scope="scope">
            <span class="time-cell">
              {{ scope.row['最后上线时间'] }}
              <i v-if="scope.row.status === 1" class="fks-icon-flash status-icon online"></i>
            </span>
          </template>
        </fks-table-column>
      </template>

      <!-- 事件模式表格列 -->
      <template v-else>
        <fks-table-column
          prop="管线名称"
          label="管线名称"
          min-width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="桩号"
          label="桩号"
          min-width="120"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点编号"
          label="站点编号"
          width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="检测类型"
          label="检测类型"
          width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="级别"
          label="级别"
          width="60"
          align="center"
        />
        <fks-table-column
          prop="发生时间"
          label="发生时间"
          min-width="130"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="上报时间"
          label="上报时间"
          min-width="130"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="事件状态"
          label="事件状态"
          width="80"
          align="center"
        />
        <fks-table-column
          prop="报警标签"
          label="报警标签"
          width="80"
          align="center"
        />
      </template>
    </fks-table>
    
    <fks-pagination
      v-model="currentPage"
      :page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50]"
      layout="total, sizes, jumper, prev, pager, next"
      class="mt-2 d-flex justify-content-end"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    viewMode: {
      type: String,
      default: 'station',
      validator: value => ['station', 'event'].includes(value)
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10
    };
  },
  methods: {
    /**
     * 处理行点击事件
     */
    handleRowClick(row) {
      this.$emit('row-click', row);
    },

    /**
     * 处理页码变化
     */
    handlePageChange(page) {
      this.currentPage = page;
      this.$emit('page-change', { page, pageSize: this.pageSize });
    },

    /**
     * 处理页面大小变化
     */
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.$emit('page-change', { page: 1, pageSize: size });
    },

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      switch (status) {
        case 1:
          return 'status-normal';
        case 2:
          return 'status-alarm';
        case 3:
          return 'status-offline';
        default:
          return '';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.data-table {
  // 状态样式
  :deep(.status-normal) {
    color: #67C23A;
    font-weight: 500;
  }

  :deep(.status-alarm) {
    color: #F56C6C;
    font-weight: 500;
  }

  :deep(.status-offline) {
    color: #909399;
    font-weight: 500;
  }

  // 时间单元格样式
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    .status-icon {
      font-size: 12px;

      &.online {
        color: #F6AD55;
        animation: flash 1.5s infinite;
      }
    }
  }

  @keyframes flash {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0.3;
    }
  }
}
</style>
