<template>
  <div class="event-cards">
    <common-title title="诊断事件" is-cube class="mb-2"/>
    <div class="cards-grid">
      <event-card
        v-for="(item, index) in eventCards"
        v-bind="item"
        :key="index"
        :selected-type="selectedEventType"
        @click="handleCardClick"
      />
    </div>
  </div>
</template>

<script>
import EventCard from './event-card.vue';

export default {
  name: 'EventCards',
  components: {
    EventCard
  },
  props: {
    selectedEventType: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      eventCards: [
        { title: '压力', value: 6, imgName: 'pressure', type: 'pressure' },
        { title: '渗漏', value: 2, imgName: 'leak', type: 'leak' },
        { title: '爆管', value: 1, imgName: 'burstPipe', type: 'burstPipe' }
      ]
    };
  },
  methods: {
    /**
     * 处理事件卡片点击
     */
    handleCardClick(params) {
      this.$emit('card-click', params);
    },

    /**
     * 更新事件卡片数据
     */
    updateEventCards(eventData) {
      if (!eventData) return;
      
      this.eventCards.forEach(card => {
        const count = eventData[card.type] || 0;
        card.value = count;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.event-cards {
  .cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 12px;
  }
}
</style>
