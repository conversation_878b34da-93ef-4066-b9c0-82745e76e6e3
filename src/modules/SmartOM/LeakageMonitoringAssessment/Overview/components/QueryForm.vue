<template>
  <div class="query-form">
    <fks-row>
      <!-- 站点模式表单 -->
      <fks-form v-if="viewMode === 'station'" :model="stationForm" label-width="70px">
        <fks-col :span="12">
          <fks-form-item label="管线名称">
            <fks-select v-model="stationForm.pipelineName" placeholder="全部" size="small">
              <fks-option
                v-for="item in formOptions.pipelineOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12">
          <fks-form-item label="桩号">
            <fks-input v-model="stationForm.stakeNumber" placeholder="请输入" size="small"/>
          </fks-form-item>
        </fks-col>
        
        <fks-col :span="12">
          <fks-form-item label="站点编号">
            <fks-select v-model="stationForm.stationNumber" placeholder="全部" size="small">
              <fks-option
                v-for="item in formOptions.stationOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12">
          <fks-form-item label="事件状态">
            <fks-select v-model="stationForm.eventStatus" placeholder="全部" size="small">
              <fks-option
                v-for="item in formOptions.statusOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        
        <fks-col :span="24">
          <fks-form-item label="开始时间">
            <fks-date-picker
              v-model="stationForm.startTime"
              type="datetime"
              placeholder="选择日期时间"
              size="small"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </fks-form-item>
        </fks-col>
        
        <fks-col :span="12">
          <fks-form-item label="报警标签">
            <fks-select v-model="stationForm.alarmTag" placeholder="全部" size="small">
              <fks-option
                v-for="item in formOptions.alarmOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12" class="d-flex justify-content-end align-items-end">
          <fks-form-item>
            <fks-button size="small" plain icon="fks-icon-refresh" @click="handleReset">清空</fks-button>
            <fks-button size="small" plain icon="fks-icon-search" @click="handleSearch">搜索</fks-button>
          </fks-form-item>
        </fks-col>
      </fks-form>

      <!-- 事件模式表单 -->
      <fks-form v-else :model="eventForm" label-width="70px">
        <fks-col :span="8">
          <fks-form-item label="管线名称">
            <fks-select v-model="eventForm.pipelineName" placeholder="请选择管线名称" size="small">
              <fks-option
                v-for="item in formOptions.eventPipelineOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="检测类型">
            <fks-select v-model="eventForm.detectionType" placeholder="请选择检测类型" size="small">
              <fks-option
                v-for="item in formOptions.detectionOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="级别">
            <fks-select v-model="eventForm.level" placeholder="请选择级别" size="small">
              <fks-option
                v-for="item in formOptions.levelOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="事件状态">
            <fks-select v-model="eventForm.eventStatus" placeholder="请选择事件状态" size="small">
              <fks-option
                v-for="item in formOptions.eventStatusOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
      </fks-form>
    </fks-row>
  </div>
</template>

<script>
export default {
  name: 'QueryForm',
  props: {
    viewMode: {
      type: String,
      default: 'station',
      validator: value => ['station', 'event'].includes(value)
    }
  },
  data() {
    return {
      stationForm: {
        pipelineName: 'all',
        stakeNumber: '',
        stationNumber: 'all',
        startTime: '',
        eventStatus: 'all',
        alarmTag: 'all'
      },
      eventForm: {
        pipelineName: 'all',
        detectionType: 'all',
        level: 'all',
        eventStatus: 'all'
      },
      formOptions: {
        pipelineOptions: [
          { label: '全部', value: 'all' },
          { label: '东干线', value: '1' },
          { label: '西干线（临江加压站至黄瓜山管线段）', value: '2' },
          { label: '西干线（临江加压泵站至孙家口水库管线段）', value: '3' }
        ],
        stationOptions: [
          { label: '全部', value: 'all' },
          { label: 'S001', value: 'S001' },
          { label: 'S002', value: 'S002' },
          { label: 'S003', value: 'S003' },
          { label: 'S004', value: 'S004' },
          { label: 'S005', value: 'S005' }
        ],
        statusOptions: [
          { label: '全部', value: 'all' },
          { label: '正常', value: '1' },
          { label: '报警', value: '2' },
          { label: '离线', value: '3' }
        ],
        alarmOptions: [
          { label: '全部', value: 'all' },
          { label: '压力波动', value: '1' },
          { label: '爆管', value: '2' },
          { label: '渗漏', value: '4' },
          { label: '大流量', value: '3' },
          { label: '小流量', value: '5' },
          { label: '高液位', value: '6' }
        ],
        eventPipelineOptions: [
          { label: '全部', value: 'all' },
          { label: '东干线', value: '东干线' },
          { label: '西干线（临江加压站至黄瓜山管线段）', value: '西干线（临江加压站至黄瓜山管线段）' },
          { label: '西干线（临江加压泵站至孙家口水库管线段）', value: '西干线（临江加压泵站至孙家口水库管线段）' }
        ],
        detectionOptions: [
          { label: '全部', value: 'all' },
          { label: '基准线上升', value: '基准线上升' },
          { label: '低于智能频带', value: '低于智能频带' },
          { label: '爆管快测', value: '爆管快测' },
          { label: '压力瞬变检测', value: '压力瞬变检测' }
        ],
        levelOptions: [
          { label: '全部', value: 'all' },
          { label: '高', value: '高' },
          { label: '中', value: '中' },
          { label: '低', value: '低' }
        ],
        eventStatusOptions: [
          { label: '全部', value: 'all' },
          { label: '已处理', value: '已处理' },
          { label: '未处理', value: '未处理' },
          { label: '已调查', value: '已调查' },
          { label: '待调查', value: '待调查' }
        ]
      }
    };
  },
  computed: {
    currentForm() {
      return this.viewMode === 'station' ? this.stationForm : this.eventForm;
    }
  },
  methods: {
    /**
     * 重置表单
     */
    handleReset() {
      if (this.viewMode === 'station') {
        this.stationForm = {
          pipelineName: 'all',
          stakeNumber: '',
          stationNumber: 'all',
          startTime: '',
          eventStatus: 'all',
          alarmTag: 'all'
        };
      } else {
        this.eventForm = {
          pipelineName: 'all',
          detectionType: 'all',
          level: 'all',
          eventStatus: 'all'
        };
      }
      this.$emit('reset', this.currentForm);
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.$emit('search', this.currentForm);
    }
  }
};
</script>

<style lang="scss" scoped>
.query-form {
  margin-bottom: 16px;
}
</style>
