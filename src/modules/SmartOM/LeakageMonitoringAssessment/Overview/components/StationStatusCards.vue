<template>
  <div class="station-status-cards">
    <common-title title="站点状态" is-cube class="mb-2"/>
    <div class="cards-grid">
      <station-card
        v-for="(item, index) in stationCards"
        v-bind="item"
        :key="index"
        :selected-type="selectedCardType"
        @card-click="handleCardClick"
      />
    </div>
  </div>
</template>

<script>
import StationCard from './station-card.vue';
import storage from '@/utils/storage';

export default {
  name: 'StationStatusCards',
  components: {
    StationCard
  },
  props: {
    deviceList: {
      type: Array,
      default: () => []
    },
    selectedCardType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      stationCards: this.initializeStationCards()
    };
  },
  watch: {
    deviceList: {
      handler(newDeviceList) {
        this.updateStationCards(newDeviceList);
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 初始化状态卡片数据
     */
    initializeStationCards() {
      const favoriteItems = JSON.parse(storage.get('start-items')) || [];
      const favoriteCount = favoriteItems.length;
      
      return [
        {
          title: '正常',
          value: 0,
          type: 1,
          percentage: 0,
          rgbColor: [40, 211, 141]
        },
        {
          title: '报警',
          value: 0,
          type: 2,
          percentage: 0,
          rgbColor: [255, 92, 92]
        },
        {
          title: '离线',
          value: 0,
          type: 3,
          percentage: 0,
          rgbColor: [111, 109, 109]
        },
        {
          title: '关注',
          value: favoriteCount,
          type: 0,
          rgbColor: [77, 129, 255]
        }
      ];
    },

    /**
     * 更新状态卡片数据
     */
    updateStationCards(deviceList) {
      if (!deviceList || deviceList.length === 0) return;
      
      const statusCounts = this.countDeviceStatus(deviceList);
      const totalDevices = deviceList.length;
      
      this.stationCards.forEach(card => {
        if (card.type !== 0) { // 不是关注卡片
          card.value = statusCounts[card.type] || 0;
          card.percentage = totalDevices > 0 ? Math.round((card.value / totalDevices) * 100) : 0;
        }
      });
    },

    /**
     * 统计设备状态数量
     */
    countDeviceStatus(deviceList) {
      const statusCounts = {
        1: 0, // 正常设备数量 (bjstatus = 0)
        2: 0, // 报警设备数量 (bjstatus = 3)
        3: 0  // 离线设备数量 (bjstatus = 2)
      };
      
      deviceList.forEach(device => {
        const bjstatus = device.bjstatus ? parseInt(device.bjstatus) : 0;
        
        if (bjstatus === 2) {
          statusCounts[3]++; // 离线设备
        } else if (bjstatus === 3) {
          statusCounts[2]++; // 报警设备
        } else {
          statusCounts[1]++; // 正常设备
        }
      });
      
      return statusCounts;
    },

    /**
     * 处理卡片点击事件
     */
    handleCardClick(params) {
      this.$emit('card-click', params);
    },

    /**
     * 更新关注数量
     */
    updateFollowCount(type) {
      const followCard = this.stationCards.find(card => card.type === 0);
      if (followCard) {
        if (type === 0) {
          followCard.value = Math.max(0, followCard.value - 1);
        } else {
          followCard.value++;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.station-status-cards {
  .cards-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 12px;
  }
}
</style>
