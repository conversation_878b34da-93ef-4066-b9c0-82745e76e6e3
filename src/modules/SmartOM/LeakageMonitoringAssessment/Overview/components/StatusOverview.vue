<template>
  <div class="overview-container">
    <common-title title="站点状态" is-cube class="mb-2"/>
    <div class="station-cards">
      <station-card
        v-for="(item, index) in stationCards"
        v-bind="item"
        :key="index"
        :selected-type="selectedCardType"
        @card-click="handleStatusCardClick"
      />
    </div>
    <div class="split-horizontal-line my-4"/>
    <common-title title="诊断事件" is-cube class="mb-2"/>
    <div class="event-cards">
      <event-card
        v-for="(item, index) in eventCards"
        v-bind="item"
        :key="index"
        :selected-type="selectedEventType"
        @click="handleEventCardClick"
      />
    </div>
    <div class="split-horizontal-line my-4"/>
    <common-title title="站点列表" is-cube class="mb-2"/>
    <fks-row>
      <!-- 站点模式表单 -->
      <fks-form v-if="currentViewMode === 'station'" :model="queryForm" label-width="70px">
        <!-- 第一行：管线名称 + 桩号 -->
        <fks-col :span="12">
          <fks-form-item label="管线名称">
            <fks-select v-model="queryForm.a" placeholder="全部" size="small">
              <fks-option
                v-for="item in currentFormOptions.options1"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12">
          <fks-form-item label="桩号">
            <fks-input v-model="queryForm.b" placeholder="请输入" size="small"/>
          </fks-form-item>
        </fks-col>
        
        <!-- 第二行：站点编号 + 事件状态 -->
        <fks-col :span="12">
          <fks-form-item label="站点编号">
            <fks-select v-model="queryForm.c" placeholder="全部" size="small">
              <fks-option
                v-for="item in currentFormOptions.options5"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12">
          <fks-form-item label="事件状态">
            <fks-select v-model="queryForm.e" placeholder="全部" size="small">
              <fks-option
                v-for="item in currentFormOptions.options3"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        
        <!-- 第三行：开始时间 -->
        <fks-col :span="24">
          <fks-form-item label="开始时间">
            <fks-date-picker
              v-model="queryForm.d"
              type="datetime"
              placeholder="选择日期时间"
              size="small"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </fks-form-item>
        </fks-col>
        
        <!-- 第四行：报警标签 + 操作按钮 -->
        <fks-col :span="12">
          <fks-form-item label="报警标签">
            <fks-select v-model="queryForm.f" placeholder="全部" size="small">
              <fks-option
                v-for="item in currentFormOptions.options4"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="12" class="d-flex justify-content-end align-items-end">
          <fks-form-item>
            <fks-button size="small" plain icon="fks-icon-refresh" @click="handleReset">清空</fks-button>
            <fks-button size="small" plain icon="fks-icon-search" @click="handleSearch">搜索</fks-button>
          </fks-form-item>
        </fks-col>
      </fks-form>

      <!-- 事件模式表单 -->
      <fks-form v-else :model="eventQueryForm" label-width="70px">
        <fks-col :span="8">
          <fks-form-item label="管线名称">
            <fks-select v-model="eventQueryForm.a" placeholder="请选择管线名称" size="small">
              <fks-option
                v-for="item in currentFormOptions.options1"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="检测类型">
            <fks-select v-model="eventQueryForm.b" placeholder="请选择检测类型" size="small">
              <fks-option
                v-for="item in currentFormOptions.options2"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="级别">
            <fks-select v-model="eventQueryForm.c" placeholder="请选择级别" size="small">
              <fks-option
                v-for="item in currentFormOptions.options3"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
        <fks-col :span="8">
          <fks-form-item label="事件状态">
            <fks-select v-model="eventQueryForm.d" placeholder="请选择事件状态" size="small">
              <fks-option
                v-for="item in currentFormOptions.options4"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </fks-select>
          </fks-form-item>
        </fks-col>
      </fks-form>
    </fks-row>
    <fks-table
      :data="tableData"
      height="250px"
      border
      stripe
      size="small"
      :header-cell-style="{background: '#F6F8FD !important'}"
      @row-click="handleRowClick"
    >
      <fks-table-column
        type="index"
        prop="index"
        label="#"
        width="38"
        align="center"
      />
      <!-- 站点模式表格列 -->
      <template v-if="currentViewMode === 'station'">
        <fks-table-column
          prop="管线名称"
          label="管线名称"
          min-width="113"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="桩号"
          label="桩号"
          min-width="120"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点编号"
          label="站点编号"
          width="101"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点类型"
          label="站点类型"
          min-width="129"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="状态"
          label="状态"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.status)">
              {{ scope.row.状态 }}
            </span>
          </template>
        </fks-table-column>
        <fks-table-column
          prop="报警类型"
          label="报警类型"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="流量 (m³/s)"
          label="流量 (m³/s)"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="压力 (MPa)"
          label="压力 (MPa)"
          width="100"
          align="center"
        />
        <fks-table-column
          prop="声能"
          label="声能"
          width="80"
          align="center"
        />
        <fks-table-column
          prop="最后上线时间"
          label="最后上线时间"
          min-width="140"
          show-overflow-tooltip
          show-tooltip-when-overflow
        >
          <template slot-scope="scope">
            <span class="time-cell">
              {{ scope.row['最后上线时间'] }}
              <i v-if="scope.row.status === 1" class="fks-icon-flash status-icon online"></i>
            </span>
          </template>
        </fks-table-column>
      </template>

      <!-- 事件模式表格列 -->
      <template v-else>
        <fks-table-column
          prop="管线名称"
          label="管线名称"
          min-width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="桩号"
          label="桩号"
          min-width="120"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="站点编号"
          label="站点编号"
          width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="检测类型"
          label="检测类型"
          width="100"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="级别"
          label="级别"
          width="60"
          align="center"
        />
        <fks-table-column
          prop="发生时间"
          label="发生时间"
          min-width="130"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="上报时间"
          label="上报时间"
          min-width="130"
          show-overflow-tooltip
          show-tooltip-when-overflow
        />
        <fks-table-column
          prop="事件状态"
          label="事件状态"
          width="80"
          align="center"
        />
        <fks-table-column
          prop="报警标签"
          label="报警标签"
          width="80"
          align="center"
        />
      </template>
    </fks-table>
    <fks-pagination
      v-model="currentPage"
      :page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50]"
      layout="total, sizes, jumper, prev, pager, next"
      class="mt-2 d-flex justify-content-end"
    />
  </div>
</template>

<script>
import StationCard from './station-card.vue';
import EventCard from './event-card.vue';
import storage from '@/utils/storage';
import {deviceList, eventList} from '@/modules/SmartOM/LeakageMonitoringAssessment/Overview/data';

export default {
  name: 'StatusOverview',
  components: {
    StationCard,
    EventCard
  },
  props: {
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    // 获取关注数
    const favoriteItems = JSON.parse(storage.get('start-items'));
    const favoriteCount = favoriteItems ? favoriteItems.length : 0;
    return {
      currentPage: 1,
      pageSize: 10,
      total: 6,
      originalEventTableData: eventList,
      originalTableData: deviceList,
      selectedCardType: null, // 当前选中的卡片类型
      selectedEventType: null, // 当前选中的事件类型
      currentViewMode: 'station', // 当前视图模式：'station' 或 'event'
      options1: [
        {label: '全部', value: 'all'},
        {label: '东干线', value: '1'},
        {label: '西干线（临江加压站至黄瓜山管线段）', value: '2'},
        {label: '西干线（临江加压泵站至孙家口水库管线段）', value: '3'}
      ],
      options2: [
        {label: '全部', value: 'all'},
        {label: '电磁流量计', value: '1'},
        {label: '超声波流量计', value: '2'},
        {label: '压力计', value: '3'},
        {label: '高频压力计、水听仪', value: '4'},
        {label: '液位计', value: '5'}
      ],
      options3: [
        {label: '全部', value: 'all'},
        {label: '正常', value: '1'},
        {label: '报警', value: '2'},
        {label: '离线', value: '3'}
      ],
      options4: [
        {label: '全部', value: 'all'},
        {label: '压力波动', value: '1'},
        {label: '爆管', value: '2'},
        {label: '渗漏', value: '4'},
        {label: '大流量', value: '3'},
        {label: '小流量', value: '5'},
        {label: '高液位', value: '6'}
      ],
      options5: [
        {label: '全部', value: 'all'},
        {label: 'S001', value: 'S001'},
        {label: 'S002', value: 'S002'},
        {label: 'S003', value: 'S003'},
        {label: 'S004', value: 'S004'},
        {label: 'S005', value: 'S005'}
      ],
      // 事件数据的表单选项
      eventOptions1: [
        {label: '全部', value: 'all'},
        {label: '东干线', value: '东干线'},
        {label: '西干线（临江加压站至黄瓜山管线段）', value: '西干线（临江加压站至黄瓜山管线段）'},
        {label: '西干线（临江加压泵站至孙家口水库管线段）', value: '西干线（临江加压泵站至孙家口水库管线段）'}
      ],
      eventOptions2: [
        {label: '全部', value: 'all'},
        {label: '基准线上升', value: '基准线上升'},
        {label: '低于智能频带', value: '低于智能频带'},
        {label: '爆管快测', value: '爆管快测'},
        {label: '压力瞬变检测', value: '压力瞬变检测'}
      ],
      eventOptions3: [
        {label: '全部', value: 'all'},
        {label: '高', value: '高'},
        {label: '中', value: '中'},
        {label: '低', value: '低'}
      ],
      eventOptions4: [
        {label: '全部', value: 'all'},
        {label: '已处理', value: '已处理'},
        {label: '未处理', value: '未处理'},
        {label: '已调查', value: '已调查'},
        {label: '待调查', value: '待调查'}
      ],
      queryForm: {
        a: 'all',     // 管线名称
        b: '',        // 桩号
        c: 'all',     // 站点编号
        d: '',        // 开始时间
        e: 'all',     // 事件状态
        f: 'all'      // 报警标签
      },
      eventQueryForm: {
        a: 'all', // 管线名称
        b: 'all', // 检测类型
        c: 'all', // 级别
        d: 'all' // 事件状态
      },
      stationCards: [
        {
          title: '正常',
          value: 0,
          type: 1,
          percentage: 0,
          rgbColor: [40, 211, 141]
        },
        {
          title: '报警',
          value: 0,
          type: 2,
          percentage: 0,
          rgbColor: [255, 92, 92]
        },
        {
          title: '离线',
          value: 0,
          type: 3,
          percentage: 0,
          rgbColor: [111, 109, 109]
        },
        {
          title: '关注',
          value: favoriteCount,
          type: 0,
          rgbColor: [77, 129, 255]
        }
      ],
      eventCards: [
        {title: '压力', value: 6, imgName: 'pressure', type: 'pressure'},
        {title: '渗漏', value: 2, imgName: 'leak', type: 'leak'},
        {title: '爆管', value: 1, imgName: 'burstPipe', type: 'burstPipe'}
      ]
    };
  },
  watch: {
    // 监听 deviceList 变化，更新 stationCards 数据
    deviceList: {
      handler(newDeviceList) {
        this.updateStationCards(newDeviceList);
      },
      immediate: true
    }
  },
  computed: {
    tableData() {
      if (this.currentViewMode === 'event') {
        // 事件模式：根据选中的事件类型过滤数据
        if (this.selectedEventType === null) {
          return this.originalEventTableData;
        }
        return this.originalEventTableData.filter(item => item.type === this.selectedEventType);
      } else {
        // 站点模式：根据选中的卡片类型过滤数据
        const currentTableData = this.deviceList.length > 0 ? this.convertDeviceListToTableData() : this.originalTableData;
        
        if (this.selectedCardType === null) {
          return currentTableData;
        }

        if (this.selectedCardType === 0) {
          // 关注卡片：筛选出收藏的站点
          const favoriteItems = JSON.parse(storage.get('start-items')) || [];
          const favoriteIds = favoriteItems.map(item => item.id);
          return currentTableData.filter(item => favoriteIds.includes(item.id));
        } else {
          // 其他卡片：根据状态筛选
          return currentTableData.filter(item => item.status === this.selectedCardType);
        }
      }
    },
    currentFormOptions() {
      // 根据当前视图模式返回不同的表单选项
      if (this.currentViewMode === 'event') {
        return {
          options1: this.eventOptions1,
          options2: this.eventOptions2,
          options3: this.eventOptions3,
          options4: this.eventOptions4
        };
      } else {
        return {
          options1: this.options1,
          options2: this.options2,
          options3: this.options3,
          options4: this.options4,
          options5: this.options5
        };
      }
    },
    currentQueryForm() {
      return this.currentViewMode === 'event' ? this.eventQueryForm : this.queryForm;
    }
  },
  methods: {
    // 更新 stationCards 数据
    updateStationCards(deviceList) {
      if (!deviceList || deviceList.length === 0) return;
      
      // 设备状态映射：0-正常，2-离线，3-报警
      const statusCounts = {
        1: 0, // 正常设备数量 (bjstatus = 0)
        2: 0, // 报警设备数量 (bjstatus = 3)
        3: 0  // 离线设备数量 (bjstatus = 2)
      };
      
      // 统计各状态设备数量
      deviceList.forEach(device => {
        const bjstatus = device.bjstatus ? parseInt(device.bjstatus) : 0;
        
        if (bjstatus === 2) {
          // 离线设备
          statusCounts[3]++;
        } else if (bjstatus === 3) {
          // 报警设备
          statusCounts[2]++;
        } else {
          // 正常设备 (bjstatus = 0 或其他)
          statusCounts[1]++;
        }
      });
      
      const totalDevices = deviceList.length;
      
      // 更新 stationCards 数据
      this.stationCards.forEach(card => {
        if (card.type !== 0) { // 不是关注卡片
          card.value = statusCounts[card.type] || 0;
          card.percentage = totalDevices > 0 ? Math.round((card.value / totalDevices) * 100) : 0;
        }
      });
    },
    
    // 将 deviceList 转换为表格数据格式
    convertDeviceListToTableData() {
      return this.deviceList.map((device, index) => {
        const bjstatus = device.bjstatus ? parseInt(device.bjstatus) : 0;
        let status, statusText;
        
        if (bjstatus === 2) {
          status = 3;
          statusText = '离线';
        } else if (bjstatus === 3) {
          status = 2;
          statusText = '报警';
        } else {
          status = 1;
          statusText = '正常';
        }
        
        return {
          id: device.sid || device._id || `device-${index}`,
          管线名称: '主管线', // 可根据实际数据调整
          桩号: device.sbbh || device.name || '-',
          站点编号: device.sbbh_name || device.sbbh || '-',
          站点类型: device.waterHydrantType || '水锤监测设备',
          状态: statusText,
          status: status,
          报警类型: device.alarms?.[0]?.nm || '-',
          '流量 (m³/s)': '-',
          '压力 (MPa)': device.YL !== null && device.YL !== undefined ? device.YL.toString() : '-',
          声能: '-',
          最后上线时间: device.uploadt ? new Date(device.uploadt * 1000).toLocaleString() : '-'
        };
      });
    },
    
    handleRowClick(row) {
      if (this.currentViewMode === 'station') {
        const {id, status} = row;
        const map = window.rxMap;
        // 清除layer的所有图标
        map.setFilter('type1-layer', ['==', 'type', 0]);
        map.setFilter('type2-layer', ['==', 'type', 0]);
        map.setFilter('type3-layer', ['==', 'type', 0]);
        map.setFilter(`type${status}-layer`, ['==', ['get', 'id'], id]);
        // 根据id查询mapbox的source里的数据
        const sourceItem = map.getSource(`source-${status - 1}`);
        const sourceData = sourceItem._data.features.find(feature => feature.properties.id === id);
        this.$emit('showPopup', sourceData);
      } else if (this.currentViewMode === 'event') {
        this.$router.push({
          name: 'OverviewEventDetail',
          query: {id: row.id, type: row.type}
        });
      }
    },
    updateFollow(type) {
      // 0 代表减少一个关注，1代表新增一个关注
      if (type === 0) {
        this.stationCards[3].value--;
      } else {
        this.stationCards[3].value++;
      }
    },
    handleStatusCardClick(params) {
      const { selected, type } = params;

      // 切换到站点视图模式
      this.currentViewMode = 'station';
      // 清除事件选中状态
      this.selectedEventType = null;

      const map = window.rxMap;

      // 先隐藏所有站点图层和事件图层
      const stationLayers = ['type1-layer', 'type2-layer', 'type3-layer'];
      const eventLayers = ['event-layer-pressure', 'event-layer-leak', 'event-layer-burstPipe'];

      // 隐藏所有站点图层
      stationLayers.forEach(layerId => {
        map.setFilter(layerId, ['<', 'type', 0]);
      });

      // 隐藏所有事件图层
      eventLayers.forEach(layerId => {
        const eventType = layerId.replace('event-layer-', '');
        this.$parent.$parent.$parent.toggleEventLayer(eventType, false);
      });

      if (type === 0) {
        // 设置选中的卡片类型为关注类型
        this.selectedCardType = selected ? 0 : null;

        const favoriteItems = JSON.parse(storage.get('start-items')) || [];
        if (selected && favoriteItems.length > 0) {
          // 显示收藏的站点 - 使用 'in' 操作符来匹配多个ID
          const favoriteIds = favoriteItems.map(item => item.id);
          stationLayers.forEach(layerId => {
            map.setFilter(layerId, ['in', ['get', 'id'], ['literal', favoriteIds]]);
          });
        }
      } else {
        // 设置选中的卡片类型
        this.selectedCardType = selected ? type : null;

        if (selected) {
          // 只显示选中类型的图层
          const selectedLayerId = `type${type}-layer`;
          map.setFilter(selectedLayerId, ['==', 'type', type]);
        }
      }
    },
    handleEventCardClick({type, selected}) {
      // 切换到事件视图模式
      this.currentViewMode = 'event';
      // 清除站点选中状态
      this.selectedCardType = null;

      const map = window.rxMap;

      // 先隐藏所有站点图层和事件图层
      const stationLayers = ['type1-layer', 'type2-layer', 'type3-layer'];
      const eventLayers = ['event-layer-pressure', 'event-layer-leak', 'event-layer-burstPipe'];

      // 隐藏所有站点图层
      stationLayers.forEach(layerId => {
        map.setFilter(layerId, ['<', 'type', 0]);
      });

      // 隐藏所有事件图层
      eventLayers.forEach(layerId => {
        const eventType = layerId.replace('event-layer-', '');
        this.$parent.$parent.$parent.toggleEventLayer(eventType, false);
      });

      if (selected) {
        // 如果事件卡片被选中，设置选中的事件类型
        this.selectedEventType = type;

        // 通知父组件显示对应的事件图层
        this.$parent.$parent.$parent.toggleEventLayer(type, true);
      } else {
        // 如果事件卡片被取消选中，清除选中状态
        this.selectedEventType = null;
      }
    },
    getStatusClass(status) {
      // 状态关系：1-正常，2-报警，3-离线
      switch (status) {
        case 1:
          return 'status-normal';
        case 2:
          return 'status-alarm';
        case 3:
          return 'status-offline';
        default:
          return '';
      }
    },
    
    // 重置表单
    handleReset() {
      if (this.currentViewMode === 'station') {
        this.queryForm = {
          a: 'all',     // 管线名称
          b: '',        // 桩号
          c: 'all',     // 站点编号
          d: '',        // 开始时间
          e: 'all',     // 事件状态
          f: 'all'      // 报警标签
        };
      } else {
        this.eventQueryForm = {
          a: 'all', // 管线名称
          b: 'all', // 检测类型
          c: 'all', // 级别
          d: 'all'  // 事件状态
        };
      }
    },
    
    // 搜索功能
    handleSearch() {
      // 这里可以添加搜索逻辑，比如调用API或者过滤表格数据
      console.log('搜索条件:', this.currentViewMode === 'station' ? this.queryForm : this.eventQueryForm);
      // 可以在这里触发数据重新加载或过滤
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/modules/split-line';

.overview-container {
  padding: 16px;

  .station-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 12px;
  }

  .event-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 12px;
  }

  // 状态样式
  .status-normal {
    color: #67C23A;
    font-weight: 500;
  }

  .status-alarm {
    color: #F56C6C;
    font-weight: 500;
  }

  .status-offline {
    color: #909399;
    font-weight: 500;
  }

  // 时间单元格样式
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    .status-icon {
      font-size: 12px;

      &.online {
        color: #F6AD55;
        animation: flash 1.5s infinite;
      }
    }
  }

  @keyframes flash {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0.3;
    }
  }
}
</style>
