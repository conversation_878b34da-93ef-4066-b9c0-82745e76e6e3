<template>
  <div class="overview-container">
    <!-- 状态卡片 -->
    <station-status-cards
      :device-list="deviceList"
      :selected-card-type="selectedCardType"
      @card-click="handleStatusCardClick"
      ref="statusCards"
    />

    <div class="split-horizontal-line my-4"/>

    <!-- 事件卡片 -->
    <event-cards
      :selected-event-type="selectedEventType"
      @card-click="handleEventCardClick"
    />

    <div class="split-horizontal-line my-4"/>

    <!-- 查询表单和数据表格 -->
    <common-title title="站点列表" is-cube class="mb-2"/>

    <query-form
      :view-mode="currentViewMode"
      @search="handleSearch"
      @reset="handleReset"
    />

    <data-table
      :table-data="tableData"
      :view-mode="currentViewMode"
      :total="total"
      @row-click="handleRowClick"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import StationStatusCards from './StationStatusCards.vue';
import EventCards from './EventCards.vue';
import QueryForm from './QueryForm.vue';
import DataTable from './DataTable.vue';
import storage from '@/utils/storage';
import { deviceList, eventList } from '../data';

export default {
  name: 'StatusOverview',
  components: {
    StationStatusCards,
    EventCards,
    QueryForm,
    DataTable
  },
  props: {
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedCardType: null,
      selectedEventType: null,
      currentViewMode: 'station',
      total: 6,
      originalEventTableData: eventList,
      originalTableData: deviceList
    };
  },
  computed: {
    tableData() {
      if (this.currentViewMode === 'event') {
        return this.getEventTableData();
      } else {
        return this.getStationTableData();
      }
    }
  },
  methods: {
    /**
     * 获取事件表格数据
     */
    getEventTableData() {
      if (this.selectedEventType === null) {
        return this.originalEventTableData;
      }
      return this.originalEventTableData.filter(item => item.type === this.selectedEventType);
    },

    /**
     * 获取站点表格数据
     */
    getStationTableData() {
      const currentTableData = this.deviceList.length > 0
        ? this.convertDeviceListToTableData()
        : this.originalTableData;

      if (this.selectedCardType === null) {
        return currentTableData;
      }

      if (this.selectedCardType === 0) {
        // 关注卡片：筛选出收藏的站点
        const favoriteItems = JSON.parse(storage.get('start-items')) || [];
        const favoriteIds = favoriteItems.map(item => item.id);
        return currentTableData.filter(item => favoriteIds.includes(item.id));
      } else {
        // 其他卡片：根据状态筛选
        return currentTableData.filter(item => item.status === this.selectedCardType);
      }
    },

    /**
     * 将设备列表转换为表格数据格式
     */
    convertDeviceListToTableData() {
      return this.deviceList.map((device, index) => {
        const bjstatus = device.bjstatus ? parseInt(device.bjstatus) : 0;
        let status, statusText;

        if (bjstatus === 2) {
          status = 3;
          statusText = '离线';
        } else if (bjstatus === 3) {
          status = 2;
          statusText = '报警';
        } else {
          status = 1;
          statusText = '正常';
        }

        return {
          id: device.sid || device._id || `device-${index}`,
          管线名称: '主管线',
          桩号: device.sbbh || device.name || '-',
          站点编号: device.sbbh_name || device.sbbh || '-',
          站点类型: device.waterHydrantType || '水锤监测设备',
          状态: statusText,
          status: status,
          报警类型: device.alarms?.[0]?.nm || '-',
          '流量 (m³/s)': '-',
          '压力 (MPa)': device.YL !== null && device.YL !== undefined ? device.YL.toString() : '-',
          声能: '-',
          最后上线时间: device.uploadt ? new Date(device.uploadt * 1000).toLocaleString() : '-'
        };
      });
    },
    /**
     * 处理表格行点击
     */
    handleRowClick(row) {
      if (this.currentViewMode === 'station') {
        this.handleStationRowClick(row);
      } else if (this.currentViewMode === 'event') {
        this.handleEventRowClick(row);
      }
    },

    /**
     * 处理站点行点击
     */
    handleStationRowClick(row) {
      const { id, status } = row;
      const map = window.rxMap;

      // 清除所有图层
      map.setFilter('type1-layer', ['==', 'type', 0]);
      map.setFilter('type2-layer', ['==', 'type', 0]);
      map.setFilter('type3-layer', ['==', 'type', 0]);

      // 显示选中的站点
      map.setFilter(`type${status}-layer`, ['==', ['get', 'id'], id]);

      // 获取并显示弹窗
      const sourceItem = map.getSource(`source-${status - 1}`);
      const sourceData = sourceItem._data.features.find(feature => feature.properties.id === id);
      this.$emit('showPopup', sourceData);
    },

    /**
     * 处理事件行点击
     */
    handleEventRowClick(row) {
      this.$router.push({
        name: 'OverviewEventDetail',
        query: { id: row.id, type: row.type }
      });
    },

    /**
     * 更新关注数量
     */
    updateFollow(type) {
      if (this.$refs.statusCards) {
        this.$refs.statusCards.updateFollowCount(type);
      }
    },
    /**
     * 处理状态卡片点击
     */
    handleStatusCardClick(params) {
      const { selected, type } = params;

      this.currentViewMode = 'station';
      this.selectedEventType = null;
      this.selectedCardType = selected ? type : null;

      this.updateMapLayers(type, selected);
    },

    /**
     * 更新地图图层显示
     */
    updateMapLayers(type, selected) {
      const map = window.rxMap;
      const stationLayers = ['type1-layer', 'type2-layer', 'type3-layer'];
      const eventLayers = ['event-layer-pressure', 'event-layer-leak', 'event-layer-burstPipe'];

      // 隐藏所有图层
      stationLayers.forEach(layerId => {
        map.setFilter(layerId, ['<', 'type', 0]);
      });

      eventLayers.forEach(layerId => {
        const eventType = layerId.replace('event-layer-', '');
        this.$parent.$parent.$parent.toggleEventLayer(eventType, false);
      });

      if (selected) {
        if (type === 0) {
          // 关注类型：显示收藏的站点
          this.showFavoriteStations(stationLayers);
        } else {
          // 其他类型：显示对应状态的站点
          map.setFilter(`type${type}-layer`, ['==', 'type', type]);
        }
      }
    },

    /**
     * 显示收藏的站点
     */
    showFavoriteStations(stationLayers) {
      const favoriteItems = JSON.parse(storage.get('start-items')) || [];
      if (favoriteItems.length > 0) {
        const favoriteIds = favoriteItems.map(item => item.id);
        stationLayers.forEach(layerId => {
          window.rxMap.setFilter(layerId, ['in', ['get', 'id'], ['literal', favoriteIds]]);
        });
      }
    },
    /**
     * 处理事件卡片点击
     */
    handleEventCardClick({ type, selected }) {
      this.currentViewMode = 'event';
      this.selectedCardType = null;
      this.selectedEventType = selected ? type : null;

      this.updateEventLayers(type, selected);
    },

    /**
     * 更新事件图层显示
     */
    updateEventLayers(type, selected) {
      const map = window.rxMap;
      const stationLayers = ['type1-layer', 'type2-layer', 'type3-layer'];
      const eventLayers = ['event-layer-pressure', 'event-layer-leak', 'event-layer-burstPipe'];

      // 隐藏所有图层
      stationLayers.forEach(layerId => {
        map.setFilter(layerId, ['<', 'type', 0]);
      });

      eventLayers.forEach(layerId => {
        const eventType = layerId.replace('event-layer-', '');
        this.$parent.$parent.$parent.toggleEventLayer(eventType, false);
      });

      // 显示选中的事件图层
      if (selected) {
        this.$parent.$parent.$parent.toggleEventLayer(type, true);
      }
    },

    /**
     * 处理表单重置
     */
    handleReset(formData) {
      console.log('表单重置:', formData);
    },

    /**
     * 处理搜索
     */
    handleSearch(formData) {
      console.log('搜索条件:', formData);
    },

    /**
     * 处理分页变化
     */
    handlePageChange({ page, pageSize }) {
      console.log('分页变化:', { page, pageSize });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/modules/split-line';

.overview-container {
  padding: 16px;
}
</style>
