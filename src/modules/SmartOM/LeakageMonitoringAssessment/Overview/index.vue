<template>
  <div class="full-height">
    <rx-app
      :right-contain-show="animationShow"
      :header-show="false"
      :banner-show="true"
      :create="onCreate"
      :max-right-container-width="480"
      type="渗漏监测"
    >
      <div slot="rxBanner" class="w-70">
        <div class="position-relative" @click="handleBannerClick">
          <img
            :src="require('@/assets/images/LeakManagement/alert.svg')"
            class="position-absolute alert-icon"
            style="width: 30px;height: 40px;left: 30px;top: -10px;z-index: 2005"
            alt="#"
          >
          <div class="pointed-banner">
            <img style="width: 12px" class="ml-2" :src="require('@/assets/images/LeakManagement/leak-left-arrow.svg')"
                 alt="#">
            <span class="cursor-pointer banner-text ml-7">德西线SXK19+315.751DN300排气阀1#上游侧200米发生爆管</span>
          </div>
        </div>
      </div>
      <template #rightToolsContainer>
        <rx-right-tools-item>
          <div
            class="mask-contain text-16 font-weight-600 cursor-pointer text-primary"
            @click="animationShow = !animationShow"
          >
            <span :class="{
              'text-primary': true,
              'el-icon-d-arrow-left': true,
              'mask-icon': animationShow,
              'mask-icon-retract': !animationShow
            }"></span>
          </div>
        </rx-right-tools-item>
        <rx-right-tools-item :selected="layerArr[0].selected">
          <div id="statusOverview" @click="handleToolItemClick(0)"><span>实时监测</span></div>
        </rx-right-tools-item>
        <rx-right-tools-item :selected="layerArr[1].selected">
          <div id="tunnelDanger" @click="handleToolItemClick(1)"><span>管网风险</span></div>
        </rx-right-tools-item>
      </template>
      <template #rightContainer>
        <div class="full-height">
          <transition name="right-fade" mode="out-in">
            <status-overview
              v-if="layerArr[0].selected && isTokenReady"
              ref="statusOverviewRef"
              :device-list="deviceList"
              @showPopup="showPopup"
            />
            <pipe-risk
              v-else-if="layerArr[1].selected && isTokenReady"
            />
            <div 
              v-else-if="!isTokenReady"
              class="d-flex align-items-center justify-content-center full-height"
              style="color: #666; font-size: 14px;"
            >
              <i class="el-icon-loading mr-2"></i>
              正在获取认证信息...
            </div>
          </transition>
        </div>
      </template>
    </rx-app>
  </div>
</template>
<script>
import StatusOverview from './components/StatusOverview.vue';
import PipeRisk from './components/PipeRisk.vue';
import Popup from './components/popup.vue';
import { mapService, LayerService } from './services/MapService.js';
import DeviceUtils from './utils/DeviceUtils.js';
import {
  LAYER_IDS,
  EVENT_TYPES,
  TOOLBAR_CONFIG,
  STATION_LAYER_IDS,
  EVENT_LAYER_IDS
} from './config/constants.js';
import {
  eventList,
  highRiskPipeData,
  lowRiskPipeData,
  middleRiskPipeData
} from './data';
import { authenticateAndStoreToken, getDeviceStatusList } from "./api";

export default {
  components: { StatusOverview, Popup, PipeRisk },
  data() {
    return {
      animationShow: false,
      deviceList: [],
      layerArr: [
        { name: 'statusOverview', selected: true },
        { name: 'tunnelDanger', selected: false }
      ],
      layerService: null
    };
  },
  computed: {
    isTokenReady() {
      return this.$store.getters['SmartOM/leakage/isAuthenticated'];
    }
  },
  methods: {
    /**
     * 处理工具栏项点击
     */
    handleToolItemClick(i) {
      // 重置选中状态
      this.layerArr.forEach(item => { item.selected = false; });
      this.layerArr[i].selected = true;

      if (i === 1) {
        // 切换到管网风险视图
        this.switchToPipeRiskView();
      } else if (i === 0) {
        // 切换到实时监测视图
        this.switchToMonitoringView();
      }
    },

    /**
     * 切换到管网风险视图
     */
    switchToPipeRiskView() {
      this.layerService.hideAllEventLayers();
      this.layerService.hideAllStationLayers();
      this.layerService.togglePipeRiskLayer(true);
      this.layerService.visibleRiskLevels = ['low', 'middle', 'high'];
      this.layerService.updatePipeRiskFilter();
    },

    /**
     * 切换到实时监测视图
     */
    switchToMonitoringView() {
      this.layerService.togglePipeRiskLayer(false);
    },
    /**
     * 处理横幅点击
     */
    handleBannerClick() {
      this.$router.push({
        name: 'OverviewEventDetail',
        query: { id: 'b1', type: 'burstPipe' }
      });
    },

    /**
     * 地图创建回调
     */
    async onCreate() {
      try {
        // 初始化服务
        mapService.init();
        this.layerService = new LayerService(window.rxMap);

        // 加载地图资源和图层
        await this.initializeMap();

        // 设置事件监听
        this.setupEventListeners();

        // 启动动画
        mapService.startAlertAnimation();

        console.log('地图初始化完成');
      } catch (error) {
        console.error('地图初始化失败:', error);
      }
    },

    /**
     * 初始化地图
     */
    async initializeMap() {
      await DeviceUtils.loadMapIcons(window.rxMap);
      await this.loadDeviceData();
      await this.loadEventData();
      await this.loadPipeRiskData();
    },
    /**
     * 显示弹窗
     */
    showPopup(data) {
      mapService.showDevicePopup(data);
    },

    /**
     * 设置事件监听
     */
    setupEventListeners() {
      // 设置站点图层监听
      mapService.addLayerListeners(LAYER_IDS);

      // 设置事件图层监听
      mapService.addEventLayerListeners(EVENT_TYPES, ({ id, type }) => {
        this.$router.push({
          name: 'OverviewEventDetail',
          query: { id, type }
        });
      });
    },
    /**
     * 加载设备数据
     */
    async loadDeviceData() {
      try {
        const response = await getDeviceStatusList();
        this.deviceList = response.rows;

        // 使用工具类处理设备数据
        const classifiedData = DeviceUtils.classifyDevicesByStatus(this.deviceList);
        const geoJsonDataList = DeviceUtils.transformToGeoJSON(classifiedData);

        // 添加数据源和图层
        this.addDeviceLayersToMap(geoJsonDataList);

        console.log('设备数据加载成功');
      } catch (error) {
        console.error('设备数据加载失败:', error);
        this.loadDefaultDeviceData();
      }
    },

    /**
     * 添加设备图层到地图
     */
    addDeviceLayersToMap(geoJsonDataList) {
      const map = window.rxMap;

      geoJsonDataList.forEach((geoJsonData, index) => {
        const sourceId = `source-${index}`;
        const layerId = LAYER_IDS[index];

        // 添加数据源
        this.layerService.addSource(sourceId, geoJsonData);

        // 添加图层
        this.layerService.addLayer({
          id: layerId,
          type: 'symbol',
          source: sourceId,
          layout: {
            'icon-image': ['get', 'icon'],
            'icon-size': 0.8,
            'icon-allow-overlap': true
          },
          filter: ['<', 'type', 0]
        });
      });
    },

    /**
     * 加载默认设备数据
     */
    loadDefaultDeviceData() {
      const defaultData = DeviceUtils.getDefaultStationData();
      const geoJsonDataList = DeviceUtils.transformToGeoJSON([defaultData, [], []]);
      this.addDeviceLayersToMap(geoJsonDataList);
    },
    /**
     * 加载管道风险数据
     */
    async loadPipeRiskData() {
      const geoJsonData = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: lowRiskPipeData.geometry,
            properties: lowRiskPipeData.properties
          },
          {
            type: 'Feature',
            geometry: middleRiskPipeData.geometry,
            properties: middleRiskPipeData.properties
          },
          {
            type: 'Feature',
            geometry: highRiskPipeData.geometry,
            properties: highRiskPipeData.properties
          }
        ]
      };

      // 添加数据源
      this.layerService.addSource('pipe-risk-source', geoJsonData);

      // 添加管道风险图层
      this.layerService.addLayer({
        id: 'pipe-risk-layer',
        type: 'line',
        source: 'pipe-risk-source',
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
          'visibility': 'none'
        },
        paint: {
          'line-color': [
            'case',
            ['==', ['get', 'riskLevel'], 'low'], '#28D38D',
            ['==', ['get', 'riskLevel'], 'middle'], '#FFC117',
            ['==', ['get', 'riskLevel'], 'high'], '#FF5C5C',
            '#CCCCCC'
          ],
          'line-width': 10,
          'line-opacity': 0.8
        }
      });

      console.log('管道风险图层加载成功');
    },
    /**
     * 加载事件数据
     */
    async loadEventData() {
      const eventTypeData = [
        { type: 'pressure', data: eventList.filter(item => item.type === 'pressure') },
        { type: 'leak', data: eventList.filter(item => item.type === 'leak') },
        { type: 'burstPipe', data: eventList.filter(item => item.type === 'burstPipe') }
      ];

      eventTypeData.forEach(({ type, data }) => {
        const geoJsonData = {
          type: 'FeatureCollection',
          features: data.map(item => ({
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: item.coordinates
            },
            properties: item
          }))
        };

        // 添加数据源
        this.layerService.addSource(`event-source-${type}`, geoJsonData);

        // 添加图层
        this.layerService.addLayer({
          id: `event-layer-${type}`,
          type: 'symbol',
          source: `event-source-${type}`,
          layout: {
            'icon-image': 'alert',
            'icon-size': 0.2,
            'icon-allow-overlap': true,
            'icon-ignore-placement': true,
            'visibility': 'none'
          }
        });
      });

      console.log('事件图层加载成功');
    },
    /**
     * 显示位置标记
     */
    showLocationMarker(locationData) {
      mapService.showLocationMarker(locationData);
    },
  },

  /**
   * 组件创建时的初始化
   */
  async created() {
    try {
      await authenticateAndStoreToken();
      console.info('渗漏认证Token已成功存储到Vuex');
    } catch (error) {
      console.error('渗漏认证失败:', error);
    }
  },

  /**
   * 组件销毁时的清理
   */
  beforeDestroy() {
    mapService.destroy();
  }

};
</script>
<style>
/* 提高 popup 的层级 */
.high-z-popup {
  z-index: 10000 !important;
}

.high-z-popup .mapboxgl-popup-content {
  z-index: 10001 !important;
}

.high-z-popup .mapboxgl-popup-tip {
  z-index: 10001 !important;
}

.my-popup {
  padding: 0;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  min-width: 200px;
  
  .popup-header {
    padding: 12px 16px 8px;
    border-bottom: 1px solid #f0f0f0;
    
    .device-id {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;
    }
  }
  
  .popup-content {
    padding: 8px 16px 12px;
  }
  
  .popup-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    line-height: 1.4;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .popup-label {
    font-size: 13px;
    color: #8c8c8c;
    margin-right: 12px;
    white-space: nowrap;
  }

  .popup-value {
    font-size: 13px;
    color: #262626;
    font-weight: 500;
    text-align: right;
    
    &.status-normal {
      color: #52c41a;
      background: #f6ffed;
      padding: 1px 6px;
      border-radius: 2px;
      font-size: 12px;
    }
    
    &.status-alarm {
      color: #ff4d4f;
      background: #fff2f0;
      padding: 1px 6px;
      border-radius: 2px;
      font-size: 12px;
    }
    
    &.status-offline {
      color: #8c8c8c;
      background: #f5f5f5;
      padding: 1px 6px;
      border-radius: 2px;
      font-size: 12px;
    }
  }
}
</style>
<style lang="scss" scoped>
.alert-icon {
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.pointed-banner {
  height: 34px;
  /* 1. 设置基础背景色 */
  //background: linear-gradient(90deg, #4D81FF 0%, rgba(0, 74, 255, 0.2902) 79%, rgba(0, 38, 131, 0) 99%);
  background-size: 4px 4px;
  background: linear-gradient(90deg, #4D81FF 0%, rgba(0, 74, 255, 0.2902) 79%, rgba(0, 38, 131, 0) 99%);

  /* 3. 使用 clip-path 创建左侧的尖角形状 */
  /* polygon(点1, 点2, 点3, 点4, 点5) */
  /* 点1: 0% 50% -> 最左侧的尖端中心点 */
  /* 点2: 4% 0% -> 矩形部分的左上角 */
  /* 点3: 100% 0% -> 矩形部分的右上角 */
  /* 点4: 100% 100% -> 矩形部分的右下角 */
  /* 点5: 4% 100% -> 矩形部分的左下角 */
  clip-path: polygon(0% 50%, 3% 0%, 100% 0%, 100% 100%, 3% 100%);

  /* 使用 Flexbox 方便内部文字居中 */
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

/* 5. 文字内容的样式 */
.banner-text {
  color: white;
  font-size: 14px;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);


  /* 增加内边距，让背景色区域比文字稍大一些 */
  padding: 8px 15px;

  /* 确保文字在红线之上 */
  z-index: 2;
}
</style>
