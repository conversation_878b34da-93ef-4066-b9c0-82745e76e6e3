import mapboxgl from 'mapbox-gl';

/**
 * 地图操作服务类
 * 负责处理所有与地图相关的操作：图层管理、事件监听、弹窗等
 */
export class MapService {
  constructor() {
    this.map = null;
    this.mapPopup = null;
    this.alertAnimationTimer = null;
    this.alertAnimationFrame = 0;
    this.locationMarker = null;
    this.visibleRiskLevels = ['low', 'middle', 'high'];
  }

  /**
   * 初始化地图服务
   */
  init() {
    this.map = window.rxMap;
    this.initPopup();
  }

  /**
   * 初始化单例 Popup
   */
  initPopup() {
    this.mapPopup = new mapboxgl.Popup({
      closeButton: false,
      closeOnClick: false,
      offset: 15,
      className: 'high-z-popup'
    });
  }

  /**
   * 显示设备弹窗
   */
  showDevicePopup(locationData) {
    if (!this.map || !locationData) return;

    const { properties, geometry } = locationData;
    const coordinates = geometry.coordinates;
    const data = typeof properties.data === 'string' ? JSON.parse(properties.data) : properties.data;

    const html = this.generatePopupHTML(properties, data);

    this.mapPopup
      .setLngLat(coordinates)
      .setHTML(html)
      .addTo(this.map);
  }

  /**
   * 生成弹窗HTML内容
   */
  generatePopupHTML(properties, data) {
    return `
      <div class="my-popup">
        <div class="popup-header">
          <div class="device-id">${properties.name}</div>
        </div>
        <div class="popup-content">
          <div class="popup-row">
            <div class="popup-label">站点类型：</div>
            <div class="popup-value">${data.siteType}</div>
          </div>
          <div class="popup-row">
            <div class="popup-label">站点状态：</div>
            <div class="popup-value status-${this.getStatusClass(data.siteStatus)}">${data.siteStatus}</div>
          </div>
          <div class="popup-row">
            <div class="popup-label">报警类型：</div>
            <div class="popup-value">${data.alertType}</div>
          </div>
          ${data.appendProps.map(item => `
            <div class="popup-row">
              <div class="popup-label">${item.label}：</div>
              <div class="popup-value">${item.value}</div>
            </div>
          `).join('')}
          <div class="popup-row">
            <div class="popup-label">最后上线时间：</div>
            <div class="popup-value">${data.alertTime}</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 获取状态对应的CSS类名
   */
  getStatusClass(status) {
    const statusMap = {
      '正常': 'normal',
      '报警': 'alarm',
      '离线': 'offline'
    };
    return statusMap[status] || 'normal';
  }

  /**
   * 隐藏弹窗
   */
  hidePopup() {
    if (this.mapPopup) {
      this.mapPopup.remove();
    }
  }

  /**
   * 添加图层事件监听
   */
  addLayerListeners(layerIds, eventCallback) {
    if (!this.map || !layerIds) return;

    layerIds.forEach(layerId => {
      // 鼠标进入显示弹窗
      this.map.on('mouseenter', layerId, (e) => {
        this.showDevicePopup(e.features[0]);
        this.map.getCanvas().style.cursor = 'pointer';
      });

      // 鼠标离开隐藏弹窗
      this.map.on('mouseleave', layerId, () => {
        this.hidePopup();
        this.map.getCanvas().style.cursor = '';
      });
    });
  }

  /**
   * 添加事件图层监听
   */
  addEventLayerListeners(eventTypes, clickCallback) {
    if (!this.map || !eventTypes) return;

    eventTypes.forEach(eventType => {
      const layerId = `event-layer-${eventType}`;
      this.map.on('click', layerId, (e) => {
        const locationData = e.features[0];
        const { id, type } = locationData.properties;
        if (clickCallback) {
          clickCallback({ id, type });
        }
      });
    });
  }

  /**
   * 控制图层显示/隐藏
   */
  toggleLayer(layerId, visible) {
    if (!this.map || !this.map.getLayer(layerId)) return;

    this.map.setLayoutProperty(layerId, 'visibility', visible ? 'visible' : 'none');
  }

  /**
   * 设置图层过滤器
   */
  setLayerFilter(layerId, filter) {
    if (!this.map || !this.map.getLayer(layerId)) return;

    this.map.setFilter(layerId, filter);
  }

  /**
   * 启动警报动画
   */
  startAlertAnimation() {
    if (!this.map) return;

    this.stopAlertAnimation();

    this.alertAnimationTimer = setInterval(() => {
      this.alertAnimationFrame++;
      const scale = 0.6 + 0.05 * Math.sin((this.alertAnimationFrame * Math.PI) / 30);

      const eventTypes = ['pressure', 'leak', 'burstPipe'];
      eventTypes.forEach(eventType => {
        const layerId = `event-layer-${eventType}`;
        if (this.map.getLayer(layerId)) {
          const visibility = this.map.getLayoutProperty(layerId, 'visibility');
          if (visibility === 'visible') {
            this.map.setPaintProperty(layerId, 'icon-opacity', 1);
            this.map.setLayoutProperty(layerId, 'icon-size', scale);
          }
        }
      });
    }, 50);
  }

  /**
   * 停止警报动画
   */
  stopAlertAnimation() {
    if (this.alertAnimationTimer) {
      clearInterval(this.alertAnimationTimer);
      this.alertAnimationTimer = null;
    }
  }

  /**
   * 显示位置标记
   */
  showLocationMarker(locationData) {
    if (!this.map) return;

    const { longitude, latitude, pipeInfo } = locationData;

    // 移除之前的标记
    if (this.locationMarker) {
      this.locationMarker.remove();
    }

    // 创建标记
    const markerElement = this.createMarkerElement();
    this.locationMarker = new mapboxgl.Marker(markerElement)
      .setLngLat([longitude, latitude])
      .addTo(this.map);

    // 创建并显示弹窗
    const popupContent = this.generateLocationPopupHTML(pipeInfo);
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: false
    }).setHTML(popupContent);

    this.locationMarker.setPopup(popup);
    popup.addTo(this.map);
    this.locationMarker.togglePopup();

    // 移动地图中心
    this.map.flyTo({
      center: [longitude, latitude],
      zoom: 15,
      duration: 1000
    });
  }

  /**
   * 创建标记元素
   */
  createMarkerElement() {
    const markerElement = document.createElement('div');
    markerElement.className = 'location-marker';
    markerElement.style.cssText = `
      width: 20px;
      height: 20px;
      background-color: #FF4444;
      border: 2px solid #FFFFFF;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      cursor: pointer;
    `;
    return markerElement;
  }

  /**
   * 生成位置弹窗HTML
   */
  generateLocationPopupHTML(pipeInfo) {
    return `
      <div style="padding: 8px; min-width: 200px;">
        <h4 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">${pipeInfo.管段ID}</h4>
        <div style="font-size: 12px; line-height: 1.5;">
          <div><strong>管线名称:</strong> ${pipeInfo.管线名称}</div>
          <div><strong>管材:</strong> ${pipeInfo.管材}</div>
          <div><strong>风险等级:</strong> <span style="color: ${this.getRiskColor(pipeInfo.风险等级)}">${pipeInfo.风险等级}</span></div>
          <div><strong>管龄:</strong> ${pipeInfo.管龄}</div>
          <div><strong>管径:</strong> ${pipeInfo.管径}</div>
        </div>
      </div>
    `;
  }

  /**
   * 获取风险等级颜色
   */
  getRiskColor(riskLevelText) {
    const colorMap = {
      '高风险': '#FF5C5C',
      '中风险': '#FFC117',
      '低风险': '#28D38D'
    };
    return colorMap[riskLevelText] || '#666666';
  }

  /**
   * 清理资源
   */
  destroy() {
    this.stopAlertAnimation();

    if (this.locationMarker) {
      this.locationMarker.remove();
      this.locationMarker = null;
    }

    if (this.mapPopup) {
      this.mapPopup.remove();
      this.mapPopup = null;
    }
  }
}

/**
 * 图层管理服务类
 */
export class LayerService {
  constructor(map) {
    this.map = map;
    this.visibleRiskLevels = ['low', 'middle', 'high'];
  }

  /**
   * 添加数据源
   */
  addSource(sourceId, sourceData) {
    if (!this.map || this.map.getSource(sourceId)) return;

    this.map.addSource(sourceId, {
      type: 'geojson',
      data: sourceData
    });
  }

  /**
   * 添加图层
   */
  addLayer(layerConfig) {
    if (!this.map || this.map.getLayer(layerConfig.id)) return;

    this.map.addLayer(layerConfig);
  }

  /**
   * 控制事件图层显示/隐藏
   */
  toggleEventLayer(eventType, visible) {
    const layerId = `event-layer-${eventType}`;
    this.toggleLayer(layerId, visible);
  }

  /**
   * 控制管道风险图层显示/隐藏
   */
  togglePipeRiskLayer(visible) {
    this.toggleLayer('pipe-risk-layer', visible);
  }

  /**
   * 控制图层显示/隐藏
   */
  toggleLayer(layerId, visible) {
    if (!this.map || !this.map.getLayer(layerId)) return;

    this.map.setLayoutProperty(layerId, 'visibility', visible ? 'visible' : 'none');
  }

  /**
   * 设置图层过滤器
   */
  setLayerFilter(layerId, filter) {
    if (!this.map || !this.map.getLayer(layerId)) return;

    this.map.setFilter(layerId, filter);
  }

  /**
   * 隐藏所有站点图层
   */
  hideAllStationLayers() {
    const layerIds = ['type1-layer', 'type2-layer', 'type3-layer'];
    layerIds.forEach(layerId => {
      this.setLayerFilter(layerId, ['<', 'type', 0]);
    });
  }

  /**
   * 隐藏所有事件图层
   */
  hideAllEventLayers() {
    const eventTypes = ['pressure', 'leak', 'burstPipe'];
    eventTypes.forEach(eventType => {
      this.toggleEventLayer(eventType, false);
    });
  }

  /**
   * 控制特定风险等级管线的显隐
   */
  togglePipeRiskByLevel(level, visible) {
    if (visible) {
      if (!this.visibleRiskLevels.includes(level)) {
        this.visibleRiskLevels.push(level);
      }
    } else {
      const index = this.visibleRiskLevels.indexOf(level);
      if (index > -1) {
        this.visibleRiskLevels.splice(index, 1);
      }
    }

    this.updatePipeRiskFilter();
  }

  /**
   * 更新管道风险图层的过滤器
   */
  updatePipeRiskFilter() {
    const layerId = 'pipe-risk-layer';
    if (!this.map || !this.map.getLayer(layerId)) return;

    if (this.visibleRiskLevels.length === 0) {
      this.setLayerFilter(layerId, ['==', 'riskLevel', '']);
    } else if (this.visibleRiskLevels.length === 3) {
      this.setLayerFilter(layerId, null);
    } else {
      const filter = ['any'];
      this.visibleRiskLevels.forEach(level => {
        filter.push(['==', ['get', 'riskLevel'], level]);
      });
      this.setLayerFilter(layerId, filter);
    }
  }
}

// 创建单例实例
export const mapService = new MapService();
