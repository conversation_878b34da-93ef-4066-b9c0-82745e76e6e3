/**
 * Overview组件常量配置
 */

// 图层ID配置
export const LAYER_IDS = [
  'type1-layer',
  'type2-layer',
  'type3-layer'
];

// 事件类型配置
export const EVENT_TYPES = ['pressure', 'leak', 'burstPipe'];

// 事件图层ID配置
export const EVENT_LAYER_IDS = [
  'event-layer-pressure',
  'event-layer-leak',
  'event-layer-burstPipe'
];

// 站点图层ID配置
export const STATION_LAYER_IDS = [
  'type1-layer',
  'type2-layer',
  'type3-layer'
];

// 管道风险等级配置
export const RISK_LEVELS = ['low', 'middle', 'high'];

// 风险等级颜色映射
export const RISK_COLORS = {
  low: '#28D38D',    // 绿色
  middle: '#FFC117', // 黄色
  high: '#FF5C5C'    // 红色
};

// 风险等级文本映射
export const RISK_LEVEL_TEXT = {
  '高风险': '#FF5C5C',
  '中风险': '#FFC117',
  '低风险': '#28D38D'
};

// 动画配置
export const ANIMATION_CONFIG = {
  ALERT_FRAME_INTERVAL: 50,  // 动画帧间隔（毫秒）
  ALERT_SCALE_BASE: 0.6,     // 基础缩放比例
  ALERT_SCALE_AMPLITUDE: 0.05, // 缩放幅度
  ALERT_CYCLE_FRAMES: 30     // 动画周期帧数
};

// 地图配置
export const MAP_CONFIG = {
  DEFAULT_ZOOM: 15,
  FLY_DURATION: 1000,
  POPUP_OFFSET: 15,
  MARKER_OFFSET: 25
};

// 工具栏配置
export const TOOLBAR_CONFIG = [
  {
    id: 'statusOverview',
    name: '实时监测',
    index: 0
  },
  {
    id: 'tunnelDanger',
    name: '管网风险',
    index: 1
  }
];

// 默认查询表单配置
export const DEFAULT_QUERY_FORM = {
  stationType: '',
  status: '',
  alertType: ''
};

// 默认事件查询表单配置
export const DEFAULT_EVENT_QUERY_FORM = {
  eventType: '',
  timeRange: []
};

// 分页配置
export const PAGINATION_CONFIG = {
  PAGE_SIZE: 10,
  CURRENT_PAGE: 1
};

// 表格列配置
export const TABLE_COLUMNS = {
  STATION: [
    { prop: 'name', label: '站点名称' },
    { prop: 'type', label: '站点类型' },
    { prop: 'status', label: '状态' },
    { prop: 'alertType', label: '报警类型' },
    { prop: 'lastOnlineTime', label: '最后上线时间' }
  ],
  EVENT: [
    { prop: 'id', label: '事件ID' },
    { prop: 'type', label: '事件类型' },
    { prop: 'location', label: '位置' },
    { prop: 'time', label: '发生时间' },
    { prop: 'status', label: '状态' }
  ]
};

// 状态卡片配置
export const STATUS_CARDS_CONFIG = [
  {
    title: '正常',
    type: 1,
    rgbColor: [40, 211, 141]
  },
  {
    title: '报警',
    type: 2,
    rgbColor: [255, 92, 92]
  },
  {
    title: '离线',
    type: 3,
    rgbColor: [111, 109, 109]
  },
  {
    title: '关注',
    type: 0,
    rgbColor: [77, 129, 255]
  }
];

// 事件卡片配置
export const EVENT_CARDS_CONFIG = [
  { title: '压力', imgName: 'pressure', type: 'pressure' },
  { title: '渗漏', imgName: 'leak', type: 'leak' },
  { title: '爆管', imgName: 'burstPipe', type: 'burstPipe' }
];

// 管道风险图层配置
export const PIPE_RISK_LAYER_CONFIG = {
  id: 'pipe-risk-layer',
  type: 'line',
  layout: {
    'line-join': 'round',
    'line-cap': 'round',
    'visibility': 'none'
  },
  paint: {
    'line-color': [
      'case',
      ['==', ['get', 'riskLevel'], 'low'], RISK_COLORS.low,
      ['==', ['get', 'riskLevel'], 'middle'], RISK_COLORS.middle,
      ['==', ['get', 'riskLevel'], 'high'], RISK_COLORS.high,
      '#CCCCCC'
    ],
    'line-width': 10,
    'line-opacity': 0.8
  }
};

// 图标布局配置
export const ICON_LAYOUT_CONFIG = {
  'icon-size': 0.8,
  'icon-allow-overlap': true
};

// 事件图标布局配置
export const EVENT_ICON_LAYOUT_CONFIG = {
  'icon-image': 'alert',
  'icon-size': 0.2,
  'icon-allow-overlap': true,
  'icon-ignore-placement': true,
  'visibility': 'none'
};

// 默认过滤器（隐藏所有）
export const HIDE_ALL_FILTER = ['<', 'type', 0];

// 消息配置
export const MESSAGE_CONFIG = {
  FOLLOW_SUCCESS: {
    type: 'success',
    message: '关注成功',
    duration: 500
  },
  FOLLOW_CANCEL: {
    type: 'info',
    message: '取消关注',
    duration: 500
  }
};

export default {
  LAYER_IDS,
  EVENT_TYPES,
  EVENT_LAYER_IDS,
  STATION_LAYER_IDS,
  RISK_LEVELS,
  RISK_COLORS,
  RISK_LEVEL_TEXT,
  ANIMATION_CONFIG,
  MAP_CONFIG,
  TOOLBAR_CONFIG,
  DEFAULT_QUERY_FORM,
  DEFAULT_EVENT_QUERY_FORM,
  PAGINATION_CONFIG,
  TABLE_COLUMNS,
  STATUS_CARDS_CONFIG,
  EVENT_CARDS_CONFIG,
  PIPE_RISK_LAYER_CONFIG,
  ICON_LAYOUT_CONFIG,
  EVENT_ICON_LAYOUT_CONFIG,
  HIDE_ALL_FILTER,
  MESSAGE_CONFIG
};
