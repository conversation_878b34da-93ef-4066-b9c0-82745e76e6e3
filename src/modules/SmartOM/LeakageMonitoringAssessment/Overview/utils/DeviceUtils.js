/**
 * 设备数据处理工具类
 * 负责设备状态映射、图标获取、数据转换等逻辑
 */

// 设备状态配置
export const DEVICE_CONFIG = {
  NORMAL: {
    bjstatus: 0,
    type: 1,
    status: '正常',
    iconSuffix: 'normal'
  },
  OFFLINE: {
    bjstatus: 2,
    type: 3,
    status: '离线',
    iconSuffix: 'gray'
  },
  ALARM: {
    bjstatus: 3,
    type: 2,
    status: '报警',
    iconSuffix: 'warning'
  }
};

// 设备图标映射
export const DEVICE_ICON_MAP = {
  '水锤和水音': 'hydrophone',
  '压力计': 'pressureGauge',
  '电磁流量计': 'electromagneticFlowmeter',
  '超声波流量计': 'ultrasonicFlowmeter',
  '液位计': 'levelGauge',
  'WH': 'hydrophone' // 默认水锤设备使用水听仪图标
};

// 图标资源列表
export const ICON_LIST = [
  // 正常状态图标
  { name: 'electromagneticFlowmeter', url: '/electromagneticFlowmeter' },
  { name: 'pressureGauge', url: '/pressureGauge' },
  { name: 'ultrasonicFlowmeter', url: '/ultrasonicFlowmeter' },
  { name: 'hydrophone', url: '/hydrophone' },
  { name: 'levelGauge', url: '/levelGauge' },
  // 离线状态图标
  { name: 'electromagneticFlowmeter-gray', url: '/electromagneticFlowmeter-gray' },
  { name: 'pressureGauge-gray', url: '/pressureGauge-gray' },
  { name: 'ultrasonicFlowmeter-gray', url: '/ultrasonicFlowmeter-gray' },
  { name: 'hydrophone-gray', url: '/hydrophone-gray' },
  { name: 'levelGauge-gray', url: '/levelGauge-gray' },
  // 异常状态图标
  { name: 'electromagneticFlowmeter-warning', url: '/electromagneticFlowmeter-warning' },
  { name: 'pressureGauge-warning', url: '/pressureGauge-warning' },
  { name: 'ultrasonicFlowmeter-warning', url: '/ultrasonicFlowmeter-warning' },
  { name: 'hydrophone-warning', url: '/hydrophone-warning' },
  { name: 'levelGauge-warning', url: '/levelGauge-warning' },
  // 诊断事件图标
  { name: 'alert', url: '/alert' }
];

/**
 * 设备数据处理工具类
 */
export class DeviceUtils {
  /**
   * 根据bjstatus获取设备配置
   */
  static getDeviceConfig(bjstatus) {
    const status = bjstatus ? parseInt(bjstatus) : DEVICE_CONFIG.NORMAL.bjstatus;
    return Object.values(DEVICE_CONFIG).find(config => config.bjstatus === status) || DEVICE_CONFIG.NORMAL;
  }

  /**
   * 根据设备类型和状态获取图标名称
   */
  static getDeviceIcon(deviceType, status = 'normal') {
    const baseIcon = DEVICE_ICON_MAP[deviceType] || 'hydrophone';
    
    if (status === 'warning') {
      return `${baseIcon}-warning`;
    } else if (status === 'gray') {
      return `${baseIcon}-gray`;
    }
    
    return baseIcon;
  }

  /**
   * 根据设备数据生成属性列表
   */
  static getDeviceProps(device) {
    const props = [];
    
    if (device.YL !== null && device.YL !== undefined) {
      props.push({ label: '压力(MPa)', value: device.YL.toString() });
    }
    if (device.BV !== null && device.BV !== undefined) {
      props.push({ label: '电池电压(V)', value: device.BV.toString() });
    }
    if (device.installaddr) {
      props.push({ label: '安装地址', value: device.installaddr });
    }
    
    return props;
  }

  /**
   * 将设备数据转换为地图站点数据
   */
  static transformDeviceToStation(device, index) {
    const deviceConfig = this.getDeviceConfig(device.bjstatus);
    
    return {
      id: device.sid || device._id || `device-${index}`,
      name: device.sbbh_name || device.name || device.sbbh,
      img: this.getDeviceIcon(device.waterHydrantType || device.type, deviceConfig.iconSuffix),
      type: deviceConfig.type,
      coordinates: device.positions,
      data: {
        siteType: device.waterHydrantType || '水锤监测设备',
        siteStatus: deviceConfig.status,
        alertType: device.alarms?.[0]?.nm || '-',
        alertTime: device.uploadt ? new Date(device.uploadt * 1000).toLocaleString() : '-',
        appendProps: this.getDeviceProps(device)
      }
    };
  }

  /**
   * 将设备列表按状态分类
   */
  static classifyDevicesByStatus(deviceList) {
    const sourceData1 = []; // 正常设备
    const sourceData2 = []; // 报警设备
    const sourceData3 = []; // 离线设备
    
    deviceList.forEach((device, index) => {
      const stationData = this.transformDeviceToStation(device, index);
      
      // 根据设备类型分类
      if (stationData.type === DEVICE_CONFIG.NORMAL.type) {
        sourceData1.push(stationData);
      } else if (stationData.type === DEVICE_CONFIG.ALARM.type) {
        sourceData2.push(stationData);
      } else if (stationData.type === DEVICE_CONFIG.OFFLINE.type) {
        sourceData3.push(stationData);
      }
    });
    
    return [sourceData1, sourceData2, sourceData3];
  }

  /**
   * 将站点数据转换为GeoJSON格式
   */
  static transformToGeoJSON(sourceDataList) {
    return sourceDataList.map(sourceItem => ({
      type: 'FeatureCollection',
      features: sourceItem.map(station => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: station.coordinates
        },
        properties: {
          id: station.id,
          name: station.name,
          type: station.type,
          icon: station.img,
          data: station.data
        }
      }))
    }));
  }

  /**
   * 统计设备状态数量
   */
  static countDeviceStatus(deviceList) {
    const statusCounts = {
      1: 0, // 正常设备数量
      2: 0, // 报警设备数量
      3: 0  // 离线设备数量
    };
    
    deviceList.forEach(device => {
      const deviceConfig = this.getDeviceConfig(device.bjstatus);
      statusCounts[deviceConfig.type]++;
    });
    
    return statusCounts;
  }

  /**
   * 获取默认站点数据（后备数据）
   */
  static getDefaultStationData() {
    return [
      {
        id: 's1',
        name: '站点A',
        img: 'electromagneticFlowmeter',
        type: 1,
        coordinates: [106.232255613, 29.596231566],
        data: {
          siteType: '电磁流量计',
          siteStatus: '正常',
          alertType: '-',
          alertTime: '2025-05-12 14:30:00',
          appendProps: [
            { label: '流量(m³/s)', value: '123.45' }
          ]
        }
      }
    ];
  }

  /**
   * 加载地图图标资源
   */
  static async loadMapIcons(map) {
    const loadImagePromises = ICON_LIST.map(({ name: iconId, url }) => {
      return new Promise((resolve, reject) => {
        if (map.hasImage(iconId)) {
          resolve();
          return;
        }
        
        map.loadImage(
          require(`@/modules/GisService/rx-app/src/assets/image/base${url}.png`),
          (error, image) => {
            if (error) {
              console.error(`Error loading image ${iconId}:`, error);
              return reject(error);
            }
            map.addImage(iconId, image);
            resolve();
          }
        );
      });
    });
    
    await Promise.all(loadImagePromises);
    console.log('All custom icons loaded.');
  }
}

export default DeviceUtils;
