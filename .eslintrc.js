module.exports = {
  root: true,
  parser: 'babel-eslint',
  env: {
    browser: true,
    node: true
  },
  extends: ['standard'],
  // required to lint *.vue files
  plugins: [
    'html',
    'import',
  ],
  // add your custom rules here
  rules: {
    "semi": ["error", "always"],
    "space-before-function-paren": ["error", {
      "anonymous": "always",
      "named": "never",
      "asyncArrow": "always"
    }],
    'no-console': process.env.NODE_ENV === 'development' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'development' ? 'warn' : 'off',
  },
  globals: {
    'AMapUI': true,
    'L': true,
    'turf': true,
    'Cesium': true,
    'SuperMap': true
  }
}
