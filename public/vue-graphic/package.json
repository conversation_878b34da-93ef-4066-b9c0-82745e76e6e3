{"name": "vue-graphic", "version": "0.4.11", "description": "a Vue 2.0 based component library for graphics drawing", "author": "<EMAIL>", "private": true, "main": "./lib/vue-graphic.umd.min.js", "repository": {"type": "git", "url": "http://*************:8888/dam/micro-front/processlinecomponent.git"}, "license": "ISC", "files": ["lib/*", "package.json"], "scripts": {"lib": "vue-cli-service build --target lib --dest lib --name vue-graphic src/lib.js"}, "dependencies": {"axios": "^0.19.0", "core-js": "^3.6.5", "core-js-compat": "^3.6.5", "d3": "^5.10.0", "d3-selection-multi": "^1.0.1", "dayjs": "^1.8.16", "element-ui": "^2.12.0", "file-saver": "^2.0.2", "html2canvas": "1.0.0-rc.1", "lodash": "^4.17.15", "print-js": "^1.0.63", "query-string": "5", "v-contextmenu": "2.8.1", "vue": "^2.6.10", "vue-i18n": "^8.17.7", "vue-router": "^3.0.3"}, "devDependencies": {"@babel/polyfill": "^7.4.4", "@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-service": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "node-sass": "^4.9.0", "sass-loader": "^8.0.0", "vue-template-compiler": "^2.6.10"}}