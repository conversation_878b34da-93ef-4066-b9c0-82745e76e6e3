"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _es = _interopRequireDefault(require("element-ui/lib/locale/lang/es"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = { ..._es.default,
  "graphic": {
    "whole": {
      "back": "volver",
      "finish": "completar",
      "name": "nombre",
      "none": "ninguna",
      "day": "días",
      "endTime": "hora de finalización",
      "empty": "limpiar",
      "prompt": "insinuación",
      "sure": "confirmar",
      "cancel": "cancelar",
      "time": "hora",
      "artificial": "manual",
      "export": "exportar",
      "exportFailed": "fracaso de exportación",
      "exportSuccess": "exportado",
      "measurementValue": "Valor medido",
      "year": "año",
      "month": "mes"
    },
    "time": {
      "week": "Semana",
      "midTenDays": "2",
      "hour": "Hora",
      "lastTenDays": "3",
      "firstTenDays": "1",
      "season": "Trimestre",
      "day": "Día",
      "tenDays": "Diez días"
    },
    "codeTree": {
      "keyWordPlaceholder": 'Ingresar una búsqueda de palabras clave ...'
    },
    "graphStyle": {
      "auto": "automático",
      "fangsong": "Como una canción",
      "songti": "Times New Roman",
      "cannotBeDeleted": "¿Está seguro de eliminarlo?",
      "save": "Guardar cambios",
      "axis": "eje",
      "down": "debajo",
      "out": "exterior",
      "graphStyle": "Atributos de hoja",
      "min": "Valor mínimo",
      "decimalNumber": "Lugares decimales",
      "fontFamily": "Fuente",
      "kaiti": "Cursiva",
      "up": "arriba",
      "inverse": "Visualización del eje inverso",
      "padding": "Margen",
      "tickNumber": "Número de escala",
      "max": "Máximo",
      "microsoftYaHei": "Microsoft Yahei",
      "heiti": "Negrita",
      "right": "Correcto",
      "deleteGraph": "Eliminar gráfico",
      "applyToAll": "Aplicar a todo",
      "microsoftJhenghei": "Microsoft es negro",
      "left": "izquierda",
      "showTitle": "Mostrar título",
      "fontSize": "tamaño de fuente",
      "position": "posición",
      "newSongti": "Nueva dinastía Song"
    },
    "graphWorkArea": {
      "template": "modelo",
      "valueFilter": "Valor",
      "distributionLine": "Subcableado",
      "code": "Punto de medición",
      "auditStatusOption3": "Comprobación fallida",
      "auditStatusOption2": "Comprobado",
      "showDepth": "Mostrar profundidad enterrada",
      "processLine": "Línea de proceso",
      "valueStatus": "Estado de medición",
      "dateValidInfo1": "La hora límite no puede ser anterior a la hora de inicio",
      "showVector": "Mostrar nombre del componente",
      "dateValidInfo3": "La última hora no puede ser anterior a la hora de inicio",
      "dateValidInfo2": "El tiempo de corte no puede ser anterior al tiempo de inicio",
      "writeTypeOption2": "automático",
      "writeType": "Tipo de colección",
      "printSetting": "Configuraciones de impresión",
      "valueType": "Tipo de medida",
      "auditStatusOption1": "Desenfrenado",
      "valueStatusOption3": "anormal",
      "valueStatusOption2": "normal",
      "valueStatusOption1": "desconocido",
      "cannotBeCleared": "Confirmar para vaciar, ¿no se puede restaurar después de vaciar?",
      "valueStatusOption5": "Desaparecido",
      "environmentVectorWarning": "Seleccionar el marco de la imagen y la cantidad de entorno que se agregará.",
      "valueStatusOption4": "error",
      "latestValueDate": "Hace poco",
      "valueTypeOption1": "Medida ordinaria",
      "codeAndVectorWarning": "Seleccionar el punto de medición o componente",
      "valueTypeOption2": "Medida encriptada",
      "valueTypeOption3": "Medido durante la temporada de inundaciones",
      "valueTypeOption4": "Medida especifica",
      "addToGraph": "Agregar al marco seleccionado",
      "firstValueDate": "Tiempo de empezar",
      "clinographLine": "Clinómetro",
      "valueFilterSettingTip": "(El marco dibujado después de guardar no cambiará)",
      "print": "Imprimir",
      "maxLineCount": "Número de líneas por cuadro para un solo componente.",
      "auditStatus": "Estado de aprobación",
      "graphEmptyPlaceholder": "Arrastre el punto seleccionado a la izquierda para establecer esto",
      "valueFilterSetting": "Presets de dibujo",
      "startDate": "Hora de inicio",
      "environmentVector": "Cantidad ambiental",
      "distributionGraph": "Distribución"
    },
    "lineStyle": {
      "dashed": "linea punteada",
      "strokeWidth": "Grosor de línea",
      "solid": "línea sólida",
      "missingExamineDays": "Falta de tiempo de prueba",
      "color": "Color de linea",
      "baseLineStyle": "Atributos básicos",
      "symbolSize": "Talla",
      "yPosition": "Posición del eje vertical",
      "triangle": "triángulo",
      "straight": "línea recta",
      "square": "cuadrado",
      "curves": "curva",
      "bar": "Histograma",
      "diamond": "diamante",
      "lineStyle": "Atributos de línea",
      "marker": "marca",
      "deleteLine": "Tachado",
      "name": "Nombre de línea",
      "dasharray": "Lineal",
      "style": "estilo",
      "circle": "Redondo",
      "dotted": "Linea punteada",
      "symbolNumber": "Número"
    },
    "lineChart": {
      "copyGraph": "Copiar marco de imagen",
      "exportWPS": "Para exportar a WPS, se necesita pegar la imagen por sí mismo y el marco de la imagen se ha copiado para usted…",
      "exporting": "Exportando, esperar ...",
      "messageCopyError": "Copia fallida",
      "exportAs": "Exportar cómo",
      "messageCopySuccess": "Copia exitosa",
      "copyGraphPlaceholder": "Haga clic derecho en la imagen para copiar",
      "viewCode": "Ver valores medidos",
      "cancelManualComparison": "Cancele la comparación manual",
      "manualComparison": "Comparación manual"
    }
  }
};
exports.default = _default;
