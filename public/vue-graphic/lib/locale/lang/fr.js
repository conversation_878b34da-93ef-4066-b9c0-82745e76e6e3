"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _fr = _interopRequireDefault(require("element-ui/lib/locale/lang/fr"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = { ..._fr.default,
  "graphic": {
    "whole": {
      "back": "Retour",
      "finish": "Finir",
      "name": "Nom",
      "none": "Rien",
      "day": "Jour",
      "endTime": "Temps de fin",
      "empty": "Vider",
      "prompt": "Note",
      "sure": "Assurer",
      "cancel": "Annuler",
      "time": "Temps",
      "artificial": "Artificiel",
      "export": "Exporter",
      "exportFailed": "Échec de l'exportation",
      "exportSuccess": "Succès de l'exportation",
      "measurementValue": "Valeur de surveillance",
      "year": "Année",
      "month": "Mois"
    },
    "time": {
      "week": "semaine",
      "midTenDays": "2",
      "hour": "heure",
      "lastTenDays": "3",
      "firstTenDays": "1",
      "season": "saison",
      "day": "journée",
      "tenDays": "dix jours"
    },
    "codeTree": {
      "keyWordPlaceholder": 'Veuillez entrer les mots clés pour rechercher ...'
    },
    "graphStyle": {
      "auto": "automatique",
      "fangsong": "FangSong",
      "songti": "Simsuncss",
      "cannotBeDeleted": "déterminer de le supprimer, non récupérable après la suppression?",
      "save": "enregistrer",
      "axis": "axe",
      "down": "bas",
      "out": "en dehors",
      "graphStyle": "propriété graphique",
      "min": "le minimum",
      "decimalNumber": "décimale",
      "fontFamily": "Police de caractère",
      "kaiti": "KaiTi",
      "up": "haut",
      "inverse": "Coordonner l'axe inverse",
      "padding": "marge",
      "tickNumber": "échelle",
      "max": "maximum",
      "microsoftYaHei": "Microsoft Yahei",
      "heiti": "Simhei",
      "right": "droite",
      "deleteGraph": "supprimer",
      "applyToAll": "s'appliquer à tous",
      "microsoftJhenghei": "Microsoft Jhenghei",
      "left": "gauche",
      "showTitle": "Titre",
      "fontSize": "taille de police",
      "position": "position",
      "newSongti": "NSimSun"
    },
    "graphWorkArea": {
      "template": "modèle",
      "valueFilter": "évaluation",
      "distributionLine": "graphique de distribution",
      "code": "point de mesure",
      "auditStatusOption3": "échoué",
      "auditStatusOption2": "vérifié",
      "showDepth": "profondeur enfouie",
      "processLine": "hydrogramme",
      "valueStatus": "état de la mesure",
      "dateValidInfo1": "date limite ne peut pas être antérieure à l'heure de début",
      "showVector": "Nom du composant",
      "dateValidInfo3": "la dernière heure ne peut pas être antérieure à l'heure de début",
      "dateValidInfo2": "date limite ne peut pas être antérieure à l'heure de début de la mesure",
      "writeTypeOption2": "automatique",
      "writeType": "type d'acquisition",
      "printSetting": "paramètres d'impression",
      "valueType": "type de mesure",
      "auditStatusOption1": "décoché",
      "valueStatusOption3": "anormal",
      "valueStatusOption2": "Ordinaire",
      "valueStatusOption1": "inconnus",
      "cannotBeCleared": "Décider de le vider et vous ne pouvez pas récupérer après avoir vidé?",
      "valueStatusOption5": "absence",
      "environmentVectorWarning": "Sélectionner le graphique et les variables d'environnement à ajouter.",
      "valueStatusOption4": "Erreur",
      "latestValueDate": "dernière fois",
      "valueTypeOption1": "mesures générales",
      "codeAndVectorWarning": "Sélectionner un point de mesure ou un composant",
      "valueTypeOption2": "mesure cryptée",
      "valueTypeOption3": "mesure des crues",
      "valueTypeOption4": "mesure spécifique",
      "addToGraph": "Ajouter au graphique sélectionné",
      "firstValueDate": "heure de début de la mesure",
      "clinographLine": "diagramme d'inclinaison",
      "valueFilterSettingTip": "",
      "print": "impression",
      "maxLineCount": "Nombre de lignes par graphique pour un seul composant",
      "auditStatus": "état de l'audit",
      "graphEmptyPlaceholder": "Glisser le point de mesure sélectionné ici.",
      "valueFilterSetting": "dessin prédéfini",
      "startDate": "Heure de début",
      "environmentVector": "variables environnementales",
      "distributionGraph": "diagramme de distribution"
    },
    "lineStyle": {
      "dashed": "ligne pointillée",
      "strokeWidth": "largeur",
      "solid": "ligne continue",
      "missingExamineDays": "manque de temps de mesure",
      "color": "couleur",
      "baseLineStyle": "propriété de base",
      "symbolSize": "taille",
      "yPosition": "position de l'axe Y",
      "triangle": "triangle",
      "straight": "tout droit",
      "square": "carré",
      "curves": "courbe",
      "bar": "bar",
      "diamond": "rhombe",
      "lineStyle": "propriété de ligne",
      "marker": "étiquette",
      "deleteLine": "supprimer",
      "name": "nom de ligne",
      "dasharray": "type de ligne",
      "style": "style",
      "circle": "cercle",
      "dotted": "ligne de chaîne",
      "symbolNumber": "nombre"
    },
    "lineChart": {
      "copyGraph": "copier le graphique",
      "exportWPS": "Pour exporter vers WPS, vous devez coller l’image vous-même, la feuille de carte a été copiée pour vous...",
      "exporting": "Exportation maintenant, veuillez patienter…",
      "messageCopyError": "copie échoue",
      "exportAs": "exporté en tant que",
      "messageCopySuccess": "succès de la copie",
      "copyGraphPlaceholder": "cliquer droit sur l'image à copier.",
      "viewCode": "vue",
      "cancelManualComparison": "Annuler la comparaison manuelle",
      "manualComparison": "Comparaison manuelle"
    }
  }
};
exports.default = _default;
