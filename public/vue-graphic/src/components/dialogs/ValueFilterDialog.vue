<template>
  <el-dialog
    custom-class="normal-padding"
    :width="width"
    :visible.sync="dialogVisible"
  >
    <h5 class="h5 el-dialog__title vg-my-0" slot="title">
      {{t('graphic.graphWorkArea.valueFilterSetting')}}
      <span style="font-size: 13px;">
        ({{t('graphic.graphWorkArea.valueFilterSettingTip')}})
      </span>
    </h5>
    <el-form :model="form" ref="form" inline label-position="left">
      <el-row>
        <el-col :span="12">
          <el-form-item
            class="label-no-padding form-item--flex"
            :label="`${t('graphic.graphWorkArea.maxLineCount')}:`"
            label-width="180px"
            prop="maxLineCount"
          >
            <el-input
              size="small"
              type="number"
              :min="1"
              v-model.number="form.maxLineCount"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="showVector">
            <el-checkbox v-model="form.showVector">{{t('graphic.graphWorkArea.showVector')}}</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="showDepth">
            <el-checkbox v-model="form.showDepth">{{t('graphic.graphWorkArea.showDepth')}}</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.graphWorkArea.valueFilter')}}</h6>
      <el-row>
        <el-form-item
          :label="`${t('graphic.whole.time')}:`"
          prop="value.startDayTime"
        >
          <el-time-picker
            style="width: 120px;"
            :default-value="defaultStartDayTime"
            prefix-icon="el-icon-date"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="form.value.startDayTime"
            @change="startDayTimeChange"
          ></el-time-picker>
        </el-form-item>
        <el-form-item label="~" prop="endDayTime">
          <el-time-picker
            style="width: 120px;"
            :default-value="defaultEndDayTime"
            prefix-icon="el-icon-date"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="form.value.endDayTime"
            @change="endDayTimeChange"
          ></el-time-picker>
        </el-form-item>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            class="label-no-padding form-item--flex"
            :label="`${t('graphic.graphWorkArea.writeType')}:`"
            label-width="74px"
            prop="value.writeType"
          >
            <el-select v-model="form.value.writeType" multiple>
              <el-option
                v-for="(item, index) in writeTypes"
                :key="item"
                :label="item"
                :value="index">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item
            class="label-no-padding form-item--flex"
            :label="`${t('graphic.graphWorkArea.auditStatus')}:`"
            label-width="74px"
            prop="value.auditStatus"
          >
            <el-select v-model="form.value.auditStatus" multiple>
              <el-option
                v-for="(item, index) in auditStatus"
                :key="item"
                :label="item"
                :value="index">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            class="label-no-padding form-item--flex vg-mb-1"
            :label="`${t('graphic.graphWorkArea.valueStatus')}:`"
            label-width="74px"
            prop="value.valueStatus"
          >
            <el-select v-model="form.value.valueStatus" multiple>
              <el-option
                v-for="(item, index) in valueStatus"
                :key="item"
                :label="item"
                :value="Number(index)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item
            class="label-no-padding form-item--flex vg-mb-1"
            :label="`${t('graphic.graphWorkArea.valueType')}:`"
            label-width="74px"
            prop="value.valueType"
          >
            <el-select v-model="form.value.valueType" multiple>
              <el-option
                v-for="(item, index) in valueTypes"
                :key="item"
                :label="item"
                :value="index">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button class="vg-px-3 vg-mx-2" type="secondary" size="small" @click="close">{{t('graphic.whole.cancel')}}</el-button>
      <el-button class="vg-px-3 vg-mx-2" type="primary" size="small" @click="save">{{t('graphic.whole.sure')}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import * as _ from 'lodash';
  import dayjs from 'dayjs';
  import {VALUE_FILTER_OPTION} from '../../assets/js/options';
  import {getWriteTypes, getAuditStatus, getValueStatus, getValueTypes} from '../../assets/js/constants';

  const defaultStartDayTime = dayjs(new Date()).format('YYYY-MM-DD [00:00:00]');
  const defaultEndDayTime = dayjs(new Date()).format('YYYY-MM-DD [23:59:59]');

  export default {
    name: 'ValueFilterDialog',
    props: {
      width: {
        type: String,
        default: '570px'
      },
      writeTypes: {
        type: [Array, Object],
        default: function () {
          return getWriteTypes.call(this);
        }
      },
      auditStatus: {
        type: [Array, Object],
        default: function () {
          return getAuditStatus.call(this);
        }
      },
      valueStatus: {
        type: [Array, Object],
        default: function () {
          return getValueStatus.call(this);
        }
      },
      valueTypes: {
        type: [Array, Object],
        default: function () {
          return getValueTypes.call(this);
        }
      }
    },
    data() {
      return {
        dialogVisible: false,
        defaultStartDayTime,
        defaultEndDayTime,
        form: _.cloneDeep(VALUE_FILTER_OPTION)
      };
    },
    methods: {
      startDayTimeChange(value) {
        if (!value) {
          this.$set(this.form.value, 'startDayTime', '');
        }
      },
      endDayTimeChange(value) {
        if (!value) {
          this.$set(this.form.value, 'endDayTime', '');
        }
      },
      /**
       * 打开取值弹框
       */
      open(form) {
        if (form) {
          this.form = _.cloneDeep(form);
        }
        this.dialogVisible = true;
      },
      /**
       * 关闭取值弹框
       */
      close() {
        this.$refs.form.resetFields();
        this.dialogVisible = false;
      },
      /**
       * 保存取值
       */
      save() {
        this.$emit('save', _.cloneDeep(this.form));
        this.close();
      }
    }
  };
</script>
