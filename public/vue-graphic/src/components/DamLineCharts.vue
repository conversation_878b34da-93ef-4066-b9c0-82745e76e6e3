<template>
  <div v-if="graphOptions.length">
    <div id="lineCharts">
      <template v-for="(item, index) in graphOptions">
        <LineChart
          :key="index"
          :ref="'graph_' + index"
          class="vg-border vg-border-transparent vg-rounded"
          :class="{active: index === graphIndex || multiSelectedGraph.includes(index)}"
          :style="`margin-top: ${index > 0 ? '18px': 0};`"
          :option="item.option"
          :editable="item.editable"
          :replicable="item.replicable"
          :exportable="item.exportable"
          :queryable="item.queryable"
          :scaleable="item.scaleable"
          :width="item.width"
          :height="item.height"
          :minHeight="item.minHeight"
          @drop.native.stop="$emit('dropOnGraph', $event, index)"
          @dragover.prevent=""
          @titleChange="updateOption(index, $event)"
          @lineSelect="lineSelect(index, $event)"
          @lineDelete="updateOption(index, $event)"
          @dblclick="graphSelect(index)"
          @graphSelect="graphSelect(index)"
          @graphDelete="deleteGraph(index)"
          @valueQuery="$emit('valueQuery', $event)"
          @ctrlClick="ctrlClick(index)"
          @comparedChange="$emit('comparedChange', index)"
          @dataPointClick="$emit('dataPointClick', index, $event)"
          @scaleChange="(zoomed, data) => $emit('scaleChange', index, zoomed, data)"
          @load="loadedChartsNum++"
        ></LineChart>
      </template>
    </div>
    <!-- 修改图幅、线条属性的部分功能需要在多图幅条件下（如：删除图幅），且考虑到性能就放在多控件中 -->
    <!--图幅属性-->
    <GraphStyleDrawer
      ref="graphStyleDrawer"
      @cancel="closeGraphStyle"
      @delete="deleteGraph"
      @save="saveGraphStyle"
    ></GraphStyleDrawer>
    <!--线条属性-->
    <LineStyleDrawer
      ref="lineStyleDrawer"
      @cancel="closeLineStyle"
      @delete="deleteLine"
      @save="saveLineStyle"
    ></LineStyleDrawer>
  </div>
</template>

<script>
  import * as _ from 'lodash';
  import LineStyleDrawer from './drawers/LineStyleDrawer';
  import GraphStyleDrawer from './drawers/GraphStyleDrawer';
  import { serialize } from '../assets/js/d3-util';

  export default {
    name: 'DamLineCharts',
    components: {
      LineStyleDrawer,
      GraphStyleDrawer
    },
    props: {
      // 多图幅初始化配置
      options: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        // 可修改的多图幅配置
        graphOptions: [],
        graphIndex: null,
        lineIndex: null,
        multiSelectedGraph: [],
        loadedChartsNum: 0
      };
    },
    watch: {
      loadedChartsNum(num) {
        if (this.graphOptions.length > 0 && num === this.graphOptions.length) {
          this.$emit('load');
        }
      }
    },
    methods: {
      /**
       * 获取所有图幅完整配置
       */
      getGraphOptions() {
        return this.graphOptions.map((option, index) => this.getGraphOption(index));
      },
      /**
       * 获取图幅完整配置
       * @param index 图幅下标
       */
      getGraphOption(index) {
        return this.$refs['graph_' + index][0].getGraphOption();
      },
      /**
       * 更新单个图幅配置
       * @param option 图幅对象
       * @param index 图幅下标
       */
      updateGraphOption(option, index) {
        // 更新前需重置图幅
        this.resetGraph(index);
        this.graphOptions.splice(index, 1, option);
        this.$nextTick(() => {
          this.reloadGraph(index);
          this.$emit('change', this.graphOptions);
        });
      },
      /**
       * 添加单个图幅配置
       * @param option 图幅对象
       * @param insertIndex 插入图幅的下标，默认为在末尾追加
       */
      addGraphOption(option, insertIndex = this.graphOptions.length) {
        this.graphOptions.splice(insertIndex, 0, option);
        this.$nextTick(() => {
          this.reloadGraph(insertIndex);
          this.$emit('change', this.graphOptions);
        });
      },
      /**
       * 选中图幅
       * @param index 图幅下标
       */
      graphSelect(index) {
        if (this.graphOptions[index].editable) {
          this.graphIndex = index;
          this.$refs.graphStyleDrawer.open(this.getGraphOption(index));
        }
      },
      /**
       * 清空图幅
       */
      clear() {
        this.graphOptions = [];
        this.$emit('change', this.graphOptions);
      },
      /**
       * 删除图幅
       */
      deleteGraph(graphIndex = this.graphIndex) {
        this.graphOptions.splice(graphIndex, 1);
        this.closeGraphStyle();
        this.$emit('change', this.graphOptions);
        this.reloadAllGraph();
      },
      /**
       * 关闭图幅属性
       */
      closeGraphStyle() {
        this.graphIndex = null;
      },
      /**
       * 保存图幅属性
       * @param isToAll 是否应用到所有图幅
       * @param option 已修改的图幅配置
       */
      saveGraphStyle(isToAll, option) {
        if (isToAll) {
          this.graphOptions.forEach((graph) => {
            this.$set(graph, 'option', option);
          });
          this.reloadAllGraph(false);
        } else {
          this.$set(this.graphOptions[this.graphIndex], 'option', option);
          this.reloadGraph(this.graphIndex, false);
        }
        this.closeGraphStyle();
        this.$emit('change', this.graphOptions);
      },
      /**
       * 选中图幅线条
       * @param graphIndex 图幅下标
       * @param lineIndex 线条下标
       */
      lineSelect(graphIndex, lineIndex) {
        if (this.graphOptions[graphIndex].editable) {
          this.graphIndex = graphIndex;
          this.lineIndex = lineIndex;
          const graphConfig = this.getGraphOption(graphIndex);
          const lineConfig = graphConfig.series[lineIndex];
          const yAxis = graphConfig.yAxis[lineConfig.yAxisIndex];
          this.$refs.lineStyleDrawer.open({
            ...lineConfig,
            position: yAxis.position
          }, graphConfig.type);
        }
      },
      /**
       * 删除线条
       */
      deleteLine(graphIndex = this.graphIndex, lineIndex = this.lineIndex) {
        this.$refs['graph_' + graphIndex][0].deleteLine(lineIndex);
        this.saveLineStyle(false, null);
        this.closeLineStyle();
      },
      updateOption(index, option) {
        this.$set(this.graphOptions[index], 'option', option);
      },
      /**
       * 关闭线条属性
       */
      closeLineStyle() {
        this.graphIndex = null;
        this.lineIndex = null;
      },
      /**
       * 保存线条属性
       * @param isToAll 是否应用到所有图幅线条
       * @param series 已修改的线条配置，为空时删除该线条
       */
      saveLineStyle(isToAll, series) {
        if (series) {
          const {position, ...lineConfig} = series;
          if (isToAll) {
            this.graphOptions.forEach((graph) => {
              // 改变纵轴位置
              graph.option.yAxis[lineConfig.yAxisIndex].position = position;
              graph.option.series.splice(this.lineIndex, 1, lineConfig);
            });
            this.reloadAllGraph();
          } else {
            const option = this.graphOptions[this.graphIndex].option;
            // 改变纵轴位置
            option.yAxis[lineConfig.yAxisIndex].position = position;
            option.series.splice(this.lineIndex, 1, lineConfig);
            this.reloadGraph(this.graphIndex);
          }
        } else {
          this.$set(
            this.graphOptions[this.graphIndex],
            'option',
            this.getGraphOption(this.graphIndex)
          );
        }
        this.closeLineStyle();
      },
      /**
       * 重置所有图幅
       */
      resetAllGraph() {
        for (let i = 0; i < this.graphOptions.length; i++) {
          this.resetGraph(i);
        }
      },
      /**
       * 重置单个图幅
       * @param index 图幅下标
       */
      resetGraph(index) {
        if (this.loadedChartsNum > 0) this.loadedChartsNum--;
        this.$refs['graph_' + index][0].resetChart();
      },
      /**
       * 重新加载所有图幅
       * @param needReloadData 是否需要重新加载数据
       */
      reloadAllGraph(needReloadData = true) {
        for (let i = 0; i < this.graphOptions.length; i++) {
          this.reloadGraph(i, needReloadData);
        }
      },
      /**
       * 加载单个图幅
       * @param index 图幅下标
       * @param needReloadData 是否需要重新加载数据
       */
      reloadGraph(index, needReloadData = true) {
        if (this.loadedChartsNum > 0) this.loadedChartsNum--;
        this.$refs['graph_' + index][0].renderChart(this.graphOptions[index].option, needReloadData);
      },
      /**
       * 清除某个图幅的多选状态
       * @param index
       */
      clearSelected(index) {
        const findIndex = this.multiSelectedGraph.indexOf(index);
        if (findIndex > -1) {
          this.multiSelectedGraph.splice(findIndex, 1);
        }
      },
      /**
       * ctrl+单击多选
       * @param index 当前选中的图幅下标
       */
      ctrlClick(index) {
        if (this.selectedGraph === index) {
          this.selectedGraph = null;
          return;
        }
        const findIndex = this.multiSelectedGraph.indexOf(index);
        if (findIndex > -1) {
          this.multiSelectedGraph.splice(findIndex, 1);
        } else {
          this.multiSelectedGraph.push(index);
        }
        this.$emit('multiSelect', this.multiSelectedGraph);
      },
      /**
       * 获取svg图片
       * @returns {Promise<Blob[]>}
       */
      getImages() {
        const els = Array.from(document.getElementById('lineCharts').childNodes);
        return Promise.all(els.map((ref) => serialize(ref.querySelector('svg'))));
      }
    },
    mounted() {
      this.graphOptions = _.cloneDeep(this.options);
    }
  };
</script>

<style scoped>
  .active {
    border-color: #6dbdff !important;
  }
</style>
