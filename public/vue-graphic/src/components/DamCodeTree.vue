<template>
  <div
    class="d-flex flex-column flex-grow-1 ofy-auto"
    :class="{'align-items-end': showVectors}"
    :draggable="draggable"
    @dragstart="dragStart"
  >
    <el-input
      :placeholder="t('graphic.codeTree.keyWordPlaceholder')"
      prefix-icon="el-icon-search"
      style="line-height: 30px;"
      clearable
      :maxlength="50"
      v-model.trim="filterText"
      v-if="showFilter"
    ></el-input>
    <el-tree
      class="damCodeTree align-self-start flex-grow-1 of-auto"
      :style="`width: ${treeWidth}`"
      :data="treeList"
      :props="defaultProps"
      :filter-node-method="filterNode"
      ref="tree"
      node-key="id"
      highlight-current
      :expand-on-click-node="false"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="checkedNodeKeys"
      :current-node-key="currentNodeKey"
      :show-checkbox="showCheckbox"
      :allow-drop="allowDrop"
      :lazy="lazy"
      :load="load"
      @node-click="handleNodeClick"
      @check="check"
      v-if="treeList && treeList.length > 0">
      <span
        class="custom-tree-node"
        slot-scope="{node, data}"
        :title="node.label"
        @dblclick="expand(node)"
        @contextmenu.prevent="$emit('contextmenu', $event, node)"
      >
        <slot name="node" :node="node" :data="data">
          <template v-if="data.itemId">
            <i class="vg-icon iconicon_folder vg-text-warning" v-if="node.expanded"></i>
            <i class="vg-icon iconwenjianjiashouqi vg-text-info" v-else></i>
          </template>
          {{node.label}}
          <span class="vg-text-gray-dark" v-if="data.templateCount"> {{'('+data.templateCount+')'}}</span>
        </slot>
      </span>
    </el-tree>
    <div
      class="vg-bg-white full-width ofy-auto vg-border-top d-flex flex-column flex-shrink-0"
      :style="`height: ${showVector ? 120 : 20}px`"
      v-if="showVectors"
    >
      <div class="cursor-pointer text-center">
        <img class="rotate-180" src="../assets/images/image-fold.png"
             @click="showVector = false" v-if="showVector">
        <img class="rotate-180" src="../assets/images/image-unfold.png"
             @click="showVector = true" v-else>
      </div>
      <div class="flex-grow-1 ofy-auto vg-p-2" v-if="showVector">
        <div v-for="(item, key) in checkedNodesGroup" :key="key">
          <p class="font-weight-bold vg-my-1">{{itemMap[key]}}
            <span v-if="environmentLeaves.indexOf(item[0].codeId) > -1">({{t('graphic.graphWorkArea.environmentVector')}})</span>
          </p>
          <el-checkbox-group v-model="selectedVectorGroup[key]">
            <el-checkbox :label="JSON.stringify(vector)"
                         :key="vector.valueVectorId"
                         v-for="vector in item[0].vectors">
              {{vector.valueVectorName}}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import _ from 'lodash';
  import {makePath} from '../assets/js/utils';

  export default {
    name: 'DamCodeTree',
    props: {
      // 展示数据
      treeList: {
        type: Array,
        required: true
      },
      // 树节点配置选项
      defaultProps: {
        type: Object,
        default: () => ({
          children: 'children',
          label: 'name',
          isLeaf: (data) => !!data.codeId
        })
      },
      // 默认展开的节点的 key 的数组
      defaultExpandedKey: {
        type: [String, Array]
      },
      // 默认勾选的节点的 key 的数组
      checkedNodeKeys: {
        type: Array
      },
      // 测点树当前选中的节点
      currentNodeKey: {
        type: [String, Number]
      },
      // 测点树是否可拖拽
      draggable: {
        type: Boolean,
        default: false
      },
      // 测点树是否可筛选
      showFilter: {
        type: Boolean,
        default: true
      },
      // 树节点是否可选中
      showCheckbox: {
        type: Boolean,
        default: false
      },
      // 测点树是否懒加载
      lazy: {
        type: Boolean,
        default: true
      },
      // 测点树懒加载对应方法（load: 展开拉取数据, filter: 搜索过滤数据）
      lazyFunctions: {
        type: Object,
        default: () => ({
          load: () => {},
          code: () => [],
          filter: () => ({})
        })
      },
      // 测点树是否显示分量
      showVectors: {
        type: Boolean,
        default: false
      },
      treeWidth: {
        type: String,
        default: '100%'
      }
    },
    watch: {
      filterText(val) {
        if (this.lazy && val) {
          this.lazyLoadAndFilter(val);
        } else {
          this.$refs.tree.filter(val);
        }
      }
    },
    methods: {
      /**
       * 懒加载数据筛选
       * @param val 搜索关键字
       */
      lazyLoadAndFilter: _.debounce(async function (val) {
        // 清空之前的状态
        this.localNeedExpandKeys.forEach(key => {
          const node = this.$refs.tree.getNode(key);
          node.loaded = false;
          node.expanded = false;
          return node;
        });
        this.filterLazyLoadKeyMap = {};
        this.localNeedExpandKeys = [];
        // 不需懒加载数据搜索
        const noLazyExpandKeys = makePath(this.treeList, val, {containsSearchKey: false, searchProperty: this.defaultProps.label, findFirst: false, compareFunction: (a, b) => a.toLowerCase().includes(b.toLowerCase())}).map(node => node.id);
        // 加载过滤数据
        const data = await this.lazyFunctions.filter(this.treeList, val) || [];
        this.filterLazyLoadKeyMap = data;
        const lazyLoadKeys = Object.keys(data);
        const lazyExpandKeys = lazyLoadKeys.map(parentKey => makePath(this.treeList, parentKey).map(node => node.id))
          .reduce((acc, item) => [...acc, ...item], []);
        this.localNeedExpandKeys = noLazyExpandKeys.concat(lazyExpandKeys);
        // 如果测点未搜索到结果，则直接搜索未懒加载节点
        if (!lazyLoadKeys.length) {
          this.$nextTick(() => this.$refs.tree.filter(val));
        }
      }, 500),
      /**
       * 懒加载方法中的搜索回调
       */
      searchCallback() {
        if (
          Object.keys(this.filterLazyLoadKeyMap).length &&
          Object.keys(this.filterLazyLoadKeyMap).every(key => this.localLoadedKeys.includes(key))
        ) {
          this.$nextTick(() => {
            this.$refs.tree.filter(this.filterText);
          });
        }
      },
      /**
       * 展开节点
       * @param node 需要获取子集的节点
       */
      async expand(node) {
        node.expanded = !node.expanded;
        if (!node.data.instrId) {
          node.data.instrId = node.parent.data.instrId;
        }
        await this._getTreeCodes(node);
      },
      /**
       * 前端过滤节点
       * @param node 需要获取子集的节点
       * @return array<{}>
       */
      async _getTreeCodes(node) {
        if (this.lazy) {
          const data = await this.lazyFunctions.code(this.treeList, node);
          if (data) {
            this.$refs.tree.updateKeyChildren(node.data.id, data);
            this.parseTreeData(data);
            return data;
          }
        }
        return node.data.children;
      },
      /**
       * 前端过滤节点
       * @param value 当前输入的关键字
       * @param data 当前被遍历的节点数据
       * @return boolean
       */
      filterNode(value, data) {
        if (!value) return true;
        return data.name.toLowerCase().includes(value.toLowerCase());
      },
      /**
       * 点击节点回调
       * @param data 当前节点数据
       * @param node 当前节点
       */
      handleNodeClick(data, node) {
        this.$emit('nodeClick', node);
      },
      /**
       * 不允许节点间拖拽
       * @return boolean false
       */
      allowDrop() {
        return false;
      },
      /**
       * 拖拽树
       * @param event drag事件
       */
      dragStart(event) {
        let data = this.parseDragData();
        // 在QQ浏览器下如果Data类型为text，在拖拽结束时会打开新标签
        event.dataTransfer.setData('pointDragData', JSON.stringify(data));
      },
      /**
       * 获取拖拽树时被选中的数据
       * @return array<{}>
       */
      parseDragData() {
        let data = [];
        for (let index in this.selectedVectorGroup) {
          if (this.selectedVectorGroup[index].length > 0) {
            data.push({
              vectors: this.selectedVectorGroup[index].length
                ? this.selectedVectorGroup[index].map(vector => JSON.parse(vector))
                : [],
              codes: this.checkedNodesGroup[index]
            });
          }
        }
        return data;
      },
      /**
       * 选中节点
       * @param currentData 传递给 data 属性的数组中该节点所对应的对象
       * @param currentState 树目前的选中状态对象，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性
       */
      async check(currentData, currentState) {
        const node = this.$refs.tree.getNode(currentData);
        let state = currentState;
        if (node) {
          const nodes = await this._getTreeCodes(node);
          if (node.checked) {
            state = {...currentState, checkedNodes: [...currentState.checkedNodes, ...nodes]};
          }
        }
        this._cleanChange();
        this._parseCheckedData(state.checkedNodes);
        if (Object.keys(this.checkedNodesGroup).length > 0) {
          this.$nextTick(() => (this.showVector = true));
        }
        this.$emit('check', currentState);
      },
      /**
       * 递归设置选中的测点和分量
       * @param children 需要遍历的节点列表
       */
      _parseCheckedData(children) {
        for (const item of children) {
          if (!item.children || item.children.length === 0) {
            if (!this.checkedNodesGroup[item.parentItemId]) {
              this.$set(this.checkedNodesGroup, item.parentItemId, []);
            }
            // 防止节点重复添加
            const nodesJsonGroup = this.checkedNodesGroup[item.parentItemId].map((code) => JSON.stringify(code));
            if (nodesJsonGroup.indexOf(JSON.stringify(item)) === -1) {
              this.checkedNodesGroup[item.parentItemId].push(item);
              if (!this.selectedVectorGroup[item.parentItemId] && item.vectors) {
                this.$set(this.selectedVectorGroup, item.parentItemId, item.vectors.filter(vector => vector.valueVectorType === 3).map(vector => JSON.stringify(vector)));
              }
            }
          } else {
            // 选中多个组时checkedNodes可能不包含叶子节点，所以需要递归遍历
            this._parseCheckedData(item.children);
          }
        }
      },
      /**
       * 清空选中的测点和分量信息
       */
      _cleanChange() {
        this.showVector = false;
        this.checkedNodesGroup = {};
        this.selectedVectorGroup = {};
      },
      /**
       * 清空选中的节点
       */
      clearCheckedNodes() {
        this.$refs.tree.setCheckedNodes([]);
        this._cleanChange();
      },
      async load(node, resolve) {
        const resolveCallback = (data, callback) => {
          resolve(data);
          this.localLoadedKeys.push(node.data.id);
          callback && callback(node);
        };
        if (node.level === 0) {
          return resolveCallback(this.treeList);
        }
        if (node.data.children && node.data.children.length > 0) {
          return resolveCallback(node.data.children);
        }
        if (Object.keys(this.filterLazyLoadKeyMap).includes(node.data.id)) {
          const loadData = this.filterLazyLoadKeyMap[node.data.id];
          return resolveCallback(loadData, this.searchCallback);
        }
        // 外部懒加载函数
        const children = await this.lazyFunctions.load(this.treeList, node);
        if (children) {
          this.$refs.tree.updateKeyChildren(node.data.id, children);
          this.parseTreeData(children);
          return resolveCallback(children);
        }
        const loadData = await this._getTreeCodes(node);
        return resolveCallback(loadData);
      },
      /**
       * 根据显示数据提取信息，如节点itemId和name映射的map，环境量节点列表等
       * @param data 节点列表数据
       */
      parseTreeData(data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id !== null) {
            this.itemMap[data[i].id] = data[i].name;
          }
          if (data[i].children && data[i].children.length > 0) {
            this.parseTreeData(data[i].children);
          }
          /* 寻找环境量的叶子节点并记录 */
          if (data[i].itemId === 1003) {
            this.environmentLeaves = data[i].children.length ? data[i].children.filter(item => item.children && item.children.length)
              .reduce((arr, item) => arr.concat(item.children.map(codeItem => codeItem.codeId)), []) : [];
          }
        }
      }
    },
    data() {
      return {
        // 搜索关键字
        filterText: '',
        // 懒加载过滤
        filterLazyLoadKeyMap: {},
        // 本地需要展开的的node-key
        localNeedExpandKeys: [],
        // 本地已懒加载的node-key
        localLoadedKeys: [],
        // 节点itemId和name映射的map，主要用于显示分量监测项目
        itemMap: {},
        // 已选中的节点组
        checkedNodesGroup: {},
        // 已选中的分量组
        selectedVectorGroup: {},
        // 环境量节点列表
        environmentLeaves: [],
        // 是否显示分量（是否有已选中的测点）
        showVector: false
      };
    },
    computed: {
      /**
       * 根据显示数据提取信息，如节点itemId和name映射的map，环境量节点列表等
       * @return array<string>
       */
      expandedKeys() {
        const expandKey = this.defaultExpandedKey || this.currentNodeKey || (this.treeList && this.treeList.length && this.treeList[0].id);
        let propNeedExpandKeys = Array.isArray(expandKey) ? expandKey : expandKey ? [expandKey] : [];
        // 懒加载情况下获取完整路径并展开
        if (this.lazy) {
          propNeedExpandKeys = propNeedExpandKeys.map(itemKey => makePath(this.treeList, itemKey).map(node => node.id)).reduce((acc, item) => [...acc, ...item], []);
        }
        return [...new Set([...this.localNeedExpandKeys, ...propNeedExpandKeys])];
      }
    }
  };
</script>

<style lang="scss">
  @import "../assets/css/_variables.scss";
  .el-tree.damCodeTree {
    &.el-tree--highlight-current {
      .el-tree-node.is-current {
        > .el-tree-node__content {
          color: $brand-primary;
          background-color: $gray-lighter1;
          border-left-color: $brand-primary;
        }
      }
    }
    .el-tree-node__content {
      height: 35px !important;
      &:hover {
        background-color: $gray-lighter1;
        .el-tree__hover-text {
          opacity: 1;
        }
      }
      .custom-tree-node {
        width: 100%;
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
  .rotate-180 {
    transform: rotate(180deg);
  }
</style>
