<template>
  <el-drawer
    class="graphStyleDrawer"
    :modal="false"
    :visible.sync="visible"
    @close="$emit('cancel')"
  >
    <h5
      slot="title"
      class="h5 vg-text-black-light vg-my-0"
    >{{t('graphic.graphStyle.graphStyle')}}</h5>
    <el-form :form="form" size="mini" label-position="left" class="vg-px-4 vg-pb-4">
      <el-tabs>
        <el-tab-pane
          v-for="(value, position) in form.yAxis"
          :key="position"
          :label="getYAxisPosition(position)"
        >
          <el-row>
            <el-col :span="4">
              <el-form-item
                :prop="`{yAxis[${position}].axisTick.tickNumberAuto`"
                class="vg-mb-2"
                label-width="0"
              >
                <el-checkbox
                  class="itemChecked"
                  v-model="value.axisTick.tickNumberAuto"
                  @change="configChange('tickNumberAuto', value.axisTick.tickNumberAuto, value.axisTick)"
                >
                  <span
                    :class="{'vg-text-underline-danger': value.axisTick.tickNumberAuto !== config.yAxis[position].axisTick.tickNumberAuto}"
                  >{{t('graphic.graphStyle.auto')}}</span>
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item
                :prop="`{yAxis[${position}].axisTick.tickNumber`"
                class="label-no-padding vg-mb-2"
                label-width="66px"
              >
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.axisTick.tickNumber !== config.yAxis[position].axisTick.tickNumber}"
                >{{t('graphic.graphStyle.tickNumber')}}</span>
                <el-input-number
                  class="full-width"
                  v-model.number="value.axisTick.tickNumber"
                  controls-position="right"
                  :min="1"
                  :step="1"
                  step-strictly
                  @change="configChange('tickNumber', value.axisTick.tickNumber, value.axisTick)"
                  :disabled="value.axisTick.tickNumberAuto"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item
                :prop="`{yAxis[${position}].maxAuto`"
                class="vg-mb-2"
                label-width="0"
              >
                <el-checkbox
                  class="itemChecked"
                  @change="configChange('maxAuto', value.maxAuto, value)"
                  v-model="value.maxAuto"
                >
                  <span
                    :class="{'vg-text-underline-danger': value.maxAuto !== config.yAxis[position].maxAuto}"
                  >{{t('graphic.graphStyle.auto')}}</span>
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item
                :prop="`{yAxis[${position}].max`"
                class="label-no-padding vg-mb-2"
                label-width="66px"
              >
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.max !== config.yAxis[position].max}"
                >{{t('graphic.graphStyle.max')}}</span>
                <el-input-number
                  class="full-width"
                  v-model.number="value.max"
                  controls-position="right"
                  @change="configChange('max', value.max, value)"
                  :disabled="value.maxAuto"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item :prop="`{yAxis[${position}].minAuto`" class="vg-mb-2" label-width="0">
                <el-checkbox
                  class="itemChecked"
                  @change="configChange('minAuto', value.minAuto, value)"
                  v-model="value.minAuto"
                >{{t('graphic.graphStyle.auto')}}</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item
                :prop="`{yAxis[${position}].min`"
                class="label-no-padding vg-mb-2"
                label-width="66px"
              >
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.min !== config.yAxis[position].min}"
                >{{t('graphic.graphStyle.min')}}</span>
                <el-input-number
                  class="full-width"
                  v-model.number="value.min"
                  controls-position="right"
                  @change="configChange('min', value.min, value)"
                  :disabled="value.minAuto"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item
                :prop="`{yAxis[${position}].axisTick.decimalNumberAuto`"
                class="vg-mb-2"
                label-width="0"
              >
                <el-checkbox
                  class="itemChecked"
                  @change="configChange('decimalNumberAuto', value.axisTick.decimalNumberAuto, value.axisTick)"
                  v-model="value.axisTick.decimalNumberAuto"
                >{{t('graphic.graphStyle.auto')}}</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item
                :prop="`{yAxis[${position}].axisTick.decimalNumber`"
                class="label-no-padding vg-mb-2"
                label-width="66px"
              >
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.axisTick.decimalNumber !== config.yAxis[position].axisTick.decimalNumber}"
                >{{t('graphic.graphStyle.decimalNumber')}}</span>
                <el-input-number
                  class="full-width"
                  v-model.number="value.axisTick.decimalNumber"
                  controls-position="right"
                  :min="0"
                  :max="100"
                  :step="1"
                  step-strictly
                  @change="configChange('decimalNumber', value.axisTick.decimalNumber, value.axisTick)"
                  :disabled="value.axisTick.decimalNumberAuto"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :prop="`{yAxis[${position}].axisLabel.fontSize`" class="vg-mb-2">
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.axisLabel.fontSize !== config.yAxis[position].axisLabel.fontSize}"
                >{{t('graphic.graphStyle.fontSize')}}</span>
                <el-input-number
                  class="full-width"
                  v-model.number="value.axisLabel.fontSize"
                  controls-position="right"
                  :min="7"
                  :step="1"
                  step-strictly
                  @change="configChange('fontSize', value.axisLabel.fontSize, value.axisLabel)"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="`{yAxis[${position}].axisLabel.fontFamily`" class="vg-mb-2">
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.axisLabel.fontFamily !== config.yAxis[position].axisLabel.fontFamily}"
                >{{t('graphic.graphStyle.fontFamily')}}</span>
                <el-select
                  @change="configChange('fontFamily', value.axisLabel.fontFamily, value.axisLabel)"
                  v-model="value.axisLabel.fontFamily"
                >
                  <el-option
                    v-for="item in FONT_FAMILIES"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                    :style="`font-family: ${item.value}`">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="`{yAxis[${position}].axisLabel.text`" class="vg-mb-2">
                <span
                  slot="label"
                  :class="{'vg-text-underline-danger': value.axisLabel.text !== config.yAxis[position].axisLabel.text}"
                >{{t('graphic.whole.name')}}</span>
                <el-input
                  :maxlength="10"
                  @change="configChange('text', value.axisLabel.text, value.axisLabel)"
                  v-model="value.axisLabel.text"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item :prop="`{yAxis[${position}].inverse`" class="vg-mb-0" label-width="0">
            <el-checkbox
              class="itemChecked"
              @change="configChange('inverse', value.inverse, value)"
              v-model="value.inverse"
            >
              <span
                :class="{'vg-text-underline-danger': value.inverse !== config.yAxis[position].inverse}"
              >{{t('graphic.graphStyle.inverse')}}</span>
            </el-checkbox>
          </el-form-item>
          <hr>
        </el-tab-pane>
      </el-tabs>
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">
        <el-checkbox
          @change="configChange('show', form.title.show, form.title)"
          v-model="form.title.show"
        >{{t('graphic.graphStyle.showTitle')}}</el-checkbox>
      </h6>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="title.textStyle.fontSize" class="vg-mb-2">
            <span
              slot="label"
              :class="{'vg-text-underline-danger': form.title.textStyle.fontSize !== config.title.textStyle.fontSize}"
            >{{t('graphic.graphStyle.fontSize')}}</span>
            <el-input-number
              class="full-width"
              v-model.number="form.title.textStyle.fontSize"
              controls-position="right"
              :min="7"
              :step="1"
              step-strictly
              @change="configChange('fontSize', form.title.textStyle.fontSize, form.title.textStyle)"
              :disabled="!form.title.show"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="title.textStyle.fontFamily" class="vg-mb-2">
            <span
              slot="label"
              :class="{'vg-text-underline-danger': form.title.textStyle.fontFamily !== config.title.textStyle.fontFamily}"
            >{{t('graphic.graphStyle.fontFamily')}}</span>
            <el-select
              @change="configChange('fontFamily', form.title.textStyle.fontFamily, form.title.textStyle)"
              v-model="form.title.textStyle.fontFamily"
              :disabled="!form.title.show"
            >
              <el-option
                v-for="item in FONT_FAMILIES"
                :key="item.value"
                :label="item.name"
                :value="item.value"
                :style="`font-family: ${item.value}`"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="title.position" class="vg-mb-0">
            <span
              slot="label"
              :class="{'vg-text-underline-danger': form.title.position !== config.title.position}"
            >{{t('graphic.graphStyle.position')}}</span>
            <el-select
              @change="configChange('position', form.title.position, form.title)"
              v-model="form.title.position"
              :disabled="!form.title.show"
            >
              <el-option
                v-for="(value, key) in positions"
                :key="key"
                :label="value"
                :value="key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="title.text" label-width="50px" class="vg-mb-0">
        <span
          slot="label"
          :class="{'vg-text-underline-danger': form.title.text !== config.title.text}"
        >{{t('graphic.whole.name')}}</span>
        <el-input
          :maxlength="10"
          @change="configChange('text', form.title.text, form.title)"
          v-model="form.title.text"
          :disabled="!form.title.show"
        ></el-input>
      </el-form-item>
      <hr>
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.graphStyle.padding')}}</h6>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item prop="leftPadding" label-width="30px" class="label-no-padding vg-mb-2">
            <span
              slot="label"
              :class="{'vg-text-underline-danger': form.leftPadding !== config.leftPadding}"
            >{{t('graphic.graphStyle.left')}}</span>
            <el-input-number
              class="full-width"
              v-model.number="form.leftPadding"
              controls-position="right"
              :min="0"
              :step="1"
              step-strictly
              @change="configChange('leftPadding', form.leftPadding, form)"
              :disabled="form.leftPaddingAuto"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="rightPadding" label-width="30px" class="label-no-padding vg-mb-2">
            <span
              slot="label"
              :class="{'vg-text-underline-danger': form.rightPadding !== config.rightPadding}"
            >{{t('graphic.graphStyle.right')}}</span>
            <el-input-number
              class="full-width"
              v-model.number="form.rightPadding"
              controls-position="right"
              :min="0"
              :step="1"
              step-strictly
              @change="configChange('rightPadding', form.rightPadding, form)"
              :disabled="form.rightPaddingAuto"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <hr>
      <el-button type="danger" plain @click="removeGraph" round size="small">{{t('graphic.graphStyle.deleteGraph')}}</el-button>
      <div class="position-fixed" style="bottom: 20px;right: 20px;">
        <el-button
          class="d-block vg-mx-0 vg-mt-2"
          :title="t('graphic.graphStyle.applyToAll')"
          icon="vg-icon iconlouceng"
          plain
          circle
          @click="save(true)"
        ></el-button>
        <el-button
          class="d-block vg-mx-0 vg-mt-2"
          type="primary"
          :title="t('graphic.graphStyle.save')"
          icon="el-icon-check"
          circle
          @click="save(false)"
        ></el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script>
  import * as _ from 'lodash';
  import {getFontFamilies, getYAxis} from '../../assets/js/constants';

  export default {
    name: 'GraphStyleDrawer',
    data() {
      return {
        visible: false,
        FONT_FAMILIES: getFontFamilies.call(this),
        Y_AXES: getYAxis.call(this),
        positions: {
          top: this.t('graphic.graphStyle.up'),
          bottom: this.t('graphic.graphStyle.down')
        },
        // 原图幅属性
        config: {
          yAxis: {},
          title: {
            textStyle: {}
          }
        },
        // 图幅属性表单
        form: {
          yAxis: {},
          title: {
            textStyle: {}
          }
        }
      }
    },
    methods: {
      open(form) {
        this.config = form;
        this.form = _.cloneDeep(form);
        this.visible = true;
      },
      close() {
        this.visible = false;
      },
      save(isToAll) {
        this.$emit('save', isToAll, this.form);
        this.close();
      },
      getYAxisPosition(index) {
        let existSamePosition = false;
        for (let i = 0; i < index; i++) {
          if (this.config.yAxis[index].position === this.config.yAxis[i].position) {
            existSamePosition = true;
          }
        }
        return (this.config.yAxis[index].position === 'left' ? this.t('graphic.graphStyle.left') : this.t('graphic.graphStyle.right')) + ' ' +
          (existSamePosition ? this.t('graphic.graphStyle.out') : ' ') +
          this.t('graphic.graphStyle.axis');
      },
      configChange(attr, value, object) {
        const toPositiveInt = (value, defaultMinValue) => value <= defaultMinValue ? defaultMinValue : parseInt(value);
        const toPositive = (value, defaultMinValue) => value <= defaultMinValue ? defaultMinValue : value;
        switch (attr) {
          case 'tickNumber': {
            object[attr] = toPositiveInt(value, 1);
            break;
          }
          case 'decimalNumber': {
            object[attr] = toPositiveInt(value, 0);
            break;
          }
          case 'rightPadding':
          case 'leftPadding': {
            object[attr] = toPositive(value, 0);
            break;
          }
          default: break;
        }
      },
      removeGraph() {
        this.$confirm(this.t('graphic.graphStyle.cannotBeDeleted'), this.t('graphic.whole.prompt'), {
          confirmButtonText: this.t('graphic.whole.sure'),
          cancelButtonText: this.t('graphic.whole.cancel')
        }).then(() => {
          this.$emit('delete');
          this.close();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .graphStyleDrawer {
    /deep/ .el-drawer {
      width: 360px !important;
    }

    /deep/ .el-drawer__header {
      margin-bottom: 20px;
    }
    /deep/ .el-drawer__body {
      overflow-y: auto;
    }
    /deep/ .el-input-number.is-controls-right .el-input__inner {
      padding-left: 5px;
      padding-right: 33px;
    }
  }
</style>
