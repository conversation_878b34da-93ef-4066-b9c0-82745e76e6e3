<template>
  <el-drawer
    class="lineStyleDrawer"
    :modal="false"
    :visible.sync="visible"
    @close="$emit('cancel')"
  >
    <h5 slot="title" class="h5 vg-text-black-light vg-my-0">{{t('graphic.lineStyle.lineStyle')}}</h5>
    <el-form :form="form" size="mini" label-position="left" class="vg-px-4 vg-pb-4">
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.lineStyle.baseLineStyle')}}</h6>
      <el-form-item prop="name" label-width="80px" class="vg-mb-0">
        <span slot="label" :class="{'vg-text-underline-danger': form.name !== config.name}">{{t('graphic.lineStyle.name')}}</span>
        <el-input
          :maxlength="10"
          @change="configChange('name', form.name, form)"
          v-model="form.name">
        </el-input>
      </el-form-item>
      <hr>
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.lineStyle.lineStyle')}}</h6>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item prop="itemStyle.color" class="vg-mb-0" style="height: 32px;">
            <span slot="label" :class="{'vg-text-underline-danger': form.itemStyle.color !== config.itemStyle.color}">{{t('graphic.lineStyle.color')}}</span>
            <el-color-picker
              class="full-width not-square"
              v-model="form.itemStyle.color"
              @change="configChange('color', form.itemStyle.color, form.itemStyle)"
              show-alpha
              :predefine="COLORS">
            </el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="itemStyle.style" class="vg-mb-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.itemStyle.style !== config.itemStyle.style}">{{t('graphic.lineStyle.style')}}</span>
            <el-select
              :disabled="type === 'correlationLine'"
              @change="configChange('style', form.itemStyle.style, form.itemStyle)"
              v-model="form.itemStyle.style">
              <el-option
                v-for="item in lineStyles"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="itemStyle.dasharray" class="vg-mb-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.itemStyle.dasharray !== config.itemStyle.dasharray}">{{t('graphic.lineStyle.dasharray')}}</span>
            <el-select
              :disabled="type === 'correlationLine'"
              @change="configChange('dasharray', form.itemStyle.dasharray, form.itemStyle)"
              v-model="form.itemStyle.dasharray">
              <el-option
                v-for="item in lineTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="itemStyle.strokeWidth" class="vg-mb-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.itemStyle.strokeWidth !== config.itemStyle.strokeWidth}">{{t('graphic.lineStyle.strokeWidth')}}</span>
            <el-input
              class="append--small-padding"
              :disabled="type === 'correlationLine'"
              @change="configChange('strokeWidth', form.itemStyle.strokeWidth, form.itemStyle)" :min="1"
              v-model.number="form.itemStyle.strokeWidth" type="number">
              <span slot="append" style="margin: 0 -10px;">mm</span>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="position" class="vg-mb-0">
            <span slot="label" :class="{'vg-text-underline-danger': form.position !== config.position}">{{t('graphic.lineStyle.yPosition')}}</span>
            <el-select
              @change="configChange('position', form.position, form)"
              v-model="form.position">
              <el-option
                v-for="(value, key) in positions"
                :key="key"
                :label="value"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <hr>
      <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.lineStyle.marker')}}</h6>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item prop="marker.symbol" class="vg-mb-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.marker.symbol !== config.marker.symbol}">{{t('graphic.lineStyle.style')}}</span>
            <el-select
              @change="configChange('symbol', form.marker.symbol, form.marker)"
              v-model="form.marker.symbol">
              <el-option
                v-for="item in markerStyles"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="marker.symbolSize" class="vg-mb-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.marker.symbolSize !== config.marker.symbolSize}">{{t('graphic.lineStyle.symbolSize')}}</span>
            <el-input type="number"
                      @change="configChange('symbolSize', form.marker.symbolSize, form.marker)"
                      v-model.number="form.marker.symbolSize" min="20"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="type !== 'correlationLine'">
          <el-form-item prop="marker.symbolNumber" class="vg-mb-0">
            <span slot="label" :class="{'vg-text-underline-danger': form.marker.symbolNumber !== config.marker.symbolNumber}">{{t('graphic.lineStyle.symbolNumber')}}</span>
            <el-input type="number"
                      @change="configChange('symbolNumber', form.marker.symbolNumber, form.marker)"
                      v-model.number="form.marker.symbolNumber" min="0"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="form.value">
        <hr>
        <h6 class="h6 headline-primary vg-text-black-light vg-mb-2">{{t('graphic.graphWorkArea.valueFilter')}}</h6>
        <el-row type="flex">
          <el-form-item prop="value.startDayTime" label-width="40px" class="label-no-padding vg-mr-2">
            <span slot="label" :class="{'vg-text-underline-danger': form.value.startDayTime !== config.value.startDayTime || form.value.endDayTime !== config.value.endDayTime}">{{t('graphic.whole.day')}}</span>
            <el-time-picker
              style="width: 110px;"
              prefix-icon="el-icon-date"
              :default-value="defaultStartDayTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="configChange('startDayTime', form.value.startDayTime, form.value)"
              v-model="form.value.startDayTime">
            </el-time-picker>
          </el-form-item>
          <el-form-item label="~" prop="value.endDayTime" label-width="20px" class="label-no-padding">
            <el-time-picker
              style="width: 110px;"
              prefix-icon="el-icon-date"
              :default-value="defaultEndDayTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="configChange('endDayTime', form.value.endDayTime, form.value)"
              v-model="form.value.endDayTime">
            </el-time-picker>
          </el-form-item>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item prop="value.writeType" class="vg-mb-2">
              <span slot="label" :class="{'vg-text-underline-danger': !arraysEqual(form.value.writeType, config.value.writeType)}">{{t('graphic.graphWorkArea.writeType')}}</span>
              <el-select class="numValueSet"
                         multiple
                         @change="configChange('writeType', form.value.writeType, form.value)"
                         v-model="form.value.writeType">
                <el-option
                  v-for="(item, index) in writeTypes"
                  :key="item"
                  :label="item"
                  :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="value.auditStatus" class="vg-mb-2">
              <span slot="label" :class="{'vg-text-underline-danger': !arraysEqual(form.value.auditStatus, config.value.auditStatus)}">{{t('graphic.graphWorkArea.auditStatus')}}</span>
              <el-select class="numValueSet"
                         multiple
                         @change="configChange('auditStatus', form.value.auditStatus, form.value)"
                         v-model="form.value.auditStatus">
                <el-option
                  v-for="(item, index) in auditStatus"
                  :key="item"
                  :label="item"
                  :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="value.valueStatus" class="vg-mb-2">
              <span slot="label" :class="{'vg-text-underline-danger': !arraysEqual(form.value.valueStatus, config.value.valueStatus)}">{{t('graphic.graphWorkArea.valueStatus')}}</span>
              <el-select class="numValueSet"
                         multiple
                         @change="configChange('valueStatus', form.value.valueStatus, form.value)"
                         v-model="form.value.valueStatus">
                <el-option
                  v-for="(item, index) in valueStatus"
                  :key="item"
                  :label="item"
                  :value="Number(index)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="value.valueType" class="vg-mb-2">
              <span slot="label" :class="{'vg-text-underline-danger': !arraysEqual(form.value.valueType, config.value.valueType)}">{{t('graphic.graphWorkArea.valueType')}}</span>
              <el-select class="numValueSet"
                         multiple
                         @change="configChange('valueType', form.value.valueType, form.value)"
                         v-model="form.value.valueType">
                <el-option
                  v-for="(item, index) in valueTypes"
                  :key="item"
                  :label="item"
                  :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="type === 'processLine'">
            <el-form-item prop="value.missingExamineDays" class="vg-mb-0">
              <span slot="label" :class="{'vg-text-underline-danger': form.value.missingExamineDays !== config.value.missingExamineDays}">{{t('graphic.lineStyle.missingExamineDays')}}</span>
              <el-input type="number" :min="0"
                        @change="configChange('missingExamineDays', form.value.missingExamineDays, form.value)"
                        v-model.number="form.value.missingExamineDays">
                <span slot="append" style="margin: 0 -10px;">{{t('graphic.whole.day')}}</span>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <hr>
      <el-button type="danger" plain @click="removeLine" round size="small">{{t('graphic.lineStyle.deleteLine')}}</el-button>
      <div class="position-fixed" style="bottom: 20px;right: 20px;">
        <el-button
          class="d-block vg-mx-0 vg-mt-2"
          :title="t('graphic.graphStyle.applyToAll')"
          icon="vg-icon iconlouceng"
          plain
          circle
          @click="save(true)"
        ></el-button>
        <el-button
          class="d-block vg-mx-0 vg-mt-2"
          type="primary"
          :title="t('graphic.graphStyle.save')"
          icon="el-icon-check"
          circle
          @click="save(false)"
        ></el-button>
      </div>
    </el-form>
  </el-drawer>
</template>
<script>
  import * as _ from 'lodash';
  import dayjs from 'dayjs';
  import * as querystring from 'query-string';
  import {COLORS, getWriteTypes, getAuditStatus, getValueStatus, getValueTypes} from '../../assets/js/constants';
  import {arraysEqual} from '../../assets/js/utils';
  export default {
    name: 'LineStyleDrawer',
    props: {
      writeTypes: {
        type: [Array, Object],
        default: function () {
          return getWriteTypes.call(this);
        }
      },
      auditStatus: {
        type: [Array, Object],
        default: function () {
          return getAuditStatus.call(this);
        }
      },
      valueStatus: {
        type: [Array, Object],
        default: function () {
          return getValueStatus.call(this);
        }
      },
      valueTypes: {
        type: [Array, Object],
        default: function () {
          return getValueTypes.call(this);
        }
      }
    },
    data() {
      return {
        // 原线条属性
        config: {
          itemStyle: {},
          marker: {},
          value: {}
        },
        // 线条属性表单
        form: {
          itemStyle: {},
          marker: {},
          value: {}
        },
        type: 'processLine',
        visible: false,
        COLORS,
        positions: {
          left: this.t('graphic.graphStyle.left'),
          right: this.t('graphic.graphStyle.right')
        },
        lineStyles: [
          {value: 'curves', label: this.t('graphic.lineStyle.curves')},
          {value: 'straight', label: this.t('graphic.lineStyle.straight')},
          {value: 'bar', label: this.t('graphic.lineStyle.bar')}
        ],
        lineTypes: [
          {value: '0', label: this.t('graphic.lineStyle.solid')},
          {value: '15 10 5 10', label: this.t('graphic.lineStyle.dotted')},
          {value: '6 3', label: this.t('graphic.lineStyle.dashed')}
        ],
        markerStyles: [
          {value: 'none', label: this.t('graphic.whole.none')},
          {value: 'symbolCircle', label: '● ' + this.t('graphic.lineStyle.circle')},
          {value: 'symbolDiamond', label: '◆ ' + this.t('graphic.lineStyle.diamond')},
          {value: 'symbolTriangle', label: '▲ ' + this.t('graphic.lineStyle.triangle')},
          {value: 'symbolSquare', label: '■ ' + this.t('graphic.lineStyle.square')}
        ],
        defaultStartDayTime: dayjs(new Date()).format('YYYY-MM-DD [00:00:00]'),
        defaultEndDayTime: dayjs(new Date()).format('YYYY-MM-DD [23:59:59]')
      };
    },
    methods: {
      arraysEqual,
      open(form, type) {
        this.config = form;
        this.type = type;
        this.form = _.cloneDeep(form);
        this.visible = true;
      },
      close() {
        this.visible = false;
      },
      save(isToAll) {
        if (this.form.queryUrl) {
          this.updateQueryUrl();
        }
        this.$emit('save', isToAll, this.form);
        this.close();
      },
      /**
       * 根据取值更新queryUrl
       */
      updateQueryUrl() {
        const {value} = this.form;
        const config = querystring.parseUrl(this.form.queryUrl);
        config.query = {
          ...config.query,
          startDayTime: value.startDayTime,
          endDayTime: value.endDayTime,
          writeType: value.writeType.join(','),
          auditStatus: value.auditStatus.join(','),
          valueStatus: value.valueStatus.join(','),
          valueType: value.valueType.join(','),
          missingExamineDays: value.missingExamineDays
        }
        this.form.queryUrl = `${config.url}?${querystring.stringify(config.query)}`;
      },
      configChange(attr, value, object) {
        switch (attr) {
          case 'startDayTime':
          case 'endDayTime': {
            object[attr] = object[attr] || '';
            break;
          }
          case 'strokeWidth': {
            object[attr] = value <= 1 ? 1 : value;
            break;
          }
          case 'symbolSize': {
            object[attr] = value <= 20 ? 20 : value;
            break;
          }
          case 'missingExamineDays':
          case 'symbolNumber': {
            object[attr] = value <= 0 ? 0 : parseInt(value);
            break;
          }
          default:
            break;
        }
      },
      removeLine() {
        this.$confirm(this.t('graphic.graphStyle.cannotBeDeleted'), this.t('graphic.whole.prompt'), {
          confirmButtonText: this.t('graphic.whole.sure'),
          cancelButtonText: this.t('graphic.whole.cancel')
        }).then(() => {
          this.$emit('delete');
          this.close();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .lineStyleDrawer {
    /deep/ .el-drawer {
      width: 360px !important;
    }

    /deep/ .el-drawer__body {
      overflow-y: auto;
    }
  }
</style>
