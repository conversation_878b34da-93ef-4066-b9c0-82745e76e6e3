 <template>
  <el-container class="vg-bg-white">
    <el-aside class="vg-px-2 vg-pt-2 vg-border-right d-flex flex-column" :width="asideWidth">
      <el-tabs v-model="asideActiveTab">
        <el-tab-pane :label="t('graphic.graphWorkArea.code')"></el-tab-pane>
        <el-tab-pane :label="t('graphic.graphWorkArea.template')" disabled></el-tab-pane>
      </el-tabs>
      <template v-if="asideActiveTab === '0'">
        <el-form :model="dateForm" label-width="80px">
          <el-form-item class="vg-mb-2" :label="`${t('graphic.graphWorkArea.startDate')}:`">
            <el-date-picker
              class="full-width"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              :placeholder="t('graphic.graphWorkArea.firstValueDate')"
              v-model="dateForm.startDate"
              @change="dateChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="vg-mb-2" :label="`${t('graphic.whole.endTime')}:`">
            <el-date-picker
              class="full-width"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              :placeholder="t('graphic.graphWorkArea.latestValueDate')"
              v-model="dateForm.endDate"
              @change="dateChange"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <DamCodeTree
          :treeList="codeTreeList"
          :showCheckbox="true"
          :showVectors="true"
          :draggable="true"
          :lazy="codeTreeConfig.lazy"
          :lazyFunctions="codeTreeConfig.lazyFunctions"
          :defaultProps="codeTreeConfig.defaultProps"
          treeWidth="350px"
          @contextmenu="oncontextmenu"
          @multiSelect="multiSelect"
          ref="codeTree"
        >
          <!-- 测点树节点插槽，也可由父组件插入 -->
          <template v-slot:node="{node, data}">
            <slot name="node" :node="node" :data="data"></slot>
          </template>
        </DamCodeTree>
        <v-contextmenu style="width: 160px;" ref="contextMenu">
          <v-contextmenu-item class="vg-py-2" @click="addToAllGraph">{{t('graphic.graphWorkArea.addToGraph')}}</v-contextmenu-item>
        </v-contextmenu>
      </template>
    </el-aside>
    <el-main>
      <el-container class="full-height">
        <el-header class="vg-px-0 d-flex justify-content-between vg-border-bottom" height="auto">
          <el-radio-group v-model="currentLineType" size="medium">
            <el-radio-button label="processLine">{{t('graphic.graphWorkArea.processLine')}}</el-radio-button>
            <el-radio-button label="distributionLine" disabled>{{t('graphic.graphWorkArea.distributionLine')}}</el-radio-button>
            <el-radio-button label="clinographLine" disabled>{{t('graphic.graphWorkArea.clinographLine')}}</el-radio-button>
            <el-radio-button label="distributionGraph" disabled>{{t('graphic.graphWorkArea.distributionGraph')}}</el-radio-button>
          </el-radio-group>
          <el-button-group style="margin-top: 2px;" size="medium">
            <el-button
              class="vg-py-1 vg-px-2"
              type="text"
              icon="vg-icon iconxiazai14"
              :disabled="!graphs.length"
              @click="clear"
            >&emsp;{{t('graphic.whole.empty')}}</el-button>
            <el-button class="vg-py-1 vg-px-2" type="text" icon="vg-icon iconzhanghuchongzhi" @click="openValueFilterDialog">&emsp;{{t('graphic.graphWorkArea.valueFilter')}}</el-button>
            <!--          <el-button-->
            <!--            class="vg-py-1 vg-px-2"-->
            <!--            type="text" -->
            <!--            icon="vg-icon iconbaocun1"-->
            <!--            :disabled="!graphs.length"-->
            <!--            @click="save"-->
            <!--          >&emsp;{{t('graphic.graphWorkArea.save')}}</el-button>-->
            <!--          <el-button-->
            <!--            class="vg-py-1 vg-px-2"-->
            <!--            type="text"-->
            <!--            icon="vg-icon iconbaocun1"-->
            <!--            :disabled="!graphs.length"-->
            <!--            @click="saveAs"-->
            <!--          >&emsp;{{t('graphic.graphWorkArea.saveAs')}}</el-button>-->
            <el-button
              class="vg-py-1 vg-px-2"
              type="text"
              icon="vg-icon icondadanfahuo-dayinshezhi"
              :disabled="!graphs.length || isPrinting"
              :loading="isPrinting"
              @click="print"
            >&emsp;{{t('graphic.graphWorkArea.printSetting')}}</el-button>
            <el-button
              class="vg-py-1 vg-px-2"
              type="text"
              icon="vg-icon icondayin"
              :disabled="!graphs.length || isPrinting"
              :loading="isPrinting"
              @click="print"
            >&emsp;{{t('graphic.graphWorkArea.print')}}</el-button>
            <el-button
              class="vg-py-1 vg-px-2"
              type="text"
              icon="vg-icon icondaochu"
              :disabled="!graphs.length || isExporting"
              :loading="isExporting"
              @click="exportWord"
            >&emsp;{{t('graphic.whole.export')}}</el-button>
          </el-button-group>
        </el-header>
        <el-main class="vg-mt-4 vg-p-0">
          <div class="full-height ofy-auto" @drop="dropOnEmpty" @dragover.prevent="">
            <DamLineCharts
              ref="lineCharts"
              @change="graphs = $event"
              @dropOnGraph="dropOnGraph"
              @valueQuery="$emit('valueQuery', $event)"
            ></DamLineCharts>
            <p
              v-if="!graphs.length"
              class="full-height flex-center"
              style="font-size: 18px;border: 2px dashed #e6e6e6;color: #cccccc;"
            >{{t('graphic.graphWorkArea.graphEmptyPlaceholder')}}</p>
          </div>
        </el-main>
      </el-container>
    </el-main>
    <!-- 取值弹框 -->
    <ValueFilterDialog
      :writeTypes="listConfig.writeTypes"
      :auditStatus="listConfig.auditStatus"
      :valueStatus="listConfig.valueStatus"
      :valueType="listConfig.valueTypes"
      @save="saveValueFilter"
      ref="valueFilterDialog"
    ></ValueFilterDialog>
  </el-container>
</template>

<script>
  import dayjs from 'dayjs';
  import { saveAs } from 'file-saver';
  import * as querystring from 'query-string';
  import * as _ from 'lodash';
  import printJS from 'print-js';
  import html2canvas from 'html2canvas';
  import ValueFilterDialog from './dialogs/ValueFilterDialog';
  import {COLORS, getValueFilter} from '../assets/js/constants';
  import {VALUE_FILTER_OPTION, LINE_CONFIG, mergeOption, Y_AXIS_OPTION, Y_POSITION} from '../assets/js/options';

  export default {
    name: 'DamGraphWorkArea',
    components: {
      ValueFilterDialog
    },
    props: {
      // 侧边栏宽度
      asideWidth: {
        type: String,
        default: '250px'
      },
      codeTreeList: {
        type: Array,
        default: () => []
      },
      codeTreeConfig: {
        type: Object,
        default: () => ({
          lazy: false,
          lazyFunctions: {
            type: Object,
            default: () => ({
              load: () => {},
              code: () => [],
              filter: () => ({})
            })
          },
          defaultProps: {
            children: 'children',
            label: 'name',
            isLeaf: (data) => !!data.codeId
          }
        })
      },
      listConfig: {
        type: Object,
        default: function () {
          return getValueFilter.call(this);
        }
      },
      // api配置
      urlConfig: {
        type: Object,
        required: true
      },
      // 取值配置
      valueFilterConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        // 侧边栏当前选中tab
        asideActiveTab: '0',
        // 起止时间表单
        dateForm: {
          startDate: null,
          endDate: null
        },
        // 当前绘制图形类型
        currentLineType: 'processLine',
        // 取值表单
        valueFilterForm: {
          ..._.cloneDeep(VALUE_FILTER_OPTION),
          ...this.valueFilterConfig
        },
        // 测点树数据
        treeList: [],
        // 可修改的图幅
        graphs: [],
        // 是否正在打印
        isPrinting: false,
        // 是否正在导出
        isExporting: false
      };
    },
    methods: {
      /**
       * 修改起始时间/截止时间
       */
      dateChange() {
        if (this.dateForm.endDate < this.dateForm.startDate) {
          this.$message.info(this.t('graphic.graphWorkArea.dateValidInfo1'));
          return false;
        }
      },
      /**
       * 测点右击
       * @param event contextmenu事件
       * @param node 当前右击的节点（测点）
       */
      oncontextmenu(event, node) {
        if (this.$refs.codeTree.environmentLeaves.includes(node.data.codeId)) {
          this.$refs.contextMenu.show({
            left: event.clientX,
            top: document.documentElement.scrollTop + event.clientY
          });
        }
      },
      /**
       * （测点右击）添加到所有图幅
       */
      async addToAllGraph() {
        const data = this.parseDragData();
        if (this.multiSelectCallback && data.length) {
          await this.multiSelectCallback(data);
          this.multiSelectCallback = null;
          this.$refs.codeTree.clearCheckedNodes();
        } else {
          this.$message.warning(this.t('graphic.graphWorkArea.environmentVectorWarning'));
        }
      },
      /**
       * （ctrl+单击）多选图幅的回调
       * @param multiSelectedGraphsIndex 当前选中的图幅下标
       */
      multiSelect(multiSelectedGraphsIndex) {
        this.multiSelectCallback = (data) => {
          Promise.all(multiSelectedGraphsIndex.map(async graphIndex => {
            this.parseGraphData(data, this.graphs[graphIndex].option);
            await this.generateRequestUrl(this.graphs[graphIndex].option);
            this.$refs.lineCharts.updateGraphOption(this.graphs[graphIndex], graphIndex);
          }));
        };
      },
      /**
       * 获取测点树拖拽的数据
       * @param event drop事件
       */
      getPointDragData(event) {
        const dragData = event.dataTransfer.getData('pointDragData');
        if (dragData) {
          // Array<{codes, vectors}>
          const data = JSON.parse(dragData);
          // 将Array<{codes: Array<{}>, vectors: Array<{}>}>拆分为单线条Array<{code: Array<{}>, vector: Array<{}>}>
          return data.reduce((arr, item) => arr.concat(
            item.codes.reduce((codes, code) => codes.concat(
              item.vectors.map((vector) => ({code, vector}))
            ), [])
          ), []);
        }
        return [];
      },
      /**
       * 新增图幅
       * @param event drop事件
       */
      dropOnEmpty(event) {
        const data = this.getPointDragData(event);
        if (data.length) {
          this.parseData(data);
        } else {
          this.$message.warning(this.t('graphic.graphWorkArea.codeAndVectorWarning'));
        }
        this.$refs.codeTree.clearCheckedNodes();
      },
      /**
       * 在图幅上新增线条
       * @param event drop事件
       * @param graphIndex 当前图幅下标
       */
      async dropOnGraph(event, graphIndex) {
        const data = this.getPointDragData(event);
        if (data.length) {
          this.parseGraphData(data, graphIndex);
          await this.generateRequestUrl(this.graphs[graphIndex].option);
          this.$refs.lineCharts.updateGraphOption(this.graphs[graphIndex], graphIndex);
          this.$refs.codeTree.clearCheckedNodes();
        } else {
          this.$message.warning(this.t('graphic.graphWorkArea.codeAndVectorWarning'));
        }
      },
      /**
       * 获取当前选择的测点个数（去重）
       * @param config 当前图幅配置
       */
      checkCodeCount(config) {
        let codeList = new Set();
        config.series.forEach(item => {
          codeList.add(item.value.codeId);
        });
        return codeList.length;
      },
      /**
       * 判断当前可用的Y轴位置（left, right）
       * @param position 当前可能的Y轴位置
       * @param config 当前图幅配置
       */
      checkPosition(position, config) {
        let positionCount = 0;
        config.yAxis.forEach(yConfig => {
          if (position === yConfig.position) {
            positionCount++;
          }
        });
        if (positionCount >= 2) {
          position = position === 'left' ? 'right' : 'left';
        }
        return position;
      },
      /**
       * 处理拖拽后的单个图幅数据
       * @param data 测点和分量信息
       * @param graphIndex 图幅下标
       */
      parseGraphData(data, graphIndex) {
        const option = this.graphs[graphIndex].option;
        let yAxisCount = option.yAxis.length;
        let lineChartsData = [];
        const addNewLine = (item) => {
          let existVectorSeries = option.series.find(series => {
            return item.vector.valueVectorId === series.value.vectorId;
          });
          let yAxisIndex;
          if (existVectorSeries) {
            yAxisIndex = existVectorSeries.yAxisIndex;
          } else {
            let position = Y_POSITION[yAxisCount++];
            position = this.checkPosition(position, option);
            const yConfig = this.loadDefaultYAxisConfig(item.vector.valueVectorId, item.vector.valueVectorName, position);
            option.yAxis.push(yConfig);
            yAxisIndex = option.yAxis.length - 1;
          }
          let lineConfig = this.loadDefaultLineConfig(yAxisIndex, item.code, item.vector, COLORS[option.series.length % COLORS.length]);
          option.series.push(lineConfig);
        };
        let needUpdateGraph = false;
        data.forEach(item => {
          const notExistYConfig = option.series.map((series) => series.value.vectorId).indexOf(item.vector.valueVectorId) === -1;
          if ((notExistYConfig ? 1 : 0) + yAxisCount > 4) {
            lineChartsData.push(item);
          } else {
            if (option.series.length < this.valueFilterForm.maxLineCount) {
              addNewLine(item);
              needUpdateGraph = true;
            } else {
              lineChartsData.push(item);
            }
          }
        });
        if (needUpdateGraph) {
          this.$refs.lineCharts.updateGraphOption(this.graphs[graphIndex], graphIndex);
        }
        if (lineChartsData.length > 0) {
          this.parseData(lineChartsData, graphIndex + 1);
        }
      },
      /**
       * 完成图幅的配置和绘制
       * @param data 测点和分量信息
       * @param insertIndex 从中间插入的下标，默认为末尾下标
       */
      async parseData(data, insertIndex = this.graphs.length) {
        const defaultConfig = () => ({
          yAxis: [],
          series: [],
          defines: {
            x: d => d.isBrokenConnected ? null : dayjs(d.watchTime, 'YYYY-MM-DDTHH:mm:ss').toDate(),
            y: d => d.isBrokenConnected ? null : d.value
          }
        });
        const graphs = [];
        let lineCount = 0;
        let lineChart = defaultConfig();
        const vectorYAxisMap = new Map();
        data.forEach((item, index) => {
          if (!vectorYAxisMap.has(item.vector.valueVectorId)) {
            lineChart.yAxis.push(this.loadDefaultYAxisConfig(item.vector.valueVectorId, item.vector.valueVectorName, Y_POSITION[lineCount % Y_POSITION.length]));
            vectorYAxisMap.set(item.vector.valueVectorId, item.vector);
          }
          const yAxisIndex = lineChart.yAxis.findIndex((yAxis) => yAxis.id === item.vector.valueVectorId);
          let lineConfig = this.loadDefaultLineConfig(yAxisIndex, item.code, vectorYAxisMap.get(item.vector.valueVectorId), COLORS[lineChart.series.length % COLORS.length]);
          lineChart.series.push(lineConfig);
          lineCount++;
          if (lineCount >= this.valueFilterForm.maxLineCount || index === data.length - 1) {
            vectorYAxisMap.clear();
            // 根据取值参数配置线条名称显示
            const seriesNameFields = ['codeName'];
            if (this.valueFilterForm.showVector) {
              seriesNameFields.push('vectorName', 'vectorUnit');
            }
            if (this.valueFilterForm.showDepth) {
              seriesNameFields.push('codeInstallDepth');
            }
            lineChart.defines.seriesName = d => seriesNameFields.map((field) => d[field]).join('.');
            graphs.push(lineChart);
            lineCount = 0;
            lineChart = defaultConfig();
          }
        });
        for (let i = 0; i < graphs.length; i++) {
          const option = {
            editable: true,
            replicable: false,
            queryable: true,
            scaleable: false,
            width: '100%',
            option: await this.generateRequestUrl(graphs[i])
          };
          this.$refs.lineCharts.addGraphOption(option, insertIndex + i);
        }
      },
      /**
       * 加载默认Y轴配置
       * @param id Y轴id
       * @param name Y轴名称
       * @param position Y轴位置
       */
      loadDefaultYAxisConfig(id, name, position) {
        return mergeOption(Y_AXIS_OPTION, {
          id,
          position: position,
          max: 0,
          min: 0,
          maxAuto: true,
          minAuto: true,
          axisLabel: {
            text: name
          }
        });
      },
      /**
       * 加载默认线条配置
       * @param yAxisIndex y坐标轴下标
       * @param code 测点信息
       * @param vector 分量信息
       * @param color 线条颜色
       */
      loadDefaultLineConfig(yAxisIndex, code, vector, color) {
        return mergeOption(LINE_CONFIG, {
          yAxisIndex,
          data: [],
          itemStyle: {
            color: color
          },
          value: {
            ...this.valueFilterForm.value,
            codeId: code.codeId,
            codeName: code.name,
            vectorId: vector.valueVectorId,
            vectorName: vector.valueVectorName,
            vectorUnit: vector.unit
          }
        });
      },
      /**
       * 加载图幅始测和最近时间
       * @param graph 图幅配置
       */
      async loadDefaultDate(graph) {
        // '{codeId: 0, vectorId: 0};{codeId: 2, vectorId: 2}'
        const params = graph.series.map(series => JSON.stringify({
          codeId: series.value.codeId,
          vectorId: series.value.vectorId
        })).join(';');
        // [0: startDate, 1: endDate]
        return (await this.$api.$get(this.urlConfig.startEndTime, {params: {params}}));
      },
      /**
       * 生成图幅线条取值url
       * @param graph 图幅配置
       */
      async generateRequestUrl(graph) {
        let date = _.cloneDeep(this.dateForm);
        if (!date.startDate || !date.endDate) {
          // 查询始测时间和最近时间
          const defaultDate = await this.loadDefaultDate(graph);
          if (!date.startDate) {
            date.startDate = defaultDate[0];
            if (date.endDate < date.startDate) {
              this.$message.info(this.t('graphic.graphWorkArea.dateValidInfo2'));
              return false;
            }
          }
          if (!date.endDate) {
            date.endDate = defaultDate[1];
            if (date.endDate < date.startDate) {
              this.$message.info(this.t('graphic.graphWorkArea.dateValidInfo3'));
              return false;
            }
          }
        }
        graph.series.map((series) => {
          let params = {
            codeId: series.value.codeId,
            vectorId: series.value.vectorId,
            startTime: dayjs(date.startDate).format('YYYY-MM-DD [00:00:00]'),
            endTime: dayjs(date.endDate).format('YYYY-MM-DD [23:59:59]'),
            with: '',
            startDayTime: series.value.startDayTime,
            endDayTime: series.value.endDayTime,
            writeType: series.value.writeType.join(','),
            auditStatus: series.value.auditStatus.join(','),
            valueStatus: series.value.valueStatus.join(','),
            valueType: series.value.valueType.join(','),
            missingExamineDays: series.value.missingExamineDays
          };
          series.queryUrl = `${this.urlConfig.query[this.currentLineType]}?${querystring.stringify(params)}`;
        });
        return graph;
      },
      /**
       * 清空
       */
      clear() {
        this.$confirm(this.t('graphic.graphWorkArea.cannotBeCleared'), this.t('graphic.whole.prompt'), {
          confirmButtonText: this.t('graphic.whole.sure'),
          cancelButtonText: this.t('graphic.whole.cancel')
        }).then(() => {
          this.$refs.lineCharts.clear();
        }).catch(() => {});
      },
      /**
       * 打开取值弹框
       */
      openValueFilterDialog() {
        this.$refs.valueFilterDialog.open(this.valueFilterForm);
      },
      /**
       * 保存取值（修改后已绘制的图幅配置保持不变）
       * @param form 取值表单
       */
      saveValueFilter(form) {
        this.valueFilterForm = form;
      },
      /**
       * 保存
       */
      save() {},
      /**
       * 另存为
       */
      saveAs() {},
      /**
       * 打印
       */
      async print() {
        this.isPrinting = true;
        const canvas = await html2canvas(document.getElementById('lineCharts'));
        const image = canvas.toDataURL('image/jpeg', 1);
        this.isPrinting = false;
        printJS({
          printable: image,
          documentTitle: '',
          base64: true,
          type: 'image'
        });
      },
      /**
       * 导出（word)
       */
      async exportWord() {
        this.isExporting = true;
        const svgFiles = await this.$refs.lineCharts.getImages();
        let form = new FormData();
        svgFiles.forEach((file) => {
          form.append('file', file);
        })
        this.$api.post(this.urlConfig.export, form, {responseType: 'blob'})
          .then((res) => {
            saveAs(res.data, 'word.doc');
          })
          .catch(() => {
            this.$message.error(this.t('graphic.whole.exportFailed'));
          })
          .finally(() => {
            this.isExporting = false;
          });
      }
    }
  };
</script>
