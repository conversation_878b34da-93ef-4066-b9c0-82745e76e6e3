<template>
  <div style="height: 0;" ref="graphImage">
    <img src="" alt="img" style="opacity: 0;width: 0;height: 0;">
    <el-dialog custom-class="normal-padding" :title="t('graphic.lineChart.copyGraph')" :width="width + 30" :visible.sync="imageVisible">
      <div>
        <p class="vg-text-primary">{{t('graphic.lineChart.copyGraphPlaceholder')}}</p>
        <img class="full-width" :src="base64" :alt="t('graphic.lineChart.copyGraph')">
      </div>
      <div slot="footer" class="text-center">
        <el-button type="primary" @click="imageVisible = false">{{t('graphic.whole.finish')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const html2canvas = require('html2canvas');
  export default {
    name: 'DamLineChartImage',
    props: {
      width: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        base64: '',
        imageVisible: false
      };
    },
    methods: {
      async copy(node) {
        const canvas = await html2canvas(node);
        this.base64 = canvas.toDataURL('image/jpeg', 1);
        const agent = navigator.userAgent.toLowerCase();
        if (agent.indexOf('chrome/') > -1) {
          const chromeAgent = agent.split(' ').find((a) => a.indexOf('chrome/') > -1);
          const version = +chromeAgent.substring(chromeAgent.indexOf('chrome/') + 'chrome/'.length, chromeAgent.indexOf('.'));
          if (version >= 76) {
            // chrome76以上才支持复制img标签
            const $imgContainer = this.$refs.graphImage;
            $imgContainer.setAttribute('contenteditable', '');
            const $img = $imgContainer.childNodes[0];
            $img.src = this.base64;
            $img.onload = () => {
              this._selectImg($img);
              document.execCommand('Copy');
              window.getSelection().removeAllRanges();
              $imgContainer.removeAttribute('contenteditable');
              this.$message.success(this.t('graphic.lineChart.messageCopySuccess'));
            };
          } else {
            // 弹窗自行复制图片
            this.imageVisible = true;
          }
        }
      },
      _selectImg(targetNode) {
        if (window.getSelection) {
          // chrome等主流浏览器
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNode(targetNode);
          selection.removeAllRanges();
          selection.addRange(range);
        } else if (document.body.createTextRange) {
          // ie
          const range = document.body.createTextRange();
          range.moveToElementText(targetNode);
          range.select();
        }
      }
    }
  }
</script>
