<template>
  <div class="position-absolute" @keyup.enter="save">
    <el-input
      ref="titleInput"
      :style="`left: ${textInput.x}px;top:${textInput.y - 10}px;width: ${textInput.width}px;`"
      size="mini"
      @blur="save"
      v-model="textInput.name"
      v-show="titleEditable"
    ></el-input>
  </div>
</template>

<script>
  import * as _ from 'lodash';

  export default {
    name: 'DamLineChartTitle',
    data() {
      return {
        titleEditable: false,
        textInput: {
          x: 'unset',
          y: 'unset',
          name: '',
          width: 'auto'
        }
      }
    },
    methods: {
      show(data) {
        this.textInput = _.cloneDeep(data);
        this.titleEditable = true;
        this.$nextTick(() => {
          this.$refs['titleInput'].focus();
        });
      },
      save() {
        this.$emit('save', this.textInput);
        this.titleEditable = false;
      }
    }
  }
</script>

<style scoped>
  .el-input .el-input__inner {
    text-align: center;
  }
</style>
