<template>
  <div
    class="mx-auto position-relative main"
    :style="{ width, height }"
    @click.meta.prevent="$emit('ctrlClick')"
  >
    <el-card
      class="position-relative cursor-pointer full-height"
      :body-style="{ padding: '10px 0', height: '100%', boxSizing: 'border-box'}"
      @dblclick.native.stop="$emit('dblclick')"
      v-loading="loading"
    >
      <!-- 图幅绘制 -->
      <div class="card-body full-height" :style="{ minHeight }" ref="card"></div>
    </el-card>
    <template v-if="editable">
      <!-- 标题修改文本框 -->
      <DamLineChartTitle
        ref="lineChartTitle"
        @save="titleChange"
      ></DamLineChartTitle>
    </template>
    <!-- 复制图幅的图片 -->
    <DamLineChartImage
      ref="lineChartImage"
      :width="width"
    ></DamLineChartImage>
    <!-- 右键菜单 -->
    <v-contextmenu style="width: 160px;" ref="contextMenu">
      <v-contextmenu-item v-if="isScaled" class="vg-py-2" @click="goBack">{{t('graphic.whole.back')}}</v-contextmenu-item>
      <v-contextmenu-item v-if="replicable" class="vg-py-2" @click="copy">{{t('graphic.lineChart.copyGraph')}}</v-contextmenu-item>
      <template v-if="comparable">
        <v-contextmenu-item class="vg-py-2" @click="compare">{{option.compared ? t('graphic.lineChart.cancelManualComparison') : t('graphic.lineChart.manualComparison')}}</v-contextmenu-item>
      </template>
      <template v-if="exportable">
        <v-contextmenu-item class="vg-py-2" @click="exportGraph('wmf')">{{t('graphic.lineChart.exportAs')}} WMF</v-contextmenu-item>
        <v-contextmenu-item class="vg-py-2" @click="exportGraph('emf')">{{t('graphic.lineChart.exportAs')}} EMF</v-contextmenu-item>
        <v-contextmenu-item class="vg-py-2" @click="exportGraph('doc')">{{t('graphic.lineChart.exportAs')}} Word</v-contextmenu-item>
        <v-contextmenu-item class="vg-py-2" @click="exportGraph('wps')">{{t('graphic.lineChart.exportAs')}} WPS</v-contextmenu-item>
      </template>
      <template v-if="completedOption.type !== 'correlationLine'">
        <v-contextmenu-item class="vg-py-2" @click="getCompValues" v-if="queryable && selectedLineSeries">{{t('graphic.lineChart.viewCode')}}</v-contextmenu-item>
      </template>
      <template v-if="editable">
        <!-- <v-contextmenu-item class="vg-py-2" @click="$emit('graphSelect')">{{t('graphic.graphStyle.graphStyle')}}</v-contextmenu-item> -->
        <v-contextmenu-item class="vg-py-2" @click="$emit('graphDelete')">{{t('graphic.graphStyle.deleteGraph')}}</v-contextmenu-item>
        <template v-if="selectedLineSeries">
          <v-contextmenu-item class="vg-py-2" @click="$emit('lineSelect', selectedLineSeries.lineIndex)">{{t('graphic.lineStyle.lineStyle')}}</v-contextmenu-item>
          <v-contextmenu-item class="vg-py-2" @click="deleteLine()">{{t('graphic.lineStyle.deleteLine')}}</v-contextmenu-item>
        </template>
      </template>
    </v-contextmenu>
  </div>
</template>

<script>
  import * as d3 from 'd3';
  import _ from 'lodash';
  import * as querystring from 'query-string';
  import { saveAs } from 'file-saver';
  import ProcessLine from '../assets/js/processLine';
  import DistributionLine from '../assets/js/distributionLine';
  import CorrelationLine from '../assets/js/correlationLine';
  import { completeOption } from '../assets/js/options';
  import { calculateCorrelationCoefficient } from '../assets/js/utils';
  import { serialize } from '../assets/js/d3-util';
  import DamLineChartTitle from './DamLineChartTitle';
  import DamLineChartImage from './DamLineChartImage';
  export default {
    name: 'LineChart',
    components: {
      DamLineChartTitle,
      DamLineChartImage
    },
    props: {
      // 图幅可拖拽
      draggable: {
        type: Boolean,
        default: false
      },
      // 图幅可人工比测/取消人工比测
      comparable: {
        type: Boolean,
        default: false
      },
      // 图幅可编辑（标题、图幅属性、线条属性）
      editable: {
        type: Boolean,
        default: false
      },
      // 图幅可复制
      replicable: {
        type: Boolean,
        default: true
      },
      // 图幅可导出
      exportable: {
        type: Boolean,
        default: false
      },
      // 图幅可查询测值
      queryable: {
        type: Boolean,
        default: false
      },
      // 图幅可缩放
      scaleable: {
        type: Boolean,
        default: true
      },
      // 图幅配置
      option: {
        type: Object
      },
      // 图幅宽度
      width: {
        type: String,
        default: 'auto'
      },
      // 图幅高度
      height: {
        type: String,
        default: 'auto'
      },
      minHeight: {
        type: String,
        default: '228px'
      },
    },
    data() {
      return {
        completedOption: completeOption(this.option),
        // 图幅缩放后的新比例尺
        newXScale: null,
        newXScaleMap: null,
        // 图幅对象（ProcessLine, DistributionLine, CorrelationLine)
        lineChart: null,
        loading: false,
        isScaled: false,
        needReloadData: true,
        // {series, xScale, yScale, yAxis, lineIndex}
        selectedLineSeries: null
      };
    },
    computed: {
      // 是否显示右键菜单
      contextMenuVisible() {
        return this.isScaled ||
          this.replicable ||
          this.comparable ||
          this.exportable ||
          !!(
            this.completedOption.type !== 'correlationLine' &&
            this.queryable && this.selectedLineSeries
          );
      }
    },
    methods: {
      /**
       * 获取图幅完整配置
       */
      getGraphOption() {
        return this.completedOption;
      },
      /**
       * 绘制图幅
       * @param option 图幅配置
       * @param needReloadData 是否需要重新加载数据
       */
      async renderChart(option = this.completedOption, needReloadData = true) {
        const loadingInstance = this.$loading({target: this.$refs.card, fullscreen: false});
        this.completedOption = option = completeOption(option);
        this.needReloadData = needReloadData;
        if (this.isScaled) {
          option.defines.xScale = this.newXScale;
          option.defines.yScaleMap = this.newYScaleMap;
        } else {
          option.defines.xScale = null;
          option.defines.yScaleMap = null;
        }
        if (option.type === 'processLine') {
          await this.loadData(option);
          this.lineChart = new ProcessLine(option, this);
        } else if (option.type === 'distributionLine') {
          await this.loadDistributionData(option);
          this.lineChart = new DistributionLine(option, this);
        } else if (option.type === 'correlationLine') {
          await this.loadCorrelationData(option);
          this.lineChart = new CorrelationLine(option, this);
        }
        this.$refs.card.innerHTML = '';
        this.lineChart.render(this.$refs.card);
        loadingInstance.close();
        this.$nextTick(() => {
          this.$emit('load');
        });
      },
      /**
       * 加载线条数据
       * @param lineStyle 线条属性
       */
      loadLineData(lineStyle) {
        return lineStyle.queryUrl && this.needReloadData
          ? this.$api.$get(lineStyle.queryUrl)
          : { data: lineStyle.data, ...lineStyle.value };
      },
      /**
       * 加载过程线数据
       * @param option 图幅配置
       */
      async loadData(option) {
        const { yAxis, series, defines } = option;
        let max = {};
        let min = {};
        const graphLines = await Promise.all(
          series.map(line => this.loadLineData(line))
        );
        graphLines.forEach((item, index) => {
          const line = series[index];
          if (line.queryUrl) {
            // 传入url请求测值时默认生成线条名称和坐标轴文字
            if (!line.name) {
              line.name = defines.seriesName(item);
            }
            const {codeName, vectorId, vectorName, vectorUnit} = item;
            // 初始化线条筛选属性
            line.value = Object.assign(
              { codeName, vectorId, vectorName, vectorUnit },
              line.value
            );
            const config = querystring.parseUrl(line.queryUrl);
            // 记录url传入的起始时间和截止时间，右键返回时需要还原
            if (!line.value.startTime) {
              line.value.startTime = config.query.startTime;
            }
            if (!line.value.endTime) {
              line.value.endTime = config.query.endTime;
            }
            if (!line.value.codeId) {
              line.value.codeId = item.codeId || null;
            }
            if (!yAxis[line.yAxisIndex].axisLabel.text) {
              yAxis[line.yAxisIndex].axisLabel.text = defines.yAxisText(item);
            }
          }
          line.data = item.data;
          const extent = d3.extent(line.data, defines.y);
          const minValue = extent[0];
          const maxValue = extent[1];
          max[line.yAxisIndex] = max[line.yAxisIndex] === undefined
            ? maxValue
            : d3.max([max[line.yAxisIndex], maxValue]);
          min[line.yAxisIndex] = min[line.yAxisIndex] === undefined
            ? minValue
            : d3.min([min[line.yAxisIndex], minValue]);
        });
        // 缩放后不自动计算最大最小值
        if (!this.isScaled) {
          yAxis.forEach((config, index) => {
            config.max = config.maxAuto ? max[index] || 0 : config.max;
            config.min = config.minAuto ? min[index] || 0 : config.min;
            if (config.maxAuto || config.minAuto) {
              this.computeMaxAndMin(config);
            }
          });
        }
        yAxis.forEach(config => {
          if (config.axisTick.decimalNumberAuto) {
            this.computeDecimalNumber(config);
          }
        });
      },
      /**
       * 加载分布线数据
       * @param option 图幅配置
       */
      async loadDistributionData(option) {
        const { xAxis, yAxis, series, defines } = option;
        let max = {};
        let min = {};
        const graphLines = await Promise.all(
          series.map(line => this.loadLineData(line))
        );
        graphLines.forEach((item, index) => {
          const line = series[index];
          if (line.queryUrl) {
            // 传入url请求测值时默认生成线条名称和坐标轴文字
            if (!line.name) {
              line.name = defines.seriesName(item);
            }
            const {vectorId, vectorName, vectorUnit} = item;
            // 初始化线条筛选属性
            line.value = Object.assign({vectorId, vectorName, vectorUnit}, line.value);
            // 过滤无效数据
            item.data = item.data.filter((d) => !!d.code);
            // 当x轴非等间距时需要将根据显示的字段值从小到大排序
            if (xAxis.coordinateType !== '') {
              item.data = _.sortBy(item.data, (d) => d.code[xAxis.coordinateType]);
            }
            // 当传入数量不同的点时以最大数量的点为基准
            if (xAxis.data.length === 0 || item.data.length > xAxis.data.length) {
              // x轴文字显示(传入静态数据时必填)
              xAxis.data = item.data.map((d) => d.code[xAxis.nameType]);
              // x轴真实刻度值，默认为等间距([1, 2, 3...])
              xAxis.ticks = xAxis.coordinateType === ''
                ? xAxis.data.map((d, i) => i + 1)
                : item.data.map((d) => d.code[xAxis.coordinateType]);
            }
            if (!line.value.codeIds) {
              line.value.codeIds = this.needReloadData
                ? item.data.map((data) => data.code.codeId)
                : [];
            }
            if (!yAxis[line.yAxisIndex].axisLabel.text) {
              yAxis[line.yAxisIndex].axisLabel.text = defines.yAxisText(item);
            }
          } else {
            // 不传入queryUrl时默认为等间距
            if (!xAxis.ticks.length) {
              xAxis.ticks = xAxis.data.map((d, i) => i + 1);
            }
          }
          line.data = item.data;
          const extent = d3.extent(line.data, defines.y);
          const minValue = extent[0];
          const maxValue = extent[1];
          max[line.yAxisIndex] = max[line.yAxisIndex] === undefined
            ? maxValue
            : d3.max([max[line.yAxisIndex], maxValue]);
          min[line.yAxisIndex] = min[line.yAxisIndex] === undefined
            ? minValue
            : d3.min([min[line.yAxisIndex], minValue]);
        });
        // 缩放后不自动计算最大最小值
        if (!this.isScaled) {
          yAxis.forEach((config, index) => {
            config.max = config.maxAuto ? max[index] || 0 : config.max;
            config.min = config.minAuto ? min[index] || 0 : config.min;
            if (config.maxAuto || config.minAuto) {
              this.computeMaxAndMin(config);
            }
          });
        }
        yAxis.forEach(config => {
          if (config.axisTick.decimalNumberAuto) {
            this.computeDecimalNumber(config);
          }
        });
      },
      /**
       * 加载相关线数据
       * @param option 图幅配置
       */
      async loadCorrelationData(option) {
        const { xAxis, yAxis, series, defines } = option;
        let max = {};
        let min = {};
        const graphLines = await Promise.all(series.map(line => this.loadLineData(line)));
        graphLines.forEach((item, index) => {
          const line = series[index];
          line.data = item.data;
          if (line.queryUrl) {
            // 初始化线条筛选属性
            line.value = Object.assign({}, line.value);
            const xCodeId = xAxis.value.codeId;
            const yCodeId = yAxis[line.yAxisIndex].value.codeId;
            if (this.needReloadData) {
              line.data = line.data.map((d) => ({x: d.value[xCodeId], y: d.value[yCodeId]}));
            }
            // 传入url请求测值时默认生成线条名称和坐标轴文字
            if (!line.name) {
              line.name = defines.seriesName(item, yCodeId);
            }
            if (!xAxis.axisLabel.text) {
              xAxis.axisLabel.text = defines.xAxisText(item, xCodeId);
            }
            if (!yAxis[line.yAxisIndex].axisLabel.text) {
              yAxis[line.yAxisIndex].axisLabel.text = defines.yAxisText(item, yCodeId);
            }
          }
          // 设置X轴
          const xExtent = d3.extent(line.data, defines.x);
          const tick = Math.round((xExtent[1] - xExtent[0]) / xAxis.axisTick.tickNumber);
          if (!this.isScaled) {
            xAxis.min = xAxis.min < xExtent[0] ? xAxis.min : xExtent[0] - tick;
            xAxis.max = xAxis.max > xExtent[1] ? xAxis.max : xExtent[1] + tick;
          }
          // 设置Y轴
          const extent = d3.extent(line.data, defines.y);
          const minValue = extent[0];
          const maxValue = extent[1];
          const coefficient = calculateCorrelationCoefficient(line.data, defines);
          max[line.yAxisIndex] = _.max([
            max[line.yAxisIndex],
            Math.ceil(coefficient), maxValue
          ]);
          min[line.yAxisIndex] = _.min([
            min[line.yAxisIndex],
            Math.floor(coefficient), minValue
          ]);
        });
        // 缩放后不自动计算最大最小值
        if (!this.isScaled) {
          yAxis.forEach((config, index) => {
            config.max = config.maxAuto ? max[index] || 0 : config.max;
            config.min = config.minAuto ? min[index] || 0 : config.min;
            if (config.maxAuto || config.minAuto) {
              this.computeMaxAndMin(config);
            }
          });
        }
        yAxis.forEach(config => {
          if (config.axisTick.decimalNumberAuto) {
            this.computeDecimalNumber(config);
          }
        });
      },
      /**
       * 计算Y轴最大最小值
       * @param config 某一Y轴配置
       */
      computeMaxAndMin(config) {
        let delta0 = (config.max - config.min) / config.axisTick.tickNumber;
        let delta0String = delta0.toString();
        let pointIndex = delta0String.indexOf('.');
        let firstNotZeroChar = null;
        let secondChar = null;
        let m = null;
        let k = null;
        for (let i = 0; i < delta0String.length; i++) {
          if (
            delta0String.charAt(i) !== '0' &&
            delta0String.charAt(i) !== '.' &&
            delta0String.charAt(i) !== '-' &&
            !firstNotZeroChar
          ) {
            firstNotZeroChar = delta0String.charAt(i);
            if (pointIndex === -1) {
              m = delta0String.length;
            } else {
              m = Math.abs(i - pointIndex);
            }
            break;
          }
          if (
            delta0String.charAt(i) !== '0' &&
            delta0String.charAt(i) !== '.' &&
            firstNotZeroChar
          ) {
            secondChar = delta0String.charAt(i);
            break;
          }
        }
        firstNotZeroChar = firstNotZeroChar === null ? 0 : firstNotZeroChar;
        secondChar = secondChar === null ? 0 : secondChar;
        if (delta0 >= 1) {
          k = Math.pow(10, m - 2);
        } else {
          k = 1 / Math.pow(10, m + 1);
        }
        const valueList = [1, 2, 5, 10, 20, 50, 100, 200, 250, 500, 1000];
        let tempValue = 10 * parseInt(firstNotZeroChar) + parseInt(secondChar);
        let id = 0;
        for (let i = 0; i < valueList.length; i++) {
          if (valueList[i] >= tempValue) {
            id = i;
            break;
          }
        }
        let x;
        let minValue0 = config.min;
        let maxValue0 = config.max;
        while (true) {
          let delta = k * valueList[id];
          x = minValue0 / delta;
          if (config.minAuto) {
            config.min = Math.floor(x) * delta; // 注：[x]代表小于或等于x的最大整数。
          }
          if (config.maxAuto) {
            config.max = config.min + config.axisTick.tickNumber * delta; // 注：此n 是坐标轴中间的刻度个数
          }
          if (config.max >= maxValue0) {
            break;
          }
          id++;
          if (id > valueList.length - 1) { // 可证明此种情况不可能发生
            break;
          }
        }
      },
      /**
       * 计算小数位数，小数位数设置为自动时自动调整
       * @param config 某一Y轴配置
       */
      computeDecimalNumber(config) {
        const step = (config.max - config.min) / config.axisTick.tickNumber;
        let tickValues = [];
        for (let i = 0; i <= config.axisTick.tickNumber; i++) {
          tickValues.push(config.min + step * i);
        }
        // 为刻度值保留合适的小数位数
        for (let i = 0; i < 100; i++) {
          const fixedTickValues = tickValues.map((value) => value.toFixed(i));
          if (fixedTickValues.length === _.uniq(fixedTickValues).length) {
            if (config.axisTick.decimalNumberAuto) {
              config.axisTick.decimalNumber = i;
            }
            break;
          }
        }
      },
      /**
       * 选中标题
       * @param data {x, y, name, width}，x: 标题x偏移量,y: 标题y偏移量, name: 标题名称, width: 标题所占页面宽度
       */
      titleSelect(data) {
        if (this.editable) {
          this.$refs.lineChartTitle.show(data);
        }
      },
      /**
       * 保存标题
       * @param textInput {x, y, name, width}，x: 标题x偏移量,y: 标题y偏移量, name: 标题名称, width: 标题所占页面宽度
       */
      titleChange(textInput) {
        const { title } = this.completedOption;
        title.text = textInput.name;
        this.renderChart();
        this.$emit('titleChange', this.completedOption);
      },
      /**
       * 打开右键菜单
       * @param event contextmenu事件
       * @param series 选中的（最近的）线条{series, xScale, yScale, yAxis, lineIndex}，series: 线条属性, xScale: x比例尺, yScale: y比例尺, yAxis: y轴属性, lineIndex: 线条下标
       */
      openContextMenu(event, series = null) {
        console.log('caidan');
        this.selectedLineSeries = series;
        if (this.contextMenuVisible) {
          this.$refs['contextMenu'].show({
            left: event.clientX,
            top: document.documentElement.scrollTop + event.clientY
          });
        } else {
          this.$refs['contextMenu'].hide();
        }
        event.preventDefault();
      },
      /**
       * （缩放后）返回
       */
      goBack() {
        this.resetChart();
        this.$emit('scaleChange', false, null);
      },
      /**
       * 重置图幅
       */
      resetChart() {
        this.isScaled = false;
        this.renderChart();
      },
      /**
       * 右键查看测值
       */
      getCompValues() {
        let {series, xScale, yScale} = this.selectedLineSeries;
        const { defines } = this.completedOption;
        if (defines.xScale) {
          xScale = defines.xScale;
        }
        if (defines.yScaleMap) {
          yScale = defines.yScaleMap.get(series.yAxisIndex)
        }
        const xDomain = xScale.domain();
        const yDomain = yScale.domain();
        this.$emit('valueQuery', {
          xMax: xDomain[1],
          xMin: xDomain[0],
          yMax: yDomain[1],
          yMin: yDomain[0],
          ...series.value
        });
      },
      deleteLine(lineIndex = this.selectedLineSeries.lineIndex) {
        const { yAxis, series } = this.completedOption;
        series.splice(lineIndex, 1);
        // 当图幅无线条时删除图幅
        if (series.length === 0) {
          this.$emit('graphDelete');
          return;
        }
        // 删除没有被引用的Y轴
        for (let i = 0; i < yAxis.length; i++) {
          // 获取完整配置中的线条的Y轴下标
          const yAxisIndexes = series.map(line => line.yAxisIndex);
          if (yAxisIndexes.indexOf(i) === -1) {
            yAxis.splice(i, 1);
            // 往后的yAxis下标被推前，需要更新线条yAxisIndex属性
            series.forEach(line => {
              if (line.yAxisIndex > i) {
                line.yAxisIndex--;
              }
            });
          }
        }
        this.renderChart();
        this.$emit('lineDelete', this.completedOption);
      },
      /**
       * 复制图幅
       */
      copy() {
        this.$refs.lineChartImage.copy(this.$refs.card);
      },
      /**
       * 人工比测/取消人工比测
       */
      compare() {
        this.$emit('comparedChange');
      },
      /**
       * 导出WMF、EMF、WORD
       * @param type 文件类型
       * @returns {Promise<void>}
       */
      async exportGraph(type) {
        if (type === 'wps') {
          this.$message.info({
            message: this.t('graphic.lineChart.exportWPS'),
            duration: 4000
          });
          this.copy();
          return;
        }
        const name = 'image';
        this.$message.info(this.t('graphic.lineChart.exporting'));
        const formData = new FormData();
        formData.append('name', name);
        formData.append('content', serialize(this.$el.querySelector('svg.line-graph')));
        formData.append('type', type);
        const res = await this.$api.post(
          '/api/file/svg/export',
          formData,
          {responseType: 'blob'}
        );
        saveAs(res.data, name + '.' + type);
      },
      /**
       * 框选末事件回调
       * @param newXScale 新的x比例尺
       * @param newYScaleMap 新的y比例尺map
       * @param xScaleType x比例尺类型（LINEAR: 线性比例尺, ORDINAL: 普通比例尺）
       */
      onSelectEnd(newXScale, newYScaleMap, xScaleType) {
        this.onZoom(newXScale, newYScaleMap, xScaleType);
      },
      /**
       * 滚轮缩放事件回调
       * @param newXScale 新的x比例尺
       * @param newYScaleMap 新的y比例尺map
       * @param xScaleType x比例尺类型（LINEAR: 线性比例尺, ORDINAL: 普通比例尺）
       */
      onZoomed: _.debounce(function (newXScale, newYScaleMap, xScaleType) {
        this.onZoom(newXScale, newYScaleMap, xScaleType);
      }, 500),
      /**
       * 缩放事件回调
       * @param newXScale 新的x比例尺
       * @param newYScaleMap 新的y比例尺map
       */
      onZoom(newXScale, newYScaleMap) {
        this.isScaled = true;
        this.newXScale = newXScale;
        this.newYScaleMap = newYScaleMap;
        this.renderChart();
        const newXDomain = newXScale.domain();
        this.$emit('scaleChange', true, {
          xMax: newXDomain[1],
          xMin: newXDomain[0],
          yDomains: [...newYScaleMap.values()].map((item) => {
            const yDomain = item.domain();
            return {
              yMax: yDomain[1],
              yMin: yDomain[0]
            }
          })
        });
      }
    },
    mounted() {
      this.renderChart();
    }
  };
</script>

<style lang="scss">
  .main {
    border-radius: 5px;
    flex-grow: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  circle:hover, .point, .legend-entry {
    cursor: pointer;
  }

  .line .point:hover {
    stroke-width: 20px;
    stroke-opacity: .5;
  }

  /*线条*/
  g path.line:hover, .line-hover, g path.selected, .scatter-hover {
    stroke-width: 3;
    stroke: blueviolet;
    filter: url(#filterShadow);
  }

  /*提示框*/
  .tooltip {
    box-sizing: content-box;
    background-color: rgba(255, 255, 255, 1);
    padding: 5px 10px;
    border: 1px solid #ddd;

    font-size: 13px;

    transition: opacity 500ms linear;

    transition-delay: 500ms;

    box-shadow: 4px 4px 12px rgba(0, 0, 0, .5);

    border-radius: 10px;
    h5 {
      font-size: 16px;
      margin: 8px 0;
      border-bottom: 1px solid black;
    }
    p {
      font-size: 13px;
      line-height: 18px;
    }
  }

  .unit {
    font-size: 9px;
  }

  .point, .symbol {
    cursor: pointer;

    &:hover {
      fill: red;
    }
  }

  .focus {
    pointer-events: none;
  }
</style>
