html,
body {
  padding: 0;
  margin: 0;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

html {
  font-size: 14px;
  box-sizing: border-box;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  color: $body-color;
  background-color: $body-bg;
  text-rendering: optimizeLegibility;
}

label {
  font-weight: normal;
}

* {
  outline: none !important;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
}

a {
  color: inherit;
  font-size: 14px;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  text-decoration: none;
}

button {
  cursor: pointer;
  border: none;
  font-size: 14px;
  text-align: center;
  background-color: transparent;
  &[disabled] {
    cursor: not-allowed !important;
  }
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  display: block;
  line-height: 32px;
  font-size: 15px;
  font-family: "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;
  a {
    color: $brand-info;
    cursor: pointer;
  }
}

ul {
  margin-top: 0;
  margin-bottom: 0;
}

img {
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

p {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 0;
}

// 去掉webkit表单自动填写时的黄色背景
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track {
  background-color: rgba(240,240,240,1);
}
::-webkit-scrollbar-thumb {
  background-color: rgba(216,216,216,1);
  border: 2px solid transparent;
  border-radius: 10px;
  background-clip: padding-box;
  &:hover {
    background-color: rgba(0, 0, 0, 0.2);
  }
}
