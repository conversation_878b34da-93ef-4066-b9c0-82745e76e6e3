.vg-border,
.vg-border-top,
.vg-border-right,
.vg-border-bottom,
.vg-border-left {
  border: 1px solid $border-color;
}

.vg-border-top {
  border-right-width: 0;
  border-bottom-width: 0;
  border-left-width: 0;
}
.vg-border-right {
  border-top-width: 0;
  border-bottom-width: 0;
  border-left-width: 0;
}
.vg-border-bottom {
  border-top-width: 0;
  border-right-width: 0;
  border-left-width: 0;
}
.vg-border-left {
  border-top-width: 0;
  border-right-width: 0;
  border-bottom-width: 0;
}
.vg-border-x {
  border-top-width: 0;
  border-bottom-width: 0;
}
.vg-border-y {
  border-right-width: 0;
  border-left-width: 0;
}

.vg-border-transparent { border-color: transparent; }

.vg-rounded {
  -webkit-border-radius: $border-radius;
  -moz-border-radius: $border-radius;
  border-radius: $border-radius;
}

.vg-rounded-circle { border-radius: 50%; }
.vg-rounded-0      { border-radius: 0; }
