// Mar<PERSON> and Pa<PERSON>
@each $prop, $abbrev in (margin: m, padding: p) {
  @for $size from 0 through 30 {
    .vg-#{$abbrev}-#{$size}  { #{$prop}:        $spacer * $size !important; }
    .vg-#{$abbrev}t-#{$size} { #{$prop}-top:    $spacer * $size !important; }
    .vg-#{$abbrev}r-#{$size} { #{$prop}-right:  $spacer * $size !important; }
    .vg-#{$abbrev}b-#{$size} { #{$prop}-bottom: $spacer * $size !important; }
    .vg-#{$abbrev}l-#{$size} { #{$prop}-left:   $spacer * $size !important; }
    .vg-#{$abbrev}x-#{$size} {
      #{$prop}-right: $spacer * $size !important;
      #{$prop}-left:  $spacer * $size !important;
    }
    .vg-#{$abbrev}y-#{$size} {
      #{$prop}-top:    $spacer * $size !important;
      #{$prop}-bottom: $spacer * $size !important;
    }
  }
}

// Some special margin utils
.mx-auto {
  margin-right: auto;
  margin-left:  auto;
}
