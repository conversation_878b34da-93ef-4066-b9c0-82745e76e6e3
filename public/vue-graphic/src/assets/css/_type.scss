//
// Headings
//

.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: $headings-margin-bottom;
  font-family: $headings-font-family;
  font-weight: $headings-font-weight;
  line-height: $headings-line-height;
  color: $headings-color;
}

.h1 { font-size: $font-size-h1; }
.h2 { font-size: $font-size-h2; }
.h3 { font-size: $font-size-h3; }
.h4 { font-size: $font-size-h4; }
.h5 { font-size: $font-size-h5; }
.h6 { font-size: $font-size-h6; }


//
// Horizontal rules
//

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: $hr-border-width solid $hr-border-color;
}

mark,
.mark {
  padding: $mark-padding;
  margin: $mark-margin;
  background-color: $mark-bg;
  border-radius: $border-radius;
}
