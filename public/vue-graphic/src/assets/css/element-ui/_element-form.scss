// 解决input number输中文换行问题
input.el-input__inner[type="number"] {
  line-height: inherit;
}

.el-form-item.form-item--flex {
  @extend .d-flex;
  label.el-form-item__label {
    @extend .flex-shrink-0;
  }
  .el-form-item__content {
    @extend .flex-fill;
    > * {
      width: 100%;
    }
  }
}

.el-form-item.label-no-padding {
  label.el-form-item__label {
    padding: 0;
  }
}

// 宽的颜色选择器
.el-color-picker.not-square {
  .el-color-picker__trigger {
    width: 100%;
    .el-color-picker__icon {
      display: none;
    }
  }
}
