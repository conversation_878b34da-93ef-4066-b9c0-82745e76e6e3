import * as d3 from 'd3';
import * as _ from 'lodash';
import 'd3-selection-multi';
import {measureText, UUID, elementWidth, elementHeight} from './d3-util';
import Tip from './d3-tip';

const MIN_X_PADDING = 30; // 保证最小的边距，显示x轴名称，否则需要动态布局
const MIN_GRAPH_HEIGHT = 110; // 图幅最小高度
const GRAPH_Y_PADDING = 20; // 图幅纵向内边距
const GRAPH_BLANK_TOP = 10; // 图幅上方留白
const LEGEND_MARGIN = 25; // 图例和网格间距

export default class BaseLine {
  constructor(configuration, vue) {
    this.configuration = configuration;
    this.$vue = vue;
    this.dispatch = d3.dispatch('lineSelect');
    this.graphId = UUID();
    this.yAxisMap = new Map(); // y轴相关信息（比例尺yScale、坐标轴yAxis）
    this.axisPadding = 5; // y轴之间的边距
    this.yAxisUnitHeight = 10; // y轴刻度单位所占高度
    this.containerWidth = 0; // 图幅容器宽度
    this.containerHeight = 0; // 图幅容器高度
    this.left = 0; // 图幅左轴宽度
    this.right = 0; // 图幅右轴宽度
    this.graphWidth = 0; // 图幅主体宽度
    this.graphHeight = 0; // 图幅主体高度
    this.graphBlankBottom = 15; // 图幅下方留白
    this.titleHeight = 0; // 图幅标题高度
    this.legendLineWidth = 30; // 图例线条长度
    this.legendLineHeight = 30; // 图例行高
    this.legendLinePadding = 10; // 单个图例内间距（x方向）
    this.legendHeight = this.legendLineHeight; // 图例高度，默认为一行高度
    this.legendWidthLists = []; // 所有行的线条宽度（二维数组）

    this.$containerSelector = null;
    this.$svg = null;
    this.$tip = null;
    this.$topContainer = null;
    this.$titleContainer = null;
    this.$legendContainer = null;
    this.$xAxisContainer = null;
    this.$leftYAxisContainer = null;
    this.$rightYAxisContainer = null;
    this.$timeAxisGridContainer = null;
    this.$linesContainer = null;
    this.$interactionContainer = null;
    this.$interactionArea = null;
    this.$voronoiDiagram = null;
  }

  /**
   * 初始化
   * @private
   */
  _init() {
    this.dispatch.on('lineSelect', (config, key) => {
      const line = this.$containerSelector.select(`[data-id="line-${key}"]`).node() || this.$containerSelector.select(`[data-id="bar-${key}"`).node();
      // 选中线条
      this.$vue.$emit('lineSelect', key);
      if (line && d3.select(line).classed('selected')) {
        d3.select(line).classed('selected', false);
      } else {
        this.$containerSelector.selectAll('g path.selected').each(function () {
          d3.select(this).classed('selected', false);
        });
        d3.select(line).classed('selected', true);
      }
    });
  }

  /**
   * 初始化布局
   *
   * 有些东西并不能全部在绘图前就能测量，比如在动态修改配置时需要动态调整大小，这里暂时未处理
   *
   * @private
   */
  _initDimensions() {
    this._initAxisDimensions();
    this._initLegendDimensions();
    this._initGraphDimensions();

  }

  /**
   * 初始化坐标轴尺寸
   * @private
   */
  _initAxisDimensions() {
    const yAxisMap = {
      left: {
        yAxis: [],
        maxWidth: this.configuration.leftPadding,
      },
      right: {
        yAxis: [],
        maxWidth: this.configuration.rightPadding,
      }
    };
    let svg = this.$containerSelector.append('svg');
    const { yAxis, maxYAxisCount } = this.configuration;
    yAxis.slice(0, maxYAxisCount).forEach((axis) => {
      const nameWidth = yAxisMap[axis.position].yAxis.length === 0
        ? 0
        : measureText(
          axis.axisLabel.text,
          svg.node(),
          { fontSize: axis.axisLabel.fontSize + 'px' }
        ).width;
      const maxValueWidth = measureText(
        axis.max.toFixed(axis.axisTick.decimalNumber),
        svg.node(),
        { fontSize: axis.axisLabel.fontSize + 'px' }
      ).width;
      const minValueWidth = measureText(
        axis.min.toFixed(axis.axisTick.decimalNumber),
        svg.node(),
        { fontSize: axis.axisLabel.fontSize + 'px' }
      ).width;
      yAxisMap[axis.position].yAxis.push(yAxis);
      yAxisMap[axis.position].maxWidth += d3.max([nameWidth, maxValueWidth, minValueWidth]);
    });
    const left = yAxisMap.left.maxWidth + (yAxisMap.left.yAxis.length + 1) * this.axisPadding;
    const right = yAxisMap.right.maxWidth + (yAxisMap.right.yAxis.length + 1) * this.axisPadding;
    this.left = yAxisMap.left.yAxis.length && left < MIN_X_PADDING
      ? MIN_X_PADDING
      : left;
    this.right = yAxisMap.right.yAxis.length && right < MIN_X_PADDING
      ? MIN_X_PADDING
      : right;
    this.graphWidth = this.containerWidth - this.left - this.right;
    svg.remove();
  }

  /**
   * 初始化图例尺寸
   * @private
   */
  _initLegendDimensions() {
    let svg = this.$containerSelector.append('svg');
    // 每行的线条宽度
    let list = [];
    // 图例剩余可放置的宽度
    let restWidth = this.graphWidth;
    const { series, defines } = this.configuration;
    series.forEach(line => {
      // 线条无数据时将图例宽度设为0
      let width = 0;
      if (this.isDataValid(line)) {
        const legend = measureText(
          defines.legend ? defines.legend(line) : line.name,
          svg.node(),
        ).width;
        width = legend + this.legendLineWidth + this.legendLinePadding * 2;
      }
      if (restWidth < width) {
        if (list.length > 0) {
          this.legendWidthLists.push(list);
          list = [width];
          restWidth = this.graphWidth - width;
        } else {
          this.legendWidthLists.push([width]);
          list = [];
          restWidth = this.graphWidth;
        }
      } else {
        list.push(width);
        restWidth -= width;
      }
    });
    if (list.length > 0) {
      this.legendWidthLists.push(list);
    }
    this.legendHeight = this.legendLineHeight * this.legendWidthLists.length;
    svg.remove();
  }

  /**
   * 初始化图幅尺寸
   * @private
   */
  _initGraphDimensions() {
    const { title, legend } = this.configuration;
    // 动态计算图幅高度和位置
    let topHeight = 0;
    let bottomHeight = 0;
    if (title.show) {
      // 标题高度，默认1.4倍行高
      this.titleHeight = title.textStyle.fontSize * 1.4;
      if (title.position === 'bottom') {
        bottomHeight += this.titleHeight;
      } else {
        topHeight += this.titleHeight;
      }
    }
    if (legend.show) {
      if (legend.position === 'bottom') {
        bottomHeight += this.legendHeight;
      } else {
        topHeight += this.legendHeight;
      }
    }
    const restHeight = GRAPH_Y_PADDING + GRAPH_BLANK_TOP + this.graphBlankBottom + topHeight + bottomHeight;
    if (this.containerHeight - restHeight < MIN_GRAPH_HEIGHT) {
      this.containerHeight = MIN_GRAPH_HEIGHT + restHeight;
      this.graphHeight = MIN_GRAPH_HEIGHT;
    } else {
      this.graphHeight = this.containerHeight - restHeight;
    }
    this.graphTop = topHeight + LEGEND_MARGIN;
  }

  /**
   * 绘图
   *
   * @private
   */
  renderGraph() {
    this._init();

    this._initDimensions();

    this._renderTopContainer();

    this._renderTitle();

    this._renderLegend();

    this._renderContainer();

    this._initX();

    this._initY();

    this._renderXAxis();

    this._hideOverflowXTicks();

    this._renderYAxis();

    this._createLines();

    this._createMarkLines();

    this._renderInteractionArea();
  }

  /**
   * 创建容器
   *
   * @private
   */
  _renderContainer() {
    this.graph = this.$svg.append('g')
      .attr('class', 'graph')
      .attr('transform', `translate(${this.left}, ${this.graphTop})`);

    // 限制渲染区域
    this.graph.append('clipPath')
      .attr('id', this.graphId + 'clip')
      .append('rect')
      .attr('width', this.graphWidth)
      .attr('height', this.graphHeight);

    // 阴影
    const filter = this.graph.append('defs')
      .append('filter')
      .attr('id', 'filterShadow')
      .attr('filterUnits', 'userSpaceOnUse');

    filter.append('feGaussianBlur')
      .attr('in', 'SourceAlpha')
      .attr('stdDeviation', 2);

    filter.append('feOffset')
      .attr('dx', 0)
      .attr('dy', 0);

    const feComponentTransfer = filter.append('feComponentTransfer');
    feComponentTransfer
      .append('feFuncA')
      .attr('type', 'linear')
      .attr('slope', 0.5);

    const feMerge = filter.append('feMerge');
    feMerge.append('feMergeNode');
    feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

    // 创建提示
    this.$tip = Tip()
      .rootElement(this.$containerSelector.node())
      .attr('class', 'd3-tip')
      .offset([0, 20]);
    this.graph.call(this.$tip);

    // 左侧 y 轴容器
    this.$leftYAxisContainer = this.graph.append('g')
      .attr('class', 'y-axis-container left');

    // 右侧 y 轴容器
    this.$rightYAxisContainer = this.graph.append('g')
      .attr('class', 'y-axis-container right')
      .attr('transform', `translate(${this.graphWidth}, 0)`);

    // x 轴容器
    this.$xAxisContainer = this.graph.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0, ${this.graphHeight})`);

    // x 轴网格线容器
    this.$timeAxisGridContainer = this.graph.append('g')
      .attr('class', 'x-axis grid-line')
      .attr('pointer-events', 'none')
      .attr('transform', `translate(0, ${this.graphHeight})`);

    // 线条容器
    this.$linesContainer = this.graph.append('g')
      .attr('class', 'line-container');

    // 交互区域容器
    this.$interactionContainer = this.graph.append('g')
      .classed('interaction-container', true)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('pointer-events', 'all');

    // 实际交互的区域
    // 不要修改样式名称，会影响缩放区域
    this.$interactionArea = this.$interactionContainer.append('rect')
      .attr('class', 'overlay')
      .attr('width', this.graphWidth)
      .attr('height', this.graphHeight)
      .style('fill', 'none')
      .style('stroke', '#444')
      .style('stroke-width', '1px');
  }

  /**
   * 创建顶部容器
   *
   * @private
   */
  _renderTopContainer() {
    this.$svg = this.$containerSelector.append('svg')
      .attr('class', 'line-graph')
      .attr('width', this.containerWidth)
      .attr('height', this.containerHeight);

    this.$topContainer = this.$svg.append('g')
      .classed('top-container', true)
      .attr('transform', 'scale(1, 1)');
    this.$titleContainer = this.$topContainer.append('g')
      .attr('transform', `translate(0, 0)`)
      .attr('class', 'title');
    this.$legendContainer = this.$topContainer.append('g')
      .attr('transform', `translate(0, 0)`)
      .attr('class', 'legend');
  }

  /**
   * 单独绘制 x 轴数组
   *
   * @private
   */
  _renderXAxis() {
  }

  /**
   * 隐藏超出范围的x刻度
   *
   * @private
   */
  _hideOverflowXTicks() {
    this.$xAxisContainer.selectAll('.tick')
      .attr('opacity', (d, i, nodes) => {
        const transform = nodes[i].getAttribute('transform');
        const translateX = +transform.substring('translate('.length, transform.indexOf(','));
        return translateX > 0 && translateX < this.graphWidth + 1 ? 1 : 0
      });
  }

  /**
   * 单独绘制 y 轴数组
   *
   * 在单侧绘制多条轴时，需要计算出之前轴所占的最大宽度，进行偏移轴及网格线
   *
   * @private
   */
  _renderYAxis() {
    const { yAxis, maxYAxisCount } = this.configuration;
    yAxis.slice(0, maxYAxisCount).forEach((axis, index) => {
      // 检测之前有几个同方向的Y坐标轴
      const positionCount = yAxis.filter((y, i) => y.position === axis.position && i < index).length;
      const container = axis.position === 'right' ? this.$rightYAxisContainer : this.$leftYAxisContainer;
      let translateWidth = (positionCount + 1) * this.axisPadding;
      container.selectAll('.y-axis').each(function () {
        let textWidthArray = d3.select(this).selectAll('text').nodes()
          .map((d, i, nodes) => i === nodes.length - 1 ? 0 : d.getBBox().width);
        translateWidth += Math.max(...textWidthArray);
      });
      const translate = `translate(${axis.position === 'left' ? '-' : '+'}${translateWidth}, 0)`;
      const g = container.append('g')
        .attr('data-id', index)
        .attr('class', `y-axis grid`)
        .attr('transform', translate)
        .call(this.yAxisMap.get(index).yAxis);

      g.select('path').remove();

      const lineTranslate = axis.position === 'left' ? translateWidth : -translateWidth;

      // 重绘网格线
      g.selectAll('.tick line').each((data, i, nodes) => {
        d3.select(nodes[i]).attr('transform', `translate(${lineTranslate}, 0)`);
      }).style('stroke', (d, i, nodes) => {
        // 隐藏超出overlay范围的刻度线
        const transform = nodes[i].parentNode.getAttribute('transform');
        const translateY = +transform.substring(transform.indexOf(',') + 1, transform.indexOf(')'));
        const isOverflow = axis.position === 'left' && translateY < axis.axisLabel.fontSize / 2 ||
          axis.position === 'left' && translateY > this.graphHeight ||
          axis.position === 'right' && translateY > axis.axisLabel.fontSize / 2 ||
          axis.position === 'right' && translateY < this.graphHeight;
        return isOverflow ? 'none' : 'lightgrey'
      })
        .style('stroke-dasharray', '3 3');
      g.selectAll('.tick text')
        .style('font-size', `${axis.axisLabel.fontSize + 'px'}`)
        .style('fill', 'black')
        .style('font-family', `${axis.axisLabel.fontFamily}`);
      if (positionCount === 0) {
        let yNameWidth = measureText(
          axis.axisLabel.text,
          g.node(),
          { fontSize: axis.axisLabel.fontSize + 'px' }
        ).width;
        g.append('text')
          .attr('y', -this.yAxisUnitHeight)
          .attr('x', axis.position === 'left' ? (yNameWidth + 10) : (-10 - yNameWidth))
          .attr('class', 'unit')
          .style('fill', 'black')
          .style('font-size', `${axis.axisLabel.fontSize + 'px'}`)
          .style('font-family', `${axis.axisLabel.fontFamily}`)
          .text(axis.axisLabel.text);
      } else {
        g.append('text')
          .attr('y', -this.yAxisUnitHeight)
          .attr('class', 'unit')
          .style('fill', 'black')
          .style('font-size', `${axis.axisLabel.fontSize + 'px'}`)
          .style('font-family', `${axis.axisLabel.fontFamily}`)
          .text(axis.axisLabel.text);
      }
    });
  }

  /**
   * 构建线条
   *
   * @private
   */
  _createLines() {
    this.configuration.series.map((series, index) => {
      const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
      if (!yScale) {
        return [];
      } else {
        if (series.itemStyle.style === 'bar') {
          this._createBarRect(series, index);
        } else {
          this._createLine(series, index);
        }
      }
    });
  }

  /**
   * 创建标线和标线文字
   * @private
   */
  _createMarkLines() {
    this.configuration.markLines.forEach((markLine, index) => {
      const g = this.$linesContainer.append('g')
        .attr('id', `markLine-${index}`)
        .attr('width', d3.max(markLine.lineStyle.width, 5));
      const line = g.append('line')
        .attr('fill', 'none')
        .attr('stroke', markLine.lineStyle.color)
        .attr('stroke-width', markLine.lineStyle.width)
        .attr('stroke-dasharray', markLine.lineStyle.dasharray);
      const [PADDING_Y, PADDING_X] = [3, 6];
      if (markLine.axis === 'x') {
        const x = this.scale(markLine.value) || 0;
        line
          .attr('x1', x)
          .attr('y1', 0)
          .attr('x2', x)
          .attr('y2', this.graphHeight);
        if (markLine.label.show) {
          // 标线边框
          const {width, height} = measureText(
            markLine.label.text,
            g.node(),
            { fontSize: markLine.label.fontSize + 'px' }
          );
          g.append('rect')
            .attr('fill', '#ddd')
            .attr('opacity', '0.5')
            .attr('rx', 4)
            .attr('ry', 4)
            .attr('x', x - width / 2 - PADDING_X + 2)
            .attr('y', -height - PADDING_Y * 2)
            .attr('width', width + PADDING_X * 2)
            .attr('height', height + PADDING_Y * 2);
          g.append('rect')
            .attr('fill', '#fff')
            .attr('stroke', '#ddd')
            .attr('rx', 4)
            .attr('ry', 4)
            .attr('x', x - width / 2 - PADDING_X)
            .attr('y', -height - PADDING_Y * 2 - 2)
            .attr('width', width + PADDING_X * 2)
            .attr('height', height + PADDING_Y * 2);
          // 标线文字
          g.append('text')
            .attr('fill', markLine.label.color)
            .attr('text-anchor', 'middle')
            .attr('x', x)
            .attr('y', -markLine.label.fontSize / 3 - PADDING_Y - 2)
            .attr('font-size', markLine.label.fontSize)
            .attr('font-family', markLine.label.fontFamily)
            .attr('font-weight', 'bold')
            .text(markLine.label.text);
        }
      } else {
        const y = this.yAxisMap.get(markLine.yAxisIndex).yScale(markLine.value);
        line
          .attr('x1', 0)
          .attr('y1', y)
          .attr('x2', this.graphWidth)
          .attr('y2', y);
        if (markLine.label.show) {
          // 标线边框
          const {width, height} = measureText(
            markLine.label.text,
            g.node(),
            { fontSize: markLine.label.fontSize + 'px' }
          );
          g.append('rect')
            .attr('fill', '#ddd')
            .attr('opacity', '0.5')
            .attr('rx', 4)
            .attr('ry', 4)
            .attr('x', 4)
            .attr('y', y - height - PADDING_Y * 2)
            .attr('width', width + PADDING_X * 2)
            .attr('height', height + PADDING_Y * 2);
          g.append('rect')
            .attr('fill', '#fff')
            .attr('stroke', '#ddd')
            .attr('rx', 4)
            .attr('ry', 4)
            .attr('x', 2)
            .attr('y', y - height - PADDING_Y * 2 - 2)
            .attr('width', width + PADDING_X * 2)
            .attr('height', height + PADDING_Y * 2);
          // 标线文字
          g.append('text')
            .attr('fill', markLine.label.color)
            .attr('text-anchor', 'middle')
            .attr('x', 2 + width / 2 + PADDING_X)
            .attr('y', y - markLine.label.fontSize / 3 - PADDING_Y - 2)
            .attr('font-size', markLine.label.fontSize)
            .attr('font-family', markLine.label.fontFamily)
            .attr('font-weight', 'bold')
            .text(markLine.label.text);
        }
      }
    });
  }

  /**
   * 创建柱状图矩形
   * @private
   */
  _createBarRect(series, index) {
    const yAxis = this.configuration.yAxis[series.yAxisIndex];
    if (!this.$containerSelector.select(`[data-id="barContainer-${index}"]`).empty()) {
      return;
    }
    const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
    if (!yScale) {
      return;
    }
    const { defines } = this.configuration;
    let bars = this.$linesContainer.append('g')
      .attr('data-id', `barContainer-${index}`)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('class', 'barContainer');
    bars.selectAll('.bar').data(series.data.filter(d => this.isXValueValid(d)))
      .enter().append('rect')
      .attr('data-id', `bar-${index}`)
      .attr('class', 'bar')
      .attr('fill', series.itemStyle.color)
      .attr('x', d => {
        let [xMin, xMax] = this.scale().domain();
        const ms = new Date(defines.x(d)).getTime();
        const x = this.scale(defines.x(d));
        // 解决测值时间与x轴一致时不显示柱形或显示错位的问题
        if (xMin.getTime() === ms) {
          return x;
        } else if (xMax.getTime() === ms) {
          return x - series.itemStyle.strokeWidth;
        }
        return x - series.itemStyle.strokeWidth / 2;
      })
      .attr('y', d => {
        // 柱状图应从0画起
        return yAxis.inverse
          ? d3.max([0, yScale(0)])
          : yScale(defines.y(d));
      })
      .attr('height', d => {
        // 柱状图高度应减掉<0的部分
        const y = yScale(defines.y(d));
        return d3.max([
          !yAxis.inverse
            ? this.graphHeight - y - d3.max([0, this.graphHeight - yScale(0)])
            : y - d3.max([0, yScale(0)]),
          0
        ]);
      })
      .attr('width', series.itemStyle.strokeWidth)
      .each((d) => {
        this._renderMarkCircles(bars, d, yScale, series.marker);
      });
  }

  /**
   * 创建单独线条
   *
   * @param series
   * @param index
   * @private
   */
  _createLine(series, index) {
    if (!this.$containerSelector.select(`[data-id="lineContainer-${index}"]`).empty()) {
      return;
    }
    const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
    if (!yScale) {
      return;
    }
    const line = this.$linesContainer.append('g')
      .attr('data-id', `lineContainer-${index}`)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('class', 'lineContainer');

    const _this = this;

    line.append('path')
      .datum(series.data)
      .attr('data-id', `line-${index}`)
      .classed('line', true)
      .attr('fill', 'none')
      .attr('stroke-width', series.itemStyle.strokeWidth)
      .attr('stroke', series.itemStyle.color)
      .attr('stroke-dasharray', series.itemStyle.dasharray)
      .attr('d', this._lineFunction(yScale, series))
      .on('dblclick', () => {
        d3.event.stopPropagation();
        this.dispatch.call('lineSelect', this, series, index);
      })
      .on('contextmenu', () => {
        this.$vue.openContextMenu(d3.event, {series, xScale: _this.xScale, ..._this.yAxisMap.get(series.yAxisIndex), lineIndex: index});
      });

    // 绘制标示点
    this._drawPoints(line, series, yScale);
    // 绘制测值标记（分布线不支持）
    if (this.configuration.defines.x) {
      this._drawValueMarker(line, series, yScale)
    }
  }

  /**
   * 绘制数据点与标示点
   *
   * @param container 容器
   * @param series 线条配置
   * @param yScale y轴比例尺
   * @private
   */
  _drawPoints(container, series, yScale) {
    const getCircleYPosition = (path, xPosition) => {
      let beginning = 0,
        end = path.node().getTotalLength(),
        target = null,
        pos;

      while (true) {
        target = Math.floor((beginning + end) / 2);
        pos = path.node().getPointAtLength(target);
        if ((target === end || target === beginning) && pos.x !== xPosition) {
          break;
        }
        if (pos.x > xPosition) {
          end = target;
        } else if (pos.x < xPosition) {
          beginning = target;
        } else {
          break;
        } //position found
      }

      return pos.y;
    };
    const markCircleConfiguration = series.marker;
    if (markCircleConfiguration.symbol === 'none') {
      return;
    }
    const symbol = d3.symbol().size(markCircleConfiguration.symbolSize);

    let filterData = [];
    const symbolNumber = markCircleConfiguration.symbolNumber;
    if (symbolNumber) {
      const extent = d3.extent(series.data.map(item => this.configuration.defines.x(item))).map(item => this.scale(item));
      let step = 1;
      if (symbolNumber !== 1) {
        step = (extent[1] - extent[0]) / (symbolNumber - 1);
      }
      for (let i = 0; i < symbolNumber; i++) {
        const stepValue = extent[0] + step * i;
        filterData.push({x: stepValue, y: getCircleYPosition(container.select('path.line'), stepValue)});
      }
    }

    series.data.forEach(d => {
      this._renderMarkCircles(container, d, yScale, markCircleConfiguration);
    });

    container.selectAll('dot')
      .data(filterData)
      .enter().append('path')
      .attr('class', 'point')
      .attr('d', symbol.type(d3[markCircleConfiguration.symbol])())
      .attr('transform', d => {
        return `translate(${d.x}, ${d.y})`;
      })
      .style('fill', series.itemStyle.color)
      .style('stroke', 'transparent')
      .exit().remove();
  }

  /**
   * 绘制测值标记
   *
   * @param container 容器
   * @param series 线条配置
   * @param yScale y轴比例尺
   * @private
   */
  _drawValueMarker(container, series, yScale) {
    if (!series.valueMarker.show) {
      return false;
    }
    const defines = this.configuration.defines;
    series.valueMarker.data.forEach((d) => {
      container.append('circle')
        .attr('r', series.valueMarker.r)
        .attr('transform', `translate(${this.scale(defines.x(d))}, ${yScale(defines.y(d))})`)
        .style('fill', defines.valueMarkerColor(d));
    });
  }

  /**
   * 绘制标示点
   *
   * @param container 线条容器
   * @param data 标示点数据(数组)
   * @param yScale y 轴比例尺
   * @private
   */
  _renderMarkCircles(container, data, yScale) {
    const { defines } = this.configuration;
    let enterPoints;

    if (Array.isArray(data) && data.length) {
      enterPoints = container.selectAll('g.pointContainer').data(data.filter(d => this.isXValueValid(d))).enter();
    } else {
      return;
    }

    const circleContainer = enterPoints.append('g')
      .attr('data-id', d => `id-${defines.x(d).valueOf()}`)
      .attr('class', 'pointContainer')
      .attr('transform', d => `translate(${this.scale(defines.x(d))},${yScale(defines.y(d)) - 20})`)
      .style('cursor', 'pointer');
    circleContainer.append('circle')
      .attr('r', 8)
      .style('fill', 'none')
      .style('stroke', 'black');
  }

  /**
   * 初始化 x 坐标
   *
   * @private
   */
  _initX() {
    throw new Error('Please implement _initX() method in subclass of BaseLine and Set xScale, xAxis and _xScaleType.');
  }

  /**
   * 设置或返回 x 轴比例尺类型
   *
   * @param scaleType
   * @returns {function(*=)}
   */
  xScaleType(scaleType) {
    if (scaleType) {
      this._xScaleType = scaleType;
    }
    return this._xScaleType;
  }

  /**
   *
   * 初始化 y 坐标
   *
   * @private
   */
  _initY() {
    const yScaleMap = this.configuration.defines.yScaleMap;
    this.configuration.yAxis.forEach((item, index) => {
      const domain = d3.extent([item.min, item.max]);
      const yScale = yScaleMap ? yScaleMap.get(index) : d3.scaleLinear().domain(item.inverse ? domain.reverse() : domain).range([this.graphHeight, 0]);
      const temp = item.position === 'left' ? d3.axisLeft(yScale) : d3.axisRight(yScale);
      // 缩放后重新给定最大最小值
      if (yScaleMap) {
        const extent = yScale.domain();
        item.max = extent[1];
        item.min = extent[0];
      }
      let step = (item.max - item.min) / item.axisTick.tickNumber;
      let tickValues = [];
      for (let i = 0; i <= item.axisTick.tickNumber; i++) {
        tickValues.push(item.min + step * i);
      }
      const yAxis = temp
        .tickSize(-this.graphWidth)
        .tickFormat(d => d.toFixed(item.axisTick.decimalNumber))
        .tickValues(tickValues);

      this.yAxisMap.set(index, {yScale, yAxis});
    });
  }

  /**
   * 设置当前比例尺 或 获取比例尺计算数据
   *
   * @returns {*}
   */
  scale() {
  }

  /**
   * 线段定义
   *
   * @param yScale
   * @param series
   * @private
   */
  _lineFunction(yScale, series) {
    const { defines } = this.configuration;
    return d3.line()
      .defined((d) => this.isXValueValid(d))
      .x((d) => this.scale(defines.x(d)))
      .y((d) => {
        let y = yScale(defines.y(d));
        // 解决线条在网格边界时显示不全的问题
        const halfStrokeWidth = series.itemStyle.strokeWidth / 2;
        if (y - halfStrokeWidth <= 0) {
          y += halfStrokeWidth;
        } else if (y + halfStrokeWidth >= this.graphHeight) {
          y -= halfStrokeWidth;
        }
        return y;
      })
      .curve(series.itemStyle.style === 'curves' ? d3.curveCatmullRom.alpha(0.5) : d3.curveLinear);
  }

  /**
   * 创建标题
   * @private
   */
  _renderTitle() {
    const { title } = this.configuration;
    if (!title.show) {
      return;
    }
    this.$titleContainer.selectAll('*').remove();
    this.$titleContainer.selectAll('rect').remove();

    const { width } = measureText(
      title.text,
      this.$titleContainer.node(),
      { fontSize: title.textStyle.fontSize + 'px' }
    );
    const x = (this.containerWidth - width) / 2;
    const y = title.position === 'top'
      ? title.textStyle.fontSize
      : this.containerHeight - 5;
    this.$titleContainer.append('text')
      .attr('x', x)
      .attr('y', y)
      .attr('font-size', title.textStyle.fontSize)
      .attr('font-family', title.textStyle.fontFamily)
      .attr('font-weight', 'bold')
      .text(title.text)
      .on('dblclick', () => {
        d3.event.stopPropagation();
        this.$vue.titleSelect({name: title.text, x: x - 15, y, width: width + 30});
      });
  }

  isDataValid(series) {
    return Array.isArray(series.data) && series.data.length > 0;
  }

  /**
   * 创建图例
   * @private
   */
  _renderLegend() {
    const { legend, series, title, defines } = this.configuration;
    if (!legend.show) {
      return;
    }
    // 无有效数据时不绘制图例
    const validColumns = series.filter((series) => this.isDataValid(series));
    if (validColumns.length === 0) {
      return;
    }

    this.$legendContainer.selectAll('*').remove();
    this.$legendContainer.selectAll('rect').remove();

    const entriesUpdate = this.$legendContainer.selectAll('g.legend-entry').data(series);
    this.$legendContainer
      .append('rect')
      .attr('width', () => {
        let sum = [];
        this.legendWidthLists.forEach((item) => {
          sum.push(_.sum(item));
        });
        // 为legend容器额外添加内边距
        return (_.max(sum) || 0) + this.legendLinePadding * 2;
      })
      .attr('height', this.legendHeight)
      .attr('x', (d, i, nodes) => {
        const rectWidth = +d3.select(nodes[i]).attr('width');
        return `${(this.containerWidth - rectWidth) / 2}`;
      })
      .attr('y', title.show && title.position === 'top' ? 30 : 0)
      .attr('rx', 6)
      .attr('class', 'legend-border')
      .style('fill', 'none')
      .style('stroke', 'black')
      .style('stroke-width', '0.4');
    const entriesEnter = entriesUpdate
      .enter()
      .append('g')
      .attr('transform', (d, i) => {
        const xOffset = +this.$legendContainer.select('rect').attr('x') + this.legendLinePadding;
        // 线条图例所在行
        let rowIndex = 0;
        // 线条图例所在列
        let colIndex = 0;
        // 当前遍历的图例个数
        let lineCount = 0;
        for (let j = 0; j < this.legendWidthLists.length; j++) {
          const list = this.legendWidthLists[j];
          for (let k = 0; k < list.length; k++) {
            lineCount++;
            if (lineCount === i + 1) {
              rowIndex = j;
              colIndex = k;
              break;
            }
          }
        }
        const width = _.sum(this.legendWidthLists[rowIndex].slice(0, colIndex));
        return `translate(
          ${xOffset + width + this.legendLinePadding},
          ${
            (title.show && title.position === 'top'
              ? (this.titleHeight + (this.legendLineHeight - 20) / 2)
              : 0) +
            -this.legendLineHeight / 2 +
            this.legendLineHeight * (rowIndex + 1)
          }
        )`;
      })
      .classed('legend-entry', true)
      // 线条无数据时将图例隐藏
      .style('display', d => this.isDataValid(d) ? '' : 'none')
      .merge(entriesUpdate);
    // 增大交互面积，g元素的事件只会作用于绘制的区域上
    entriesEnter
      .append('rect')
      .attrs({
        x: 0,
        y: -this.legendLineHeight / 2 + 2,
        width: this.legendLineWidth,
        height: 16, // 根据文字11px得出的实际高度
        fill: 'white'
      });

    entriesEnter.on('dblclick', (d, i) => {
      d3.event.stopPropagation();
      this.dispatch.call('lineSelect', this, d, i);
    });

    entriesEnter.on('contextmenu', (d, i) => {
      this.$vue.openContextMenu(d3.event, {series: d, xScale: this.xScale, ...this.yAxisMap.get(d.yAxisIndex), lineIndex: i});
    });

    entriesEnter.append('line')
      .classed('legend-line', true)
      .attr('data-id', (d, i) => `legend-${i}`)
      .attr('stroke-dasharray', (d) => d.itemStyle.dasharray)
      .attr('stroke-width', (d) => d.itemStyle.strokeWidth)
      .attr('stroke', (d) => d.itemStyle.color)
      .attr('fill', 'none')
      .attr('x2', this.legendLineWidth);

    const symbol = d3.symbol().size(50);
    entriesEnter
      .filter((d) => d.marker.symbol !== 'none')
      .append('path')
      .classed('symbol', true)
      .attr('d', (d) => symbol.type(d3[d.marker.symbol])())
      .attr('transform', 'translate(15, 0)')
      .style('fill', (d) => {
        if (d.marker.symbolNumber > 0) {
          return d.itemStyle.color;
        }
        return 'none';
      })
      .style('stroke', 'transparent');

    entriesEnter.append('text')
      .classed('legend-title', true)
      .attr('font-size', '11px')
      .attr('transform', 'translate(32, 2)')
      .attr('text-anchor', 'start')
      .attr('y', '0.15em')
      .style('user-select', 'none')
      .text(d => defines.legend ? defines.legend(d) : d.name);

    entriesUpdate.exit().remove();
  }

  /**
   * 渲染
   *
   * @param selector 可以是选择器，html元素，或者svg元素
   */
  render(selector) {
    if (
      selector instanceof HTMLElement ||
      selector instanceof SVGGraphicsElement ||
      (typeof selector === 'string' && document.querySelector(selector))
    ) {
      this.$containerSelector = d3.select(selector);
      // 计算可用的宽高
      const node = this.$containerSelector.node();
      this.containerWidth = elementWidth(node);
      this.containerHeight = elementHeight(node);
      this.renderGraph();
    } else {
      throw new Error('无法渲染在该元素上');
    }
  }

  /**
   * 渲染选择框
   * 结束回调会拿到新的 X 轴定义域和 Y 轴定义域
   *
   * @private
   */
  _renderSelectionBox() {
    const brush = d3.brush().extent([[0, 0], [this.graphWidth, this.graphHeight]]);
    let startMouse;
    let endMouse;
    brush
      .on('start', (d, i, nodes) => {
        startMouse = d3.mouse(nodes[i]);
      })
      .on('end', (d, i, nodes) => {
        const selection = d3.event.selection;
        endMouse = d3.mouse(nodes[i]);
        if (!selection) {
          // 框选时不触发click事件，这里可看作onclick事件处理
          const data = this.getVoronoiData(nodes[i]);
          if (data) {
            // 鼠标左击测值点事件回调
            // eslint-disable-next-line no-unused-vars
            const {xPosition, yPosition, yAxis, series, lineIndex, ...obj} = data;
            this.$vue.$emit('dataPointClick', obj);
          }
          return;
        }

        let newXDomain = [];
        if (this.xScaleType() === BaseLine.scaleType.LINEAR) {
          const min = this.scale().invert(selection[0][0]);
          const max = this.scale().invert(selection[1][0]);
          newXDomain = [min, max];
        } else if (this.xScaleType() === BaseLine.scaleType.ORDINAL) {
          const domain = this.scale().domain();
          const range = this.scale().range();
          const rangePoints = d3.range(range[0], range[1], this.scale().step());
          const leftIndex = d3.bisect(rangePoints, selection[0][0]);
          const rightIndex = d3.bisect(rangePoints, selection[1][0]);
          newXDomain = domain.slice(leftIndex, rightIndex);
        }

        const newYScaleMap = new Map();
        for (let item of this.yAxisMap.entries()) {
          const yScale = item[1].yScale;
          const yMax = yScale.invert(selection[0][1]);
          const yMin = yScale.invert(selection[1][1]);
          yScale.domain([yMin, yMax]);
          newYScaleMap.set(item[0], yScale);
        }
        if (startMouse && endMouse) {
          // 右下滑动，终止 x > 起止 x 且 终止 y > 起始 y，相对左上角原点
          if (endMouse[0] > startMouse[0] && endMouse[1] > startMouse[1]) {
            // 小于X轴最小区间时不放大
            if (!this.isMinDomain(newXDomain) && newXDomain[0] !== newXDomain[1]) {
              this.scale().domain(newXDomain);
              this.$vue.onSelectEnd(this.scale(), newYScaleMap, this.xScaleType());
            }
          } else if (startMouse[0] > endMouse[0] && startMouse[1] > endMouse[1]) {
            this.$vue.goBack();
          }
          brush.clear(this.$interactionContainer);
        }
      });
    this.$interactionContainer
      .call(brush)
      .selectAll('.handle').remove();
  }

  _dataHtml() {
  }

  /**
   * 获取交互区域的所有图幅数据
   */
  _getMergedSeriesData() {
    const { series, yAxis, defines } = this.configuration;
    return d3.merge(
      series.map((line, index) => {
        const yScale = this.yAxisMap.get(line.yAxisIndex).yScale;
        return line.data.filter(d => this.isXValueValid(d)).map(d => ({
          ...d,
          xPosition: this.scale(defines.x(d)),
          yPosition: yScale(defines.y(d)),
          yAxis: yAxis[line.yAxisIndex],
          series: line,
          lineIndex: index
        }));
      })
    );
  }

  /**
   * 渲染交互区域
   * 现有的交互操作包括：数据点的聚焦高亮、右键菜单、框选操作
   * 该操作区域主要作用于线条容器内的区域，如果需要添加该区域其它的操作，请优先在这里添加
   *
   * @private
   */
  _renderInteractionArea() {
    const data = this._getMergedSeriesData();

    // 数据点交互
    this.$voronoiDiagram = d3.voronoi()
      .x(d => d.xPosition)
      .y(d => d.yPosition)
      .size([this.graphWidth, this.graphHeight])(data);

    const { axisPointer, series } = this.configuration;
    const { type, pointStyle, lineStyle } = axisPointer;
    // 每一线条一聚焦点
    for (let i = 0; i < series.length; i++) {
      this.$interactionContainer.append('circle')
        .attr('class', 'focus')
        .attr('data-id', `focus-${i}`)
        .attr('r', pointStyle.r)
        .attr('fill', pointStyle.fill)
        .attr('stroke', pointStyle.stroke)
        .attr('stroke-width', pointStyle.strokeWidth)
        .style('display', 'none');
    }
    // 线型指示器
    const line = this.$interactionContainer.append('line')
      .attr('y1', 0)
      .attr('y2', this.graphHeight)
      .attr('fill', 'none')
      .attr('stroke', lineStyle.color)
      .attr('stroke-width', lineStyle.width)
      .attr('stroke-dasharray', lineStyle.dasharray)
      .style('display', 'none');

    // 显示聚焦点
    const showFocus = (d) => {
      this.$interactionContainer.select(`circle[data-id="focus-${d.lineIndex}"]`)
        .style('display', '')
        .attr('transform', `translate(${d.xPosition}, ${d.yPosition})`);
    };

    // 隐藏聚焦点
    const hideFocus = (lineIndex) => {
      if (Number.isInteger(lineIndex)) {
        this.$interactionContainer.select(`circle[data-id="focus-${lineIndex}"]`)
          .style('display', 'none');
      } else {
        this.$interactionContainer.selectAll('circle.focus')
          .style('display', 'none');
      }
    };

    const _this = this;

    // 鼠标在遮罩层移动时的回调
    const mouseMoveHandler = (d, i, nodes) => {
      hideFocus();
      const dData = this.getVoronoiData(nodes[i]);
      if (dData) {
        const { defines } = _this.configuration;
        const html = defines.tooltip
          ? defines.tooltip
          : _this._dataHtml;
        if (type === 'line') {
          line.style('display', '')
            .attr('x1', dData.xPosition)
            .attr('x2', dData.xPosition);
          const focusData = [];
          // 查询同一横坐标下的其他数据
          const sameXData = data.filter((item) =>
            item.xPosition === dData.xPosition);
          sameXData.forEach((dPoint) => {
            focusData.push(dPoint);
            showFocus(dPoint);
          });
          _this.$tip.html(`${html.call(_this, focusData, _this.configuration)}`).show();
        } else {
          // y值有效时才显示focus和tooltip
          if (this.isYValueValid(dData)) {
            showFocus(dData);
            _this.$tip.html(`${html.call(_this, dData, _this.configuration)}`).show();
          }
        }
      } else {
        _this.$tip.hide();
      }
    };

    this.$interactionArea
      .on('click', (d, i, nodes) => {
        const dData = this.getVoronoiData(nodes[i]);
        if (dData) {
          // 鼠标左击测值点事件回调
          // eslint-disable-next-line no-unused-vars
          const {xPosition, yPosition, yAxis, series, lineIndex, ...obj} = dData;
          this.$vue.$emit('dataPointClick', obj);
        }
      })
      .on('mousemove', mouseMoveHandler)
      .on('mouseleave', () => {
        if (type === 'line') {
          line.style('display', 'none');
        }
        hideFocus();
        _this.$tip.hide();
      })
      // 遮罩层鼠标右键回调
      .on('contextmenu', (d, i, nodes) => {
        const data = this.getVoronoiData(nodes[i]);
        if (data) {
          this.$vue.openContextMenu(d3.event, {series: data.series, xScale: _this.xScale, ..._this.yAxisMap.get(data.series.yAxisIndex), lineIndex: data.lineIndex});
        } else {
          this.$vue.openContextMenu(d3.event);
        }
      });

    if (this.$vue.scaleable) {
      // 缩放，只响应滚轮事件
      this.$interactionArea.call(this._zoom());
      // 框选操作
      this._renderSelectionBox();
    }
  }

  // 获取泰森多边形区域中离鼠标最近的数据点
  getVoronoiData(node) {
    // 获取当前鼠标位置
    const [mx, my] = d3.mouse(node);
    // 最大的查找半径
    const voronoiRadius = this.graphWidth;
    // 使用 diagram.find() 函数找到泰森多边形区域中离鼠标最近的数据点，并限制最大的查找半径
    const site = this.$voronoiDiagram.find(mx, my, voronoiRadius);
    return site && site.data;
  }

  /**
   * 是否为最大（缩放）区间
   */
  isMaxDomain() {
    return false;
  }

  /**
   * 是否为最小（缩放）区间
   */
  isMinDomain() {
    return false;
  }

  // 检查x坐标值是否合法
  isXValueValid(d) {
    const { x } = this.configuration.defines;
    return !x || (x(d) !== null && x(d) !== undefined);
  }

  // 检查y坐标值是否合法
  isYValueValid(d) {
    const { y } = this.configuration.defines;
    return !y || (y(d) !== null && y(d) !== undefined);
  }

  _zoom() {
    const updateChart = () => {
      let domain = this.scale().domain();
      // 达到最大区间后不再缩小
      if (d3.event.sourceEvent.wheelDelta < 0 && this.isMaxDomain(domain)) {
        return;
      }
      // 达到最小区间后不再放大
      if (d3.event.sourceEvent.wheelDelta > 0 && this.isMinDomain(domain)) {
        return;
      }
      // 新比例尺
      const newX = d3.event.transform.rescaleX(this.scale());

      const newYScaleMap = new Map(
        Array
          .from(this.yAxisMap)
          .map(([key, value]) => [key, d3.event.transform.rescaleY(value.yScale)])
      );
      this.$vue.onZoomed(newX, newYScaleMap);
    };

    return d3.zoom()
      .scaleExtent([0.5, 20])
      .translateExtent([[0, 0], [this.graphWidth, this.graphHeight]])
      .extent([[0, 0], [this.graphWidth, this.graphHeight]])
      .on('zoom', updateChart)
      .filter(() => d3.event.type === 'wheel');
  }
}

BaseLine.scaleType = {
  // 线性比例尺
  LINEAR: 'LINEAR',
  // 普通比例尺
  ORDINAL: 'ORDINAL'
};
