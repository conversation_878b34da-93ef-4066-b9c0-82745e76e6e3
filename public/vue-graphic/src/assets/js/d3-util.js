import * as d3 from 'd3';

/**
 * 保留小数，如果保留位数不合法则不保留
 *
 * @param x
 * @param d
 * @returns {string|*|string}
 */
export function toFixedDecimalNotZero(x, d) {
  if (!d || d < 0) return x;
  return x.toFixed(d).replace(/\.?0+$/, '');
}

export function makeEnum(values) {
  return values.reduce((obj, v) => {
    obj[v] = v;
    return obj;
  }, {});
}

export const randomArray = (array) => array[Math.floor(Math.random() * array.length)];

export const bindElements = (selection, elementName, className, data) => {
  const elementsUpdate = selection.selectAll(`.${className}`).data(data);
  const elements =
    elementsUpdate
      .enter()
      .append(elementName)
      .classed(className, true)
      .merge(elementsUpdate);
  elementsUpdate.exit().remove();
  return elements;
};

/**
 * Checks if the first ClientRect overlaps the second.
 *
 * @param {ClientRect} clientRectA The first ClientRect
 * @param {ClientRect} clientRectB The second ClientRect
 * @returns {boolean} If the ClientRects overlap each other.
 */
export function clientRectsOverlap(clientRectA, clientRectB) {
  if (Math.floor(clientRectA.right) <= Math.ceil(clientRectB.left)) {
    return false;
  }
  if (Math.ceil(clientRectA.left) >= Math.floor(clientRectB.right)) {
    return false;
  }
  if (Math.floor(clientRectA.bottom) <= Math.ceil(clientRectB.top)) {
    return false;
  }
  if (Math.ceil(clientRectA.top) >= Math.floor(clientRectB.bottom)) {
    return false;
  }
  return true;
}

/**
 * Applies the accessor, if provided, to each element of `array` and returns the maximum value.
 * If no maximum value can be computed, returns defaultValue.
 */
export function max(array, defaultValue) {
  const maxValue = d3.max(array);
  return maxValue !== undefined ? maxValue : defaultValue;
}

export const TimeInterval = makeEnum([
  'second',
  'minute',
  'hour',
  'day',
  'week',
  'tenDays',
  'quarter',
  'month',
  'year'
]);

export const Locale = d3.timeFormatLocale({
  'dateTime': '%x %A %X',
  'date': '%Y年%-m月%-d日',
  'time': '%H:%M:%S',
  'periods': ['上午', '下午'],
  'days': ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
  'shortDays': ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
  'months': ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
  'shortMonths': ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
});

export function timeIntervalToD3Time(timeInterval) {
  switch (timeInterval) {
    case TimeInterval.second:
      return d3.timeSecond;
    case TimeInterval.minute:
      return d3.timeMinute;
    case TimeInterval.hour:
      return d3.timeHour;
    case TimeInterval.day:
      return d3.timeDay;
    case TimeInterval.week:
      return d3.timeWeek;
    case TimeInterval.month:
      return d3.timeMonth;
    case TimeInterval.year:
      return d3.timeYear;
    case TimeInterval.tenDays:
      return d3.timeDay;
    case TimeInterval.quarter:
      return d3.timeMonth;
    default:
      throw Error('TimeInterval specified does not exist: ' + timeInterval);
  }
}

export const UUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

export function makeRandomData(numPoints, scaleFactor) {
  if (typeof scaleFactor === 'undefined') { scaleFactor = 1; }
  const data = [];
  for (let i = 0; i < numPoints; i++) {
    const x = Math.random();
    const r = { x: x, y: (x + x * Math.random()) * scaleFactor };
    data.push(r);
  }
  data.sort(function (a, b) {
    return a.x - b.x;
  });
  return data;
}

/**
 * 测量文字长度和宽度
 *
 * @param text  文字
 * @param container 文字所在的外部容器
 * @param styleObject 文字样式
 * @returns {*} {width: , height: }
 */
export function measureText(text, container, styleObject = { fontSize: '11px' }) {
  if (!text || text.trim() === '') {
    return {width: 0, height: 0};
  }

  const ruler = createRuler(container, styleObject);

  const linesDimensions = text.trim().split('\n').map((line) => ruler(line));
  return {
    height: linesDimensions.reduce((acc, dim) => acc + dim.height, 0),
    width: linesDimensions.reduce((acc, dim) => Math.max(acc, dim.width), 0)
  };
}

const createRuler = (container, styleObject) => {
  const {parentElement, containerElement, textElement} = getTextElements(container, styleObject);
  return (text) => {
    parentElement.appendChild(containerElement);
    textElement.textContent = text;
    const dimensions = getDimensions(textElement);
    parentElement.removeChild(containerElement); // element.remove() doesn't work in IE11
    return dimensions;
  };
};

function getTextElements(element, styleObject) {
  // if element is already a text element, return it
  if (element.tagName === 'text') {
    let parentElement = element.parentElement;
    if (parentElement == null) {
      parentElement = element.parentNode;
    }
    // must be removed from parent since we re-add it on every measurement
    parentElement.removeChild(element);

    return {
      containerElement: element,
      parentElement,
      textElement: element
    };
  }

  // otherwise create a new text element
  const created = create('text', styleObject);
  return {
    containerElement: created,
    parentElement: element,
    textElement: created
  };
}

/**
 * Returns the width/height of svg element's bounding box
 */
function getDimensions(element) {
  // using feature detection, safely return the bounding box dimensions of the
  // provided svg element
  if (element.getBBox) {
    try {
      const {width, height} = element.getBBox();
      // copy to prevent NoModificationAllowedError
      return {width, height};
    } catch (err) {
      // swallow any errors that occur (Firefox Linux)
    }
  }

  // if can't get valid bbox, return 0,0
  return {height: 0, width: 0};
}

/**
 * Creates and returns a new SVGElement with the attached style.
 */
function create(tagName, styleObject) {
  const element = document.createElementNS('http://www.w3.org/2000/svg', tagName);
  Object.keys(styleObject).forEach(key => {
    element.style[key] = styleObject[key];
  })
  return element;
}

function _parseStyleValue(style, property) {
  let value = style.getPropertyValue(property);
  let parsedValue = parseFloat(value);
  return parsedValue || 0;
}

/**
 * Calculates the width of the element.
 * The width includes the padding and the border on the element's left and right sides.
 *
 * @param {Element} elementOrSelection The element to query
 * @returns {number} The width of the element.
 */
export function elementWidth(elementOrSelection) {
  const element = elementOrSelection instanceof d3.selection
    ? elementOrSelection.node()
    : elementOrSelection;
  const style = window.getComputedStyle(element);
  return _parseStyleValue(style, "width")
    + _parseStyleValue(style, "padding-left")
    + _parseStyleValue(style, "padding-right")
    + _parseStyleValue(style, "border-left-width")
    + _parseStyleValue(style, "border-right-width");
}

/**
 * Calculates the height of the element.
 * The height includes the padding the and the border on the element's top and bottom sides.
 *
 * @param {Element} elementOrSelection The element to query
 * @returns {number} The height of the element
 */
export function elementHeight(elementOrSelection) {
  const element = elementOrSelection instanceof d3.selection
    ? elementOrSelection.node()
    : elementOrSelection;
  const style = window.getComputedStyle(element);
  return _parseStyleValue(style, "height")
    + _parseStyleValue(style, "padding-top")
    + _parseStyleValue(style, "padding-bottom")
    + _parseStyleValue(style, "border-top-width")
    + _parseStyleValue(style, "border-bottom-width");
}

const xmlns = 'http://www.w3.org/2000/xmlns/';
const xlinkns = 'http://www.w3.org/1999/xlink';
const svgns = 'http://www.w3.org/2000/svg';

export function serialize(svg) {
  svg = svg.cloneNode(true);
  svg.setAttributeNS(xmlns, 'xmlns', svgns);
  svg.setAttributeNS(xmlns, 'xmlns:xlink', xlinkns);
  const serializer = new XMLSerializer();
  const string = serializer.serializeToString(svg);
  return new Blob([string], {type: 'image/svg+xml'});
}
