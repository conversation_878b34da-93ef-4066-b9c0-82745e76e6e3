export const COLORS = [
  '#ff0000',
  '#0000ff',
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
];

export const getLangs = function (isAll = true) {
  const otherLangs = [
    {value: 'en', name: '英语'},
    {value: 'es', name: '西班牙语'},
    {value: 'fr', name: '法语'},
    {value: 'pt', name: '葡萄牙语'},
    {value: 'lo', name: '老挝语'}
  ];
  return isAll
    ? [{value: 'zh', name: '中文'}, ...otherLangs]
    : otherLangs;
}

export const getFontFamilies = function () {
  return [
    {name: this.t('graphic.graphStyle.microsoftYaHei'), value: '"Microsoft YaHei"'},
    {name: this.t('graphic.graphStyle.songti'), value: 'Sim<PERSON>un, "Songti SC"'},
    {name: this.t('graphic.graphStyle.heiti'), value: 'SimHei, "Heiti SC"'},
    {name: this.t('graphic.graphStyle.fangsong'), value: 'FangSong'},
    {name: this.t('graphic.graphStyle.kaiti'), value: 'KaiTi, "KaiTi SC"'},
    {name: this.t('graphic.graphStyle.newSongti'), value: 'NSimSun'},
    {name: this.t('graphic.graphStyle.microsoftJhenghei'), value: '"Microsoft JhengHei"'},
    {name: 'Arial', value: 'Arial'},
    {name: 'Times New Roman', value: '"Times New Roman"'}
  ];
};

export const getYAxis = function () {
  return {
    left: this.t('graphic.graphStyle.left') + ' ' + this.t('graphic.graphStyle.axis'),
    right: this.t('graphic.graphStyle.right') + ' ' + this.t('graphic.graphStyle.axis'),
    'left-out': this.t('graphic.graphStyle.left') + ' ' + this.t('graphic.graphStyle.out') + ' ' + this.t('graphic.graphStyle.axis'),
    'right-out': this.t('graphic.graphStyle.right') + ' ' + this.t('graphic.graphStyle.out') + ' ' + this.t('graphic.graphStyle.axis')
  };
};

// 采集类型
export const getWriteTypes = function () {
  return [
    this.t('graphic.whole.artificial'),
    this.t('graphic.graphWorkArea.writeTypeOption2')
  ];
};

// 审核状态
export const getAuditStatus = function () {
  return [
    this.t('graphic.graphWorkArea.auditStatusOption1'),
    this.t('graphic.graphWorkArea.auditStatusOption2'),
    this.t('graphic.graphWorkArea.auditStatusOption3')
  ];
};

// 测值状态
export const getValueStatus = function () {
  return {
    1: this.t('graphic.graphWorkArea.valueStatusOption2'),
    2: this.t('graphic.graphWorkArea.valueStatusOption3'),
    3: this.t('graphic.graphWorkArea.valueStatusOption4')
  };
};

// 测值类型
export const getValueTypes = function () {
  return [
    this.t('graphic.graphWorkArea.valueTypeOption1'),
    this.t('graphic.graphWorkArea.valueTypeOption2'),
    this.t('graphic.graphWorkArea.valueTypeOption3'),
    this.t('graphic.graphWorkArea.valueTypeOption4')
  ];
};

// 所有筛选取值
export const getValueFilter = function () {
  return {
    writeTypes: getWriteTypes.call(this),
    auditStatus: getAuditStatus.call(this),
    valueStatus: getValueStatus.call(this),
    valueTypes: getValueTypes.call(this)
  }
};
