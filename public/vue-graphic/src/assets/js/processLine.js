import * as d3 from 'd3';
import 'd3-selection-multi';
import dayjs from 'dayjs';
import {
  clientRectsOverlap,
  Locale,
  max,
  measureText,
  TimeInterval,
  timeIntervalToD3Time, toFixedDecimalNotZero
} from './d3-util';
import BaseLine from './baseLine';

const timeParse = (value) => value instanceof Date ? value : dayjs(value, 'YYYY-MM-DD HH:mm:ss').toDate();

// 最大区间定义域
const MAX_X_DOMAIN = [new Date('1900-01-01 00:00:00'), new Date('2099-12-31 23:59:59')];

const LONG_DATE = new Date(9999, 8, 29, 12, 59, 9999)

export default class ProcessLine extends BaseLine {
  constructor(configuration, vue) {
    super(configuration, vue);
    this.tickLabelPadding = 5; // 标记文字的边距
    this.endTickLength = 20; // x时间轴最后一个刻度的高度
    this.innerTickLength = 20; // x时间轴内部刻度的高度
    this.possibleTimeAxisConfigurations = ProcessLine._DEFAULT_TIME_AXIS_CONFIGURATIONS.call(this); // 当前时间轴配置
    this.numTiers = max(this.possibleTimeAxisConfigurations.map((config) => config.length), 0);
    this.tierHeights = [15 + this.tickLabelPadding, 15 + this.tickLabelPadding]; // x 时间轴每层的高度
    this.graphBlankBottom = 40; // 图幅下方留白

    this.$tierLabelContainers = [];
    this.$tierMarkContainers = [];
    this.$tierBaselines = [];
    this.$tierGridContainers = [];
    this.$yAxisTickContainer = [];
  }

  /**
   * 单独绘制 x 轴数组
   *
   * @private
   */
  _renderXAxis() {
    this._createTimeAxis();

    this._renderTimeAxis();
  }

  /**
   * 初始化 x 坐标
   *
   * @private
   */
  _initX() {
    this.xScaleType(BaseLine.scaleType.LINEAR);
    const { xScale } = this.configuration.defines;
    const { max, min } =  this.configuration.xAxis;
    let xDomain = xScale
      ? xScale.domain()
      : d3.extent(
        this.configuration.series.reduce(
          (arr, series) => arr.concat(series.data.map(this.configuration.defines.x)),
          [max, min]
        )
      ).map(timeParse);
    // 时间区间不得超过['1900-01-01', '2099-12-31']
    if (xDomain[0] < MAX_X_DOMAIN[0]) {
      xDomain[0] = MAX_X_DOMAIN[0];
    }
    if (xDomain[1] > MAX_X_DOMAIN[1]) {
      xDomain[1] = MAX_X_DOMAIN[1];
    }
    this.xScale = d3.scaleTime().domain(xDomain).range([0, this.graphWidth]);
  }

  /**
   * 渲染时间轴
   *
   * @private
   */
  _createTimeAxis() {
    this.$xAxisContainer.selectAll('.time-axis-tier').remove();

    for (let i = 0; i < this.numTiers; ++i) {
      const $tierContainer = this.$xAxisContainer.append('g').classed('time-axis-tier', true);
      this.$tierMarkContainers.push(
        $tierContainer.append('g').classed('tick-mark-container', true)
      );
      this.$tierGridContainers.push(
        this.$timeAxisGridContainer.append('g').classed('grid', true)
      );
      this.$tierLabelContainers.push(
        $tierContainer.append('g').classed('tick-label-container', true)
      );
      this.$tierBaselines.push(
        $tierContainer.append('line').classed('baseline', true)
      );
      this.$yAxisTickContainer.push(
        $tierContainer.append('g').classed('y-axis-tick-container', true)
      );
      if (i + 1 === this.numTiers) {
        this.$tierBaselines.push(
          $tierContainer.append('line').classed('baseline', true)
        );
      }
    }
  }

  _cleanTiers() {
    for (let index = 0; index < this.$tierLabelContainers.length; index++) {
      this.$tierGridContainers[index].selectAll('line').remove();
      this.$tierMarkContainers[index].selectAll('.tick-mark').remove();
      this.$tierLabelContainers[index].selectAll('.tick-label').remove();
      this.$tierBaselines[index].style('fill', 'none');
      this.$tierBaselines[index + 1].style('fill', 'none');
      this.$yAxisTickContainer[index].selectAll('.tick-text').remove();
    }
  }

  _generateBaselineAttrHash() {
    const baselineAttrHash = {
      'x1': 0,
      'y1': 0,
      'x2': 0,
      'y2': 0
    };

    baselineAttrHash['x2'] = this.graphWidth;

    return baselineAttrHash;
  }

  _getIntervalLength(config) {
    const startDate = this.scale().domain()[0];
    const d3Interval = timeIntervalToD3Time(config.interval);
    const endDate = d3Interval.offset(startDate, config.step);
    if (endDate > this.scale().domain()[1]) {
      // 偏移过大，使用画布的宽度
      return this.graphWidth;
    }
    // 测量一个日期需要多少空间
    return Math.abs(this.scale(endDate) - this.scale(startDate));
  }

  /**
   *
   * 渲染时间轴
   *
   * @private
   */
  _renderTimeAxis() {
    this._mostPreciseConfigIndex = this._getMostPreciseConfigurationIndex();
    const tierConfigs = this.possibleTimeAxisConfigurations[this._mostPreciseConfigIndex];

    this._cleanTiers(tierConfigs);

    tierConfigs.forEach((config, i) =>
      this._renderTierLabels(this.$tierLabelContainers[i], config, i)
    );
    const tierTicks = tierConfigs.map((config) =>
      this._getTickValuesForConfiguration(config)
    );

    let baselineOffset = 0;
    for (let i = 0; i < Math.max(tierConfigs.length + 1, 1); ++i) {
      const attr = this._generateBaselineAttrHash();
      attr['y1'] += baselineOffset;
      attr['y2'] = attr['y1'];
      this.$tierBaselines[i]
        .attrs(attr)
        .style('fill', 'inherit')
        .style('stroke', '#444')
        .style('stroke-width', '1px');
      baselineOffset += this.tierHeights[i];
    }

    for (let i = 0; i < tierConfigs.length; ++i) {
      this._renderTimeAxisGrid(tierTicks, i, i === tierConfigs.length - 1);
      this._renderTickMarks(tierTicks[i], i);
      this._renderYAxisTick(tierConfigs[i], i);
      this._hideOverlappingAndCutOffLabels(i);
    }
  }

  /**
   *
   * 隐藏超出边界的或超过刻度空间的内容
   *
   * @param index 级别索引
   * @private
   */
  _hideOverlappingAndCutOffLabels(index) {
    const boundingBox = this.$xAxisContainer.node().getBoundingClientRect();

    const isInsideBBox = (tickBox) => {
      return (
        Math.floor(boundingBox.left) <= Math.ceil(tickBox.left) &&
        Math.floor(boundingBox.top) <= Math.ceil(tickBox.top) &&
        Math.floor(tickBox.right) <= Math.ceil(boundingBox.left + this.graphWidth + this.left) &&
        Math.floor(tickBox.bottom) <= Math.ceil(boundingBox.top + 50)
      );
    };

    const visibleTickMarks = this.$tierMarkContainers[index]
      .selectAll('.tick-mark')
      .filter(function () {
        const fill = d3.select(this).style('fill');
        return fill !== 'none';
      });

    const visibleTickMarkRects = visibleTickMarks.nodes().map((mark) => mark.getBoundingClientRect());

    const visibleTickLabels = this.$tierLabelContainers[index]
      .selectAll('.tick-label')
      .filter(function () {
        const fill = d3.select(this).style('fill');
        return fill !== 'none';
      });
    let lastLabelClientRect;

    visibleTickLabels.each(function (d, i) {
      const clientRect = this.getBoundingClientRect();
      const tickLabel = d3.select(this);
      const leadingTickMark = visibleTickMarkRects[i];
      const trailingTickMark = visibleTickMarkRects[i + 1];

      const isOverlappingLastLabel = (lastLabelClientRect != null && clientRectsOverlap(clientRect, lastLabelClientRect));
      const isOverlappingLeadingTickMark = (leadingTickMark != null && clientRectsOverlap(clientRect, leadingTickMark));
      const isOverlappingTrailingTickMark = (trailingTickMark != null && clientRectsOverlap(clientRect, trailingTickMark));

      if (!isInsideBBox(clientRect) || isOverlappingLastLabel || isOverlappingLeadingTickMark || isOverlappingTrailingTickMark) {
        tickLabel.style('fill', 'none');
      } else {
        lastLabelClientRect = clientRect;
        tickLabel.classed('label-inside', true).style('fill', '#32313F');
      }
    });
  }

  /**
   * 渲染时间轴网格线
   *
   * @param tickValuesArray
   * @param index
   * @param isLast
   * @private
   */
  _renderTimeAxisGrid(tickValuesArray, index, isLast) {
    const tickValues = isLast ? tickValuesArray[index] : tickValuesArray[index]
      .filter(item => !tickValuesArray[index + 1].find(nextValues => nextValues.valueOf() === item.valueOf()));

    const gridLineUpdate = this.$tierGridContainers[index].selectAll('line')
      .filter((d, index, nodes) => index !== 0 && index !== nodes.length - 1)
      .data(tickValues);
    const gridLines = gridLineUpdate
      .enter()
      .append('line')
      .merge(gridLineUpdate)
      .filter((d, i, nodes) => {
        return index === 0 || (i !== 0 && i !== nodes.length - 1);
      })
      .style('stroke', 'lightgrey')
      .style('stroke-dasharray', isLast ? '0' : '3 3')
      .style('pointer-events', 'none');
    const attr = this._generateTickMarkAttrHash();
    attr['y1'] = -this.graphHeight;
    attr['y2'] = 0;
    gridLines.attrs(attr);
    gridLineUpdate.exit().remove();
  }

  /**
   *
   * 渲染刻度标记
   *
   * @param tickValues 刻度数据
   * @param index 级别索引
   * @private
   */
  _renderTickMarks(tickValues, index) {
    const tickMarksUpdate = this.$tierMarkContainers[index].selectAll('.tick-mark').data(tickValues);
    const tickMarks =
      tickMarksUpdate
        .enter()
        .append('line')
        .classed('tick-mark', true)
        .style('stroke', '#444')
        .style('stroke-width', '1px')
        .merge(tickMarksUpdate);

    const attr = this._generateTickMarkAttrHash();
    const offset = this.tierHeights.slice(0, index).reduce((translate, height) => translate + height, 0);
    attr['y1'] = offset;
    attr['y2'] = offset + this.innerTickLength;

    tickMarks.attrs(attr);
    attr['y1'] = offset;
    attr['y2'] = offset + this.endTickLength;
    d3.select(tickMarks.nodes()[0]).attrs(attr);
    d3.select(tickMarks.nodes()[tickMarks.size() - 1]).attrs(attr);

    // 给最后一个和第一个添加特殊的样式以便可以定制
    d3.select(tickMarks.nodes()[0]).classed('end-tick-mark', true);
    d3.select(tickMarks.nodes()[tickMarks.size() - 1]).classed('end-tick-mark', true);

    tickMarksUpdate.exit().remove();
  }

  /**
   * 渲染 y 轴标记
   *
   * @param config
   * @param index
   * @private
   */
  _renderYAxisTick(config, index) {
    const tickName = ProcessLine._TIME_INTERVAL_AXIS_NAME.call(this)[config.interval];
    const tickUpdate = this.$yAxisTickContainer[index].selectAll('.tick-text').data(tickName);
    const yTranslate = d3.sum(this.tierHeights.slice(0, index + 1));
    const fontSize = 12;
    tickUpdate.enter()
      .append('text')
      .classed('tick-text', true)
      .text(d => d)
      .attr('transform', d => `translate(-${measureText(
        d,
        this.graph.node(),
        { fontSize: fontSize + 'px' }
      ).width + this.axisPadding}, ${yTranslate})`)
      .style('fill', '#32313F')
      .style('font-size', `${fontSize}px`)
      .style('font-weight', '200')
      .style('line-height', 'normal');
  }

  /**
   *
   * 用于构建标尺刻度的坐标对象
   *
   * @returns {{x1: function, y1: number, x2: function, y2: number}}
   * @private
   */
  _generateTickMarkAttrHash() {
    const scalingFunction = (d) => this.scale(d);
    return {
      'x1': scalingFunction,
      'y1': 0,
      'x2': scalingFunction,
      'y2': this.innerTickLength
    };
  }

  /**
   *
   * 返回由指定间隔分隔的刻度值数组。
   *
   * @param interval 间隔
   * @param step 步长
   * @returns {*|Array}
   */
  tickInterval(interval, step = 1) {
    // 暂时从我们的线性比例尺创建一个新的时间比例尺，以便我们可以访问它的api
    const tempScale = d3.scaleTime();
    const d3Interval = timeIntervalToD3Time(interval).every(step);
    tempScale.domain(this.scale().domain());
    tempScale.range(this.scale().range());
    return tempScale.ticks(d3Interval);
  }

  /**
   *
   * 根据配置获取时间轴上的数据
   *
   * @param config
   * @returns {*}
   * @private
   */
  _getTickValuesForConfiguration(config) {
    let tickPos = this.tickInterval(config.interval, config.step);
    // 时间轴为旬时不画下旬的线
    if (config.interval === 'tenDays') {
      tickPos = tickPos.filter((data) => data.getDate() !== 31);
    }
    const domain = this.scale().domain();
    const tickPosValues = tickPos.map((d) => d.valueOf());
    if (tickPosValues.indexOf(domain[0].valueOf()) === -1) {
      tickPos.unshift(domain[0]);
    }
    if (tickPosValues.indexOf(domain[1].valueOf()) === -1) {
      tickPos.push(domain[1]);
    }
    return tickPos;
  }

  /**
   *
   * 渲染每一级标签
   *
   * @param container 容器
   * @param config 配置
   * @param index 级别索引
   * @private
   */
  _renderTierLabels(container, config, index) {
    const tickPos = this._getTickValuesForConfiguration(config);
    let labelPos = [];
    tickPos.map((datum, i) => {
      if (i + 1 >= tickPos.length) {
        return;
      }
      labelPos.push(new Date((tickPos[i + 1].valueOf() - tickPos[i].valueOf()) / 2 + tickPos[i].valueOf()));
    });
    const tickLabelsUpdate = container.selectAll('.tick-label').data(labelPos, (d) => String(d.valueOf()));
    const tickLabelsEnter =
      tickLabelsUpdate
        .enter()
        .append('g')
        .classed('tick-label', true);

    tickLabelsEnter
      .append('text')
      .style('fill', 'inherit')
      .style('font-size', '11px')
      .style('font-weight', '200')
      .style('line-height', 'normal');
    let yTranslate;
    yTranslate = d3.sum(this.tierHeights.slice(0, index + 1)) - this.tickLabelPadding;

    const tickLabels = tickLabelsUpdate.merge(tickLabelsEnter);

    const textSelection = tickLabels.selectAll('text');
    if (textSelection.size() > 0) {
      textSelection.attr('transform', `translate(0, ${yTranslate})`);
    }
    tickLabelsUpdate.exit().remove();
    tickLabels.attr('transform', (d) => 'translate(' + this.scale(d) + ',0)');
    tickLabels.selectAll('text').text(config.formatter)
      .style('user-select', 'none')
      .style('text-anchor', 'middle');
  }

  /**
   * 设置当前比例尺 或 获取比例尺计算数据
   *
   * @param values
   * @returns {*}
   */
  scale(values) {
    const scale = this.xScale;
    if (values) {
      return scale(timeParse(values));
    } else {
      return scale;
    }
  }

  /**
   * 获取最适合当前宽度的坐标轴配置
   *
   * @private
   */
  _getMostPreciseConfigurationIndex() {
    let mostPreciseIndex = this.possibleTimeAxisConfigurations.length;
    this.possibleTimeAxisConfigurations.forEach((interval, index) => {
      if (index < mostPreciseIndex && interval.every((tier) => this._checkTimeAxisTierConfiguration(tier))) {
        mostPreciseIndex = index;
      }
    });

    if (mostPreciseIndex === this.possibleTimeAxisConfigurations.length) {
      --mostPreciseIndex;
    }

    return mostPreciseIndex;
  }

  /**
   * 间隔最大宽度
   *
   * @param config
   * @returns {*}
   * @private
   */
  _maxWidthForInterval(config) {
    return measureText(
      config.formatter(LONG_DATE),
      this.$tierLabelContainers[0].node()
    ).width;
  }

  /**
   * 检查层配置是否适合当前宽度并满足最大时间精度限制
   *
   * @param config
   * @returns {boolean}
   * @private
   */
  _checkTimeAxisTierConfiguration(config) {
    const worstWidth = this._maxWidthForInterval(config) + 2 * this.tickLabelPadding;
    return Math.min(this._getIntervalLength(config), this.graphWidth) >= worstWidth;
  }

  _dataHtml(data) {
    const { axisPointer, defines } = this.configuration;
    if (axisPointer.type === 'line') {
      const x = timeParse(defines.x(data[0]));
      const title = Locale.format('%Y-%m-%d %H:%M:%S')(x);
      const { width } = measureText(
        title,
        this.$svg.node(),
        { fontSize: '16px', fontWeight: 700, boxSizing: 'content-box' }
      );
      return `<div class="tooltip" style="width:${Math.max(width, 180)}px;">
        <h5>${title}</h5>
        ${data.map((d) => {
          const y = defines.y(d);
          return `<p>
              ${d.series.name}:
              <span class="font-weight-bold">${toFixedDecimalNotZero(y)}</span>
            </p>`
        }).join('')}
      </div>`;
    } else {
      const x = timeParse(defines.x(data));
      const y = defines.y(data);
      const title = data.series.name;
      const { width } = measureText(
        title,
        this.$svg.node(),
        { fontSize: '16px', fontWeight: 700, boxSizing: 'content-box' }
      );
      return `<div class="tooltip" style="width:${Math.max(width, 180)}px;">
        <h5>${title}</h5>
        <p>${this.$vue.t('graphic.whole.time')}: <span class="font-weight-bold">${Locale.format('%Y-%m-%d %H:%M:%S')(x)}</span></p>
        <p>${this.$vue.t('graphic.whole.measurementValue')}: <span class="font-weight-bold">${toFixedDecimalNotZero(y)}</span></p>
      </div>`;
    }
  }

  // 是否为最大（缩放）区间
  isMaxDomain(domain) {
    return domain[0].getTime() === MAX_X_DOMAIN[0].getTime() &&
      domain[1].getTime() === MAX_X_DOMAIN[1].getTime();
  }

  // 是否为最小（缩放）区间
  isMinDomain(domain) {
    const milliseconds = ProcessLine._TIME_INTERVAL_MILLISECOND[this.configuration.timeMinInterval];
    return domain[1].getTime() - domain[0].getTime() <= milliseconds;
  }
}

ProcessLine._TIME_INTERVAL_MILLISECOND = {
  [TimeInterval.minute]: 60 * 1000,
  [TimeInterval.hour]: 60 * 60 * 1000,
  [TimeInterval.day]: 24 * 60 * 60 * 1000,
  [TimeInterval.week]: 7 * 24 * 60 * 60 * 1000,
  [TimeInterval.tenDays]: 10 * 24 * 60 * 60 * 1000,
  [TimeInterval.month]: 30 * 24 * 60 * 60 * 1000,
  [TimeInterval.quarter]: 3 * 30 * 24 * 60 * 60 * 1000,
  [TimeInterval.year]: 365 * 24 * 60 * 60 * 1000
}

ProcessLine._SORTED_TIME_INTERVAL_INDEX = {
  [TimeInterval.minute]: 0,
  [TimeInterval.hour]: 1,
  [TimeInterval.day]: 2,
  [TimeInterval.week]: 3,
  [TimeInterval.month]: 4,
  [TimeInterval.year]: 5
};

ProcessLine._TIME_INTERVAL_AXIS_NAME = function () {
  return {
    [TimeInterval.minute]: [this.$vue.t('graphic.time.minute')],
    [TimeInterval.hour]: [this.$vue.t('graphic.time.hour')],
    [TimeInterval.day]: [this.$vue.t('graphic.time.day')],
    [TimeInterval.week]: [this.$vue.t('graphic.time.week')],
    [TimeInterval.tenDays]: [this.$vue.t('graphic.time.tenDays')],
    [TimeInterval.quarter]: [this.$vue.t('graphic.time.season')],
    [TimeInterval.month]: [this.$vue.t('graphic.whole.month')],
    [TimeInterval.year]: [this.$vue.t('graphic.whole.year')]
  };
};

ProcessLine._DEFAULT_TIME_AXIS_CONFIGURATIONS = function () {
  const formatter = (specifier) => Locale.format(specifier);
  const quarterFormatter = (date) => {
    return `${Math.floor((date.getMonth()) / 3) + 1}`;
  };
  const tenDaysFormatter = (date) => {
    const dayOfMonth = date.getDate();
    if (dayOfMonth <= 10) {
      return this.$vue.t('graphic.time.firstTenDays');
    } else if (dayOfMonth <= 20) {
      return this.$vue.t('graphic.time.midTenDays');
    } else {
      return this.$vue.t('graphic.time.lastTenDays');
    }
  };
  return [
    [
      {interval: TimeInterval.minute, step: 1, formatter: formatter('%H:%M')}
    ],
    [
      {interval: TimeInterval.hour, step: 1, formatter: formatter('%H')}
    ],
    [
      {interval: TimeInterval.hour, step: 1, formatter: formatter('%H')},
      {interval: TimeInterval.day, step: 1, formatter: formatter('%e')}
    ],
    [
      {interval: TimeInterval.day, step: 1, formatter: formatter('%e')},
      {interval: TimeInterval.month, step: 1, formatter: formatter('%Y-%m')}
    ],
    [
      {interval: TimeInterval.tenDays, step: 10, formatter: tenDaysFormatter},
      {interval: TimeInterval.month, step: 1, formatter: formatter('%Y-%m')}
    ],
    [
      {interval: TimeInterval.month, step: 1, formatter: formatter('%m')},
      {interval: TimeInterval.year, step: 1, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.month, step: 1, formatter: formatter('%m')},
      {interval: TimeInterval.year, step: 1, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.quarter, step: 3, formatter: quarterFormatter}, // 季度
      {interval: TimeInterval.year, step: 1, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.year, step: 1, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.year, step: 1, formatter: formatter('%y')}
    ],
    [
      {interval: TimeInterval.year, step: 5, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.year, step: 25, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.year, step: 50, formatter: formatter('%Y')}
    ],
    [
      {interval: TimeInterval.year, step: 100, formatter: formatter('%Y')}
    ]
  ];
};
