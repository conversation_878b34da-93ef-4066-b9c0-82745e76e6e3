import * as d3 from 'd3';
import 'd3-selection-multi';
import BaseLine from './baseLine';
import {measureText, toFixedDecimalNotZero} from './d3-util';

export default class DistributionLine extends BaseLine {
  /**
   * 单独绘制 x 轴数组
   *
   * @private
   */
  _renderXAxis() {
    this.$xAxisContainer.call(this.xAxis);
    this.$xAxisContainer.select('path.domain')
      .attr('stroke', 'transparent');
    this.$xAxisContainer.selectAll('.tick line')
      .each((data, i, nodes) => {
        d3.select(nodes[i]).attr('transform', `translate(0, -${this.graphHeight})`);
      })
      .attr('y2', this.graphHeight)
      .style('stroke', (d, i, nodes) => {
        // 隐藏超出overlay范围的刻度线
        const transform = nodes[i].parentNode.getAttribute('transform');
        const translateX = +transform.substring('translate('.length, transform.indexOf(','));
        const isOverflow = translateX < 1 || translateX > this.graphWidth;
        return isOverflow ? 'none' :'lightgrey';
      })
      .style('stroke-opacity', 0.6)
      .style('stroke-dasharray', '3 3');
    this.$xAxisContainer.selectAll('.tick text')
      .style('font-size', '12px')
      .style('fill', 'black')
      .style('font-family', 'SimSun, "Songti SC"');
  }

  /**
   * 创建柱状图矩形
   * @private
   */
  _createBarRect(series, index) {
    const yAxis = this.configuration.yAxis[series.yAxisIndex];
    if (!this.$containerSelector.select(`[data-id="barContainer-${index}"]`).empty()) {
      return;
    }
    const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
    if (!yScale) {
      return;
    }
    const { xAxis, defines } = this.configuration;
    const bars = this.$linesContainer.append('g')
      .attr('data-id', `barContainer-${index}`)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('class', 'barContainer');
    bars.selectAll('.bar').data(series.data)
      .enter().append('rect')
      .attr('data-id', `bar-${index}`)
      .attr('class', 'bar')
      .attr('fill', series.itemStyle.color)
      .attr('x', (d, i) => {
        return this.scale(xAxis.ticks[i]);
      })
      .attr('y', d => {
        const y = defines.y(d);
        return !yAxis.inverse ? yScale(y) : 0;
      })
      .attr('height', d => {
        const y = yScale(defines.y(d));
        return d3.max([!yAxis.inverse ? this.graphHeight - y : y, 0]);
      })
      .attr('width', series.itemStyle.strokeWidth)
      .each((d) => {
        this._renderMarkCircles(bars, d, yScale, series.marker);
      });
  }

  /**
   * 绘制标示点
   *
   * @param container 线条容器
   * @param data 标示点数据(数组)
   * @param yScale y 轴比例尺
   * @private
   */
  _renderMarkCircles(container, data, yScale) {
    let enterPoints;

    if (Array.isArray(data) && data.length) {
      enterPoints = container.selectAll('g.pointContainer').data(data).enter();
    } else {
      return;
    }

    const { xAxis, defines } = this.configuration;
    const circleContainer = enterPoints.append('g')
      .attr('data-id', (d, i) => `id-${xAxis.data[i].valueOf()}`)
      .attr('class', 'pointContainer')
      .attr('transform', (d, i) => `translate(${this.scale(xAxis.ticks[i])},${yScale(defines.y(d)) - 20})`)
      .style('cursor', 'pointer');
    circleContainer.append('circle')
      .attr('r', 8)
      .style('fill', 'none')
      .style('stroke', 'black');
  }

  /**
   * 初始化 x 坐标
   *
   * @private
   */
  _initX() {
    this.xScaleType(BaseLine.scaleType.LINEAR);
    const { xAxis, defines } = this.configuration;
    const { xScale } = defines;
    let xDomain = xScale ? xScale.domain() : d3.extent(xAxis.ticks);
    // 区间不得超过this.configuration.xAxis.ticks的区间
    if (xDomain[0] < xAxis.ticks[0]) {
      xDomain[0] = xAxis.ticks[0];
    }
    if (xDomain[1] > xAxis.ticks[xAxis.ticks.length - 1]) {
      xDomain[1] = xAxis.ticks[xAxis.ticks.length - 1];
    }
    this.xScale = d3.scaleLinear().domain(xDomain).range([0, this.graphWidth]);
    this.xAxis = d3.axisBottom(this.xScale)
      .tickValues(xAxis.ticks)
      .tickFormat((d, i) => xAxis.data[i]);
  }

  /**
   * 设置当前比例尺 或 获取比例尺计算数据
   *
   * @param values
   * @returns {*}
   */
  scale(values) {
    const scale = this.xScale;
    if (values) {
      return scale(values);
    } else {
      return scale;
    }
  }

  /**
   * 线段定义
   *
   * @param yScale
   * @param series
   * @private
   */
  _lineFunction(yScale, series) {
    const { xAxis, defines } = this.configuration;
    return d3.line()
      .defined((d) => d !== null && d !== undefined)
      .x((d, i) => this.scale(xAxis.ticks[i]))
      .y((d) => {
        let y = yScale(defines.y(d));
        // 解决线条在网格边界时显示不全的问题
        const halfStrokeWidth = series.itemStyle.strokeWidth / 2;
        if (y - halfStrokeWidth <= 0) {
          y += halfStrokeWidth;
        } else if (y + halfStrokeWidth >= this.graphHeight) {
          y -= halfStrokeWidth;
        }
        return y;
      })
      .curve(series.itemStyle.style === 'curves' ? d3.curveCatmullRom.alpha(0.5) : d3.curveLinear);
  }

  _dataHtml(data) {
    const { axisPointer, defines } = this.configuration;
    if (axisPointer.type === 'line') {
      const title = data[0].xName;
      const { width } = measureText(
        title,
        this.$svg.node(),
        { fontSize: '16px', fontWeight: 700, boxSizing: 'content-box' }
      );
      return `<div class="tooltip" style="width:${Math.max(width, 180)}px;">
        <h5>${title}</h5>
        ${data.map((d) => {
          const y = d.pureValue ? d.value : defines.y(d);
          return `<p>${d.series.name}: <span class="font-weight-bold">${toFixedDecimalNotZero(y)}</span></p>`
        }).join('')}
      </div>`;
    } else {
      const x = data.xName; // 字段名称为固定（值从this.configuration.xAxis.data中获取）
      const y = data.pureValue ? data.value : defines.y(data);
      const title = data.series.name;
      const { width } = measureText(
        title,
        this.$svg.node(),
        { fontSize: '16px', fontWeight: 700, boxSizing: 'content-box' }
      );
      return `<div class="tooltip" style="width:${Math.max(width, 180)}px;">
          <h5>${title}</h5>
          <p>
            ${this.$vue.t('graphic.graphWorkArea.code')}:
            <span class="font-weight-bold">${x}</span>
          </p>
          <p>
            ${this.$vue.t('graphic.whole.measurementValue')}:
            <span class="font-weight-bold">${toFixedDecimalNotZero(y)}</span>
          </p>
        </div>`;
    }
  }

  // 是否为最大（缩放）区间
  isMaxDomain(domain) {
    const { xAxis } = this.configuration;
    return domain[0] === xAxis.ticks[0] &&
      domain[1] === xAxis.ticks[xAxis.ticks.length - 1];
  }

  /**
   * 获取交互区域的所有图幅数据
   */
  _getMergedSeriesData() {
    const { xAxis, yAxis, defines } = this.configuration;
    return d3.merge(
      this.configuration.series.map((series, index) => {
        const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
        return series.data.map((d, i) => {
          const data = {
            xName: xAxis.data[i],
            xPosition: this.scale(xAxis.ticks[i]),
            yPosition: yScale(defines.y(d)),
            yAxis: yAxis[series.yAxisIndex],
            series,
            lineIndex: index
          };
          return d.constructor === Object
            ? { ...data, ...d }
            // pureValue 用于区分纯数据还是对象
            : { ...data, value: d, pureValue: true };
        });
      })
    );
  }
}
