import * as d3 from 'd3';
import * as _ from 'lodash';
import 'd3-selection-multi';
import {measureText, toFixedDecimalNotZero} from './d3-util';
import BaseLine from './baseLine';

import {calculateCorrelationCoefficient} from './utils';

export default class CorrelationLine extends BaseLine {

  constructor(configuration, vue) {
    super(configuration, vue);
    this.xAxisUnitHeight = 10; // x轴刻度单位所占高度
    this.graphBlankBottom = 30; // 图幅下方留白
  }

  _processConfiguration(configuration) {
    return {
      ...configuration,
      series: this.mergeScatterLines(configuration)
    };
  }

  mergeScatterLines(configuration) {
    const newSeries = [];
    configuration.series.forEach((series) => {
      if (series.data.length > 0) {
        newSeries.push({
          type: 'scatter',
          ...series
        });
        const coefficient = calculateCorrelationCoefficient(series.data, configuration.defines);
        newSeries.push({
          type: 'line',
          name: series.name + ' R=' + coefficient,
          yAxisIndex: series.yAxisIndex,
          data: [
            {x: d3.min(series.data.map(configuration.defines.x)), y: coefficient},
            {x: d3.max(series.data.map(configuration.defines.x)), y: coefficient}
          ],
          ...series.line
        });
      }
    });
    return newSeries;
  }

  /**
   * 初始化
   * @private
   */
  _init() {
    this.configuration = this._processConfiguration(this.configuration);
    super._init();
  }

  /**
   * 单独绘制 x 轴数组
   *
   * @private
   */
  _renderXAxis() {
    this.$xAxisContainer.call(this.xAxis);
    this.$xAxisContainer.select('path.domain')
      .attr('stroke', 'transparent');
    this.$xAxisContainer.selectAll('.tick line')
      .each((data, i, nodes) => {
        d3.select(nodes[i]).attr('transform', `translate(0, -${this.graphHeight})`);
      })
      .attr('y2', this.graphHeight)
      .style('stroke', (d, i, nodes) => {
        // 隐藏超出overlay范围的刻度线
        const transform = nodes[i].parentNode.getAttribute('transform');
        const translateX = +transform.substring('translate('.length, transform.indexOf(','));
        const isOverflow = translateX < 1 || translateX > this.graphWidth;
        return isOverflow ? 'none' :'lightgrey';
      })
      .style('stroke-dasharray', '3 3');
    this.$xAxisContainer.selectAll('.tick text')
      .style('font-size', '12px')
      .style('fill', 'black')
      .style('font-family', 'SimSun, "Songti SC"');
    const { xAxis } = this.configuration;
    const xNameWidth = measureText(
      xAxis.axisLabel.text,
      this.$xAxisContainer.node(),
      { fontSize: '12px' }
    ).width;
    this.$xAxisContainer.append('text')
      .attr('y', this.xAxisUnitHeight + 20)
      .attr('x', this.graphWidth - xNameWidth)
      .attr('class', 'unit')
      .style('fill', 'black')
      .style('font-size', '12px')
      .style('font-family', 'SimSun, "Songti SC"')
      .text(xAxis.axisLabel.text);
  }

  /**
   * 单独绘制 y 轴数组
   *
   * 在单侧绘制多条轴时，需要计算出之前轴所占的最大宽度，进行偏移轴及网格线
   *
   * @private
   */
  _renderYAxis() {
    this.configuration.yAxis.forEach((yAxis, index) => {
      // 检测之前有几个同方向的Y坐标轴
      const positionCount = this.configuration.yAxis.filter((y, i) => y.position === yAxis.position && i < index).length;
      const container = yAxis.position === 'right' ? this.$rightYAxisContainer : this.$leftYAxisContainer;
      let translateWidth = (positionCount + 1) * this.axisPadding;
      container.selectAll('.y-axis').each(function () {
        let textWidthArray = d3.select(this).selectAll('text')
          .nodes().map((d, i, nodes) => {
            return i === nodes.length - 1 ? 0 : d.getBBox().width;
          });
        translateWidth += Math.max(...textWidthArray);
      });
      const translate = `translate(${yAxis.position === 'left' ? '-' : '+'}${translateWidth}, 0)`;
      const axis = container.append('g')
        .attr('data-id', index)
        .attr('class', `y-axis grid`)
        .attr('transform', translate)
        .call(this.yAxisMap.get(index).yAxis);

      axis.select('path').remove();

      const lineTranslate = yAxis.position === 'left' ? translateWidth : -translateWidth;

      // 重绘网格线
      axis.selectAll('.tick line').each((data, i, nodes) => {
        d3.select(nodes[i]).attr('transform', `translate(${lineTranslate}, 0)`);
      }).style('stroke', (d, i, nodes) => {
        // 隐藏超出overlay范围的刻度线
        const transform = nodes[i].parentNode.getAttribute('transform');
        const translateY = +transform.substring(transform.indexOf(',') + 1, transform.indexOf(')'));
        const isOverflow = yAxis.position === 'left' && translateY < yAxis.axisLabel.fontSize / 2 ||
          yAxis.position === 'left' && translateY > this.graphHeight ||
          yAxis.position === 'right' && translateY > yAxis.axisLabel.fontSize / 2 ||
          yAxis.position === 'right' && translateY < this.graphHeight;
        return isOverflow ? 'none' : 'lightgrey'
      })
        .style('stroke-opacity', 0.6)
        .style('stroke-dasharray', '3 3');
      axis.selectAll('.tick text')
        .style('font-size', `${yAxis.axisLabel.fontSize + 'px'}`)
        .style('fill', 'black')
        .style('font-family', `${yAxis.axisLabel.fontFamily}`);
      const text = axis.append('text')
        .attr('y', -this.yAxisUnitHeight)
        .attr('class', 'unit')
        .style('fill', 'black')
        .style('font-size', `${yAxis.axisLabel.fontSize + 'px'}`)
        .style('font-family', `${yAxis.axisLabel.fontFamily}`)
        .text(yAxis.axisLabel.text);
      if (positionCount === 0) {
        let yNameWidth = measureText(
          yAxis.axisLabel.text,
          axis.node(),
          { fontSize: yAxis.axisLabel.fontSize + 'px' }
        ).width;
        text.attr('x', yAxis.position === 'left' ? (yNameWidth + 10) : (-10 - yNameWidth));
      }
    });
  }

  /**
   * 构建线条
   *
   * @private
   */
  _createLines() {
    this.configuration.series.map((series, index) => {
      const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
      if (!yScale) {
        return [];
      } else {
        if (series.type === 'line') {
          this._createLine(series, index);
        } else if (series.type === 'scatter') {
          this._createScatter(series, index);
        }
      }
    });
  }

  /**
   * 获取交互区域的所有图幅数据
   */
  _getMergedSeriesData() {
    const { yAxis, defines } = this.configuration;
    return d3.merge(
      this.configuration.series
        .filter((series) => series.type === 'scatter')
        .map((series) => {
          const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
          return series.data
            .filter(d => this.isXValueValid(d))
            .map(d => ({
              ...d,
              xPosition: this.scale(defines.x(d)),
              yPosition: yScale(defines.y(d)),
              yAxis: yAxis[series.yAxisIndex],
              series
            }));
        })
    );
  }

  /**
   * 创建散点图
   * @private
   */
  _createScatter(series, index) {
    if (!this.$containerSelector.select(`[data-id="scatterContainer-${index}"]`).empty()) {
      return;
    }
    const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
    if (!yScale) {
      return;
    }

    this.$linesContainer.selectAll('.point').remove();
    let scatter = this.$linesContainer.append('g')
      .attr('data-id', `scatterContainer-${index}`)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('class', 'scatterContainer');

    const symbol = d3.symbol().size(series.marker.symbolSize);

    const { defines } = this.configuration;
    scatter.selectAll('dot')
      .data(series.data)
      .enter().append('path')
      .attr('class', `point scatter-${index}`)
      .attr('d', symbol.type(d3[series.marker.symbol])())
      .attr('transform', d => {
        return `translate(${this.scale(defines.x(d))}, ${yScale(defines.y(d))})`;
      })
      .style('fill', series.itemStyle.color)
      .style('stroke', series.itemStyle.color)
      .style('strokeWidth', series.itemStyle.strokeWidth)
      .exit().remove();
  }

  /**
   * 创建单独线条
   *
   * @param series
   * @param index
   * @private
   */
  _createLine(series, index) {
    if (!this.$containerSelector.select(`[data-id="lineContainer-${index}"]`).empty()) {
      return;
    }
    const yScale = this.yAxisMap.get(series.yAxisIndex).yScale;
    if (!yScale) {
      return;
    }
    const line = this.$linesContainer.append('g')
      .attr('data-id', `lineContainer-${index}`)
      .attr('clip-path', `url(#${this.graphId}clip)`)
      .attr('class', 'lineContainer');

    line.append('path')
      .datum(series.data)
      .attr('data-id', `line-${index}`)
      .classed('line', true)
      .attr('fill', 'none')
      .attr('stroke-width', series.itemStyle.strokeWidth)
      .attr('stroke', series.itemStyle.color)
      .attr('stroke-dasharray', series.itemStyle.dasharray)
      .attr('d', this._lineFunction(yScale, series))
      .on('dblclick', () => {
        d3.event.stopPropagation();
        this.dispatch.call('lineSelect', this, series, index);
      })
      .on('contextmenu', () => {
        this.$vue.openContextMenu(d3.event, {series, xScale: this.xScale, ...this.yAxisMap.get(index), lineIndex: index});
      });
  }

  /**
   * 初始化 x 坐标
   *
   * @private
   */
  _initX() {
    const { xAxis, defines } = this.configuration;
    this.xScaleType(BaseLine.scaleType.LINEAR);
    const { xScale } = defines;
    this.xScale = xScale || d3.scaleLinear().domain([xAxis.min, xAxis.max]).range([0, this.graphWidth]);
    let step = (xAxis.max - xAxis.min) / xAxis.axisTick.tickNumber;
    let tickValues = [];
    for (let i = 0; i <= xAxis.axisTick.tickNumber; i++) {
      tickValues.push(xAxis.min + step * i);
    }
    this.xAxis = d3.axisBottom(this.xScale);
    // 如果使用了新的比例尺则不手动计算刻度值
    xScale ? this.xAxis.ticks(xAxis.axisTick.tickNumber) : this.xAxis.tickValues(tickValues);
  }

  /**
   * 设置当前比例尺 或 获取比例尺计算数据
   *
   * @param values
   * @returns {*}
   */
  scale(values) {
    const scale = this.xScale;
    if (values) {
      return scale(values);
    } else {
      return scale;
    }
  }

  _dataHtml(data) {
    const { defines } = this.configuration;
    const x = defines.x(data);
    const y = defines.y(data);
    const title = data.series.name;
    const { width } = measureText(
      title,
      this.$svg.node(),
      { fontSize: '16px', fontWeight: 700, boxSizing: 'content-box' }
    );
    return `<div class="tooltip" style="width:${Math.max(width, 180)}px;">
        <h5>${title}</h5>
        <p>
          ${this.$vue.t('graphic.whole.measurementValue')} X:
          <span class="font-weight-bold">${x}</span>
        </p>
        <p>
          ${this.$vue.t('graphic.whole.measurementValue')} Y:
          <span class="font-weight-bold">${toFixedDecimalNotZero(y)}</span>
        </p>
      </div>`;
  }

  /**
   * 是否为最小（缩放）区间
   * @param domain 当前X定义域
   */
  isMinDomain(domain) {
    return Math.abs(domain[1] - domain[0]) < 0.1;
  }

  /**
   * 创建图例
   * @private
   */

  _renderLegend() {
    const { legend, series, title, defines } = this.configuration;
    if (!legend.show) {
      return;
    }

    this.$legendContainer.selectAll('*').remove();
    this.$legendContainer.selectAll('rect').remove();

    const entriesUpdate = this.$legendContainer.selectAll('g.legend-entry').data(series);
    this.$legendContainer
      .append('rect')
      .attr('width', () => {
        let sum = [];
        this.legendWidthLists.forEach((item) => {
          sum.push(_.sum(item));
        });
        // 为legend容器额外添加内边距
        return (_.max(sum) || 0) + this.legendLinePadding * 2;
      })
      .attr('height', this.legendHeight)
      .attr('x', (d, i, nodes) => {
        const rectWidth = +d3.select(nodes[i]).attr('width');
        return `${(this.containerWidth - rectWidth) / 2}`;
      })
      .attr('y', title.show && title.position === 'top' ? 30 : 0)
      .attr('rx', 6)
      .attr('class', 'legend-border')
      .style('fill', 'none')
      .style('stroke', 'black')
      .style('stroke-width', '0.4');
    const entriesEnter = entriesUpdate
      .enter()
      .append('g')
      .attr('transform', (d, i) => {
        const xOffset = +this.$legendContainer.select('rect').attr('x') + this.legendLinePadding;
        // 线条图例所在行
        let rowIndex = 0;
        // 线条图例所在列
        let colIndex = 0;
        // 当前遍历的图例个数
        let lineCount = 0;
        for (let j = 0; j < this.legendWidthLists.length; j++) {
          const list = this.legendWidthLists[j];
          for (let k = 0; k < list.length; k++) {
            lineCount++;
            if (lineCount === i + 1) {
              rowIndex = j;
              colIndex = k;
              break;
            }
          }
        }
        const width = _.sum(this.legendWidthLists[rowIndex].slice(0, colIndex));
        return `translate(
          ${xOffset + width + this.legendLinePadding},
          ${
            (title.show && title.position === 'top' ? 20 : -this.legendLineHeight / 2) +
            this.legendLineHeight * (rowIndex + 1)
          }
        )`;
      })
      .classed('legend-entry', true)
      .merge(entriesUpdate);
    // 增大交互面积，g元素的事件只会作用于绘制的区域上
    entriesEnter
      .append('rect')
      .attrs({
        x: 0,
        y: -this.legendLineHeight / 2 + 2,
        width: this.legendLineWidth,
        height: 16, // 根据文字11px得出的实际高度
        fill: 'white'
      });

    entriesEnter.on('dblclick', (d, i) => {
      d3.event.stopPropagation();
      this.dispatch.call('lineSelect', this, d, i);
    });
    entriesEnter.append('line')
      .classed('legend-line', true)
      .attr('data-id', (d, i) => `legend-${i}`)
      .attr('stroke-width', (d) => d.itemStyle.strokeWidth)
      .attr('stroke', (d) => d.type === 'line' ? d.itemStyle.color : 'none')
      .attr('fill', 'none')
      .attr('x2', this.legendLineWidth);
    entriesEnter.append('path')
      .classed('legend-dot', true)
      .attr('data-id', (d, i) => `legend-${i}`)
      .attr('d', (d) => d.type === 'scatter' ? d3.symbol().size(d.marker.symbolSize).type(d3[d.marker.symbol])() : '')
      .style('fill', (d) => d.type === 'scatter' ? d.itemStyle.color : 'none')
      .style('transform', 'translate(' + this.legendLineWidth / 2 + 'px, 0)')
      .style('stroke', 'transparent');

    entriesEnter.append('text')
      .classed('legend-title', true)
      .attr('font-size', '11px')
      .attr('transform', 'translate(32, 2)')
      .attr('text-anchor', 'start')
      .attr('y', '0.15em')
      .style('user-select', 'none')
      .text(d => defines.legend ? defines.legend(d) : d.name);

    entriesUpdate.exit().remove();
  }
}
