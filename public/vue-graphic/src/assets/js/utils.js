import * as _ from 'lodash';

export const makePath = function (tree, target, config) {
  let done = false;
  const path = [];
  const defaultConfig = {
    // 需要搜索的属性
    searchProperty: 'id',
    // 是否包含搜索的节点
    containsSearchKey: true,
    // 默认子节点的属性
    childrenProperty: 'children',
    // 是否只返回第一个搜索到的路径，为 false 时会搜索所有
    findFirst: true,
    // 是否只返回匹配的节点
    targetOnly: false,
    // 比较函数
    compareFunction: (a, b) => a === b
  };

  const localConfig = {...defaultConfig, ...config};

  function traverse(tree, target) {
    tree.forEach(item => {
      if (!done || !localConfig.findFirst) {
        if (item.hasOwnProperty(localConfig.searchProperty) && localConfig.compareFunction(item[localConfig.searchProperty], target)) {
          localConfig.containsSearchKey && path.push(item);
          done = true;
        } else {
          if (item[localConfig.childrenProperty] && item[localConfig.childrenProperty].length > 0) {
            if (!localConfig.targetOnly) {
              path.push(item);
            }
            traverse(item[localConfig.childrenProperty], target);
          }
        }
      }
    });
    if (!done && !localConfig.targetOnly) {
      path.pop();
    }
  }

  traverse(tree, target);

  return path;
};

export const arraysEqual = function (arr1, arr2) {
  if (arr1 && arr2) {
    return arr1.length === arr2.length && _.union(arr1, arr2).length === arr1.length;
  }
  return false;
};

// 生成线型随机坐标
export const generateCoordinates = function ([minX, maxX], [minY, maxY], num) {
  if (typeof minX !== 'number' || typeof maxX !== 'number' || minX >= maxX) {
    console.error('error xRange');
  }
  if (typeof minY !== 'number' || typeof maxY !== 'number' || minY >= maxY) {
    console.error('error yRange');
  }
  const data = [];
  if (num > 0) {
    for (let i = 0; i < num; i++) {
      data.push({
        x: +((Math.random() * (maxX - minX + 1) + minX).toFixed(2)),
        y: +((Math.random() * (maxY - minY + 1) + minY).toFixed(2))
      });
    }
  }
  return data;
};

// 计算相关系数
export const calculateCorrelationCoefficient = function (data, defines) {
  const xAvg = _.sum(data.map(defines.x)) / data.length;
  const yAvg = _.sum(data.map(defines.y)) / data.length;
  let denominator = 0;
  let moleculeX = 0;
  let moleculeY = 0;
  data.forEach((item) => {
    denominator += (defines.x(item) - xAvg) * (defines.y(item) - yAvg);
    moleculeX += Math.pow((defines.x(item) - xAvg), 2);
    moleculeY += Math.pow((defines.y(item) - yAvg), 2);
  });
  if (moleculeX * moleculeY === 0) {
    return 0;
  } else {
    return +((denominator / Math.sqrt(moleculeX * moleculeY)).toFixed(2));
  }
};
