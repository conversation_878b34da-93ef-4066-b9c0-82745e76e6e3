import * as _ from 'lodash';
import {COLORS} from './constants';

// 图形工作区取值配置
export const VALUE_FILTER_OPTION = {
  maxLineCount: 4,
  showVector: true,
  showDepth: false,
  value: {
    startDayTime: '',
    endDayTime: '',
    writeType: [0, 1],
    auditStatus: [0, 1],
    valueStatus: [1, 2],
    valueType: [0, 1, 2]
  }
};

// 图幅默认配置
export const DEFAULT_OPTION = {
  type: 'processLine',
  compared: false,
  timeMinInterval: 'hour',
  title: {
    show: false,
    position: 'top',
    text: '',
    textStyle: {
      fontFamily: 'SimSun, "Songti SC"',
      fontSize: 18
    }
  },
  legend: {
    show: true,
    positionAuto: true,
    position: 'top',
    orient: 'horizontal'
  },
  axisPointer: {
    type: 'point',
    pointStyle: {
      fill: 'black',
      stroke: 'black',
      strokeWidth: 0,
      r: 4
    },
    lineStyle: {
      color: 'black',
      width: 1,
      dasharray: '0'
    }
  },
  xAxis: {
    max: null,
    min: null
  },
  maxYAxisCount: 4,
  leftPadding: 20,
  rightPadding: 20,
  defines: {
    x: (d) => d.x,
    y: (d) => d.y,
    seriesName: (d) => d.codeName + '.' + d.vectorName + '.' + d.vectorUnit,
    yAxisText: (d) => d.vectorName + '(' + d.vectorUnit + ')',
    valueMarkerColor: (d) => {
      switch(d.status2) {
        case 3:
        case 4:
          return 'black';
        case 9:
          return 'green';
      }
      return 'none';
    }
  }
};

// 分布线图幅配置
export const DISTRIBUTION_OPTION = {
  ...DEFAULT_OPTION,
  type: 'distributionLine',
  defines: {
    y: (d) => d,
    seriesName: (d) => d.time,
    yAxisText: (d) => d.vectorName + '(' + d.vectorUnit + ')'
  }
};

// 分布线X轴配置
export const DISTRIBUTION_X_AXIS_OPTION = {
  coordinateType: '',
  nameType: 'codeName',
  data: [],
  ticks: []
};

// 相关线图幅配置
export const CORRELATION_OPTION = {
  ...DEFAULT_OPTION,
  type: 'correlationLine',
  defines: {
    ...DEFAULT_OPTION.defines,
    seriesName: (d, codeId) => d.codeInfo[codeId].codeName,
    xAxisText: (d, codeId) => d.vectorInfo[codeId].vectorName + '(' + d.vectorInfo[codeId].vectorUnit + ')',
    yAxisText: (d, codeId) => d.vectorInfo[codeId].vectorName + '(' + d.vectorInfo[codeId].vectorUnit + ')'
  }
};

// 相关线X轴配置
export const CORRELATION_X_AXIS_OPTION = {
  max: 0,
  min: 0,
  axisTick: {
    tickNumber: 15,
    decimalNumber: 2
  },
  axisLabel: {
    text: ''
  },
  value: {
    // 默认采集类型：人工+自动化
    writeType: [0, 1],
    // 默认审核状态：未校核+已校核
    auditStatus: [0, 1],
    // 默认测值状态：正常+异常
    valueStatus: [1, 2],
    // 默认测值类型：普通+加密观测+汛期
    valueType: [0, 1, 2]
  }
};

// 图幅y轴默认配置
export const Y_AXIS_OPTION = {
  position: 'left',
  maxAuto: true,
  max: 0,
  minAuto: true,
  min: 0,
  inverse: false,
  axisTick: {
    tickNumberAuto: true,
    tickNumber: 4,
    decimalNumberAuto: true,
    decimalNumber: 2
  },
  axisLabel: {
    text: '',
    fontFamily: 'SimSun, "Songti SC"',
    fontSize: 12,
    rotate: 0
  }
};

// 图幅标线默认配置
export const MARK_LINE_OPTION = {
  yAxisIndex: 0,
  axis: 'y',
  value: 0,
  label: {
    show: true,
    text: '',
    fontFamily: 'SimSun, "Songti SC"',
    color: 'black',
    fontSize: 12
  },
  lineStyle: {
    color: 'black',
    width: 1,
    dasharray: '0'
  }
};

// 图幅线条默认配置
export const SERIES_OPTION = {
  type: 'line',
  name: '',
  data: [],
  itemStyle: {
    color: 'red',
    strokeWidth: 1,
    dasharray: '0',
    style: 'straight'
  },
  yAxisIndex: 0,
  marker: {
    symbol: 'none',
    symbolSize: 6,
    symbolNumber: 0
  },
  valueMarker: {
    show: false,
    r: 4,
    data: []
  }
};

// 相关线图幅线条默认配置
export const CORRELATION_SERIES_OPTION = {
  name: '',
  data: [],
  itemStyle: {
    color: 'red',
    strokeWidth: 0
  },
  yAxisIndex: 0,
  marker: {
    symbol: 'symbolCircle',
    symbolSize: 50
  },
  line: {
    itemStyle: {
      color: 'blue',
      strokeWidth: 1
    }
  }
};

// 线条属性
export const LINE_CONFIG = {
  name: '',
  position: 'left',
  itemStyle: {
    color: 'red',
    strokeWidth: 1,
    dasharray: '0',
    style: 'straight'
  },
  marker: {
    symbol: 'none',
    symbolSize: 50,
    symbolNumber: 0
  },
  value: {
    startDayTime: null,
    endDayTime: null,
    // 默认采集类型：人工+自动化
    writeType: [0, 1],
    // 默认审核状态：未校核+已校核
    auditStatus: [0, 1],
    // 默认测值状态：正常+异常
    valueStatus: [1, 2],
    // 默认测值类型：普通+加密观测+汛期
    valueType: [0, 1, 2],
    missingExamineDays: 0
  }
};

// 分布线线条属性
export const DISTRIBUTION_LINE_CONFIG = {
  name: '',
  position: 'left',
  itemStyle: {
    color: 'red',
    strokeWidth: 1,
    dasharray: '0',
    style: 'straight'
  },
  marker: {
    symbol: 'none',
    symbolSize: 50,
    symbolNumber: 0
  },
  value: {
    // 默认采集类型：人工+自动化
    writeType: [0, 1],
    // 默认审核状态：未校核+已校核
    auditStatus: [0, 1],
    // 默认测值状态：正常+异常
    valueStatus: [1, 2],
    // 默认测值类型：普通+加密观测+汛期
    valueType: [0, 1, 2]
  }
};

// 相关线线条属性
export const CORRELATION_LINE_CONFIG = {
  name: '',
  position: 'left',
  itemStyle: {
    color: 'red',
    strokeWidth: 1
  },
  marker: {
    symbol: 'symbolCircle',
    symbolSize: 50
  },
  value: {
    // 默认采集类型：人工+自动化
    writeType: [0, 1],
    // 默认审核状态：未校核+已校核
    auditStatus: [0, 1],
    // 默认测值状态：正常+异常
    valueStatus: [1, 2],
    // 默认测值类型：普通+加密观测+汛期
    valueType: [0, 1, 2]
  }
};

export const Y_POSITION = ['left', 'right', 'left', 'right'];

// 合并配置项
export const mergeOption = (obj1, obj2) => {
  const obj = _.cloneDeep(obj1);
  const _merge = (obj1, obj2) => {
    const keys = _.union(
      obj1 !== null ? Object.keys(obj1) : [],
      obj2 !== null ? Object.keys(obj2) : []
    );
    keys.forEach((key) => {
      if (typeof obj2[key] === 'object') {
        if (typeof obj1[key] === 'object') {
          _merge(obj1[key], obj2[key]);
        } else {
          obj1[key] = _.cloneDeep(obj2[key]);
        }
      } else if (obj2[key] !== undefined && obj1[key] !== obj2[key]) {
        obj1[key] = obj2[key];
      }
    });
  };
  _merge(obj, obj2);
  return obj;
};

// 补全配置项
export const completeOption = (opt = {}) => {
  let option;
  if (opt.type === 'distributionLine') {
    option = mergeOption(DISTRIBUTION_OPTION, opt);
    option.xAxis = mergeOption(DISTRIBUTION_X_AXIS_OPTION, option.xAxis || {});
  } else if (opt.type === 'correlationLine') {
    option = mergeOption(CORRELATION_OPTION, opt);
    option.xAxis = mergeOption(CORRELATION_X_AXIS_OPTION, option.xAxis || {});
  } else {
    option = mergeOption(DEFAULT_OPTION, opt);
  }
  if (!option.yAxis) {
    option.yAxis = [];
  } else if (option.yAxis.length > 0) {
    option.yAxis = option.yAxis.map((yAxis) => mergeOption(Y_AXIS_OPTION, yAxis));
  }
  if (!option.markLines) {
    option.markLines = [];
  } else if (option.markLines.length > 0) {
    option.markLines = option.markLines.map((markLine) => mergeOption(MARK_LINE_OPTION, markLine));
  }
  if (option.series && option.series.length > 0) {
    option.series = option.series
      .map((series, index) => {
        const mergeSeries = mergeOption(opt.type === 'correlationLine' ? CORRELATION_SERIES_OPTION : SERIES_OPTION, series);
        if (!option.yAxis[mergeSeries.yAxisIndex]) {
          option.yAxis[mergeSeries.yAxisIndex] = _.cloneDeep(Y_AXIS_OPTION);
        }
        if (!series.itemStyle || !series.itemStyle.color) {
          mergeSeries.itemStyle.color = COLORS[index % COLORS.length];
        }
        return mergeSeries;
      })
      .filter((series) => option.yAxis[series.yAxisIndex]);
  } else {
    console.error('empty option.series');
  }
  return option;
};
