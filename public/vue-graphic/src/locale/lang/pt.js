import pt from 'element-ui/lib/locale/lang/pt';
export default {
  ...pt,
  "graphic": {
    "whole": {
      "back": "voltar",
      "finish": "terminar",
      "name": "nome",
      "none": "nenhum",
      "day": "dia",
      "endTime": "tempo de fim",
      "empty": "esvaziar",
      "prompt": "sugestão",
      "sure": "certeza",
      "cancel": "cancelar",
      "time": "tempo",
      "artificial": "artificial",
      "export": "exportar",
      "exportFailed": "falha em exportar",
      "exportSuccess": "sucesso em exportar",
      "measurementValue": "valor de medição",
      "year": "ano",
      "month": "mês"
    },
    "time": {
      "week": "semana",
      "midTenDays": "2",
      "hour": "hora",
      "lastTenDays": "3",
      "firstTenDays": "1",
      "season": "estação",
      "day": "dia",
      "tenDays": "dez dias"
    },
    "codeTree": {
      "keyWordPlaceholder": 'Preencha as palavras-chave para pesquisar ...'
    },
    "graphStyle": {
      "auto": "automático",
      "fangsong": "FangSong",
      "songti": "Simsuncss",
      "cannotBeDeleted": "determinar excluí-lo, não recuperável após a exclusão?",
      "save": "registro",
      "axis": "eixo",
      "down": "baixo",
      "out": "lado de fora",
      "graphStyle": "propriedade gráfica",
      "min": "mínimo",
      "decimalNumber": "decimal",
      "fontFamily": "Fonte do caractere",
      "kaiti": "KaiTi",
      "up": "Alto",
      "inverse": "Coordenar o eixo reverso",
      "padding": "margem",
      "tickNumber": "escada",
      "max": "máximo",
      "microsoftYaHei": "Yahei da Microsoft",
      "heiti": "Simhei",
      "right": "direita",
      "deleteGraph": "retirar",
      "applyToAll": "aplicar a todos",
      "microsoftJhenghei": "Microsoft Jhenghei",
      "left": "esquerda",
      "showTitle": "Título",
      "fontSize": "tamanho da fonte",
      "position": "posição",
      "newSongti": "NSimSun"
    },
    "graphWorkArea": {
      "template": "modelo",
      "valueFilter": "Avaliação",
      "distributionLine": "gráfico de distribuição",
      "code": "ponto de medição",
      "auditStatusOption3": "falhou",
      "auditStatusOption2": "verificado",
      "showDepth": "profundidade enterrada",
      "processLine": "hidrógrafo",
      "valueStatus": "status de medição",
      "dateValidInfo1": "prazo não pode ser anterior à hora de início",
      "showVector": "Nome do componente",
      "dateValidInfo3": "a última hora não pode ser anterior à hora de início",
      "dateValidInfo2": "prazo não pode ser anterior ao horário de início da medição",
      "writeTypeOption2": "automático",
      "writeType": "tipo de aquisição",
      "printSetting": "configurações de impressão",
      "valueType": "tipo de medida",
      "auditStatusOption1": "desmarcado",
      "valueStatusOption3": "antinatural",
      "valueStatusOption2": "Comum",
      "valueStatusOption1": "desconhecido",
      "cannotBeCleared": "Decida esvaziá-lo e você não poderá se recuperar após o esvaziamento?",
      "valueStatusOption5": "ausência",
      "environmentVectorWarning": "Selecione o gráfico e as variáveis ​​de ambiente a serem adicionadas.",
      "valueStatusOption4": "Culpa",
      "latestValueDate": "última vez",
      "valueTypeOption1": "medidas gerais",
      "codeAndVectorWarning": "Selecione um ponto ou componente de medição",
      "valueTypeOption2": "medição criptografada",
      "valueTypeOption3": "medição de inundação",
      "valueTypeOption4": "medida específica",
      "addToGraph": "Adicionar ao gráfico selecionado",
      "firstValueDate": "hora de início da medição",
      "clinographLine": "diagrama de inclinação",
      "valueFilterSettingTip": "",
      "print": "impressão",
      "maxLineCount": "Número de linhas por gráfico para um único componente",
      "auditStatus": "status de auditoria",
      "graphEmptyPlaceholder": "Arraste o ponto de medição selecionado aqui.",
      "valueFilterSetting": "desenho predefinido",
      "startDate": "Hora de início",
      "environmentVector": "variáveis ​​ambientais",
      "distributionGraph": "diagrama de distribuição"
    },
    "lineStyle": {
      "dashed": "linha pontilhada",
      "strokeWidth": "largura",
      "solid": "linha contínua",
      "missingExamineDays": "falta de tempo de medição",
      "color": "cor",
      "baseLineStyle": "propriedade básica",
      "symbolSize": "cortar",
      "yPosition": "Posição do eixo Y",
      "triangle": "triângulo",
      "straight": "direto em frente",
      "square": "quadrado",
      "curves": "curva",
      "bar": "Barra",
      "diamond": "losango",
      "lineStyle": "propriedade de linha",
      "marker": "rótulo",
      "deleteLine": "retirar",
      "name": "nome da linha",
      "dasharray": "tipo de linha",
      "style": "estilo",
      "circle": "círculo",
      "dotted": "linha de corrente",
      "symbolNumber": "número"
    },
    "lineChart": {
      "copyGraph": "copie o gráfico",
      "exportWPS": "Para exportar para a WPS, você precisa colar a imagem por si mesmo e a moldura da imagem foi copiada para você...",
      "exporting": "Exportar agora, aguarde ...",
      "messageCopyError": "cópia falha",
      "exportAs": "exportado como",
      "messageCopySuccess": "copiar sucesso",
      "copyGraphPlaceholder": "clique com o botão direito na imagem para copiar.",
      "viewCode": "Visão",
      "cancelManualComparison": "Cancelar comparação manual",
      "manualComparison": "Comparação manual"
    }
  }
};
