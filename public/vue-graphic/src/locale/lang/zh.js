import zh from 'element-ui/lib/locale/lang/zh-CN';
export default {
  ...zh,
  "graphic": {
    "whole": {
      "back": "返回",
      "finish": "完成",
      "name": "名称",
      "none": "无",
      "day": "天",
      "endTime": "截止时间",
      "empty": "清空",
      "prompt": "提示",
      "sure": "确定",
      "cancel": "取消",
      "time": "时间",
      "artificial": "人工",
      "export": "导出",
      "exportFailed": "导出失败",
      "exportSuccess": "导出成功",
      "measurementValue": "测值",
      "year": "年",
      "month": "月"
    },
    "time": {
      "week": "周",
      "midTenDays": "中",
      "hour": "时",
      "lastTenDays": "下",
      "firstTenDays": "上",
      "season": "季度",
      "day": "日",
      "tenDays": "旬",
      "minute": "分",
      "second": "秒",
    },
    "codeTree": {
      "keyWordPlaceholder": '请输入关键字搜索...'
    },
    "graphStyle": {
      "auto": "自动",
      "fangsong": "仿宋",
      "songti": "宋体",
      "cannotBeDeleted": "确定删除，删除后不可恢复？",
      "save": "保存修改",
      "axis": "轴",
      "down": "下",
      "out": "外",
      "graphStyle": "图幅属性",
      "min": "最小值",
      "decimalNumber": "小数位数",
      "fontFamily": "字体",
      "kaiti": "楷体",
      "up": "上",
      "inverse": "坐标轴反向显示",
      "padding": "边距",
      "tickNumber": "刻度数",
      "max": "最大值",
      "microsoftYaHei": "微软雅黑",
      "heiti": "黑体",
      "right": "右",
      "deleteGraph": "删除图幅",
      "applyToAll": "应用于所有",
      "microsoftJhenghei": "微软正黑",
      "left": "左",
      "showTitle": "显示标题",
      "fontSize": "字体大小",
      "position": "位置",
      "newSongti": "新宋体"
    },
    "graphWorkArea": {
      "valueFilter": "取值",
      "auditStatusOption3": "未通过",
      "auditStatusOption2": "通过",
      "processLine": "过程线",
      "dateValidInfo1": "截止时间不能早于起始时间",
      "dateValidInfo3": "最近时间不能早于起始时间",
      "dateValidInfo2": "截止时间不能早于始测时间",
      "writeType": "采集类型",
      "valueType": "测值类型",
      "auditStatusOption1": "未审核",
      "valueStatusOption3": "异常",
      "valueStatusOption2": "正常",
      "valueStatusOption1": "未知",
      "cannotBeCleared": "确定清空，清空后不可恢复？",
      "valueStatusOption5": "缺失",
      "environmentVectorWarning": "请先选择图幅和选中需要添加的环境量",
      "valueStatusOption4": "错误",
      "latestValueDate": "最近时间",
      "valueTypeOption1": "普通测值",
      "codeAndVectorWarning": "请选择测点或分量",
      "valueTypeOption2": "加密测值",
      "valueTypeOption3": "汛期测值",
      "valueTypeOption4": "特定测值",
      "addToGraph": "添加到选中图幅",
      "clinographLine": "测斜图",
      "startDate": "起始时间",
      "distributionGraph": "分布图",
      "template": "模板",
      "distributionLine": "分布线",
      "code": "测点",
      "showDepth": "显示埋设深度",
      "valueStatus": "测值状态",
      "showVector": "显示分量名称",
      "writeTypeOption2": "自动",
      "printSetting": "打印设置",
      "firstValueDate": "始测时间",
      "valueFilterSettingTip": "（保存后已绘制的图幅将不会发生改变）",
      "print": "打印",
      "maxLineCount": "单个分量每个图幅线的条数",
      "auditStatus": "审核状态",
      "graphEmptyPlaceholder": "拖动左侧选中测点置此",
      "valueFilterSetting": "绘图预设置",
      "environmentVector": "环境量"
    },
    "lineStyle": {
      "dashed": "虚线",
      "strokeWidth": "线条宽度",
      "missingExamineDays": "缺测时间",
      "color": "线条颜色",
      "yPosition": "纵轴位置",
      "triangle": "三角形",
      "bar": "柱状图",
      "lineStyle": "线条属性",
      "deleteLine": "删除线条",
      "dotted": "点划线",
      "symbolNumber": "个数",
      "solid": "实线",
      "baseLineStyle": "基本属性",
      "symbolSize": "大小",
      "straight": "直线",
      "square": "正方形",
      "curves": "曲线",
      "diamond": "菱形",
      "marker": "标记",
      "name": "线条名称",
      "dasharray": "线型",
      "style": "样式",
      "circle": "圆形"
    },
    "lineChart": {
      "copyGraph": "复制图幅",
      "exportWPS": "导出到WPS需自行粘贴图片，已为您复制图幅...",
      "exporting": "正在导出，请稍候...",
      "messageCopyError": "复制失败",
      "exportAs": "导出为",
      "messageCopySuccess": "复制成功",
      "copyGraphPlaceholder": "请右击图片进行复制",
      "viewCode": "查看测值",
      "cancelManualComparison": "取消人工比测",
      "manualComparison": "人工比测"
    }
  }
};
