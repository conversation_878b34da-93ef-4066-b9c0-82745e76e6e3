import en from 'element-ui/lib/locale/lang/en';
export default {
  ...en,
  "graphic": {
    "whole": {
      "back": "back",
      "finish": "complete",
      "name": "name",
      "none": "none",
      "day": "d",
      "endTime": "end time",
      "empty": "clear",
      "prompt": "hint",
      "sure": "Confirm",
      "cancel": "cancel",
      "time": "time",
      "artificial": "manual",
      "export": "export",
      "exportFailed": "export failure",
      "exportSuccess": "exported",
      "measurementValue": "measured value",
      "year": "year",
      "month": "month"
    },
    "time": {
      "week": "week",
      "midTenDays": "2",
      "hour": "hour",
      "lastTenDays": "3",
      "firstTenDays": "1",
      "season": "season",
      "day": "day",
      "tenDays": "ten-day"
    },
    "codeTree": {
      "keyWordPlaceholder": 'Please input key words to search ...'
    },
    "graphStyle": {
      "auto": "automatic",
      "fangsong": "FangSong",
      "songti": "Simsuncss",
      "cannotBeDeleted": "Determine to delete it, not recoverable after deletion?",
      "save": "save",
      "axis": "axis",
      "down": "below",
      "out": "out",
      "graphStyle": "graphy property",
      "min": "minimum",
      "decimalNumber": "decimal place",
      "fontFamily": "font ",
      "kaiti": "KaiTi",
      "up": "above",
      "inverse": "Coordinate axis reverse",
      "padding": "margin",
      "tickNumber": "scale",
      "max": "maximum",
      "microsoftYaHei": "Microsoft Yahei",
      "heiti": "Simhei",
      "right": "right",
      "deleteGraph": "delete",
      "applyToAll": "apply to all",
      "microsoftJhenghei": "Microsoft Jhenghei",
      "left": "left",
      "showTitle": "title",
      "fontSize": "font size",
      "position": "position",
      "newSongti": "NSimSun"
    },
    "graphWorkArea": {
      "template": "template",
      "valueFilter": "evaluation",
      "distributionLine": "distribution graph",
      "code": "measuring point",
      "auditStatusOption3": "failed",
      "auditStatusOption2": "checked",
      "showDepth": "buried depth",
      "processLine": "hydrograph",
      "valueStatus": "measurement status",
      "dateValidInfo1": "Deadline cannot be earlier than the start time",
      "showVector": "component name",
      "dateValidInfo3": "The latest time cannot be earlier than the start time",
      "dateValidInfo2": "Deadline cannot be earlier than the measuring start time",
      "writeTypeOption2": "automatic",
      "writeType": "acquisition type",
      "printSetting": "print settings",
      "valueType": "Type of measurement",
      "auditStatusOption1": "unchecked",
      "valueStatusOption3": "abnormal",
      "valueStatusOption2": "normal",
      "valueStatusOption1": "unknowns",
      "cannotBeCleared": "Determine to empty it and can't recover after emptying?",
      "valueStatusOption5": "absence",
      "environmentVectorWarning": "Please select the graph and environmental variables to be added first.",
      "valueStatusOption4": "error",
      "latestValueDate": "latest time",
      "valueTypeOption1": "general measurements",
      "codeAndVectorWarning": "Please select measuring point or component",
      "valueTypeOption2": "encrypted measurement",
      "valueTypeOption3": "flood measurement",
      "valueTypeOption4": "specific measurement",
      "addToGraph": "Add to selected graph",
      "firstValueDate": "measurement start time",
      "clinographLine": "inclination diagram",
      "valueFilterSettingTip": "The saved image will not change.",
      "print": "print",
      "maxLineCount": "Number of lines per graph for a single component",
      "auditStatus": "audit status",
      "graphEmptyPlaceholder": "Drag the selected measuring point to here.",
      "valueFilterSetting": "drawing pre-set",
      "startDate": "start time",
      "environmentVector": "environmental variables",
      "distributionGraph": "distribution diagram"
    },
    "lineStyle": {
      "dashed": "dashed line",
      "strokeWidth": "width",
      "solid": "solid line",
      "missingExamineDays": "measurement lack time",
      "color": "color",
      "baseLineStyle": "basic property",
      "symbolSize": "size",
      "yPosition": "Y axis position",
      "triangle": "triangle",
      "straight": "straight",
      "square": "square",
      "curves": "curve",
      "bar": "bar",
      "diamond": "rhombus",
      "lineStyle": "line property",
      "marker": "tag",
      "deleteLine": "delete",
      "name": "line name ",
      "dasharray": "line type",
      "style": "style",
      "circle": "circle",
      "dotted": "chain line",
      "symbolNumber": "number"
    },
    "lineChart": {
      "copyGraph": "copy graph",
      "exportWPS": "When exporting to WPS, you need to paste the picture yourself, and the picture frame has been copied for you...",
      "exporting": "Exporting now,please wait…",
      "messageCopyError": "copy fails",
      "exportAs": "exported as",
      "messageCopySuccess": "copy success",
      "copyGraphPlaceholder": "Please right click on the image to copy.",
      "viewCode": "view",
      "cancelManualComparison": "Cancel manual comparison",
      "manualComparison": "Manual comparison"
    }
  }
};
