import axios from './assets/js/axios';
import './assets/fonts/iconfont.css';

import VueI18n from 'vue-i18n';
import locale from './locale';
import messages from './locale/lang';

import {
  Aside,
  Button,
  ButtonGroup,
  Card,
  Checkbox,
  CheckboxGroup,
  Col,
  ColorPicker,
  Container,
  DatePicker,
  Dialog,
  Drawer,
  Form,
  FormItem,
  Header,
  Icon,
  Input,
  InputNumber,
  Loading,
  Main,
  Message,
  MessageBox,
  Option,
  RadioGroup,
  RadioButton,
  Row,
  Select,
  TabPane,
  Tabs,
  TimePicker,
  Tree,
} from 'element-ui';
import './assets/css/element-variables.scss';

import ContextMenu from 'v-contextmenu';
import 'v-contextmenu/dist/index.css';

import './assets/css/lib.scss';

import DamCodeTree from './components/DamCodeTree';
import DamGraphWorkArea from './components/DamGraphWorkArea';
import DamLineCharts from './components/DamLineCharts';
import LineChart from './components/DamLineChart';

const components = [
  Dam<PERSON>ode<PERSON>ree,
  DamGraphWorkArea,
  DamLineCharts,
  LineChart
];

const install = function (Vue, opts = {}) {
  opts = Object.assign({
    baseUrl: '',
    headers: {},
    onRequest: () => {},
    localStorageLangKey: 'lang',
    localStorageLangValue: 'zh',
    i18n: null,
    i18nMessages: messages
  }, opts);
  components.forEach(component => {
    Vue.component(component.name, component);
  });

  Vue.use(ContextMenu);

  const lang = (localStorage && localStorage[opts.localStorageLangKey]) || opts.localStorageLangValue;
  Vue.use(VueI18n);
  const i18n = new VueI18n({
    locale: lang,
    messages: opts.i18nMessages
  });
  locale.use(opts.locale || (opts.i18nMessages[lang]));
  opts.i18n && locale.i18n(opts.i18n);
  // 解决vue实例在props中无法获取国际化方法的问题
  Vue.prototype.t = (key, value) => i18n.t(key, value);

  // element-ui
  Vue.prototype.$ELEMENT = {
    size: 'medium',
    i18n: (key, value) => i18n.t(key, value)
  };
  Vue.use(Aside);
  Vue.use(Button);
  Vue.use(ButtonGroup);
  Vue.use(Card);
  Vue.use(Checkbox);
  Vue.use(CheckboxGroup);
  Vue.use(Col);
  Vue.use(ColorPicker);
  Vue.use(Container);
  Vue.use(DatePicker);
  Vue.use(Dialog);
  Vue.use(Drawer);
  Vue.use(Form);
  Vue.use(FormItem);
  Vue.use(Header);
  Vue.use(Icon);
  Vue.use(Input);
  Vue.use(InputNumber);
  Vue.use(Loading.directive);
  Vue.use(Main);
  Vue.use(Option);
  Vue.use(RadioGroup);
  Vue.use(RadioButton);
  Vue.use(Row);
  Vue.use(Select);
  Vue.use(TabPane);
  Vue.use(Tabs);
  Vue.use(TimePicker);
  Vue.use(Tree);
  Vue.prototype.$loading = Loading.service;
  Vue.prototype.$confirm = MessageBox.confirm;
  Vue.prototype.$message = Message;

  // axios
  Vue.prototype.$OPTIONS = {
    baseUrl: opts.baseUrl,
    headers: opts.headers,
    onRequest: opts.onRequest
  };
  axios(Vue.prototype);
};
export default {
  locale: locale.use,
  i18n: locale.i18n,
  install,
  ...components
};
