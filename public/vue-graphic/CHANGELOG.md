# 更新日志

## v0.4.11

*2021-11-12*

#### 新特性

- 图幅导出区分Word和WPS


## v0.4.10

*2021-09-24*

#### 新特性

- 审核状态`异常、正常、未知`改为`未通过、通过、未审核`


## v0.4.9

*2021-08-11*

#### Bug修复

- 当图幅所有线条被删除时删除图幅


## v0.4.8

*2021-07-13*

#### Bug修复

- 修复柱状图
  - x偏移量不准确导致最后一个柱形被隐藏
  - 柱状图每个柱形的绘制起点应是0


## v0.4.7

*2021-06-16*

#### Bug修复

- 优化分布线对无效数据的处理（为确保图幅绘制准确，图幅内每个线条的点数量应相等）
- 优化自定义tooltip的显示


## v0.4.6

*2021-05-12*

#### 新特性

- 测值状态默认值去掉未知和缺失
- 过程线x轴tick-label提供class名以便覆盖样式
- 优化单控件event，其中了删除 optionChange，修改了deleteLine 为 lineDelete，deleteGraph 为 graphDelete，新增了 titleChange

#### Bug修复

- 带标题的图幅图例y值计算错误的问题
- 删除线条后未更新配置导致后续更新失效的问题


## v0.4.5

*2021-04-15*

#### 新特性

- 可对过程线手动设置X轴最大最小值(option.xAxis.max, option.xAxis.min)，类型和格式要与x轴数据一致

#### Bug修复

- tooltip宽度改为自适应标题宽度


## v0.4.4

*2021-03-11*

#### 新特性

- 可设置最小高度 minHeight 作为默认占高，仅当高度 height 为'auto'（默认）或小于 minHeight 时有效，默认为'228px'
- 可在图幅传入 maxYAxisCount 属性设置 图幅最多显示的y轴个数，默认为4


## v0.4.3

*2021-03-03*

#### 新特性

- 支持图幅宽高自适应，默认为自适应


## v0.4.2

*2021-02-24*

#### Bug修复

- 调整组件样式优先级，删除多余样式


## v0.4.1

*2021-01-07*

#### Bug修复

- LineChart
  - 修复柱状图中测值小于最小值时导出图幅失败的问题


## v0.4.0

*2021-01-07*

#### Bug修复

- LineChart
  - 修复图幅无数据时计算图例元宽度错误
  - 当线条无数据时不显示图例
- DamLineCharts
  - 优化图幅属性数字输入框


## v0.3.7

*2020-10-13*

#### 新特性

- 组件库支持传入onRequest参数作为axios请求前的拦截，headers参数则是作为axios请求的common headers


## v0.3.6

*2020-09-17*

#### 新特性

- 组件库支持传入headers参数作为axios服务的headers
- LineChart
  - 增加图幅属性、删除图幅、线条属性和删除线条右键菜单（editable设置为true时显示）
  - 过程线、分布线图幅配置新增坐标轴指示器(axisPointer)和标线(markLines)，默认不显示
  - 过程线图幅配置新增测值标记(series.valueMarker)，图幅根据测值状态显示不同颜色的标点(defines.valueMarkerColor，默认可不传)，默认不显示


## v0.3.5

*2020-08-05*

#### 新特性

- LineChart
  - 添加图幅的load事件回调
- DamLineCharts
  - 添加图幅的load事件回调


## v0.3.4

*2020-08-04*

#### 新特性

- LineChart
  - 可在过程线的图幅配置传入 xMinInterval 属性设置过程线最小缩放区间，默认为'hour'，可选项有：
    - 'minute' (分)
    - 'hour' (时)
    - 'day' (日)
    - 'week' (周)
    - 'tenDays' (旬)
    - 'month' (月)
    - 'quarter' (季度)
    - 'year' (年)
  - 优化图形缩放后Y轴刻度的显示
- 国际化添加老挝语

#### Bug修复

- DamGraphWorkArea
  - 删除图幅错误的问题
  - 删除线条后修改线条属性不生效的问题


## v0.3.3

*2020-07-08*

#### Bug修复

- LineChart
  - 上中下旬有时显示错误的问题
  - 实现图例自适应排版
- DamGraphWorkArea
  - 图形工作区-增加取值时间合法验证


## v0.3.2

*2020-06-29*

#### 新特性

- DamLineCharts
  - reloadAllGraph、reloadGraph方法支持传入"是否需要重新加载数据"

#### Bug修复

- DamLineCharts
  - 编辑标题后图幅出现异常的问题
- 解决依赖 [query-string@6.x](https://github.com/sindresorhus/query-string/issues/189) 和 [core-js](https://github.com/zloirock/core-js/issues/741) 在ie11不能正常运行


## v0.3.1

*2020-06-22*

#### 新特性

- LineChart
  - 过程线图幅设置 comparable 为 true 时显示 人工比测/取消人工比测 右键菜单，图幅配置传入 compared 属性表示当前是否为人工比测，在 LineChart 或 DamLineCharts 中通过捕获 comparedChange 事件进行后续操作
  - 图幅设置 replicable 为 false 时不显示复制图幅右键菜单
  - 添加数据点的点击事件回调，在单控件或多控件中通过捕获 dataPointClick 事件进行后续操作
  - 添加图幅缩放和返回事件回调，在单控件或多控件中通过捕获 scaleChange 事件进行后续操作
- DamLineCharts
  - 添加单个图幅配置 addGraphOption 方法支持从指定位置插入

#### Bug修复

- LineChart
  - 优化刻度线显示
  - 优化tooltip的显示和隐藏
  - 简化分布线配置，支持缩放
- DamLineCharts
  - 优化线条属性-取值时间、纵轴位置、删除线
- DamGraphWorkArea
  - 取值-单个分量每个图幅线的条数、埋设深度不生效的问题
  - 测点树-优化布局，同分量应共用一条轴，勾选测点后修改分量不生效
  - 优化打印排版


## v0.3.0-beta3

*2020-06-04*

#### 新特性

- 为不同项目提供更多国际化配置项

#### Bug修复

- 修复不使用国际化时显示中文错误的问题（为避免冲突，组件内部使用this.t作为国际化服务）
- 组件兼容ie11


## v0.3.0-beta2

*2020-05-26*

#### 新特性

- 更新国际化文件（西班牙语、法语、葡萄牙语），组件默认使用中文
- LineChart
  - 新增重置图幅方法 resetChart


## v0.3.0-beta

*2020-05-25*

#### 新特性

- 提供国际化支持（英语, 西班牙语, 法语, 葡萄牙语），新增国际化文档
- DamLineCharts
  - 新增重置图幅方法 resetAllGraph、resetGraph

#### Bug修复

- LineChart
  - 使用queryUrl方式绘制图幅，图幅缩小时补全线条
  - 修复图幅缩放后修改图幅导致图形绘制错误的问题


## v0.3.0-alpha3

*2020-05-14*

#### Bug修复

- LineChart
  - 完善过程线、相关线缩放和右键返回功能（分布线暂不支持缩放），避免图幅无限放大
  - 修复滚轮缩放和框选放大不互通的问题
  - 移除不应显示的网格线


## v0.3.0-alpha2

*2020-05-11*

#### Bug修复

- LineChart
  - 修复右键返回X轴定义域显示错误的问题
- DamGraphWorkArea
  - 优化工作区和测点树样式
  - 修复测点树懒加载选中组节点显示错误，分量显示错误的问题


## v0.3.0-alpha

*2020-04-30*

#### 新特性

- GraphWorkArea
  - 新增组件

#### Bug修复

- LineChart
  - 组件升级
- DamLineCharts
  - 组件升级
  - 美化图幅属性、线条属性


## v0.2.2

*2020-04-14*

#### Bug修复

- LineChart
  - 修复测值被保留小数的问题
  - 增大线条交互面积
  - Y坐标轴最大最小值计算不准确的问题


## v0.2.1

*2020-03-02*

#### Bug修复

- 删除无用css，减少可能会覆盖的样式
- 优化右击查看测值返回数据（组件需设置 queryable 属性，valueQuery 事件回调参数为{xMax,xMin,yMax,yMin,...value}）


## v0.2.0

*2020-01-14*

#### Bug修复

- 解决了内部axios服务影响到外部的问题（为避免冲突，组件内部使用this.$api作为axios服务）
- LineChart
  - 解决了线条在网格边界时显示不全的问题
  - 坐标轴小数位数默认为“自动”
  - 优化了坐标轴刻度显示
  - 简化图形配置，当传入queryUrl时可不指定线条名称和坐标轴文字


## v0.1.4-patch3

*2019-12-18*

#### Bug修复

- LineChart
  - 修改图形tooltip排版样式
  - 可设置是否显示导出操作（默认不显示）
  - 修复过程线放大后X轴显示错误
- DamLineCharts
  - 可设置是否显示导出操作（默认不显示）


## v0.1.4-patch2

*2019-12-17*

#### Bug修复

- LineChart
  - 优化图形细节
  - 优化过程线滚轮缩放功能
- 修改公共css样式权重


## v0.1.4-patch

*2019-12-16*

#### Bug修复

- LineChart
  - 修改线条颜色顺序（<img src="./src/assets/images/colors.png" alt="线条颜色顺序" style="zoom:67%;" />）


## v0.1.4

*2019-12-12*

#### Bug修复

- LineChart
  - 可设置是否显示“查看测值”（默认不显示）
- DamLineCharts
  - 可设置是否显示“查看测值”（默认不显示）


## v0.1.3

*2019-11-15*

#### Bug修复

- LineChart
  - 修复了相关线的一些显示问题
  - 修复了复制图幅功能兼容性问题
- DamLineCharts
  - 优化编辑线条和图幅


## v0.1.2

*2019-11-12*

#### Bug修复

- 优化CSS框架


## v0.1.1

*2019-11-08*

#### Bug修复

- DamLineCharts
  - 修复线条属性最大最小值错误
  - 更新线条属性、图幅属性设置界面


## v0.1.0

*2019-11-01*

#### 新特性

- 过程线、分布线、相关线基础功能
