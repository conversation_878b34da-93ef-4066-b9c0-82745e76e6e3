!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.WebControl=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();n(82),n(81),n(83);var s=n(144),a=r(s),u=n(159),c=n(29),l=function(){var e="　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　",t=300,n=function(){function n(e){i(this,n);var t=this,r={szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:15960,iServicePortEnd:15969,szClassId:"23BF3B0A-2C56-4D97-9C03-0CB103AA8F11"};this.oOptions=Object.assign(r,e),this.bFreeze=!1,this.bFocus=!0,this.bEmbed=c.oUtils.getCreateWndMode(),this.szWndId="",this.iCreateWndTimer=-1,this.iUpdateParentWndTimer=-1,this.bDevTool=!1,this.iVCTimeStart=-1,this.iVCTimeEnd=-1,this.oWndCover={left:0,top:0,right:0,bottom:0},this.oDocOffset={left:0,top:0},this.szTitle="",this.oWindowAttr={outerWidth:0,innerWidth:0,outerHeight:0,innerHeight:0,screenTop:0,screenLeft:0,screenX:0,screenY:0},this.iFixedResizeTimer=-1,this.fVisibilityChange=function(){if(c.oUtils.isMacOS())document.hidden?t.fHideWnd():t.fShowWnd();else if(document.hidden)t.iVCTimeStart=(new Date).getTime(),t.fHideWnd();else{t.iVCTimeEnd=(new Date).getTime();var e=c.oUtils.browser();e.chrome||e.mozilla?(t.iUpdateParentWndTimer>0&&(clearTimeout(t.iUpdateParentWndTimer),t.iUpdateParentWndTimer=-1),t.iVCTimeEnd-t.iVCTimeStart<100?t.iUpdateParentWndTimer=setTimeout(function(){t.oRequest.updateParentWnd().then(function(){t.bFreeze||t.bDevTool||t.fShowWnd()},function(){})},100):t.bFreeze||t.bDevTool||t.fShowWnd()):t.bFreeze||t.bDevTool||t.fShowWnd()}},this.fHideWnd=function(){t.oRequest.hideWnd().then(function(){},function(){})},this.fShowWnd=function(){t.oRequest.showWnd().then(function(){},function(){})},this.fFocus=function(){t.bFocus=!0,setTimeout(function(){t.removeGrabImage(),document.hidden||t.bFreeze||t.bDevTool||t.fShowWnd()},200)},this.fBlur=function(){t.bFocus=!1},this.removeGrabImage=function(){if(!c.oUtils.isMacOS()){var e=null;if(""!==t.szWndId&&(e=(0,a.default)("#"+t.szWndId),null!==e&&e.length>0)){var n=null;n=(0,a.default)('[data-name="wc-grab-open-image"]',e),null!==n&&n.length>0&&n.remove()}}},this.oRequest=new u.Request({szPluginContainer:this.oOptions.szPluginContainer,cbConnectSuccess:this.oOptions.cbConnectSuccess,cbConnectError:this.oOptions.cbConnectError,cbConnectClose:function(e){t.iCreateWndTimer>0&&(clearTimeout(t.iCreateWndTimer),t.iCreateWndTimer=-1),t.removeGrabImage(),t.oOptions.cbConnectClose&&t.oOptions.cbConnectClose(e)},iServicePortStart:this.oOptions.iServicePortStart,iServicePortEnd:this.oOptions.iServicePortEnd,szClassId:this.oOptions.szClassId})}return o(n,[{key:"JS_SetWindowControlCallback",value:function(n){var r=this,i={cbSelectWnd:function(e){n.cbSelectWnd&&n.cbSelectWnd(parseInt(e.wndIndex,10),e.cameraID,e.siteID,e.opendFisheye)},cbTogglePTZ:function(e){n.cbTogglePTZ&&n.cbTogglePTZ(e.cameraID,e.siteID)},cbUpdateCameraIcon:function(e){n.cbUpdateCameraIcon&&n.cbUpdateCameraIcon(e.cameraID,parseInt(e.playing,10),e.siteID)},cbGetLastError:function(e){n.cbGetLastError&&n.cbGetLastError(e.error,parseInt(e.type,10))},cbTalkUrlEmpty:function(e){n.cbTalkUrlEmpty&&n.cbTalkUrlEmpty(e.cameraID)},cbGotoPlayback:function(e){n.cbGotoPlayback&&n.cbGotoPlayback(e.cameraID,e.siteID)},cbShowDisplayInfo:function(e){n.cbShowDisplayInfo&&n.cbShowDisplayInfo(parseInt(e.videoWidth,10),parseInt(e.videoHeight,10),parseInt(e.frameRate,10))},cbPreviewWnd3DPostion:function(e){n.cbPreviewWnd3DPostion&&n.cbPreviewWnd3DPostion(parseInt(e.startX,10),parseInt(e.startY,10),parseInt(e.endX,10),parseInt(e.endY,10))},cbStopPlayAll:function(){n.cbStopPlayAll&&n.cbStopPlayAll()},cbWheelEvent:function(e){n.cbWheelEvent&&n.cbWheelEvent(parseInt(e.delta,10))},cbAlarmDetail:function(e){n.cbAlarmDetail&&n.cbAlarmDetail(e.alarmId)},cbQuitedFullScreen:function(){setTimeout(function(){r.fShowWnd()},100)},cbManuallyClose:function(e){n.cbManuallyClose&&n.cbManuallyClose(e.cameraID,e.siteID,parseInt(e.wndIndex,10))},cbIntegrationCallBack:function(e){n.cbIntegrationCallBack&&n.cbIntegrationCallBack(e)},cbChangeStorage:function(e){n.cbChangeStorage&&n.cbChangeStorage(parseInt(e.storageType,10),e.cameraID,e.siteID)},cbFisheyeExpandChanged:function(e){n.cbFisheyeExpandChanged&&n.cbFisheyeExpandChanged(e.cameraID,e.siteID,parseInt(e.wndIndex,10),e.open)},cbGetEhomePlayInfo:function(e){n.cbGetEhomePlayInfo&&n.cbGetEhomePlayInfo(e.siteID,e.guid)},cbWndPtzControl:function(e){n.cbWndPtzControl&&n.cbWndPtzControl(parseInt(e.wndIndex,10),e.cameraID,e.command,e.speed,e.stop)},cbMessageCallBack:function(i){i=i.data,"menuOpen"===i.type?""!==r.szWndId&&(0,a.default)("#"+r.szWndId).html("<img data-name='wc-grab-open-image' src='data:image/png;base64,"+i.message.image+"' width='100%' height='100%' />"):"changeTitle"===i.type?-1===document.title.indexOf(r.oRequest.getRequestUUID())&&(r.szTitle=document.title,document.title=r.szTitle+e+r.oRequest.getRequestUUID(),setTimeout(function(){"updateParentWnd"===i.message?r.oRequest.updateParentWnd():"restoreWnd"===i.message&&r.oRequest.restoreWnd()},t)):"changeTitleDone"===i.type?""!==r.szTitle&&(document.title=r.szTitle):"splitChange"===i.type?n.cbSplitChange&&n.cbSplitChange(i.message.splitType):"showMaximized"===i.type&&n.cbShowMaximized&&n.cbShowMaximized(i.message.showMax)}};this.oRequest.setWindowControlCallback(i)}},{key:"JS_SetSadpCallback",value:function(e){var t={cbDeviceFind:null};Object.assign(t,e),this.oRequest.setSadpCallback(t)}},{key:"JS_SetSliceCallback",value:function(e){var t={cbImageSliced:function(t){e.cbImageSliced&&(""!==t.picName&&(t.picName=c.oUtils.Base64().decode(t.picName)),e.cbImageSliced(t))}};this.oRequest.setSliceCallback(t)}},{key:"JS_SetSerialCallback",value:function(e){var t={cbCardFind:function(t){e.cbCardFind&&e.cbCardFind(t)},cbFingerFind:function(t){e.cbFingerFind&&e.cbFingerFind(t.fingerPrint,t.fingerQuality)},cbImageFind:function(t){e.cbImageFind&&e.cbImageFind(t.image)},cbImageErrorFind:function(t){e.cbImageErrorFind&&e.cbImageErrorFind(t.errorModule,t.errorCode)},cbImageWndVisibleFind:function(t){e.cbImageWndVisibleFind&&e.cbImageWndVisibleFind(t.visible)}};this.oRequest.setSerialCallback(t)}},{key:"JS_SetUIControlCallback",value:function(e){var t={cbClickMenuItem:function(t){e.cbClickMenuItem&&e.cbClickMenuItem(t.itemIndex)},cbMenuMouseIn:function(){e.cbMenuMouseIn&&e.cbMenuMouseIn()},cbMenuMouseOut:function(){e.cbMenuMouseOut&&e.cbMenuMouseOut()}};this.oRequest.setUIControlCallback(t)}},{key:"JS_SetUpgradeCallback",value:function(e){var t={cbCheckUpgrade:function(t){e.cbCheckUpgrade&&e.cbCheckUpgrade(t)}};this.oRequest.setUpgradeCallback(t)}},{key:"JS_CheckVersion",value:function(e){var t=this.oRequest.getServiceVersion(),n=[],r=[];""!==t&&(t=t.replace(/,[\s]*/g,"."),n=t.split(".")),""!==e&&(e=e.replace(/,[\s]*/g,"."),r=e.split("."));var i=!1;if(r.length===n.length)for(var o=0,s=n.length;o<s;o++)if(parseInt(r[o],10)!==parseInt(n[o],10)){if(parseInt(r[o],10)>parseInt(n[o],10)){i=!0;break}break}return i}},{key:"JS_StartService",value:function(e,t){return this.oRequest.startService(e,t)}},{key:"JS_StopService",value:function(e){return this.oRequest.stopService(e)}},{key:"JS_Disconnect",value:function(){return this.oRequest.disconnect()}},{key:"JS_OpenDirectory",value:function(e){return this.oRequest.openDirectory(e)}},{key:"JS_OpenFile",value:function(e,t,n){return this.oRequest.openFile(e,t,n)}},{key:"JS_SelectDirectory",value:function(e,t){return this.oRequest.selectDirectory(e,t)}},{key:"JS_SelectFile",value:function(e,t,n){return this.oRequest.selectFile(e,t,n)}},{key:"JS_GetLocalConfig",value:function(e){return this.oRequest.getLocalConfig(e)}},{key:"JS_SetLocalConfig",value:function(e){return this.oRequest.setLocalConfig(e)}},{key:"JS_SetDocOffset",value:function(e){return e&&(this.oDocOffset=e),!0}},{key:"JS_SetWindowAttr",value:function(e){return e&&(this.oWindowAttr=e),!0}},{key:"JS_CreateWnd",value:function(n,r,i,o){var s=this;this.szWndId=n,o=o||{},"undefined"!=typeof o.bEmbed&&(this.bEmbed=o.bEmbed);var u=!0;"undefined"!=typeof o.bActiveXParentWnd&&(u=o.bActiveXParentWnd);var l=new Promise(function(l,f){var d=(0,a.default)("#"+n);if(d.length>0){var h=null,p="",v=s.oRequest.getRequestUUID();"undefined"==typeof o.cbSetDocTitle?(h=window.top,p=h.document.title,h.document.title=p+e+v):o.cbSetDocTitle(v),s.iCreateWndTimer=setTimeout(function(){var e="";if(c.oUtils.browser().msie?e="IEFrame":c.oUtils.browser().chrome?e="Chrome":c.oUtils.browser().safari&&(e=p),!s.bDevTool){var t=c.oUtils.getDevicePixelRatio(),n=c.oUtils.getWndPostion(d,s.bEmbed,s.oWindowAttr);n.left+=Math.round(s.oDocOffset.left*t),n.top+=Math.round(s.oDocOffset.top*t),r=Math.round(r*t),i=Math.round(i*t),s.oRequest.createWnd(n.left,n.top,r,i,e,s.bEmbed,u).then(function(){"undefined"==typeof o.cbSetDocTitle&&(h.document.title=p),l()},function(e){"undefined"==typeof o.cbSetDocTitle&&(h.document.title=p),5001===e.errorCode?(document.hidden||s.bFreeze||!s.bFocus||s.fShowWnd(),l()):f(e)})}},t),document.addEventListener("visibilitychange",s.fVisibilityChange,!1),(0,a.default)(window).on("focus",s.fFocus),(0,a.default)(window).on("blur",s.fBlur)}else f()});return l}},{key:"JS_ShowWnd",value:function(){this.bFreeze=!1,document.hidden||this.bDevTool||this.fShowWnd()}},{key:"JS_HideWnd",value:function(){this.bFreeze=!0,this.fHideWnd()}},{key:"JS_DestroyWnd",value:function(){return document.removeEventListener("visibilitychange",this.fVisibilityChange,!1),(0,a.default)(window).off("focus",this.fFocus),(0,a.default)(window).off("blur",this.fBlur),this.oRequest.destroyWnd()}},{key:"JS_Resize",value:function(e,n,r){var i=this,o=null,s=e,u=n;if(""!==this.szWndId&&(o=(0,a.default)("#"+this.szWndId)),null!==o&&o.length>0){var l=c.oUtils.getWndPostion(o,this.bEmbed,this.oWindowAttr),f=c.oUtils.getDevicePixelRatio();l.left+=Math.round(this.oDocOffset.left*f),l.top+=Math.round(this.oDocOffset.top*f),(!c.oUtils.browser().msie||c.oUtils.browser().msie&&"11.0"===c.oUtils.browser().version)&&(this.oWndCover.left>0&&(l.left=l.left+Math.round(this.oWndCover.left*f),e-=this.oWndCover.left),this.oWndCover.top>0&&(l.top=l.top+Math.round(this.oWndCover.top*f),n-=this.oWndCover.top),this.oWndCover.right>0&&(e-=this.oWndCover.right),this.oWndCover.bottom>0&&(n-=this.oWndCover.bottom)),e=Math.round(e*f),n=Math.round(n*f),this.oRequest.setWndGeometry(l.left,l.top,e,n),c.oUtils.browser().msie&&"11.0"===c.oUtils.browser().version&&(r&&r.bFixed?this.iFixedResizeTimer=-1:(this.iFixedResizeTimer>-1&&(clearTimeout(this.iFixedResizeTimer),this.iFixedResizeTimer=-1),this.iFixedResizeTimer=setTimeout(function(){i.JS_Resize(s,u,{bFixed:!0})},t)))}}},{key:"JS_SetWndCover",value:function(e,t){var n=c.oUtils.getDevicePixelRatio();return(!c.oUtils.browser().msie||c.oUtils.browser().msie&&"11.0"===c.oUtils.browser().version)&&("left"===e?this.oWndCover.left=t:"top"===e?this.oWndCover.top=t:"right"===e?this.oWndCover.right=t:"bottom"===e&&(this.oWndCover.bottom=t)),t=Math.round(t*n),this.oRequest.setWndCover(e,t)}},{key:"JS_CuttingPartWindow",value:function(e,t,n,r,i){var o=c.oUtils.getDevicePixelRatio();return e=Math.round(e*o),t=Math.round(t*o),n=Math.round(n*o),r=Math.round(r*o),i=Math.round(i*o),this.oRequest.cuttingPartWindow(e,t,n,r,i)}},{key:"JS_RepairPartWindow",value:function(e,t,n,r,i){var o=c.oUtils.getDevicePixelRatio();return e=Math.round(e*o),t=Math.round(t*o),n=Math.round(n*o),r=Math.round(r*o),i=Math.round(i*o),this.oRequest.repairPartWindow(e,t,n,r,i)}},{key:"JS_ChangePlayMode",value:function(e){return this.oRequest.changePlayMode(e)}},{key:"JS_SetLanguageType",value:function(e){return this.oRequest.setLanguageType(e)}},{key:"JS_InitLoginInfo",value:function(e){return this.oRequest.initLoginInfo(e)}},{key:"JS_SetTranslateFile",value:function(e){return this.oRequest.setTranslateFile(e)}},{key:"JS_SwitchToSimple",value:function(e){return this.oRequest.switchToSimple(e)}},{key:"JS_SetVsmToken",value:function(e){return this.oRequest.setVsmToken(e)}},{key:"JS_Play",value:function(e,t,n,r,i,o,s,a,u){return this.oRequest.startPlay(e,t,n,r,i,o,s,a,u)}},{key:"JS_Enable3DZoom",value:function(e){return this.oRequest.setPreview3DPosition(e)}},{key:"JS_StopTotal",value:function(){return this.oRequest.stopTotal()}},{key:"JS_SetDragMode",value:function(e){return this.oRequest.setDragMode(e)}},{key:"JS_ShowErrorInfoInFullScreen",value:function(e){return this.oRequest.showErrorInfoInFullScreen(e)}},{key:"JS_SetNumberOfWindows",value:function(e){return this.oRequest.setNumberOfWindows(e)}},{key:"JS_InitCardReader",value:function(e){return this.oRequest.initCardReader(e)}},{key:"JS_UnInitCardReader",value:function(){return this.oRequest.unInitCardReader()}},{key:"JS_StartAutoMode",value:function(){return this.oRequest.startAutoMode()}},{key:"JS_StopAutoMode",value:function(){return this.oRequest.stopAutoMode()}},{key:"JS_InitFingerprint",value:function(e){return this.oRequest.initFingerprint(e)}},{key:"JS_UnInitFingerprint",value:function(){return this.oRequest.unInitFingerprint()}},{key:"JS_StartCollectFingerprint",value:function(){return this.oRequest.startCollectFingerprint()}},{key:"JS_StopCollectFingerprint",value:function(){return this.oRequest.stopCollectFingerprint()}},{key:"JS_IsCollectingFingerprint",value:function(){return this.oRequest.isCollectingFingerprint()}},{key:"JS_InitVideocapture",value:function(e){return this.oRequest.initVideocapture(e)}},{key:"JS_UnInitVideocapture",value:function(){return this.oRequest.unInitVideocapture()}},{key:"JS_RegisterDeviceType",value:function(e){return this.oRequest.registerDeviceType(e)}},{key:"JS_ActiveOnlineDevice",value:function(e,t){return this.oRequest.activeOnlineDevice(e,t)}},{key:"JS_RefreshDeviceList",value:function(){return this.oRequest.refreshDeviceList()}},{key:"JS_ModifyDeviceNetParam",value:function(e,t,n,r,i,o,s){return this.oRequest.modifyDeviceNetParam(e,t,n,r,i,o,s)}},{key:"JS_ExportKeyFile",value:function(e){return this.oRequest.exportKeyFile(e)}},{key:"JS_ImportKeyFile",value:function(){return this.oRequest.importKeyFile()}},{key:"JS_ResetPassword",value:function(e,t,n,r){return this.oRequest.resetPassword(e,t,n,r)}},{key:"JS_UploadPicture",value:function(e){return this.oRequest.uploadPicture(e)}},{key:"JS_ShowSelectMenu",value:function(e,t,n,r,i){var o=(0,a.default)("#"+e);if(o.length>0){var s=c.oUtils.getWndPostion(o,!1,this.oWindowAttr);"center"===i?s.left=s.left-Math.round((t-o.outerWidth())/2):"right"===i&&(s.left=s.left-Math.round(t-o.outerWidth()));var u=c.oUtils.getDevicePixelRatio();t=Math.round(t*u),n=Math.round(n*u);var l=Math.round(o.height()*u);this.oRequest.showSelectMenu(s.left,s.top+l,t,n,r)}}},{key:"JS_HideSelectMenu",value:function(){this.oRequest.hideSelectMenu()}},{key:"JS_DestroySelectMenu",value:function(){return this.oRequest.destroySelectMenu()}},{key:"JS_DeviceConfig",value:function(e){return this.oRequest.deviceConfig(e)}},{key:"JS_CloudStorageConfig",value:function(e){return this.oRequest.cloudStorageConfig(e)}},{key:"JS_EzvizRemoteConfig",value:function(e){return this.oRequest.ezvizRemoteConfig(e)}},{key:"JS_ShowAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.showAlarmInfoInFullScreen(e,t,n)}},{key:"JS_SetImmediatePlaybackTime",value:function(e){return this.oRequest.setImmediatePlaybackTime(e)}},{key:"JS_SetDrawStatus",value:function(e){return this.oRequest.setDrawStatus(e)}},{key:"JS_ClearRegion",value:function(){return this.oRequest.clearRegion()}},{key:"JS_SetDrawShapeInfo",value:function(e,t){return this.oRequest.setDrawShapeInfo(e,t)}},{key:"JS_SetGridInfo",value:function(e){return this.oRequest.setGridInfo(e)}},{key:"JS_GetGridInfo",value:function(){return this.oRequest.getGridInfo()}},{key:"JS_SetPolygonInfo",value:function(e){return this.oRequest.setPolygonInfo(e)}},{key:"JS_GetPolygonInfo",value:function(){return this.oRequest.getPolygonInfo()}},{key:"JS_SetLineInfo",value:function(e){return this.oRequest.setLineInfo(e)}},{key:"JS_GetLineInfo",value:function(){return this.oRequest.getLineInfo()}},{key:"JS_SetRectInfo",value:function(e){return this.oRequest.setRectInfo(e)}},{key:"JS_GetRectInfo",value:function(){return this.oRequest.getRectInfo()}},{key:"JS_ClearShapeByType",value:function(e){return this.oRequest.clearShapeByType(e)}},{key:"JS_SensitiveEncrypt",value:function(e,t,n){return this.oRequest.sensitiveEncrypt(e,t,n)}},{key:"JS_SendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"JS_RequestInterface",value:function(e){return this.oRequest.requestInterface(e)}},{key:"JS_StopPlay",value:function(e){return this.oRequest.stopPlay(e)}},{key:"JS_ShowRemoteConfig",value:function(e){return this.oRequest.showRemoteConfig(e)}},{key:"JS_Video2Picture",value:function(){return this.oRequest.video2Picture()}},{key:"JS_Picture2Video",value:function(){return this.oRequest.picture2Video()}},{key:"JS_PtzControl",value:function(e){return this.oRequest.ptzControl(e)}},{key:"JS_SimMouseClickEvent",value:function(e,t){return this.oRequest.simMouseClickEvent(e,t)}},{key:"JS_US_SetMaxJobCount",value:function(e){return this.oRequest.us_SetMaxJobCount(e)}},{key:"JS_US_GetMaxJobCount",value:function(){return this.oRequest.us_GetMaxJobCount()}},{key:"JS_US_AddSchedule",value:function(e){return this.oRequest.us_AddSchedule(e)}},{key:"JS_US_DelSchedule",value:function(e){return this.oRequest.us_DelSchedule(e)}},{key:"JS_US_GetScheduleList",value:function(e){return this.oRequest.us_GetScheduleList(e)}},{key:"JS_US_GetSchedule",value:function(e,t){return this.oRequest.us_GetSchedule(e,t)}},{key:"JS_US_UpgradeAction",value:function(e,t){return this.oRequest.us_UpgradeAction(e,t)}},{key:"JS_US_CheckUpgradeableDevice",value:function(e){return this.oRequest.us_CheckUpgradeableDevice(e)}},{key:"JS_US_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.us_CheckUpgradeableDeviceList(e)}},{key:"JS_US_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.us_IsRunningAsyCheckUpgradeable()}},{key:"JS_US_StopAsyCheckUpgradeable",value:function(){return this.oRequest.us_StopAsyCheckUpgradeable()}},{key:"JS_GetFishEyePTZPreset",value:function(e){return this.oRequest.getFishEyePTZPreset(e)}},{key:"JS_SetFishEyePTZPreset",value:function(e,t,n){return this.oRequest.setFishEyePTZPreset(e,t,n)}},{key:"JS_ControlFishEyePTZ",value:function(e,t,n,r){return this.oRequest.controlFishEyePTZ(e,t,n,r)}},{key:"JS_ControlFishEyeParol",value:function(e,t,n){return this.oRequest.controlFishEyeParol(e,t,n)}},{key:"JS_SetFirstDayOfWeek",value:function(e){return this.oRequest.setFirstDayOfWeek(e)}},{key:"JS_SetEhomePlayInfo",value:function(e,t,n,r,i,o){return this.oRequest.setEhomePlayInfo(e,t,n,r,i,o)}},{key:"JS_PlayPatch",value:function(e){return this.oRequest.startPlayPatch(e)}},{key:"JS_SetWndAutoPanState",value:function(e,t){return this.oRequest.setWndAutoPanState(e,t)}},{key:"JS_EnablePrivileges",value:function(){return this.oRequest.enablePrivileges()}}],[{key:"JS_WakeUp",value:function(e){var t=document.createElement("iframe");t.style.display="none",t.src=e,document.body.appendChild(t),setTimeout(function(){document.body.removeChild(t)},3e3)}}]),n}();return n}();t.WebControl=l},function(e,t,n){"use strict";var r=n(4),i=n(10),o=n(19),s=n(23),a=n(18),u="prototype",c=function e(t,n,c){var l,f,d,h,p=t&e.F,v=t&e.G,g=t&e.S,y=t&e.P,m=t&e.B,b=v?r:g?r[n]||(r[n]={}):(r[n]||{})[u],w=v?i:i[n]||(i[n]={}),_=w[u]||(w[u]={});v&&(c=n);for(l in c)f=!p&&b&&void 0!==b[l],d=(f?b:c)[l],h=m&&f?a(d,r):y&&"function"==typeof d?a(Function.call,d):d,b&&s(b,l,d,t&e.U),w[l]!=d&&o(w,l,h),y&&_[l]!=d&&(_[l]=d)};r.core=i,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,n){"use strict";var r=n(46)("wks"),i=n(28),o=n(4).Symbol,s="function"==typeof o,a=e.exports=function(e){return r[e]||(r[e]=s&&o[e]||(s?o:i)("Symbol."+e))};a.store=r},function(e,t){"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};e.exports=function(e){return"object"===("undefined"==typeof e?"undefined":n(e))?null!==e:"function"==typeof e}},function(e,t){"use strict";var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,n){"use strict";var r=n(6);e.exports=function(e,t){return!!e&&r(function(){t?e.call(null,function(){},1):e.call(null)})}},function(e,t){"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,n){"use strict";var r=n(1),i=n(10),o=n(6);e.exports=function(e,t){var n=(i.Object||{})[e]||Object[e],s={};s[e]=t(n),r(r.S+r.F*o(function(){n(1)}),"Object",s)}},function(e,t,n){"use strict";var r=n(37);e.exports=function(e){return Object(r(e))}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t){"use strict";var n=e.exports={version:"2.5.1"};"number"==typeof __e&&(__e=n)},function(e,t,n){"use strict";e.exports=!n(6)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,n){"use strict";var r=n(9),i=n(53),o=n(47),s=Object.defineProperty;t.f=n(11)?Object.defineProperty:function(e,t,n){if(r(e),t=o(t,!0),r(n),i)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){"use strict";var r=n(25),i=n(37);e.exports=function(e){return r(i(e))}},function(e,t,n){"use strict";var r=n(35),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},function(e,t){"use strict";var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;t.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var i=t.shift();if(i){if("object"!==("undefined"==typeof i?"undefined":r(i)))throw new TypeError(i+"must be non-object");for(var o in i)n(i,o)&&(e[o]=i[o])}}return e},t.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var o={arraySet:function(e,t,n,r,i){if(t.subarray&&e.subarray)return void e.set(t.subarray(n,n+r),i);for(var o=0;o<r;o++)e[i+o]=t[n+o]},flattenChunks:function(e){var t,n,r,i,o,s;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(s=new Uint8Array(r),i=0,t=0,n=e.length;t<n;t++)o=e[t],s.set(o,i),i+=o.length;return s}},s={arraySet:function(e,t,n,r,i){for(var o=0;o<r;o++)e[i+o]=t[n+o]},flattenChunks:function(e){return[].concat.apply([],e)}};t.setTyped=function(e){e?(t.Buf8=Uint8Array,t.Buf16=Uint16Array,t.Buf32=Int32Array,t.assign(t,o)):(t.Buf8=Array,t.Buf16=Array,t.Buf32=Array,t.assign(t,s))},t.setTyped(i)},function(e,t,n){"use strict";var r=n(18),i=n(25),o=n(8),s=n(14),a=n(88);e.exports=function(e,t){var n=1==e,u=2==e,c=3==e,l=4==e,f=6==e,d=5==e||f,h=t||a;return function(t,a,p){for(var v,g,y=o(t),m=i(y),b=r(a,p,3),w=s(m.length),_=0,k=n?h(t,w):u?h(t,0):void 0;w>_;_++)if((d||_ in m)&&(v=m[_],g=b(v,_,y),e))if(n)k[_]=g;else if(g)switch(e){case 3:return!0;case 5:return v;case 6:return _;case 2:k.push(v)}else if(l)return!1;return f?-1:c||l?l:k}}},function(e,t,n){"use strict";var r=n(20);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}}},function(e,t,n){"use strict";var r=n(12),i=n(27);e.exports=n(11)?function(e,t,n){return r.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t){"use strict";e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){"use strict";var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,n){"use strict";var r=n(63),i=n(39);e.exports=Object.keys||function(e){return r(e,i)}},function(e,t,n){"use strict";var r=n(4),i=n(19),o=n(15),s=n(28)("src"),a="toString",u=Function[a],c=(""+u).split(a);n(10).inspectSource=function(e){return u.call(e)},(e.exports=function(e,t,n,a){var u="function"==typeof n;u&&(o(n,"name")||i(n,"name",t)),e[t]!==n&&(u&&(o(n,s)||i(n,s,e[t]?""+e[t]:c.join(String(t)))),e===r?e[t]=n:a?e[t]?e[t]=n:i(e,t,n):(delete e[t],i(e,t,n)))})(Function.prototype,a,function(){return"function"==typeof this&&this[s]||u.call(this)})},function(e,t,n){"use strict";var r=n(2)("unscopables"),i=Array.prototype;void 0==i[r]&&n(19)(i,r,{}),e.exports=function(e){i[r][e]=!0}},function(e,t,n){"use strict";var r=n(21);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t){"use strict";e.exports={}},function(e,t){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){"use strict";var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+r).toString(36))}},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.oUtils=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(145),s=function(){function e(){r(this,e),this.oBase64=o.Base64.noConflict()}return i(e,[{key:"browser",value:function(){var e=/(edge)[\/]([\w.]+)/,t=/(chrome)[\/]([\w.]+)/,n=/(safari)[\/]([\w.]+)/,r=/(opera)(?:.*version)?[\/]([\w.]+)/,i=/(msie) ([\w.]+)/,o=/(trident.*rv:)([\w.]+)/,s=/(mozilla)(?:.*? rv:([\w.]+))?/,a=navigator.userAgent.toLowerCase(),u=e.exec(a)||t.exec(a)||n.exec(a)||r.exec(a)||i.exec(a)||o.exec(a)||a.indexOf("compatible")<0&&s.exec(a)||["unknow","0"];u.length>0&&u[1].indexOf("trident")>-1&&(u[1]="msie");var c={};return c[u[1]]=!0,c.version=u[2],c}},{key:"getCreateWndMode",value:function(){var e=navigator.userAgent,t=navigator.platform,n="Win64"===t||"Win32"===t||"Windows"===t,r=this.browser(),i=!0;return window.top!==window?i=!1:n?(e.indexOf("Windows NT 10.0")>-1&&r.chrome&&(i=!1),r.edge&&(i=!1)):i=!1,i}},{key:"getWndPostion",value:function(e,t,n){var r=0,i=0,o=e.offset(),s=this.getDevicePixelRatio(),a=parseInt(e.css("border-left-width"),10),u=parseInt(e.css("border-top-width"),10);if(t)if(this.browser().msie){var c=window.outerWidth-window.innerWidth-(window.screenLeft-window.screenX);r=o.left+(window.screenLeft-window.screenX)+a-c,i=o.top+(window.screenTop-window.screenY)+u}else{var l=0,f=0,d=Math.round((window.outerWidth-window.innerWidth)/2);this.browser().chrome&&(-8===d||0===window.screen.height-window.outerHeight?-8===d&&(l=8,f=8):8===d?l=-5:0===d&&(f=8)),this.browser().mozilla&&(7===d||6===d?l=-6:8===d&&(l=-8)),r=o.left+d+a+l,i=o.top+(window.outerHeight-window.innerHeight-d)+u+f}else{var h=window.top,p=0,v=0,g=0,y=0;try{p=h.outerWidth-h.innerWidth,v=h.outerHeight-h.innerHeight,g=h.screenLeft-h.screenX,y=h.screenTop-h.screenY}catch(e){p=n.outerWidth-n.innerWidth,v=n.outerHeight-n.innerHeight,g=n.screenLeft-n.screenX,y=n.screenTop-n.screenY}if(this.browser().msie){var m=p-g;m=0,r=o.left+g+a-m,i=o.top+y+u}else{var b=p/2;r=o.left+b+a,i=o.top+(v-b)+u,this.browser().chrome&&0===b&&(r+=8,i+=8)}}(this.browser().chrome||this.browser().safari)&&(r=o.left+a,i=o.top+u);var w=0,_=0;return(!this.browser().msie||this.browser().msie&&"11.0"===this.browser().version)&&(w=window.scrollX||window.pageXOffset,_=window.scrollY||window.pageYOffset),r=Math.round((r-w)*s),i=Math.round((i-_)*s),{left:r,top:i}}},{key:"detectPort",value:function(e,t,n){var r=this,i=0,o=!1,s=null;sessionStorage&&(s=sessionStorage.getItem("HikCentralWebControlPort"),null!==s&&(s=parseInt(s,10)));for(var a=[],u=e;u<=t;u++)u!==s&&a.push(u);null!==s&&a.unshift(s);for(var c=0,l=function(){c++,a.length===c&&!o&&n.error&&(d(),h(),n.error())},f=function(e){sessionStorage&&sessionStorage.setItem("HikCentralWebControlPort",e),!o&&n.success&&(d(),h(),n.success(e))},d=function(){i>0&&clearTimeout(i)},h=function(){for(var e=0,t=p.length;e<t;e++)delete p[e]},p=[],v=(new Date).getTime(),g=function(e,t){setTimeout(function(){p.push(r.createImageHttp(a[t],{timeStamp:v+t,success:function(e){f(e)},error:function(){l()}}))},100)},y=0,m=a.length;y<m;y++)g(m,y);i=setTimeout(function(){o=!0,n.error&&(h(),n.error())},6e4)}},{key:"createImageHttp",value:function(e,t){var n=new Image;return n.onload=function(){t.success&&t.success(e)},n.onerror=function(){t.error&&t.error()},n.onabort=function(){t.abort&&t.abort()},n.src="http://127.0.0.1:"+e+"/imghttp/local?update="+t.timeStamp,n}},{key:"utf8to16",value:function(e){var t="",n=0,r=void 0,i=void 0,o=void 0,s=void 0;for(r=e.length;n<r;)switch(i=e.charCodeAt(n++),i>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:t+=e.charAt(n-1);break;case 12:case 13:o=e.charCodeAt(n++),t+=String.fromCharCode((31&i)<<6|63&o);break;case 14:o=e.charCodeAt(n++),s=e.charCodeAt(n++),t+=String.fromCharCode((15&i)<<12|(63&o)<<6|(63&s)<<0)}return t}},{key:"createEventScript",value:function(e,t,n){var r=document.createElement("script");r.htmlFor=e,r.event=t,r.innerHTML=n,document.getElementById(e).appendChild(r)}},{key:"isMacOS",value:function(){var e=navigator.platform;return"MacIntel"===e}},{key:"getDevicePixelRatio",value:function(){var e=this,t=1;return e.isMacOS()||(t=window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI),t}},{key:"Base64",value:function(){return this.oBase64||{}}}]),e}();t.oUtils=new s},function(e,t){"use strict";e.exports=!1},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=n(28)("meta"),o=n(3),s=n(15),a=n(12).f,u=0,c=Object.isExtensible||function(){return!0},l=!n(6)(function(){return c(Object.preventExtensions({}))}),f=function(e){a(e,i,{value:{i:"O"+ ++u,w:{}}})},d=function(e,t){if(!o(e))return"symbol"==("undefined"==typeof e?"undefined":r(e))?e:("string"==typeof e?"S":"P")+e;if(!s(e,i)){if(!c(e))return"F";if(!t)return"E";f(e)}return e[i].i},h=function(e,t){if(!s(e,i)){if(!c(e))return!0;if(!t)return!1;f(e)}return e[i].w},p=function(e){return l&&v.NEED&&c(e)&&!s(e,i)&&f(e),e},v=e.exports={KEY:i,NEED:!1,fastKey:d,getWeak:h,onFreeze:p}},function(e,t){"use strict";t.f={}.propertyIsEnumerable},function(e,t,n){"use strict";var r=n(12).f,i=n(15),o=n(2)("toStringTag");e.exports=function(e,t,n){e&&!i(e=n?e:e.prototype,o)&&r(e,o,{configurable:!0,value:t})}},function(e,t,n){"use strict";var r=n(35),i=Math.max,o=Math.min;e.exports=function(e,t){return e=r(e),e<0?i(e+t,0):o(e,t);
}},function(e,t){"use strict";var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t,n){"use strict";var r=n(21),i=n(2)("toStringTag"),o="Arguments"==r(function(){return arguments}()),s=function(e,t){try{return e[t]}catch(e){}};e.exports=function(e){var t,n,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=s(t=Object(e),i))?n:o?r(t):"Object"==(a=r(t))&&"function"==typeof t.callee?"Arguments":a}},function(e,t){"use strict";e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){"use strict";var r=n(3),i=n(4).document,o=r(i)&&r(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t){"use strict";e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){"use strict";var r=n(4).document;e.exports=r&&r.documentElement},function(e,t,n){"use strict";var r=n(21);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){"use strict";var r=n(9),i=n(59),o=n(39),s=n(45)("IE_PROTO"),a=function(){},u="prototype",c=function(){var e,t=n(38)("iframe"),r=o.length,i="<",s=">";for(t.style.display="none",n(40).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write(i+"script"+s+"document.F=Object"+i+"/script"+s),e.close(),c=e.F;r--;)delete c[u][o[r]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(a[u]=r(e),n=new a,a[u]=null,n[s]=e):n=c(),void 0===t?n:i(n,t)}},function(e,t,n){"use strict";var r=n(32),i=n(27),o=n(13),s=n(47),a=n(15),u=n(53),c=Object.getOwnPropertyDescriptor;t.f=n(11)?c:function(e,t){if(e=o(e),t=s(t,!0),u)try{return c(e,t)}catch(e){}if(a(e,t))return i(!r.f.call(e,t),e[t])}},function(e,t){"use strict";t.f=Object.getOwnPropertySymbols},function(e,t,n){"use strict";var r=n(46)("keys"),i=n(28);e.exports=function(e){return r[e]||(r[e]=i(e))}},function(e,t,n){"use strict";var r=n(4),i="__core-js_shared__",o=r[i]||(r[i]={});e.exports=function(e){return o[e]||(o[e]={})}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e,t){if(!r(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!r(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},function(e,t,n){"use strict";var r=n(155),i=n(156),o=i;o.v1=r,o.v4=i,e.exports=o},function(e,t,n){"use strict";var r=n(13),i=n(14),o=n(34);e.exports=function(e){return function(t,n,s){var a,u=r(t),c=i(u.length),l=o(s,c);if(e&&n!=n){for(;c>l;)if(a=u[l++],a!=a)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===n)return e||l||0;return!e&&-1}}},function(e,t,n){"use strict";var r=n(20),i=n(8),o=n(25),s=n(14);e.exports=function(e,t,n,a,u){r(t);var c=i(e),l=o(c),f=s(c.length),d=u?f-1:0,h=u?-1:1;if(n<2)for(;;){if(d in l){a=l[d],d+=h;break}if(d+=h,u?d<0:f<=d)throw TypeError("Reduce of empty array with no initial value")}for(;u?d>=0:f>d;d+=h)d in l&&(a=t(a,l[d],d,c));return a}},function(e,t,n){"use strict";var r=n(12),i=n(27);e.exports=function(e,t,n){t in e?r.f(e,t,i(0,n)):e[t]=n}},function(e,t,n){"use strict";e.exports=!n(11)&&!n(6)(function(){return 7!=Object.defineProperty(n(38)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){"use strict";var r=n(26),i=n(2)("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(r.Array===e||o[i]===e)}},function(e,t,n){"use strict";var r=n(9);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(t){var o=e.return;throw void 0!==o&&r(o.call(e)),t}}},function(e,t,n){"use strict";var r=n(30),i=n(1),o=n(23),s=n(19),a=n(15),u=n(26),c=n(92),l=n(33),f=n(62),d=n(2)("iterator"),h=!([].keys&&"next"in[].keys()),p="@@iterator",v="keys",g="values",y=function(){return this};e.exports=function(e,t,n,m,b,w,_){c(n,t,m);var k,C,S,x=function(e){if(!h&&e in E)return E[e];switch(e){case v:return function(){return new n(this,e)};case g:return function(){return new n(this,e)}}return function(){return new n(this,e)}},R=t+" Iterator",q=b==g,T=!1,E=e.prototype,P=E[d]||E[p]||b&&E[b],A=P||x(b),I=b?q?x("entries"):A:void 0,O="Array"==t?E.entries||P:P;if(O&&(S=f(O.call(new e)),S!==Object.prototype&&S.next&&(l(S,R,!0),r||a(S,d)||s(S,d,y))),q&&P&&P.name!==g&&(T=!0,A=function(){return P.call(this)}),r&&!_||!h&&!T&&E[d]||s(E,d,A),u[t]=A,u[R]=y,b)if(k={values:q?A:x(g),keys:w?A:x(v),entries:I},_)for(C in k)C in E||o(E,C,k[C]);else i(i.P+i.F*(h||T),t,k);return k}},function(e,t,n){"use strict";var r=n(2)("iterator"),i=!1;try{var o=[7][r]();o.return=function(){i=!0},Array.from(o,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!i)return!1;var n=!1;try{var o=[7],s=o[r]();s.next=function(){return{done:n=!0}},o[r]=function(){return s},e(o)}catch(e){}return n}},function(e,t,n){"use strict";function r(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=i(t),this.reject=i(n)}var i=n(20);e.exports.f=function(e){return new r(e)}},function(e,t,n){"use strict";var r=n(12),i=n(9),o=n(22);e.exports=n(11)?Object.defineProperties:function(e,t){i(e);for(var n,s=o(t),a=s.length,u=0;a>u;)r.f(e,n=s[u++],t[n]);return e}},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=n(13),o=n(61).f,s={}.toString,a="object"==("undefined"==typeof window?"undefined":r(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],u=function(e){try{return o(e)}catch(e){return a.slice()}};e.exports.f=function(e){return a&&"[object Window]"==s.call(e)?u(e):o(i(e))}},function(e,t,n){"use strict";var r=n(63),i=n(39).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},function(e,t,n){"use strict";var r=n(15),i=n(8),o=n(45)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),r(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,n){"use strict";var r=n(15),i=n(13),o=n(50)(!1),s=n(45)("IE_PROTO");e.exports=function(e,t){var n,a=i(e),u=0,c=[];for(n in a)n!=s&&r(a,n)&&c.push(n);for(;t.length>u;)r(a,n=t[u++])&&(~o(c,n)||c.push(n));return c}},function(e,t,n){"use strict";var r=n(4),i=n(12),o=n(11),s=n(2)("species");e.exports=function(e){var t=r[e];o&&t&&!t[s]&&i.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){"use strict";var r,i,o,s=n(18),a=n(91),u=n(40),c=n(38),l=n(4),f=l.process,d=l.setImmediate,h=l.clearImmediate,p=l.MessageChannel,v=l.Dispatch,g=0,y={},m="onreadystatechange",b=function(){var e=+this;if(y.hasOwnProperty(e)){var t=y[e];delete y[e],t()}},w=function(e){b.call(e.data)};d&&h||(d=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return y[++g]=function(){a("function"==typeof e?e:Function(e),t)},r(g),g},h=function(e){delete y[e]},"process"==n(21)(f)?r=function(e){f.nextTick(s(b,e,1))}:v&&v.now?r=function(e){v.now(s(b,e,1))}:p?(i=new p,o=i.port2,i.port1.onmessage=w,r=s(o.postMessage,o,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(r=function(e){l.postMessage(e+"","*")},l.addEventListener("message",w,!1)):r=m in c("script")?function(e){u.appendChild(c("script"))[m]=function(){u.removeChild(this),b.call(e)}}:function(e){setTimeout(s(b,e,1),0)}),e.exports={set:d,clear:h}},function(e,t,n){"use strict";t.f=n(2)},function(e,t,n){"use strict";var r=n(36),i=n(2)("iterator"),o=n(26);e.exports=n(10).getIteratorMethod=function(e){if(void 0!=e)return e[i]||e["@@iterator"]||o[r(e)]}},function(e,t,n){"use strict";var r=n(24),i=n(93),o=n(26),s=n(13);e.exports=n(56)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,i(1)):"keys"==t?i(0,n):"values"==t?i(0,e[n]):i(0,[n,e[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(e,t,n){"use strict";var r=n(36),i={};i[n(2)("toStringTag")]="z",i+""!="[object z]"&&n(23)(Object.prototype,"toString",function(){return"[object "+r(this)+"]"},!0)},function(e,t,n){"use strict";var r=n(102)(!0);n(56)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){"use strict";function r(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&o))return String.fromCharCode.apply(null,i.shrinkBuf(e,t));for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n}var i=n(16),o=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(e){o=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){s=!1}for(var a=new i.Buf8(256),u=0;u<256;u++)a[u]=u>=252?6:u>=248?5:u>=240?4:u>=224?3:u>=192?2:1;a[254]=a[254]=1,t.string2buf=function(e){var t,n,r,o,s,a=e.length,u=0;for(o=0;o<a;o++)n=e.charCodeAt(o),55296===(64512&n)&&o+1<a&&(r=e.charCodeAt(o+1),56320===(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),o++)),u+=n<128?1:n<2048?2:n<65536?3:4;for(t=new i.Buf8(u),s=0,o=0;s<u;o++)n=e.charCodeAt(o),55296===(64512&n)&&o+1<a&&(r=e.charCodeAt(o+1),56320===(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),o++)),n<128?t[s++]=n:n<2048?(t[s++]=192|n>>>6,t[s++]=128|63&n):n<65536?(t[s++]=224|n>>>12,t[s++]=128|n>>>6&63,t[s++]=128|63&n):(t[s++]=240|n>>>18,t[s++]=128|n>>>12&63,t[s++]=128|n>>>6&63,t[s++]=128|63&n);return t},t.buf2binstring=function(e){return r(e,e.length)},t.binstring2buf=function(e){for(var t=new i.Buf8(e.length),n=0,r=t.length;n<r;n++)t[n]=e.charCodeAt(n);return t},t.buf2string=function(e,t){var n,i,o,s,u=t||e.length,c=new Array(2*u);for(i=0,n=0;n<u;)if(o=e[n++],o<128)c[i++]=o;else if(s=a[o],s>4)c[i++]=65533,n+=s-1;else{for(o&=2===s?31:3===s?15:7;s>1&&n<u;)o=o<<6|63&e[n++],s--;s>1?c[i++]=65533:o<65536?c[i++]=o:(o-=65536,c[i++]=55296|o>>10&1023,c[i++]=56320|1023&o)}return r(c,i)},t.utf8border=function(e,t){var n;for(t=t||e.length,t>e.length&&(t=e.length),n=t-1;n>=0&&128===(192&e[n]);)n--;return n<0?t:0===n?t:n+a[e[n]]>t?n:t}},function(e,t){"use strict";function n(e,t,n,r){for(var i=65535&e|0,o=e>>>16&65535|0,s=0;0!==n;){s=n>2e3?2e3:n,n-=s;do i=i+t[r++]|0,o=o+i|0;while(--s);i%=65521,o%=65521}return i|o<<16|0}e.exports=n},function(e,t){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},function(e,t){"use strict";function n(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}function r(e,t,n,r){var o=i,s=r+n;e^=-1;for(var a=r;a<s;a++)e=e>>>8^o[255&(e^t[a])];return e^-1}var i=n();e.exports=r},function(e,t){"use strict";function n(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}e.exports=n},function(e,t){"use strict";function n(e,t){var n=t||0,i=r;return i[e[n++]]+i[e[n++]]+i[e[n++]]+i[e[n++]]+"-"+i[e[n++]]+i[e[n++]]+"-"+i[e[n++]]+i[e[n++]]+"-"+i[e[n++]]+i[e[n++]]+"-"+i[e[n++]]+i[e[n++]]+i[e[n++]]+i[e[n++]]+i[e[n++]]+i[e[n++]]}for(var r=[],i=0;i<256;++i)r[i]=(i+256).toString(16).substr(1);e.exports=n},function(e,t){(function(t){"use strict";var n,r=t.crypto||t.msCrypto;if(r&&r.getRandomValues){var i=new Uint8Array(16);n=function(){return r.getRandomValues(i),i}}if(!n){var o=new Array(16);n=function(){for(var e,t=0;t<16;t++)0===(3&t)&&(e=4294967296*Math.random()),o[t]=e>>>((3&t)<<3)&255;return o}}e.exports=n}).call(t,function(){return this}())},function(e,t){"use strict";function n(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function r(e){return 3*e.length/4-n(e)}function i(e){var t,r,i,o,s,a=e.length;o=n(e),s=new l(3*a/4-o),r=o>0?a-4:a;var u=0;for(t=0;t<r;t+=4)i=c[e.charCodeAt(t)]<<18|c[e.charCodeAt(t+1)]<<12|c[e.charCodeAt(t+2)]<<6|c[e.charCodeAt(t+3)],s[u++]=i>>16&255,s[u++]=i>>8&255,s[u++]=255&i;return 2===o?(i=c[e.charCodeAt(t)]<<2|c[e.charCodeAt(t+1)]>>4,s[u++]=255&i):1===o&&(i=c[e.charCodeAt(t)]<<10|c[e.charCodeAt(t+1)]<<4|c[e.charCodeAt(t+2)]>>2,s[u++]=i>>8&255,s[u++]=255&i),s}function o(e){return u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[63&e]}function s(e,t,n){for(var r,i=[],s=t;s<n;s+=3)r=(e[s]<<16)+(e[s+1]<<8)+e[s+2],i.push(o(r));return i.join("")}function a(e){for(var t,n=e.length,r=n%3,i="",o=[],a=16383,c=0,l=n-r;c<l;c+=a)o.push(s(e,c,c+a>l?l:c+a));return 1===r?(t=e[n-1],i+=u[t>>2],i+=u[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=u[t>>10],i+=u[t>>4&63],i+=u[t<<2&63],i+="="),o.push(i),o.join("")}t.byteLength=r,t.toByteArray=i,t.fromByteArray=a;for(var u=[],c=[],l="undefined"!=typeof Uint8Array?Uint8Array:Array,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,h=f.length;d<h;++d)u[d]=f[d],c[f.charCodeAt(d)]=d;c["-".charCodeAt(0)]=62,c["_".charCodeAt(0)]=63},function(e,t,n){(function(e){/*!
	 * The buffer module from node.js, for the browser.
	 *
	 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
	 * @license  MIT
	 */
"use strict";function r(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}function i(){return s.TYPED_ARRAY_SUPPORT?**********:**********}function o(e,t){if(i()<t)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=s.prototype):(null===e&&(e=new s(t)),e.length=t),e}function s(e,t,n){if(!(s.TYPED_ARRAY_SUPPORT||this instanceof s))return new s(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return l(this,e)}return a(this,e,t,n)}function a(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?h(e,t,n,r):"string"==typeof t?f(e,t,n):p(e,t)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function c(e,t,n,r){return u(t),t<=0?o(e,t):void 0!==n?"string"==typeof r?o(e,t).fill(n,r):o(e,t).fill(n):o(e,t)}function l(e,t){if(u(t),e=o(e,t<0?0:0|v(t)),!s.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!s.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|y(t,n);e=o(e,r);var i=e.write(t,n);return i!==r&&(e=e.slice(0,i)),e}function d(e,t){var n=t.length<0?0:0|v(t.length);e=o(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function h(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),s.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=s.prototype):e=d(e,t),e}function p(e,t){if(s.isBuffer(t)){var n=0|v(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||$(t.length)?o(e,0):d(e,t);if("Buffer"===t.type&&Q(t.data))return d(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function v(e){if(e>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),s.alloc(+e)}function y(e,t){if(s.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return J(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Z(e).length;default:if(r)return J(e).length;t=(""+t).toLowerCase(),r=!0}}function m(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return O(this,t,n);case"utf8":case"utf-8":return E(this,t,n);case"ascii":return A(this,t,n);case"latin1":case"binary":return I(this,t,n);case"base64":return T(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return D(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function b(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function w(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=s.from(t,r)),s.isBuffer(t))return 0===t.length?-1:_(e,t,n,r,i);if("number"==typeof t)return t&=255,s.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):_(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function _(e,t,n,r,i){function o(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}var s=1,a=e.length,u=t.length;if(void 0!==r&&(r=String(r).toLowerCase(),"ucs2"===r||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,n/=2}var c;if(i){var l=-1;for(c=n;c<a;c++)if(o(e,c)===o(t,l===-1?0:c-l)){if(l===-1&&(l=c),c-l+1===u)return l*s}else l!==-1&&(c-=c-l),l=-1}else for(n+u>a&&(n=a-u),c=n;c>=0;c--){for(var f=!0,d=0;d<u;d++)if(o(e,c+d)!==o(t,d)){f=!1;break}if(f)return c}return-1}function k(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[n+s]=a}return s}function C(e,t,n,r){return G(J(t,e.length-n),e,n,r)}function S(e,t,n,r){return G(Y(t),e,n,r)}function x(e,t,n,r){return S(e,t,n,r)}function R(e,t,n,r){return G(Z(t),e,n,r)}function q(e,t,n,r){return G(V(t,e.length-n),e,n,r)}function T(e,t,n){return 0===t&&n===e.length?X.fromByteArray(e):X.fromByteArray(e.slice(t,n))}function E(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o=e[i],s=null,a=o>239?4:o>223?3:o>191?2:1;if(i+a<=n){var u,c,l,f;switch(a){case 1:o<128&&(s=o);break;case 2:u=e[i+1],128===(192&u)&&(f=(31&o)<<6|63&u,f>127&&(s=f));break;case 3:u=e[i+1],c=e[i+2],128===(192&u)&&128===(192&c)&&(f=(15&o)<<12|(63&u)<<6|63&c,f>2047&&(f<55296||f>57343)&&(s=f));break;case 4:u=e[i+1],c=e[i+2],l=e[i+3],128===(192&u)&&128===(192&c)&&128===(192&l)&&(f=(15&o)<<18|(63&u)<<12|(63&c)<<6|63&l,f>65535&&f<1114112&&(s=f))}}null===s?(s=65533,a=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|1023&s),r.push(s),i+=a}return P(r)}function P(e){var t=e.length;if(t<=ee)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=ee));return n}function A(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function I(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function O(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=H(e[o]);return i}function D(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function U(e,t,n){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function F(e,t,n,r,i,o){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function j(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function z(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function W(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function M(e,t,n,r,i){return i||W(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),K.write(e,t,n,r,23,4),n+4}function N(e,t,n,r,i){return i||W(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),K.write(e,t,n,r,52,8),n+8}function L(e){if(e=B(e).replace(te,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function B(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function H(e){return e<16?"0"+e.toString(16):e.toString(16)}function J(e,t){t=t||1/0;for(var n,r=e.length,i=null,o=[],s=0;s<r;++s){if(n=e.charCodeAt(s),n>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function Y(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function V(e,t){for(var n,r,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)n=e.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}function Z(e){return X.toByteArray(L(e))}function G(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function $(e){return e!==e}var X=n(78),K=n(143),Q=n(80);t.Buffer=s,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:r(),t.kMaxLength=i(),s.poolSize=8192,s._augment=function(e){return e.__proto__=s.prototype,e},s.from=function(e,t,n){return a(null,e,t,n)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(e,t,n){return c(null,e,t,n)},s.allocUnsafe=function(e){return l(null,e)},s.allocUnsafeSlow=function(e){return l(null,e)},s.isBuffer=function(e){return!(null==e||!e._isBuffer)},s.compare=function(e,t){if(!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Q(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=s.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var o=e[n];if(!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=y,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)b(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)b(this,t,t+3),b(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)b(this,t,t+7),b(this,t+1,t+6),b(this,t+2,t+5),b(this,t+3,t+4);return this},s.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?E(this,0,e):m.apply(this,arguments)},s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},s.prototype.compare=function(e,t,n,r,i){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,i>>>=0,this===e)return 0;for(var o=i-r,a=n-t,u=Math.min(o,a),c=this.slice(r,i),l=e.slice(t,n),f=0;f<u;++f)if(c[f]!==l[f]){o=c[f],a=l[f];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1},s.prototype.indexOf=function(e,t,n){return w(this,e,t,n,!0)},s.prototype.lastIndexOf=function(e,t,n){return w(this,e,t,n,!1)},s.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return k(this,e,t,n);case"utf8":case"utf-8":return C(this,e,t,n);case"ascii":return S(this,e,t,n);case"latin1":case"binary":return x(this,e,t,n);case"base64":return R(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ee=4096;s.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);var r;if(s.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=s.prototype;else{var i=t-e;r=new s(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},s.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},s.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},s.prototype.readUInt8=function(e,t){return t||U(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return t||U(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return t||U(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return t||U(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return t||U(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},s.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},s.prototype.readInt8=function(e,t){return t||U(e,1,this.length),128&this[e]?(255-this[e]+1)*-1:this[e]},s.prototype.readInt16LE=function(e,t){t||U(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(e,t){t||U(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(e,t){return t||U(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return t||U(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return t||U(e,4,this.length),K.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return t||U(e,4,this.length),K.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return t||U(e,8,this.length),K.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return t||U(e,8,this.length),K.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;F(this,e,t,n,i,0)}var o=1,s=0;for(this[t]=255&e;++s<n&&(o*=256);)this[t+s]=e/o&255;return t+n},s.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;F(this,e,t,n,i,0)}var o=n-1,s=1;for(this[t+o]=255&e;--o>=0&&(s*=256);)this[t+o]=e/s&255;return t+n},s.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,1,255,0),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):j(this,e,t,!0),t+2},s.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):j(this,e,t,!1),t+2},s.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):z(this,e,t,!0),t+4},s.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):z(this,e,t,!1),t+4},s.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);F(this,e,t,n,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<n&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);F(this,e,t,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,1,127,-128),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):j(this,e,t,!0),t+2},s.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):j(this,e,t,!1),t+2},s.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,4,**********,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):z(this,e,t,!0),t+4},s.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||F(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):z(this,e,t,!1),t+4},s.prototype.writeFloatLE=function(e,t,n){return M(this,e,t,!0,n)},s.prototype.writeFloatBE=function(e,t,n){return M(this,e,t,!1,n)},s.prototype.writeDoubleLE=function(e,t,n){return N(this,e,t,!0,n)},s.prototype.writeDoubleBE=function(e,t,n){return N(this,e,t,!1,n)},s.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!s.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},s.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=s.isBuffer(e)?e:J(new s(e,r).toString()),u=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%u]}return this};var te=/[^+\/0-9A-Za-z-_]/g}).call(t,function(){return this}())},function(e,t){"use strict";var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";n(70),n(113),n(111),n(117),n(114),n(120),n(122),n(110),n(116),n(107),n(121),n(105),n(119),n(118),n(112),n(115),n(104),n(106),n(109),n(108),n(123),n(68),e.exports=n(10).Array},function(e,t,n){"use strict";n(141),n(125),n(127),n(126),n(129),n(131),n(136),n(130),n(128),n(138),n(137),n(133),n(134),n(132),n(124),n(135),n(139),n(69),e.exports=n(10).Object},function(e,t,n){"use strict";n(69),n(70),n(142),n(140),e.exports=n(10).Promise},function(e,t){"use strict";e.exports=function(e,t,n,r){if(!(e instanceof t)||void 0!==r&&r in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){"use strict";var r=n(8),i=n(34),o=n(14);e.exports=[].copyWithin||function(e,t){var n=r(this),s=o(n.length),a=i(e,s),u=i(t,s),c=arguments.length>2?arguments[2]:void 0,l=Math.min((void 0===c?s:i(c,s))-u,s-a),f=1;for(u<a&&a<u+l&&(f=-1,u+=l-1,a+=l-1);l-- >0;)u in n?n[a]=n[u]:delete n[a],a+=f,u+=f;return n}},function(e,t,n){"use strict";var r=n(8),i=n(34),o=n(14);e.exports=function(e){for(var t=r(this),n=o(t.length),s=arguments.length,a=i(s>1?arguments[1]:void 0,n),u=s>2?arguments[2]:void 0,c=void 0===u?n:i(u,n);c>a;)t[a++]=e;return t}},function(e,t,n){"use strict";var r=n(3),i=n(41),o=n(2)("species");e.exports=function(e){var t;return i(e)&&(t=e.constructor,"function"!=typeof t||t!==Array&&!i(t.prototype)||(t=void 0),r(t)&&(t=t[o],null===t&&(t=void 0))),void 0===t?Array:t}},function(e,t,n){"use strict";var r=n(87);e.exports=function(e,t){return new(r(e))(t)}},function(e,t,n){"use strict";var r=n(22),i=n(44),o=n(32);e.exports=function(e){var t=r(e),n=i.f;if(n)for(var s,a=n(e),u=o.f,c=0;a.length>c;)u.call(e,s=a[c++])&&t.push(s);return t}},function(e,t,n){"use strict";var r=n(18),i=n(55),o=n(54),s=n(9),a=n(14),u=n(67),c={},l={},f=e.exports=function(e,t,n,f,d){var h,p,v,g,y=d?function(){return e}:u(e),m=r(n,f,t?2:1),b=0;if("function"!=typeof y)throw TypeError(e+" is not iterable!");if(o(y)){for(h=a(e.length);h>b;b++)if(g=t?m(s(p=e[b])[0],p[1]):m(e[b]),g===c||g===l)return g}else for(v=y.call(e);!(p=v.next()).done;)if(g=i(v,m,p.value,t),g===c||g===l)return g};f.BREAK=c,f.RETURN=l},function(e,t){"use strict";e.exports=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){"use strict";var r=n(42),i=n(27),o=n(33),s={};n(19)(s,n(2)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r(s,{next:i(1,n)}),o(e,t+" Iterator")}},function(e,t){"use strict";e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";var r=n(4),i=n(65).set,o=r.MutationObserver||r.WebKitMutationObserver,s=r.process,a=r.Promise,u="process"==n(21)(s);e.exports=function(){var e,t,n,c=function(){var r,i;for(u&&(r=s.domain)&&r.exit();e;){i=e.fn,e=e.next;try{i()}catch(r){throw e?n():t=void 0,r}}t=void 0,r&&r.enter()};if(u)n=function(){s.nextTick(c)};else if(o){var l=!0,f=document.createTextNode("");new o(c).observe(f,{characterData:!0}),n=function(){f.data=l=!l}}else if(a&&a.resolve){var d=a.resolve();n=function(){d.then(c)}}else n=function(){i.call(r,c)};return function(r){var i={fn:r,next:void 0};t&&(t.next=i),e||(e=i,n()),t=i}}},function(e,t,n){"use strict";var r=n(22),i=n(44),o=n(32),s=n(8),a=n(25),u=Object.assign;e.exports=!u||n(6)(function(){var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach(function(e){t[e]=e}),7!=u({},e)[n]||Object.keys(u({},t)).join("")!=r})?function(e,t){for(var n=s(e),u=arguments.length,c=1,l=i.f,f=o.f;u>c;)for(var d,h=a(arguments[c++]),p=l?r(h).concat(l(h)):r(h),v=p.length,g=0;v>g;)f.call(h,d=p[g++])&&(n[d]=h[d]);return n}:u},function(e,t){"use strict";e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,n){"use strict";var r=n(9),i=n(3),o=n(58);e.exports=function(e,t){if(r(e),i(t)&&t.constructor===e)return t;var n=o.f(e),s=n.resolve;return s(t),n.promise}},function(e,t,n){"use strict";var r=n(23);e.exports=function(e,t,n){for(var i in t)r(e,i,t[i],n);return e}},function(e,t){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},function(e,t,n){"use strict";var r=n(3),i=n(9),o=function(e,t){if(i(e),!r(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,r){try{r=n(18)(Function.call,n(43).f(Object.prototype,"__proto__").set,2),r(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,n){return o(e,n),t?e.__proto__=n:r(e,n),e}}({},!1):void 0),check:o}},function(e,t,n){"use strict";var r=n(9),i=n(20),o=n(2)("species");e.exports=function(e,t){var n,s=r(e).constructor;return void 0===s||void 0==(n=r(s)[o])?t:i(n)}},function(e,t,n){"use strict";var r=n(35),i=n(37);e.exports=function(e){return function(t,n){var o,s,a=String(i(t)),u=r(n),c=a.length;return u<0||u>=c?e?"":void 0:(o=a.charCodeAt(u),o<55296||o>56319||u+1===c||(s=a.charCodeAt(u+1))<56320||s>57343?e?a.charAt(u):o:e?a.slice(u,u+2):(o-55296<<10)+(s-56320)+65536)}}},function(e,t,n){"use strict";var r=n(4),i=n(10),o=n(30),s=n(66),a=n(12).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=o?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t,n){"use strict";var r=n(1);r(r.P,"Array",{copyWithin:n(85)}),n(24)("copyWithin")},function(e,t,n){"use strict";var r=n(1),i=n(17)(4);r(r.P+r.F*!n(5)([].every,!0),"Array",{every:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(1);r(r.P,"Array",{fill:n(86)}),n(24)("fill")},function(e,t,n){"use strict";var r=n(1),i=n(17)(2);r(r.P+r.F*!n(5)([].filter,!0),"Array",{filter:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(1),i=n(17)(6),o="findIndex",s=!0;o in[]&&Array(1)[o](function(){s=!1}),r(r.P+r.F*s,"Array",{findIndex:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(24)(o)},function(e,t,n){"use strict";var r=n(1),i=n(17)(5),o="find",s=!0;o in[]&&Array(1)[o](function(){s=!1}),r(r.P+r.F*s,"Array",{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(24)(o)},function(e,t,n){"use strict";var r=n(1),i=n(17)(0),o=n(5)([].forEach,!0);r(r.P+r.F*!o,"Array",{forEach:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(18),i=n(1),o=n(8),s=n(55),a=n(54),u=n(14),c=n(52),l=n(67);i(i.S+i.F*!n(57)(function(e){Array.from(e)}),"Array",{from:function(e){var t,n,i,f,d=o(e),h="function"==typeof this?this:Array,p=arguments.length,v=p>1?arguments[1]:void 0,g=void 0!==v,y=0,m=l(d);if(g&&(v=r(v,p>2?arguments[2]:void 0,2)),void 0==m||h==Array&&a(m))for(t=u(d.length),n=new h(t);t>y;y++)c(n,y,g?v(d[y],y):d[y]);else for(f=m.call(d),n=new h;!(i=f.next()).done;y++)c(n,y,g?s(f,v,[i.value,y],!0):i.value);return n.length=y,n}})},function(e,t,n){"use strict";var r=n(1),i=n(50)(!1),o=[].indexOf,s=!!o&&1/[1].indexOf(1,-0)<0;r(r.P+r.F*(s||!n(5)(o)),"Array",{indexOf:function(e){return s?o.apply(this,arguments)||0:i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(1);r(r.S,"Array",{isArray:n(41)})},function(e,t,n){"use strict";var r=n(1),i=n(13),o=[].join;r(r.P+r.F*(n(25)!=Object||!n(5)(o)),"Array",{join:function(e){return o.call(i(this),void 0===e?",":e)}})},function(e,t,n){"use strict";var r=n(1),i=n(13),o=n(35),s=n(14),a=[].lastIndexOf,u=!!a&&1/[1].lastIndexOf(1,-0)<0;r(r.P+r.F*(u||!n(5)(a)),"Array",{lastIndexOf:function(e){if(u)return a.apply(this,arguments)||0;var t=i(this),n=s(t.length),r=n-1;for(arguments.length>1&&(r=Math.min(r,o(arguments[1]))),r<0&&(r=n+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1}})},function(e,t,n){"use strict";var r=n(1),i=n(17)(1);r(r.P+r.F*!n(5)([].map,!0),"Array",{map:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(1),i=n(52);r(r.S+r.F*n(6)(function(){function e(){}return!(Array.of.call(e)instanceof e)}),"Array",{of:function(){for(var e=0,t=arguments.length,n=new("function"==typeof this?this:Array)(t);t>e;)i(n,e,arguments[e++]);return n.length=t,n}})},function(e,t,n){"use strict";var r=n(1),i=n(51);r(r.P+r.F*!n(5)([].reduceRight,!0),"Array",{reduceRight:function(e){return i(this,e,arguments.length,arguments[1],!0)}})},function(e,t,n){"use strict";var r=n(1),i=n(51);r(r.P+r.F*!n(5)([].reduce,!0),"Array",{reduce:function(e){return i(this,e,arguments.length,arguments[1],!1)}})},function(e,t,n){"use strict";var r=n(1),i=n(40),o=n(21),s=n(34),a=n(14),u=[].slice;r(r.P+r.F*n(6)(function(){i&&u.call(i)}),"Array",{slice:function(e,t){var n=a(this.length),r=o(this);if(t=void 0===t?n:t,"Array"==r)return u.call(this,e,t);for(var i=s(e,n),c=s(t,n),l=a(c-i),f=Array(l),d=0;d<l;d++)f[d]="String"==r?this.charAt(i+d):this[i+d];return f}})},function(e,t,n){"use strict";var r=n(1),i=n(17)(3);r(r.P+r.F*!n(5)([].some,!0),"Array",{some:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(1),i=n(20),o=n(8),s=n(6),a=[].sort,u=[1,2,3];r(r.P+r.F*(s(function(){u.sort(void 0)})||!s(function(){u.sort(null)})||!n(5)(a)),"Array",{sort:function(e){return void 0===e?a.call(o(this)):a.call(o(this),i(e))}})},function(e,t,n){"use strict";n(64)("Array")},function(e,t,n){"use strict";var r=n(1);r(r.S+r.F,"Object",{assign:n(95)})},function(e,t,n){"use strict";var r=n(1);r(r.S,"Object",{create:n(42)})},function(e,t,n){"use strict";var r=n(1);r(r.S+r.F*!n(11),"Object",{defineProperties:n(59)})},function(e,t,n){"use strict";var r=n(1);r(r.S+r.F*!n(11),"Object",{defineProperty:n(12).f})},function(e,t,n){"use strict";var r=n(3),i=n(31).onFreeze;n(7)("freeze",function(e){return function(t){return e&&r(t)?e(i(t)):t}})},function(e,t,n){"use strict";var r=n(13),i=n(43).f;n(7)("getOwnPropertyDescriptor",function(){return function(e,t){return i(r(e),t)}})},function(e,t,n){"use strict";n(7)("getOwnPropertyNames",function(){return n(60).f})},function(e,t,n){"use strict";var r=n(8),i=n(62);n(7)("getPrototypeOf",function(){return function(e){return i(r(e))}})},function(e,t,n){"use strict";var r=n(3);n(7)("isExtensible",function(e){return function(t){return!!r(t)&&(!e||e(t))}})},function(e,t,n){"use strict";var r=n(3);n(7)("isFrozen",function(e){return function(t){return!r(t)||!!e&&e(t)}})},function(e,t,n){"use strict";var r=n(3);n(7)("isSealed",function(e){return function(t){return!r(t)||!!e&&e(t)}})},function(e,t,n){"use strict";var r=n(1);r(r.S,"Object",{is:n(99)})},function(e,t,n){"use strict";var r=n(8),i=n(22);n(7)("keys",function(){return function(e){return i(r(e))}})},function(e,t,n){"use strict";var r=n(3),i=n(31).onFreeze;n(7)("preventExtensions",function(e){return function(t){return e&&r(t)?e(i(t)):t}})},function(e,t,n){"use strict";var r=n(3),i=n(31).onFreeze;n(7)("seal",function(e){return function(t){return e&&r(t)?e(i(t)):t}})},function(e,t,n){"use strict";var r=n(1);r(r.S,"Object",{setPrototypeOf:n(100).set})},function(e,t,n){"use strict";var r,i,o,s,a=n(30),u=n(4),c=n(18),l=n(36),f=n(1),d=n(3),h=n(20),p=n(84),v=n(90),g=n(101),y=n(65).set,m=n(94)(),b=n(58),w=n(96),_=n(97),k="Promise",C=u.TypeError,S=u.process,x=u[k],R="process"==l(S),q=function(){},T=i=b.f,E=!!function(){try{var e=x.resolve(1),t=(e.constructor={})[n(2)("species")]=function(e){e(q,q)};return(R||"function"==typeof PromiseRejectionEvent)&&e.then(q)instanceof t}catch(e){}}(),P=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},A=function(e,t){if(!e._n){e._n=!0;var n=e._c;m(function(){for(var r=e._v,i=1==e._s,o=0,s=function(t){var n,o,s=i?t.ok:t.fail,a=t.resolve,u=t.reject,c=t.domain;try{s?(i||(2==e._h&&D(e),e._h=1),s===!0?n=r:(c&&c.enter(),n=s(r),c&&c.exit()),n===t.promise?u(C("Promise-chain cycle")):(o=P(n))?o.call(n,a,u):a(n)):u(r)}catch(e){u(e)}};n.length>o;)s(n[o++]);e._c=[],e._n=!1,t&&!e._h&&I(e)})}},I=function(e){y.call(u,function(){var t,n,r,i=e._v,o=O(e);if(o&&(t=w(function(){R?S.emit("unhandledRejection",i,e):(n=u.onunhandledrejection)?n({
promise:e,reason:i}):(r=u.console)&&r.error&&r.error("Unhandled promise rejection",i)}),e._h=R||O(e)?2:1),e._a=void 0,o&&t.e)throw t.v})},O=function e(t){if(1==t._h)return!1;for(var n,r=t._a||t._c,i=0;r.length>i;)if(n=r[i++],n.fail||!e(n.promise))return!1;return!0},D=function(e){y.call(u,function(){var t;R?S.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},U=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),A(t,!0))},F=function e(t){var n,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===t)throw C("Promise can't be resolved itself");(n=P(t))?m(function(){var i={_w:r,_d:!1};try{n.call(t,c(e,i,1),c(U,i,1))}catch(e){U.call(i,e)}}):(r._v=t,r._s=1,A(r,!1))}catch(e){U.call({_w:r,_d:!1},e)}}};E||(x=function(e){p(this,x,k,"_h"),h(e),r.call(this);try{e(c(F,this,1),c(U,this,1))}catch(e){U.call(this,e)}},r=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},r.prototype=n(98)(x.prototype,{then:function(e,t){var n=T(g(this,x));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=R?S.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&A(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),o=function(){var e=new r;this.promise=e,this.resolve=c(F,e,1),this.reject=c(U,e,1)},b.f=T=function(e){return e===x||e===s?new o(e):i(e)}),f(f.G+f.W+f.F*!E,{Promise:x}),n(33)(x,k),n(64)(k),s=n(10)[k],f(f.S+f.F*!E,k,{reject:function(e){var t=T(this),n=t.reject;return n(e),t.promise}}),f(f.S+f.F*(a||!E),k,{resolve:function(e){return _(a&&this===s?x:this,e)}}),f(f.S+f.F*!(E&&n(57)(function(e){x.all(e).catch(q)})),k,{all:function(e){var t=this,n=T(t),r=n.resolve,i=n.reject,o=w(function(){var n=[],o=0,s=1;v(e,!1,function(e){var a=o++,u=!1;n.push(void 0),s++,t.resolve(e).then(function(e){u||(u=!0,n[a]=e,--s||r(n))},i)}),--s||r(n)});return o.e&&i(o.v),n.promise},race:function(e){var t=this,n=T(t),r=n.reject,i=w(function(){v(e,!1,function(e){t.resolve(e).then(n.resolve,r)})});return i.e&&r(i.v),n.promise}})},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=n(4),o=n(15),s=n(11),a=n(1),u=n(23),c=n(31).KEY,l=n(6),f=n(46),d=n(33),h=n(28),p=n(2),v=n(66),g=n(103),y=n(89),m=n(41),b=n(9),w=n(13),_=n(47),k=n(27),C=n(42),S=n(60),x=n(43),R=n(12),q=n(22),T=x.f,E=R.f,P=S.f,A=i.Symbol,I=i.JSON,O=I&&I.stringify,D="prototype",U=p("_hidden"),F=p("toPrimitive"),j={}.propertyIsEnumerable,z=f("symbol-registry"),W=f("symbols"),M=f("op-symbols"),N=Object[D],L="function"==typeof A,B=i.QObject,H=!B||!B[D]||!B[D].findChild,J=s&&l(function(){return 7!=C(E({},"a",{get:function(){return E(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=T(N,t);r&&delete N[t],E(e,t,n),r&&e!==N&&E(N,t,r)}:E,Y=function(e){var t=W[e]=C(A[D]);return t._k=e,t},V=L&&"symbol"==r(A.iterator)?function(e){return"symbol"==("undefined"==typeof e?"undefined":r(e))}:function(e){return e instanceof A},Z=function(e,t,n){return e===N&&Z(M,t,n),b(e),t=_(t,!0),b(n),o(W,t)?(n.enumerable?(o(e,U)&&e[U][t]&&(e[U][t]=!1),n=C(n,{enumerable:k(0,!1)})):(o(e,U)||E(e,U,k(1,{})),e[U][t]=!0),J(e,t,n)):E(e,t,n)},G=function(e,t){b(e);for(var n,r=y(t=w(t)),i=0,o=r.length;o>i;)Z(e,n=r[i++],t[n]);return e},$=function(e,t){return void 0===t?C(e):G(C(e),t)},X=function(e){var t=j.call(this,e=_(e,!0));return!(this===N&&o(W,e)&&!o(M,e))&&(!(t||!o(this,e)||!o(W,e)||o(this,U)&&this[U][e])||t)},K=function(e,t){if(e=w(e),t=_(t,!0),e!==N||!o(W,t)||o(M,t)){var n=T(e,t);return!n||!o(W,t)||o(e,U)&&e[U][t]||(n.enumerable=!0),n}},Q=function(e){for(var t,n=P(w(e)),r=[],i=0;n.length>i;)o(W,t=n[i++])||t==U||t==c||r.push(t);return r},ee=function(e){for(var t,n=e===N,r=P(n?M:w(e)),i=[],s=0;r.length>s;)!o(W,t=r[s++])||n&&!o(N,t)||i.push(W[t]);return i};L||(A=function(){if(this instanceof A)throw TypeError("Symbol is not a constructor!");var e=h(arguments.length>0?arguments[0]:void 0),t=function t(n){this===N&&t.call(M,n),o(this,U)&&o(this[U],e)&&(this[U][e]=!1),J(this,e,k(1,n))};return s&&H&&J(N,e,{configurable:!0,set:t}),Y(e)},u(A[D],"toString",function(){return this._k}),x.f=K,R.f=Z,n(61).f=S.f=Q,n(32).f=X,n(44).f=ee,s&&!n(30)&&u(N,"propertyIsEnumerable",X,!0),v.f=function(e){return Y(p(e))}),a(a.G+a.W+a.F*!L,{Symbol:A});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;te.length>ne;)p(te[ne++]);for(var re=q(p.store),ie=0;re.length>ie;)g(re[ie++]);a(a.S+a.F*!L,"Symbol",{for:function(e){return o(z,e+="")?z[e]:z[e]=A(e)},keyFor:function(e){if(!V(e))throw TypeError(e+" is not a symbol!");for(var t in z)if(z[t]===e)return t},useSetter:function(){H=!0},useSimple:function(){H=!1}}),a(a.S+a.F*!L,"Object",{create:$,defineProperty:Z,defineProperties:G,getOwnPropertyDescriptor:K,getOwnPropertyNames:Q,getOwnPropertySymbols:ee}),I&&a(a.S+a.F*(!L||l(function(){var e=A();return"[null]"!=O([e])||"{}"!=O({a:e})||"{}"!=O(Object(e))})),"JSON",{stringify:function(e){if(void 0!==e&&!V(e)){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);return t=r[1],"function"==typeof t&&(n=t),!n&&m(t)||(t=function(e,t){if(n&&(t=n.call(this,e,t)),!V(t))return t}),r[1]=t,O.apply(I,r)}}}),A[D][F]||n(19)(A[D],F,A[D].valueOf),d(A,"Symbol"),d(Math,"Math",!0),d(i.JSON,"JSON",!0)},function(e,t,n){"use strict";for(var r=n(68),i=n(22),o=n(23),s=n(4),a=n(19),u=n(26),c=n(2),l=c("iterator"),f=c("toStringTag"),d=u.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=i(h),v=0;v<p.length;v++){var g,y=p[v],m=h[y],b=s[y],w=b&&b.prototype;if(w&&(w[l]||a(w,l,d),w[f]||a(w,f,y),u[y]=d,m))for(g in r)w[g]||o(w,g,r[g],!0)}},function(e,t){"use strict";t.read=function(e,t,n,r,i){var o,s,a=8*i-r-1,u=(1<<a)-1,c=u>>1,l=-7,f=n?i-1:0,d=n?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-l)-1,h>>=-l,l+=a;l>0;o=256*o+e[t+f],f+=d,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=r;l>0;s=256*s+e[t+f],f+=d,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:(h?-1:1)*(1/0);s+=Math.pow(2,r),o-=c}return(h?-1:1)*s*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,f=l>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=r?0:o-1,p=r?1:-1,v=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+f>=1?d/u:d*Math.pow(2,1-f),t*u>=2&&(s++,u/=2),s+f>=l?(a=0,s=l):s+f>=1?(a=(t*u-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[n+h]=255&a,h+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[n+h]=255&s,h+=p,s/=256,c-=8);e[n+h-p]|=128*v}},function(e,t,n){var r,i;(function(e){"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};/*!
	 * jQuery JavaScript Library v3.2.1
	 * https://jquery.com/
	 *
	 * Includes Sizzle.js
	 * https://sizzlejs.com/
	 *
	 * Copyright JS Foundation and other contributors
	 * Released under the MIT license
	 * https://jquery.org/license
	 *
	 * Date: 2017-03-20T18:59Z
	 */
!function(t,r){"object"===n(e)&&"object"===n(e.exports)?e.exports=t.document?r(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return r(e)}:r(t)}("undefined"!=typeof window?window:void 0,function(o,s){function a(e,t){t=t||ae;var n=t.createElement("script");n.text=e,t.head.appendChild(n).parentNode.removeChild(n)}function u(e){var t=!!e&&"length"in e&&e.length,n=we.type(e);return"function"!==n&&!we.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function c(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}function l(e,t,n){return we.isFunction(t)?we.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?we.grep(e,function(e){return e===t!==n}):"string"!=typeof t?we.grep(e,function(e){return de.call(t,e)>-1!==n}):Pe.test(t)?we.filter(t,e,n):(t=we.filter(t,e),we.grep(e,function(e){return de.call(t,e)>-1!==n&&1===e.nodeType}))}function f(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}function d(e){var t={};return we.each(e.match(Fe)||[],function(e,n){t[n]=!0}),t}function h(e){return e}function p(e){throw e}function v(e,t,n,r){var i;try{e&&we.isFunction(i=e.promise)?i.call(e).done(t).fail(n):e&&we.isFunction(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}function g(){ae.removeEventListener("DOMContentLoaded",g),o.removeEventListener("load",g),we.ready()}function y(){this.expando=we.expando+y.uid++}function m(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:Be.test(e)?JSON.parse(e):e)}function b(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(He,"-$&").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n=m(n)}catch(e){}Le.set(e,t,n)}else n=void 0;return n}function w(e,t,n,r){var i,o=1,s=20,a=r?function(){return r.cur()}:function(){return we.css(e,t,"")},u=a(),c=n&&n[3]||(we.cssNumber[t]?"":"px"),l=(we.cssNumber[t]||"px"!==c&&+u)&&Ye.exec(we.css(e,t));if(l&&l[3]!==c){c=c||l[3],n=n||[],l=+u||1;do o=o||".5",l/=o,we.style(e,t,l+c);while(o!==(o=a()/u)&&1!==o&&--s)}return n&&(l=+l||+u||0,i=n[1]?l+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=l,r.end=i)),i}function _(e){var t,n=e.ownerDocument,r=e.nodeName,i=$e[r];return i?i:(t=n.body.appendChild(n.createElement(r)),i=we.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),$e[r]=i,i)}function k(e,t){for(var n,r,i=[],o=0,s=e.length;o<s;o++)r=e[o],r.style&&(n=r.style.display,t?("none"===n&&(i[o]=Ne.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&Ze(r)&&(i[o]=_(r))):"none"!==n&&(i[o]="none",Ne.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}function C(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&c(e,t)?we.merge([e],n):n}function S(e,t){for(var n=0,r=e.length;n<r;n++)Ne.set(e[n],"globalEval",!t||Ne.get(t[n],"globalEval"))}function x(e,t,n,r,i){for(var o,s,a,u,c,l,f=t.createDocumentFragment(),d=[],h=0,p=e.length;h<p;h++)if(o=e[h],o||0===o)if("object"===we.type(o))we.merge(d,o.nodeType?[o]:o);else if(tt.test(o)){for(s=s||f.appendChild(t.createElement("div")),a=(Ke.exec(o)||["",""])[1].toLowerCase(),u=et[a]||et._default,s.innerHTML=u[1]+we.htmlPrefilter(o)+u[2],l=u[0];l--;)s=s.lastChild;we.merge(d,s.childNodes),s=f.firstChild,s.textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",h=0;o=d[h++];)if(r&&we.inArray(o,r)>-1)i&&i.push(o);else if(c=we.contains(o.ownerDocument,o),s=C(f.appendChild(o),"script"),c&&S(s),n)for(l=0;o=s[l++];)Qe.test(o.type||"")&&n.push(o);return f}function R(){return!0}function q(){return!1}function T(){try{return ae.activeElement}catch(e){}}function E(e,t,r,i,o,s){var a,u;if("object"===("undefined"==typeof t?"undefined":n(t))){"string"!=typeof r&&(i=i||r,r=void 0);for(u in t)E(e,u,r,i,t[u],s);return e}if(null==i&&null==o?(o=r,i=r=void 0):null==o&&("string"==typeof r?(o=i,i=void 0):(o=i,i=r,r=void 0)),o===!1)o=q;else if(!o)return e;return 1===s&&(a=o,o=function(e){return we().off(e),a.apply(this,arguments)},o.guid=a.guid||(a.guid=we.guid++)),e.each(function(){we.event.add(this,t,o,i,r)})}function P(e,t){return c(e,"table")&&c(11!==t.nodeType?t:t.firstChild,"tr")?we(">tbody",e)[0]||e:e}function A(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function I(e){var t=ct.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function O(e,t){var n,r,i,o,s,a,u,c;if(1===t.nodeType){if(Ne.hasData(e)&&(o=Ne.access(e),s=Ne.set(t,o),c=o.events)){delete s.handle,s.events={};for(i in c)for(n=0,r=c[i].length;n<r;n++)we.event.add(t,i,c[i][n])}Le.hasData(e)&&(a=Le.access(e),u=we.extend({},a),Le.set(t,u))}}function D(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Xe.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function U(e,t,n,r){t=le.apply([],t);var i,o,s,u,c,l,f=0,d=e.length,h=d-1,p=t[0],v=we.isFunction(p);if(v||d>1&&"string"==typeof p&&!me.checkClone&&ut.test(p))return e.each(function(i){var o=e.eq(i);v&&(t[0]=p.call(this,i,o.html())),U(o,t,n,r)});if(d&&(i=x(t,e[0].ownerDocument,!1,e,r),o=i.firstChild,1===i.childNodes.length&&(i=o),o||r)){for(s=we.map(C(i,"script"),A),u=s.length;f<d;f++)c=i,f!==h&&(c=we.clone(c,!0,!0),u&&we.merge(s,C(c,"script"))),n.call(e[f],c,f);if(u)for(l=s[s.length-1].ownerDocument,we.map(s,I),f=0;f<u;f++)c=s[f],Qe.test(c.type||"")&&!Ne.access(c,"globalEval")&&we.contains(l,c)&&(c.src?we._evalUrl&&we._evalUrl(c.src):a(c.textContent.replace(lt,""),l))}return e}function F(e,t,n){for(var r,i=t?we.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||we.cleanData(C(r)),r.parentNode&&(n&&we.contains(r.ownerDocument,r)&&S(C(r,"script")),r.parentNode.removeChild(r));return e}function j(e,t,n){var r,i,o,s,a=e.style;return n=n||ht(e),n&&(s=n.getPropertyValue(t)||n[t],""!==s||we.contains(e.ownerDocument,e)||(s=we.style(e,t)),!me.pixelMarginRight()&&dt.test(s)&&ft.test(t)&&(r=a.width,i=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=r,a.minWidth=i,a.maxWidth=o)),void 0!==s?s+"":s}function z(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function W(e){if(e in bt)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=mt.length;n--;)if(e=mt[n]+t,e in bt)return e}function M(e){var t=we.cssProps[e];return t||(t=we.cssProps[e]=W(e)||e),t}function N(e,t,n){var r=Ye.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function L(e,t,n,r,i){var o,s=0;for(o=n===(r?"border":"content")?4:"width"===t?1:0;o<4;o+=2)"margin"===n&&(s+=we.css(e,n+Ve[o],!0,i)),r?("content"===n&&(s-=we.css(e,"padding"+Ve[o],!0,i)),"margin"!==n&&(s-=we.css(e,"border"+Ve[o]+"Width",!0,i))):(s+=we.css(e,"padding"+Ve[o],!0,i),"padding"!==n&&(s+=we.css(e,"border"+Ve[o]+"Width",!0,i)));return s}function B(e,t,n){var r,i=ht(e),o=j(e,t,i),s="border-box"===we.css(e,"boxSizing",!1,i);return dt.test(o)?o:(r=s&&(me.boxSizingReliable()||o===e.style[t]),"auto"===o&&(o=e["offset"+t[0].toUpperCase()+t.slice(1)]),o=parseFloat(o)||0,o+L(e,t,n||(s?"border":"content"),r,i)+"px")}function H(e,t,n,r,i){return new H.prototype.init(e,t,n,r,i)}function J(){_t&&(ae.hidden===!1&&o.requestAnimationFrame?o.requestAnimationFrame(J):o.setTimeout(J,we.fx.interval),we.fx.tick())}function Y(){return o.setTimeout(function(){wt=void 0}),wt=we.now()}function V(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)n=Ve[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function Z(e,t,n){for(var r,i=(X.tweeners[t]||[]).concat(X.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function G(e,t,n){var r,i,o,s,a,u,c,l,f="width"in t||"height"in t,d=this,h={},p=e.style,v=e.nodeType&&Ze(e),g=Ne.get(e,"fxshow");n.queue||(s=we._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,we.queue(e,"fx").length||s.empty.fire()})}));for(r in t)if(i=t[r],kt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(v?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;v=!0}h[r]=g&&g[r]||we.style(e,r)}if(u=!we.isEmptyObject(t),u||!we.isEmptyObject(h)){f&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],c=g&&g.display,null==c&&(c=Ne.get(e,"display")),l=we.css(e,"display"),"none"===l&&(c?l=c:(k([e],!0),c=e.style.display||c,l=we.css(e,"display"),k([e]))),("inline"===l||"inline-block"===l&&null!=c)&&"none"===we.css(e,"float")&&(u||(d.done(function(){p.display=c}),null==c&&(l=p.display,c="none"===l?"":l)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),u=!1;for(r in h)u||(g?"hidden"in g&&(v=g.hidden):g=Ne.access(e,"fxshow",{display:c}),o&&(g.hidden=!v),v&&k([e],!0),d.done(function(){v||k([e]),Ne.remove(e,"fxshow");for(r in h)we.style(e,r,h[r])})),u=Z(v?g[r]:0,r,d),r in g||(g[r]=u.start,v&&(u.end=u.start,u.start=0))}}function $(e,t){var n,r,i,o,s;for(n in e)if(r=we.camelCase(n),i=t[r],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),s=we.cssHooks[r],s&&"expand"in s){o=s.expand(o),delete e[r];for(n in o)n in e||(e[n]=o[n],t[n]=i)}else t[r]=i}function X(e,t,n){var r,i,o=0,s=X.prefilters.length,a=we.Deferred().always(function(){delete u.elem}),u=function(){if(i)return!1;for(var t=wt||Y(),n=Math.max(0,c.startTime+c.duration-t),r=n/c.duration||0,o=1-r,s=0,u=c.tweens.length;s<u;s++)c.tweens[s].run(o);return a.notifyWith(e,[c,o,n]),o<1&&u?n:(u||a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c]),!1)},c=a.promise({elem:e,props:we.extend({},t),opts:we.extend(!0,{specialEasing:{},easing:we.easing._default},n),originalProperties:t,originalOptions:n,startTime:wt||Y(),duration:n.duration,tweens:[],createTween:function(t,n){var r=we.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(r),r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return t?(a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c,t])):a.rejectWith(e,[c,t]),this}}),l=c.props;for($(l,c.opts.specialEasing);o<s;o++)if(r=X.prefilters[o].call(c,e,l,c.opts))return we.isFunction(r.stop)&&(we._queueHooks(c.elem,c.opts.queue).stop=we.proxy(r.stop,r)),r;return we.map(l,Z,c),we.isFunction(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),we.fx.timer(we.extend(u,{elem:e,anim:c,queue:c.opts.queue})),c}function K(e){var t=e.match(Fe)||[];return t.join(" ")}function Q(e){return e.getAttribute&&e.getAttribute("class")||""}function ee(e,t,r,i){var o;if(Array.isArray(t))we.each(t,function(t,o){r||Ot.test(e)?i(e,o):ee(e+"["+("object"===("undefined"==typeof o?"undefined":n(o))&&null!=o?t:"")+"]",o,r,i)});else if(r||"object"!==we.type(t))i(e,t);else for(o in t)ee(e+"["+o+"]",t[o],r,i)}function te(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(Fe)||[];if(we.isFunction(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function ne(e,t,n,r){function i(a){var u;return o[a]=!0,we.each(e[a]||[],function(e,a){var c=a(t,n,r);return"string"!=typeof c||s||o[c]?s?!(u=c):void 0:(t.dataTypes.unshift(c),i(c),!1)}),u}var o={},s=e===Jt;return i(t.dataTypes[0])||!o["*"]&&i("*")}function re(e,t){var n,r,i=we.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&we.extend(!0,e,r),e}function ie(e,t,n){for(var r,i,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}function oe(e,t,n,r){var i,o,s,a,u,c={},l=e.dataTypes.slice();if(l[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=l.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=l.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(s=c[u+" "+o]||c["* "+o],!s)for(i in c)if(a=i.split(" "),a[1]===o&&(s=c[u+" "+a[0]]||c["* "+a[0]])){s===!0?s=c[i]:c[i]!==!0&&(o=a[0],l.unshift(a[1]));break}if(s!==!0)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}var se=[],ae=o.document,ue=Object.getPrototypeOf,ce=se.slice,le=se.concat,fe=se.push,de=se.indexOf,he={},pe=he.toString,ve=he.hasOwnProperty,ge=ve.toString,ye=ge.call(Object),me={},be="3.2.1",we=function e(t,n){return new e.fn.init(t,n)},_e=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ke=/^-ms-/,Ce=/-([a-z])/g,Se=function(e,t){return t.toUpperCase()};we.fn=we.prototype={jquery:be,constructor:we,length:0,toArray:function(){return ce.call(this)},get:function(e){return null==e?ce.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=we.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return we.each(this,e)},map:function(e){return this.pushStack(we.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(ce.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:fe,sort:se.sort,splice:se.splice},we.extend=we.fn.extend=function(){var e,t,r,i,o,s,a=arguments[0]||{},u=1,c=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[u]||{},u++),"object"===("undefined"==typeof a?"undefined":n(a))||we.isFunction(a)||(a={}),u===c&&(a=this,u--);u<c;u++)if(null!=(e=arguments[u]))for(t in e)r=a[t],i=e[t],a!==i&&(l&&i&&(we.isPlainObject(i)||(o=Array.isArray(i)))?(o?(o=!1,s=r&&Array.isArray(r)?r:[]):s=r&&we.isPlainObject(r)?r:{},a[t]=we.extend(l,s,i)):void 0!==i&&(a[t]=i));return a},we.extend({expando:"jQuery"+(be+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===we.type(e)},isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=we.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==pe.call(e))&&(!(t=ue(e))||(n=ve.call(t,"constructor")&&t.constructor,"function"==typeof n&&ge.call(n)===ye))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"===("undefined"==typeof e?"undefined":n(e))||"function"==typeof e?he[pe.call(e)]||"object":"undefined"==typeof e?"undefined":n(e)},globalEval:function(e){a(e)},camelCase:function(e){return e.replace(ke,"ms-").replace(Ce,Se)},each:function(e,t){var n,r=0;if(u(e))for(n=e.length;r<n&&t.call(e[r],r,e[r])!==!1;r++);else for(r in e)if(t.call(e[r],r,e[r])===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(_e,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(u(Object(e))?we.merge(n,"string"==typeof e?[e]:e):fe.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:de.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,s=e.length,a=!n;o<s;o++)r=!t(e[o],o),r!==a&&i.push(e[o]);return i},map:function(e,t,n){var r,i,o=0,s=[];if(u(e))for(r=e.length;o<r;o++)i=t(e[o],o,n),null!=i&&s.push(i);else for(o in e)i=t(e[o],o,n),null!=i&&s.push(i);return le.apply([],s)},guid:1,proxy:function e(t,n){var r,i,e;if("string"==typeof n&&(r=t[n],n=t,t=r),we.isFunction(t))return i=ce.call(arguments,2),e=function(){return t.apply(n||this,i.concat(ce.call(arguments)))},e.guid=t.guid=t.guid||we.guid++,e},now:Date.now,support:me}),"function"==typeof Symbol&&(we.fn[Symbol.iterator]=se[Symbol.iterator]),we.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){he["[object "+t+"]"]=t.toLowerCase()});var xe=/*!
	  * Sizzle CSS Selector Engine v2.3.3
	  * https://sizzlejs.com/
	  *
	  * Copyright jQuery Foundation and other contributors
	  * Released under the MIT license
	  * http://jquery.org/license
	  *
	  * Date: 2016-08-08
	  */
function(e){function t(e,t,n,r){var i,o,s,a,u,c,l,d=t&&t.ownerDocument,p=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==p&&9!==p&&11!==p)return n;if(!r&&((t?t.ownerDocument||t:N)!==O&&I(t),t=t||O,U)){if(11!==p&&(u=ye.exec(e)))if(i=u[1]){if(9===p){if(!(s=t.getElementById(i)))return n;if(s.id===i)return n.push(s),n}else if(d&&(s=d.getElementById(i))&&W(t,s)&&s.id===i)return n.push(s),n}else{if(u[2])return K.apply(n,t.getElementsByTagName(e)),n;if((i=u[3])&&k.getElementsByClassName&&t.getElementsByClassName)return K.apply(n,t.getElementsByClassName(i)),n}if(k.qsa&&!Y[e+" "]&&(!F||!F.test(e))){if(1!==p)d=t,l=e;else if("object"!==t.nodeName.toLowerCase()){for((a=t.getAttribute("id"))?a=a.replace(_e,ke):t.setAttribute("id",a=M),c=R(e),o=c.length;o--;)c[o]="#"+a+" "+h(c[o]);l=c.join(","),d=me.test(e)&&f(t.parentNode)||t}if(l)try{return K.apply(n,d.querySelectorAll(l)),n}catch(e){}finally{a===M&&t.removeAttribute("id")}}}return T(e.replace(ae,"$1"),t,n,r)}function n(){function e(n,r){return t.push(n+" ")>C.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function r(e){return e[M]=!0,e}function i(e){var t=O.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),r=n.length;r--;)C.attrHandle[n[r]]=t}function s(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function a(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function u(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function c(e){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&Se(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function l(e){return r(function(t){return t=+t,r(function(n,r){for(var i,o=e([],n.length,t),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))})})}function f(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function d(){}function h(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function p(e,t,n){var r=t.dir,i=t.next,o=i||r,s=n&&"parentNode"===o,a=B++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||s)return e(t,n,i);return!1}:function(t,n,u){var c,l,f,d=[L,a];if(u){for(;t=t[r];)if((1===t.nodeType||s)&&e(t,n,u))return!0}else for(;t=t[r];)if(1===t.nodeType||s)if(f=t[M]||(t[M]={}),l=f[t.uniqueID]||(f[t.uniqueID]={}),i&&i===t.nodeName.toLowerCase())t=t[r]||t;else{if((c=l[o])&&c[0]===L&&c[1]===a)return d[2]=c[2];if(l[o]=d,d[2]=e(t,n,u))return!0}return!1}}function v(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function g(e,n,r){for(var i=0,o=n.length;i<o;i++)t(e,n[i],r);return r}function y(e,t,n,r,i){for(var o,s=[],a=0,u=e.length,c=null!=t;a<u;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),c&&t.push(a)));return s}function m(e,t,n,i,o,s){return i&&!i[M]&&(i=m(i)),o&&!o[M]&&(o=m(o,s)),r(function(r,s,a,u){var c,l,f,d=[],h=[],p=s.length,v=r||g(t||"*",a.nodeType?[a]:a,[]),m=!e||!r&&t?v:y(v,d,e,a,u),b=n?o||(r?e:p||i)?[]:s:m;if(n&&n(m,b,a,u),i)for(c=y(b,h),i(c,[],a,u),l=c.length;l--;)(f=c[l])&&(b[h[l]]=!(m[h[l]]=f));if(r){if(o||e){if(o){for(c=[],l=b.length;l--;)(f=b[l])&&c.push(m[l]=f);o(null,b=[],c,u)}for(l=b.length;l--;)(f=b[l])&&(c=o?ee(r,f):d[l])>-1&&(r[c]=!(s[c]=f))}}else b=y(b===s?b.splice(p,b.length):b),o?o(null,s,b,u):K.apply(s,b)})}function b(e){for(var t,n,r,i=e.length,o=C.relative[e[0].type],s=o||C.relative[" "],a=o?1:0,u=p(function(e){return e===t},s,!0),c=p(function(e){return ee(t,e)>-1},s,!0),l=[function(e,n,r){var i=!o&&(r||n!==E)||((t=n).nodeType?u(e,n,r):c(e,n,r));return t=null,i}];a<i;a++)if(n=C.relative[e[a].type])l=[p(v(l),n)];else{if(n=C.filter[e[a].type].apply(null,e[a].matches),n[M]){for(r=++a;r<i&&!C.relative[e[r].type];r++);return m(a>1&&v(l),a>1&&h(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(ae,"$1"),n,a<r&&b(e.slice(a,r)),r<i&&b(e=e.slice(r)),r<i&&h(e))}l.push(n)}return v(l)}function w(e,n){var i=n.length>0,o=e.length>0,s=function(r,s,a,u,c){var l,f,d,h=0,p="0",v=r&&[],g=[],m=E,b=r||o&&C.find.TAG("*",c),w=L+=null==m?1:Math.random()||.1,_=b.length;for(c&&(E=s===O||s||c);p!==_&&null!=(l=b[p]);p++){if(o&&l){for(f=0,s||l.ownerDocument===O||(I(l),a=!U);d=e[f++];)if(d(l,s||O,a)){u.push(l);break}c&&(L=w)}i&&((l=!d&&l)&&h--,r&&v.push(l))}if(h+=p,i&&p!==h){for(f=0;d=n[f++];)d(v,g,s,a);if(r){if(h>0)for(;p--;)v[p]||g[p]||(g[p]=$.call(u));g=y(g)}K.apply(u,g),c&&!r&&g.length>0&&h+n.length>1&&t.uniqueSort(u)}return c&&(L=w,E=m),v};return i?r(s):s}var _,k,C,S,x,R,q,T,E,P,A,I,O,D,U,F,j,z,W,M="sizzle"+1*new Date,N=e.document,L=0,B=0,H=n(),J=n(),Y=n(),V=function(e,t){return e===t&&(A=!0),0},Z={}.hasOwnProperty,G=[],$=G.pop,X=G.push,K=G.push,Q=G.slice,ee=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},te="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ne="[\\x20\\t\\r\\n\\f]",re="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",ie="\\["+ne+"*("+re+")(?:"+ne+"*([*^$|!~]?=)"+ne+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+re+"))|)"+ne+"*\\]",oe=":("+re+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ie+")*)|.*)\\)|)",se=new RegExp(ne+"+","g"),ae=new RegExp("^"+ne+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ne+"+$","g"),ue=new RegExp("^"+ne+"*,"+ne+"*"),ce=new RegExp("^"+ne+"*([>+~]|"+ne+")"+ne+"*"),le=new RegExp("="+ne+"*([^\\]'\"]*?)"+ne+"*\\]","g"),fe=new RegExp(oe),de=new RegExp("^"+re+"$"),he={ID:new RegExp("^#("+re+")"),CLASS:new RegExp("^\\.("+re+")"),TAG:new RegExp("^("+re+"|[*])"),ATTR:new RegExp("^"+ie),PSEUDO:new RegExp("^"+oe),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ne+"*(even|odd|(([+-]|)(\\d*)n|)"+ne+"*(?:([+-]|)"+ne+"*(\\d+)|))"+ne+"*\\)|)","i"),bool:new RegExp("^(?:"+te+")$","i"),needsContext:new RegExp("^"+ne+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ne+"*((?:-\\d)?\\d*)"+ne+"*\\)|)(?=[^-]|$)","i")},pe=/^(?:input|select|textarea|button)$/i,ve=/^h\d$/i,ge=/^[^{]+\{\s*\[native \w/,ye=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,me=/[+~]/,be=new RegExp("\\\\([\\da-f]{1,6}"+ne+"?|("+ne+")|.)","ig"),we=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},_e=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ke=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},Ce=function(){I()},Se=p(function(e){return e.disabled===!0&&("form"in e||"label"in e)},{dir:"parentNode",next:"legend"});try{K.apply(G=Q.call(N.childNodes),N.childNodes),G[N.childNodes.length].nodeType}catch(e){K={apply:G.length?function(e,t){X.apply(e,Q.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}k=t.support={},x=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},I=t.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:N;return r!==O&&9===r.nodeType&&r.documentElement?(O=r,D=O.documentElement,U=!x(O),N!==O&&(n=O.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",Ce,!1):n.attachEvent&&n.attachEvent("onunload",Ce)),k.attributes=i(function(e){return e.className="i",!e.getAttribute("className")}),k.getElementsByTagName=i(function(e){return e.appendChild(O.createComment("")),!e.getElementsByTagName("*").length}),k.getElementsByClassName=ge.test(O.getElementsByClassName),k.getById=i(function(e){return D.appendChild(e).id=M,!O.getElementsByName||!O.getElementsByName(M).length}),k.getById?(C.filter.ID=function(e){var t=e.replace(be,we);return function(e){return e.getAttribute("id")===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&U){var n=t.getElementById(e);return n?[n]:[]}}):(C.filter.ID=function(e){var t=e.replace(be,we);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&U){var n,r,i,o=t.getElementById(e);if(o){if(n=o.getAttributeNode("id"),n&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if(n=o.getAttributeNode("id"),n&&n.value===e)return[o]}return[]}}),C.find.TAG=k.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):k.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},C.find.CLASS=k.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&U)return t.getElementsByClassName(e)},j=[],F=[],(k.qsa=ge.test(O.querySelectorAll))&&(i(function(e){D.appendChild(e).innerHTML="<a id='"+M+"'></a><select id='"+M+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&F.push("[*^$]="+ne+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||F.push("\\["+ne+"*(?:value|"+te+")"),e.querySelectorAll("[id~="+M+"-]").length||F.push("~="),e.querySelectorAll(":checked").length||F.push(":checked"),e.querySelectorAll("a#"+M+"+*").length||F.push(".#.+[+~]")}),i(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=O.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&F.push("name"+ne+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&F.push(":enabled",":disabled"),D.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&F.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),F.push(",.*:")})),(k.matchesSelector=ge.test(z=D.matches||D.webkitMatchesSelector||D.mozMatchesSelector||D.oMatchesSelector||D.msMatchesSelector))&&i(function(e){k.disconnectedMatch=z.call(e,"*"),z.call(e,"[s!='']:x"),j.push("!=",oe)}),F=F.length&&new RegExp(F.join("|")),j=j.length&&new RegExp(j.join("|")),t=ge.test(D.compareDocumentPosition),W=t||ge.test(D.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},V=t?function(e,t){if(e===t)return A=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!k.sortDetached&&t.compareDocumentPosition(e)===n?e===O||e.ownerDocument===N&&W(N,e)?-1:t===O||t.ownerDocument===N&&W(N,t)?1:P?ee(P,e)-ee(P,t):0:4&n?-1:1)}:function(e,t){if(e===t)return A=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],u=[t];if(!i||!o)return e===O?-1:t===O?1:i?-1:o?1:P?ee(P,e)-ee(P,t):0;if(i===o)return s(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;a[r]===u[r];)r++;return r?s(a[r],u[r]):a[r]===N?-1:u[r]===N?1:0},O):O},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==O&&I(e),n=n.replace(le,"='$1']"),k.matchesSelector&&U&&!Y[n+" "]&&(!j||!j.test(n))&&(!F||!F.test(n)))try{var r=z.call(e,n);if(r||k.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(e){}return t(n,O,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==O&&I(e),W(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==O&&I(e);var n=C.attrHandle[t.toLowerCase()],r=n&&Z.call(C.attrHandle,t.toLowerCase())?n(e,t,!U):void 0;return void 0!==r?r:k.attributes||!U?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},t.escape=function(e){return(e+"").replace(_e,ke)},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],r=0,i=0;if(A=!k.detectDuplicates,P=!k.sortStable&&e.slice(0),e.sort(V),A){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return P=null,e},S=t.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=S(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=S(t);return n},C=t.selectors={cacheLength:50,createPseudo:r,match:he,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(be,we),e[3]=(e[3]||e[4]||e[5]||"").replace(be,we),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return he.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&fe.test(n)&&(t=R(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(be,we).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=H[e+" "];return t||(t=new RegExp("(^|"+ne+")"+e+"("+ne+"|$)"))&&H(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(i){var o=t.attr(i,e);return null==o?"!="===n:!n||(o+="","="===n?o===r:"!="===n?o!==r:"^="===n?r&&0===o.indexOf(r):"*="===n?r&&o.indexOf(r)>-1:"$="===n?r&&o.slice(-r.length)===r:"~="===n?(" "+o.replace(se," ")+" ").indexOf(r)>-1:"|="===n&&(o===r||o.slice(0,r.length+1)===r+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var c,l,f,d,h,p,v=o!==s?"nextSibling":"previousSibling",g=t.parentNode,y=a&&t.nodeName.toLowerCase(),m=!u&&!a,b=!1;if(g){if(o){for(;v;){for(d=t;d=d[v];)if(a?d.nodeName.toLowerCase()===y:1===d.nodeType)return!1;p=v="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?g.firstChild:g.lastChild],s&&m){for(d=g,f=d[M]||(d[M]={}),l=f[d.uniqueID]||(f[d.uniqueID]={}),c=l[e]||[],h=c[0]===L&&c[1],b=h&&c[2],d=h&&g.childNodes[h];d=++h&&d&&d[v]||(b=h=0)||p.pop();)if(1===d.nodeType&&++b&&d===t){l[e]=[L,h,b];break}}else if(m&&(d=t,f=d[M]||(d[M]={}),l=f[d.uniqueID]||(f[d.uniqueID]={}),c=l[e]||[],h=c[0]===L&&c[1],b=h),b===!1)for(;(d=++h&&d&&d[v]||(b=h=0)||p.pop())&&((a?d.nodeName.toLowerCase()!==y:1!==d.nodeType)||!++b||(m&&(f=d[M]||(d[M]={}),l=f[d.uniqueID]||(f[d.uniqueID]={}),l[e]=[L,b]),d!==t)););return b-=i,b===r||b%r===0&&b/r>=0}}},PSEUDO:function(e,n){var i,o=C.pseudos[e]||C.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[M]?o(n):o.length>1?(i=[e,e,"",n],C.setFilters.hasOwnProperty(e.toLowerCase())?r(function(e,t){for(var r,i=o(e,n),s=i.length;s--;)r=ee(e,i[s]),e[r]=!(t[r]=i[s])}):function(e){return o(e,0,i)}):o}},pseudos:{not:r(function(e){var t=[],n=[],i=q(e.replace(ae,"$1"));return i[M]?r(function(e,t,n,r){for(var o,s=i(e,null,r,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:r(function(e){return function(n){return t(e,n).length>0}}),contains:r(function(e){return e=e.replace(be,we),function(t){return(t.textContent||t.innerText||S(t)).indexOf(e)>-1}}),lang:r(function(e){return de.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(be,we).toLowerCase(),function(t){var n;do if(n=U?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===D},focus:function(e){return e===O.activeElement&&(!O.hasFocus||O.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:c(!1),disabled:c(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!C.pseudos.empty(e)},header:function(e){return ve.test(e.nodeName)},input:function(e){return pe.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:l(function(){return[0]}),last:l(function(e,t){return[t-1]}),eq:l(function(e,t,n){return[n<0?n+t:n]}),even:l(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:l(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:l(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:l(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},C.pseudos.nth=C.pseudos.eq;for(_ in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})C.pseudos[_]=a(_);for(_ in{submit:!0,reset:!0})C.pseudos[_]=u(_);return d.prototype=C.filters=C.pseudos,C.setFilters=new d,R=t.tokenize=function(e,n){var r,i,o,s,a,u,c,l=J[e+" "];if(l)return n?0:l.slice(0);for(a=e,u=[],c=C.preFilter;a;){r&&!(i=ue.exec(a))||(i&&(a=a.slice(i[0].length)||a),u.push(o=[])),r=!1,(i=ce.exec(a))&&(r=i.shift(),o.push({value:r,type:i[0].replace(ae," ")}),a=a.slice(r.length));for(s in C.filter)!(i=he[s].exec(a))||c[s]&&!(i=c[s](i))||(r=i.shift(),o.push({value:r,type:s,matches:i}),a=a.slice(r.length));if(!r)break}return n?a.length:a?t.error(e):J(e,u).slice(0)},q=t.compile=function(e,t){var n,r=[],i=[],o=Y[e+" "];if(!o){for(t||(t=R(e)),n=t.length;n--;)o=b(t[n]),o[M]?r.push(o):i.push(o);o=Y(e,w(i,r)),o.selector=e}return o},T=t.select=function(e,t,n,r){var i,o,s,a,u,c="function"==typeof e&&e,l=!r&&R(e=c.selector||e);if(n=n||[],1===l.length){if(o=l[0]=l[0].slice(0),o.length>2&&"ID"===(s=o[0]).type&&9===t.nodeType&&U&&C.relative[o[1].type]){if(t=(C.find.ID(s.matches[0].replace(be,we),t)||[])[0],!t)return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=he.needsContext.test(e)?0:o.length;i--&&(s=o[i],!C.relative[a=s.type]);)if((u=C.find[a])&&(r=u(s.matches[0].replace(be,we),me.test(o[0].type)&&f(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&h(o),!e)return K.apply(n,r),n;break}}return(c||q(e,l))(r,t,!U,n,!t||me.test(e)&&f(t.parentNode)||t),n},k.sortStable=M.split("").sort(V).join("")===M,k.detectDuplicates=!!A,I(),k.sortDetached=i(function(e){return 1&e.compareDocumentPosition(O.createElement("fieldset"))}),i(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),k.attributes&&i(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),i(function(e){return null==e.getAttribute("disabled")})||o(te,function(e,t,n){var r;if(!n)return e[t]===!0?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),t}(o);we.find=xe,we.expr=xe.selectors,we.expr[":"]=we.expr.pseudos,we.uniqueSort=we.unique=xe.uniqueSort,we.text=xe.getText,we.isXMLDoc=xe.isXML,we.contains=xe.contains,we.escapeSelector=xe.escape;var Re=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&we(e).is(n))break;r.push(e)}return r},qe=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},Te=we.expr.match.needsContext,Ee=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i,Pe=/^.[^:#\[\.,]*$/;we.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?we.find.matchesSelector(r,e)?[r]:[]:we.find.matches(e,we.grep(t,function(e){return 1===e.nodeType}))},we.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(we(e).filter(function(){for(t=0;t<r;t++)if(we.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)we.find(e,i[t],n);return r>1?we.uniqueSort(n):n},filter:function(e){return this.pushStack(l(this,e||[],!1))},not:function(e){return this.pushStack(l(this,e||[],!0))},is:function(e){return!!l(this,"string"==typeof e&&Te.test(e)?we(e):e||[],!1).length}});var Ae,Ie=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Oe=we.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||Ae,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:Ie.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof we?t[0]:t,we.merge(this,we.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:ae,!0)),Ee.test(r[1])&&we.isPlainObject(t))for(r in t)we.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=ae.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):we.isFunction(e)?void 0!==n.ready?n.ready(e):e(we):we.makeArray(e,this)};Oe.prototype=we.fn,Ae=we(ae);var De=/^(?:parents|prev(?:Until|All))/,Ue={children:!0,contents:!0,next:!0,prev:!0};we.fn.extend({has:function(e){var t=we(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(we.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],s="string"!=typeof e&&we(e);if(!Te.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&we.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?we.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?de.call(we(e),this[0]):de.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(we.uniqueSort(we.merge(this.get(),we(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),we.each({parent:function e(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return Re(e,"parentNode")},parentsUntil:function(e,t,n){return Re(e,"parentNode",n)},next:function(e){return f(e,"nextSibling")},prev:function(e){return f(e,"previousSibling")},nextAll:function(e){return Re(e,"nextSibling")},prevAll:function(e){return Re(e,"previousSibling")},nextUntil:function(e,t,n){return Re(e,"nextSibling",n)},prevUntil:function(e,t,n){return Re(e,"previousSibling",n)},siblings:function(e){return qe((e.parentNode||{}).firstChild,e)},children:function(e){return qe(e.firstChild)},contents:function(e){return c(e,"iframe")?e.contentDocument:(c(e,"template")&&(e=e.content||e),we.merge([],e.childNodes))}},function(e,t){we.fn[e]=function(n,r){var i=we.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=we.filter(r,i)),this.length>1&&(Ue[e]||we.uniqueSort(i),De.test(e)&&i.reverse()),this.pushStack(i)}});var Fe=/[^\x20\t\r\n\f]+/g;we.Callbacks=function(e){e="string"==typeof e?d(e):we.extend({},e);var t,n,r,i,o=[],s=[],a=-1,u=function(){for(i=i||e.once,r=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)o[a].apply(n[0],n[1])===!1&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){we.each(n,function(n,r){we.isFunction(r)?e.unique&&c.has(r)||o.push(r):r&&r.length&&"string"!==we.type(r)&&t(r)})}(arguments),n&&!t&&u()),this},remove:function(){return we.each(arguments,function(e,t){for(var n;(n=we.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--}),this},has:function(e){return e?we.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],s.push(n),t||u()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},we.extend({Deferred:function(e){var t=[["notify","progress",we.Callbacks("memory"),we.Callbacks("memory"),2],["resolve","done",we.Callbacks("once memory"),we.Callbacks("once memory"),0,"resolved"],["reject","fail",we.Callbacks("once memory"),we.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return we.Deferred(function(n){we.each(t,function(t,r){var i=we.isFunction(e[r[4]])&&e[r[4]];s[r[1]](function(){var e=i&&i.apply(this,arguments);e&&we.isFunction(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(e,r,i){function s(e,t,r,i){return function(){var u=this,c=arguments,l=function(){var o,l;if(!(e<a)){if(o=r.apply(u,c),o===t.promise())throw new TypeError("Thenable self-resolution");l=o&&("object"===("undefined"==typeof o?"undefined":n(o))||"function"==typeof o)&&o.then,we.isFunction(l)?i?l.call(o,s(a,t,h,i),s(a,t,p,i)):(a++,l.call(o,s(a,t,h,i),s(a,t,p,i),s(a,t,h,t.notifyWith))):(r!==h&&(u=void 0,c=[o]),(i||t.resolveWith)(u,c))}},f=i?l:function(){try{l()}catch(n){we.Deferred.exceptionHook&&we.Deferred.exceptionHook(n,f.stackTrace),e+1>=a&&(r!==p&&(u=void 0,c=[n]),t.rejectWith(u,c))}};e?f():(we.Deferred.getStackHook&&(f.stackTrace=we.Deferred.getStackHook()),o.setTimeout(f))}}var a=0;return we.Deferred(function(n){t[0][3].add(s(0,n,we.isFunction(i)?i:h,n.notifyWith)),t[1][3].add(s(0,n,we.isFunction(e)?e:h)),t[2][3].add(s(0,n,we.isFunction(r)?r:p))}).promise()},promise:function(e){return null!=e?we.extend(e,i):i}},s={};return we.each(t,function(e,n){var o=n[2],a=n[5];i[n[1]]=o.add,a&&o.add(function(){r=a},t[3-e][2].disable,t[0][2].lock),o.add(n[3].fire),s[n[0]]=function(){return s[n[0]+"With"](this===s?void 0:this,arguments),this},s[n[0]+"With"]=o.fireWith}),i.promise(s),e&&e.call(s,s),s},when:function(e){var t=arguments.length,n=t,r=Array(n),i=ce.call(arguments),o=we.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?ce.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(v(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||we.isFunction(i[n]&&i[n].then)))return o.then();for(;n--;)v(i[n],s(n),o.reject);return o.promise()}});var je=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;we.Deferred.exceptionHook=function(e,t){o.console&&o.console.warn&&e&&je.test(e.name)&&o.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},we.readyException=function(e){o.setTimeout(function(){throw e})};var ze=we.Deferred();we.fn.ready=function(e){return ze.then(e).catch(function(e){we.readyException(e)}),this},we.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--we.readyWait:we.isReady)||(we.isReady=!0,e!==!0&&--we.readyWait>0||ze.resolveWith(ae,[we]))}}),we.ready.then=ze.then,"complete"===ae.readyState||"loading"!==ae.readyState&&!ae.documentElement.doScroll?o.setTimeout(we.ready):(ae.addEventListener("DOMContentLoaded",g),o.addEventListener("load",g));var We=function e(t,n,r,i,o,s,a){var u=0,c=t.length,l=null==r;if("object"===we.type(r)){o=!0;for(u in r)e(t,n,u,r[u],!0,s,a)}else if(void 0!==i&&(o=!0,we.isFunction(i)||(a=!0),l&&(a?(n.call(t,i),n=null):(l=n,n=function(e,t,n){return l.call(we(e),n)})),n))for(;u<c;u++)n(t[u],r,a?i:i.call(t[u],u,n(t[u],r)));return o?t:l?n.call(t):c?n(t[0],r):s},Me=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};y.uid=1,y.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Me(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[we.camelCase(t)]=n;else for(r in t)i[we.camelCase(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][we.camelCase(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(we.camelCase):(t=we.camelCase(t),t=t in r?[t]:t.match(Fe)||[]),n=t.length;for(;n--;)delete r[t[n]]}(void 0===t||we.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!we.isEmptyObject(t)}};var Ne=new y,Le=new y,Be=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,He=/[A-Z]/g;we.extend({hasData:function(e){return Le.hasData(e)||Ne.hasData(e)},data:function(e,t,n){return Le.access(e,t,n)},removeData:function(e,t){Le.remove(e,t)},_data:function(e,t,n){return Ne.access(e,t,n)},_removeData:function(e,t){Ne.remove(e,t)}}),we.fn.extend({data:function e(t,r){var i,o,e,s=this[0],a=s&&s.attributes;if(void 0===t){if(this.length&&(e=Le.get(s),1===s.nodeType&&!Ne.get(s,"hasDataAttrs"))){for(i=a.length;i--;)a[i]&&(o=a[i].name,0===o.indexOf("data-")&&(o=we.camelCase(o.slice(5)),b(s,o,e[o])));Ne.set(s,"hasDataAttrs",!0)}return e}return"object"===("undefined"==typeof t?"undefined":n(t))?this.each(function(){Le.set(this,t)}):We(this,function(e){var n;if(s&&void 0===e){if(n=Le.get(s,t),void 0!==n)return n;if(n=b(s,t),void 0!==n)return n}else this.each(function(){Le.set(this,t,e)})},null,r,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){Le.remove(this,e)})}}),we.extend({queue:function e(t,n,r){var e;if(t)return n=(n||"fx")+"queue",e=Ne.get(t,n),r&&(!e||Array.isArray(r)?e=Ne.access(t,n,we.makeArray(r)):e.push(r)),e||[]},dequeue:function(e,t){t=t||"fx";var n=we.queue(e,t),r=n.length,i=n.shift(),o=we._queueHooks(e,t),s=function(){we.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,s,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Ne.get(e,n)||Ne.access(e,n,{empty:we.Callbacks("once memory").add(function(){Ne.remove(e,[t+"queue",n])})})}}),we.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?we.queue(this[0],e):void 0===t?this:this.each(function(){var n=we.queue(this,e,t);we._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&we.dequeue(this,e)})},dequeue:function(e){return this.each(function(){we.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=we.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)n=Ne.get(o[s],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var Je=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ye=new RegExp("^(?:([+-])=|)("+Je+")([a-z%]*)$","i"),Ve=["Top","Right","Bottom","Left"],Ze=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&we.contains(e.ownerDocument,e)&&"none"===we.css(e,"display")},Ge=function(e,t,n,r){var i,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=s[o];return i},$e={};we.fn.extend({show:function(){return k(this,!0)},hide:function(){return k(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Ze(this)?we(this).show():we(this).hide()})}});var Xe=/^(?:checkbox|radio)$/i,Ke=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,Qe=/^$|\/(?:java|ecma)script/i,et={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]
};et.optgroup=et.option,et.tbody=et.tfoot=et.colgroup=et.caption=et.thead,et.th=et.td;var tt=/<|&#?\w+;/;!function(){var e=ae.createDocumentFragment(),t=e.appendChild(ae.createElement("div")),n=ae.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),me.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",me.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var nt=ae.documentElement,rt=/^key/,it=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ot=/^([^.]*)(?:\.(.+)|)/;we.event={global:{},add:function(e,t,n,r,i){var o,s,a,u,c,l,f,d,h,p,v,g=Ne.get(e);if(g)for(n.handler&&(o=n,n=o.handler,i=o.selector),i&&we.find.matchesSelector(nt,i),n.guid||(n.guid=we.guid++),(u=g.events)||(u=g.events={}),(s=g.handle)||(s=g.handle=function(t){return"undefined"!=typeof we&&we.event.triggered!==t.type?we.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(Fe)||[""],c=t.length;c--;)a=ot.exec(t[c])||[],h=v=a[1],p=(a[2]||"").split(".").sort(),h&&(f=we.event.special[h]||{},h=(i?f.delegateType:f.bindType)||h,f=we.event.special[h]||{},l=we.extend({type:h,origType:v,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&we.expr.match.needsContext.test(i),namespace:p.join(".")},o),(d=u[h])||(d=u[h]=[],d.delegateCount=0,f.setup&&f.setup.call(e,r,p,s)!==!1||e.addEventListener&&e.addEventListener(h,s)),f.add&&(f.add.call(e,l),l.handler.guid||(l.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,l):d.push(l),we.event.global[h]=!0)},remove:function(e,t,n,r,i){var o,s,a,u,c,l,f,d,h,p,v,g=Ne.hasData(e)&&Ne.get(e);if(g&&(u=g.events)){for(t=(t||"").match(Fe)||[""],c=t.length;c--;)if(a=ot.exec(t[c])||[],h=v=a[1],p=(a[2]||"").split(".").sort(),h){for(f=we.event.special[h]||{},h=(r?f.delegateType:f.bindType)||h,d=u[h]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)l=d[o],!i&&v!==l.origType||n&&n.guid!==l.guid||a&&!a.test(l.namespace)||r&&r!==l.selector&&("**"!==r||!l.selector)||(d.splice(o,1),l.selector&&d.delegateCount--,f.remove&&f.remove.call(e,l));s&&!d.length&&(f.teardown&&f.teardown.call(e,p,g.handle)!==!1||we.removeEvent(e,h,g.handle),delete u[h])}else for(h in u)we.event.remove(e,h+t[c],n,r,!0);we.isEmptyObject(u)&&Ne.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,s,a=we.event.fix(e),u=new Array(arguments.length),c=(Ne.get(this,"events")||{})[a.type]||[],l=we.event.special[a.type]||{};for(u[0]=a,t=1;t<arguments.length;t++)u[t]=arguments[t];if(a.delegateTarget=this,!l.preDispatch||l.preDispatch.call(this,a)!==!1){for(s=we.event.handlers.call(this,a,c),t=0;(i=s[t++])&&!a.isPropagationStopped();)for(a.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,r=((we.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,u),void 0!==r&&(a.result=r)===!1&&(a.preventDefault(),a.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,a),a.result}},handlers:function(e,t){var n,r,i,o,s,a=[],u=t.delegateCount,c=e.target;if(u&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||c.disabled!==!0)){for(o=[],s={},n=0;n<u;n++)r=t[n],i=r.selector+" ",void 0===s[i]&&(s[i]=r.needsContext?we(i,this).index(c)>-1:we.find(i,this,null,[c]).length),s[i]&&o.push(r);o.length&&a.push({elem:c,handlers:o})}return c=this,u<t.length&&a.push({elem:c,handlers:t.slice(u)}),a},addProp:function(e,t){Object.defineProperty(we.Event.prototype,e,{enumerable:!0,configurable:!0,get:we.isFunction(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[we.expando]?e:new we.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==T()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===T()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&c(this,"input"))return this.click(),!1},_default:function(e){return c(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},we.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},we.Event=function(e,t){return this instanceof we.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?R:q,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&we.extend(this,t),this.timeStamp=e&&e.timeStamp||we.now(),void(this[we.expando]=!0)):new we.Event(e,t)},we.Event.prototype={constructor:we.Event,isDefaultPrevented:q,isPropagationStopped:q,isImmediatePropagationStopped:q,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=R,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=R,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=R,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},we.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&rt.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&it.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},we.event.addProp),we.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){we.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||we.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),we.fn.extend({on:function(e,t,n,r){return E(this,e,t,n,r)},one:function(e,t,n,r){return E(this,e,t,n,r,1)},off:function(e,t,r){var i,o;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,we(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"===("undefined"==typeof e?"undefined":n(e))){for(o in e)this.off(o,t,e[o]);return this}return t!==!1&&"function"!=typeof t||(r=t,t=void 0),r===!1&&(r=q),this.each(function(){we.event.remove(this,e,r,t)})}});var st=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,at=/<script|<style|<link/i,ut=/checked\s*(?:[^=]|=\s*.checked.)/i,ct=/^true\/(.*)/,lt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;we.extend({htmlPrefilter:function(e){return e.replace(st,"<$1></$2>")},clone:function e(t,n,r){var i,o,s,a,e=t.cloneNode(!0),u=we.contains(t.ownerDocument,t);if(!(me.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||we.isXMLDoc(t)))for(a=C(e),s=C(t),i=0,o=s.length;i<o;i++)D(s[i],a[i]);if(n)if(r)for(s=s||C(t),a=a||C(e),i=0,o=s.length;i<o;i++)O(s[i],a[i]);else O(t,e);return a=C(e,"script"),a.length>0&&S(a,!u&&C(t,"script")),e},cleanData:function(e){for(var t,n,r,i=we.event.special,o=0;void 0!==(n=e[o]);o++)if(Me(n)){if(t=n[Ne.expando]){if(t.events)for(r in t.events)i[r]?we.event.remove(n,r):we.removeEvent(n,r,t.handle);n[Ne.expando]=void 0}n[Le.expando]&&(n[Le.expando]=void 0)}}}),we.fn.extend({detach:function(e){return F(this,e,!0)},remove:function(e){return F(this,e)},text:function(e){return We(this,function(e){return void 0===e?we.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return U(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=P(this,e);t.appendChild(e)}})},prepend:function(){return U(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=P(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return U(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return U(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(we.cleanData(C(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return we.clone(this,e,t)})},html:function(e){return We(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!at.test(e)&&!et[(Ke.exec(e)||["",""])[1].toLowerCase()]){e=we.htmlPrefilter(e);try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(we.cleanData(C(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return U(this,arguments,function(t){var n=this.parentNode;we.inArray(this,e)<0&&(we.cleanData(C(this)),n&&n.replaceChild(t,this))},e)}}),we.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){we.fn[e]=function(e){for(var n,r=[],i=we(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),we(i[s])[t](n),fe.apply(r,n.get());return this.pushStack(r)}});var ft=/^margin/,dt=new RegExp("^("+Je+")(?!px)[a-z%]+$","i"),ht=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=o),t.getComputedStyle(e)};!function(){function e(){if(a){a.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",a.innerHTML="",nt.appendChild(s);var e=o.getComputedStyle(a);t="1%"!==e.top,i="2px"===e.marginLeft,n="4px"===e.width,a.style.marginRight="50%",r="4px"===e.marginRight,nt.removeChild(s),a=null}}var t,n,r,i,s=ae.createElement("div"),a=ae.createElement("div");a.style&&(a.style.backgroundClip="content-box",a.cloneNode(!0).style.backgroundClip="",me.clearCloneStyle="content-box"===a.style.backgroundClip,s.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",s.appendChild(a),we.extend(me,{pixelPosition:function(){return e(),t},boxSizingReliable:function(){return e(),n},pixelMarginRight:function(){return e(),r},reliableMarginLeft:function(){return e(),i}}))}();var pt=/^(none|table(?!-c[ea]).+)/,vt=/^--/,gt={position:"absolute",visibility:"hidden",display:"block"},yt={letterSpacing:"0",fontWeight:"400"},mt=["Webkit","Moz","ms"],bt=ae.createElement("div").style;we.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=j(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function e(t,r,i,o){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,a,u,c=we.camelCase(r),l=vt.test(r),e=t.style;return l||(r=M(c)),u=we.cssHooks[r]||we.cssHooks[c],void 0===i?u&&"get"in u&&void 0!==(s=u.get(t,!1,o))?s:e[r]:(a="undefined"==typeof i?"undefined":n(i),"string"===a&&(s=Ye.exec(i))&&s[1]&&(i=w(t,r,s),a="number"),null!=i&&i===i&&("number"===a&&(i+=s&&s[3]||(we.cssNumber[c]?"":"px")),me.clearCloneStyle||""!==i||0!==r.indexOf("background")||(e[r]="inherit"),u&&"set"in u&&void 0===(i=u.set(t,i,o))||(l?e.setProperty(r,i):e[r]=i)),void 0)}},css:function(e,t,n,r){var i,o,s,a=we.camelCase(t),u=vt.test(t);return u||(t=M(a)),s=we.cssHooks[t]||we.cssHooks[a],s&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=j(e,t,r)),"normal"===i&&t in yt&&(i=yt[t]),""===n||n?(o=parseFloat(i),n===!0||isFinite(o)?o||0:i):i}}),we.each(["height","width"],function(e,t){we.cssHooks[t]={get:function(e,n,r){if(n)return!pt.test(we.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?B(e,t,r):Ge(e,gt,function(){return B(e,t,r)})},set:function(e,n,r){var i,o=r&&ht(e),s=r&&L(e,t,r,"border-box"===we.css(e,"boxSizing",!1,o),o);return s&&(i=Ye.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=we.css(e,t)),N(e,n,s)}}}),we.cssHooks.marginLeft=z(me.reliableMarginLeft,function(e,t){if(t)return(parseFloat(j(e,"marginLeft"))||e.getBoundingClientRect().left-Ge(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),we.each({margin:"",padding:"",border:"Width"},function(e,t){we.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+Ve[r]+t]=o[r]||o[r-2]||o[0];return i}},ft.test(e)||(we.cssHooks[e+t].set=N)}),we.fn.extend({css:function(e,t){return We(this,function(e,t,n){var r,i,o={},s=0;if(Array.isArray(t)){for(r=ht(e),i=t.length;s<i;s++)o[t[s]]=we.css(e,t[s],!1,r);return o}return void 0!==n?we.style(e,t,n):we.css(e,t)},e,t,arguments.length>1)}}),we.Tween=H,H.prototype={constructor:H,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||we.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(we.cssNumber[n]?"":"px")},cur:function(){var e=H.propHooks[this.prop];return e&&e.get?e.get(this):H.propHooks._default.get(this)},run:function(e){var t,n=H.propHooks[this.prop];return this.options.duration?this.pos=t=we.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):H.propHooks._default.set(this),this}},H.prototype.init.prototype=H.prototype,H.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=we.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){we.fx.step[e.prop]?we.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[we.cssProps[e.prop]]&&!we.cssHooks[e.prop]?e.elem[e.prop]=e.now:we.style(e.elem,e.prop,e.now+e.unit)}}},H.propHooks.scrollTop=H.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},we.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},we.fx=H.prototype.init,we.fx.step={};var wt,_t,kt=/^(?:toggle|show|hide)$/,Ct=/queueHooks$/;we.Animation=we.extend(X,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return w(n.elem,e,Ye.exec(t),n),n}]},tweener:function(e,t){we.isFunction(e)?(t=e,e=["*"]):e=e.match(Fe);for(var n,r=0,i=e.length;r<i;r++)n=e[r],X.tweeners[n]=X.tweeners[n]||[],X.tweeners[n].unshift(t)},prefilters:[G],prefilter:function(e,t){t?X.prefilters.unshift(e):X.prefilters.push(e)}}),we.speed=function(e,t,r){var i=e&&"object"===("undefined"==typeof e?"undefined":n(e))?we.extend({},e):{complete:r||!r&&t||we.isFunction(e)&&e,duration:e,easing:r&&t||t&&!we.isFunction(t)&&t};return we.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in we.fx.speeds?i.duration=we.fx.speeds[i.duration]:i.duration=we.fx.speeds._default),null!=i.queue&&i.queue!==!0||(i.queue="fx"),i.old=i.complete,i.complete=function(){we.isFunction(i.old)&&i.old.call(this),i.queue&&we.dequeue(this,i.queue)},i},we.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Ze).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=we.isEmptyObject(e),o=we.speed(t,n,r),s=function(){var t=X(this,we.extend({},e),o);(i||Ne.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||o.queue===!1?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=we.timers,s=Ne.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&Ct.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||we.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=Ne.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=we.timers,s=r?r.length:0;for(n.finish=!0,we.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),we.each(["toggle","show","hide"],function(e,t){var n=we.fn[t];we.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(V(t,!0),e,r,i)}}),we.each({slideDown:V("show"),slideUp:V("hide"),slideToggle:V("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){we.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),we.timers=[],we.fx.tick=function(){var e,t=0,n=we.timers;for(wt=we.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||we.fx.stop(),wt=void 0},we.fx.timer=function(e){we.timers.push(e),we.fx.start()},we.fx.interval=13,we.fx.start=function(){_t||(_t=!0,J())},we.fx.stop=function(){_t=null},we.fx.speeds={slow:600,fast:200,_default:400},we.fn.delay=function(e,t){return e=we.fx?we.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=o.setTimeout(t,e);n.stop=function(){o.clearTimeout(r)}})},function(){var e=ae.createElement("input"),t=ae.createElement("select"),n=t.appendChild(ae.createElement("option"));e.type="checkbox",me.checkOn=""!==e.value,me.optSelected=n.selected,e=ae.createElement("input"),e.value="t",e.type="radio",me.radioValue="t"===e.value}();var St,xt=we.expr.attrHandle;we.fn.extend({attr:function(e,t){return We(this,we.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){we.removeAttr(this,e)})}}),we.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?we.prop(e,t,n):(1===o&&we.isXMLDoc(e)||(i=we.attrHooks[t.toLowerCase()]||(we.expr.match.bool.test(t)?St:void 0)),void 0!==n?null===n?void we.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=we.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!me.radioValue&&"radio"===t&&c(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(Fe);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),St={set:function(e,t,n){return t===!1?we.removeAttr(e,n):e.setAttribute(n,n),n}},we.each(we.expr.match.bool.source.match(/\w+/g),function(e,t){var n=xt[t]||we.find.attr;xt[t]=function(e,t,r){var i,o,s=t.toLowerCase();return r||(o=xt[s],xt[s]=i,i=null!=n(e,t,r)?s:null,xt[s]=o),i}});var Rt=/^(?:input|select|textarea|button)$/i,qt=/^(?:a|area)$/i;we.fn.extend({prop:function(e,t){return We(this,we.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[we.propFix[e]||e]})}}),we.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&we.isXMLDoc(e)||(t=we.propFix[t]||t,i=we.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=we.find.attr(e,"tabindex");return t?parseInt(t,10):Rt.test(e.nodeName)||qt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),me.optSelected||(we.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),we.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){we.propFix[this.toLowerCase()]=this}),we.fn.extend({addClass:function(e){var t,n,r,i,o,s,a,u=0;if(we.isFunction(e))return this.each(function(t){we(this).addClass(e.call(this,t,Q(this)))});if("string"==typeof e&&e)for(t=e.match(Fe)||[];n=this[u++];)if(i=Q(n),r=1===n.nodeType&&" "+K(i)+" "){for(s=0;o=t[s++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");a=K(r),i!==a&&n.setAttribute("class",a)}return this},removeClass:function(e){var t,n,r,i,o,s,a,u=0;if(we.isFunction(e))return this.each(function(t){we(this).removeClass(e.call(this,t,Q(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(Fe)||[];n=this[u++];)if(i=Q(n),r=1===n.nodeType&&" "+K(i)+" "){for(s=0;o=t[s++];)for(;r.indexOf(" "+o+" ")>-1;)r=r.replace(" "+o+" "," ");a=K(r),i!==a&&n.setAttribute("class",a)}return this},toggleClass:function(e,t){var r="undefined"==typeof e?"undefined":n(e);return"boolean"==typeof t&&"string"===r?t?this.addClass(e):this.removeClass(e):we.isFunction(e)?this.each(function(n){we(this).toggleClass(e.call(this,n,Q(this),t),t)}):this.each(function(){var t,n,i,o;if("string"===r)for(n=0,i=we(this),o=e.match(Fe)||[];t=o[n++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else void 0!==e&&"boolean"!==r||(t=Q(this),t&&Ne.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||e===!1?"":Ne.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+K(Q(n))+" ").indexOf(t)>-1)return!0;return!1}});var Tt=/\r/g;we.fn.extend({val:function(e){var t,n,r,i=this[0];{if(arguments.length)return r=we.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,we(this).val()):e,null==i?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=we.map(i,function(e){return null==e?"":e+""})),t=we.valHooks[this.type]||we.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))});if(i)return t=we.valHooks[i.type]||we.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(Tt,""):null==n?"":n)}}}),we.extend({valHooks:{option:{get:function(e){var t=we.find.attr(e,"value");return null!=t?t:K(we.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],u=s?o+1:i.length;for(r=o<0?u:s?o:0;r<u;r++)if(n=i[r],(n.selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!c(n.parentNode,"optgroup"))){if(t=we(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=we.makeArray(t),s=i.length;s--;)r=i[s],(r.selected=we.inArray(we.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),we.each(["radio","checkbox"],function(){we.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=we.inArray(we(e).val(),t)>-1}},me.checkOn||(we.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Et=/^(?:focusinfocus|focusoutblur)$/;we.extend(we.event,{trigger:function(e,t,r,i){var s,a,u,c,l,f,d,h=[r||ae],p=ve.call(e,"type")?e.type:e,v=ve.call(e,"namespace")?e.namespace.split("."):[];if(a=u=r=r||ae,3!==r.nodeType&&8!==r.nodeType&&!Et.test(p+we.event.triggered)&&(p.indexOf(".")>-1&&(v=p.split("."),p=v.shift(),v.sort()),l=p.indexOf(":")<0&&"on"+p,e=e[we.expando]?e:new we.Event(p,"object"===("undefined"==typeof e?"undefined":n(e))&&e),e.isTrigger=i?2:3,e.namespace=v.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),t=null==t?[e]:we.makeArray(t,[e]),d=we.event.special[p]||{},i||!d.trigger||d.trigger.apply(r,t)!==!1)){if(!i&&!d.noBubble&&!we.isWindow(r)){for(c=d.delegateType||p,Et.test(c+p)||(a=a.parentNode);a;a=a.parentNode)h.push(a),u=a;u===(r.ownerDocument||ae)&&h.push(u.defaultView||u.parentWindow||o)}for(s=0;(a=h[s++])&&!e.isPropagationStopped();)e.type=s>1?c:d.bindType||p,f=(Ne.get(a,"events")||{})[e.type]&&Ne.get(a,"handle"),f&&f.apply(a,t),f=l&&a[l],f&&f.apply&&Me(a)&&(e.result=f.apply(a,t),e.result===!1&&e.preventDefault());return e.type=p,i||e.isDefaultPrevented()||d._default&&d._default.apply(h.pop(),t)!==!1||!Me(r)||l&&we.isFunction(r[p])&&!we.isWindow(r)&&(u=r[l],u&&(r[l]=null),we.event.triggered=p,r[p](),we.event.triggered=void 0,u&&(r[l]=u)),e.result}},simulate:function(e,t,n){var r=we.extend(new we.Event,n,{type:e,isSimulated:!0});we.event.trigger(r,null,t)}}),we.fn.extend({trigger:function(e,t){return this.each(function(){we.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return we.event.trigger(e,t,n,!0)}}),we.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){we.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),we.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),me.focusin="onfocusin"in o,me.focusin||we.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){we.event.simulate(t,e.target,we.event.fix(e))};we.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=Ne.access(r,t);i||r.addEventListener(e,n,!0),Ne.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=Ne.access(r,t)-1;i?Ne.access(r,t,i):(r.removeEventListener(e,n,!0),Ne.remove(r,t))}}});var Pt=o.location,At=we.now(),It=/\?/;we.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new o.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||we.error("Invalid XML: "+e),t};var Ot=/\[\]$/,Dt=/\r?\n/g,Ut=/^(?:submit|button|image|reset|file)$/i,Ft=/^(?:input|select|textarea|keygen)/i;we.param=function(e,t){var n,r=[],i=function(e,t){var n=we.isFunction(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(Array.isArray(e)||e.jquery&&!we.isPlainObject(e))we.each(e,function(){i(this.name,this.value)});else for(n in e)ee(n,e[n],t,i);return r.join("&")},we.fn.extend({serialize:function(){return we.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=we.prop(this,"elements");return e?we.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!we(this).is(":disabled")&&Ft.test(this.nodeName)&&!Ut.test(e)&&(this.checked||!Xe.test(e))}).map(function(e,t){var n=we(this).val();return null==n?null:Array.isArray(n)?we.map(n,function(e){return{name:t.name,value:e.replace(Dt,"\r\n")}}):{name:t.name,value:n.replace(Dt,"\r\n")}}).get()}});var jt=/%20/g,zt=/#.*$/,Wt=/([?&])_=[^&]*/,Mt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Nt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Lt=/^(?:GET|HEAD)$/,Bt=/^\/\//,Ht={},Jt={},Yt="*/".concat("*"),Vt=ae.createElement("a");Vt.href=Pt.href,we.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Pt.href,type:"GET",isLocal:Nt.test(Pt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Yt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":we.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?re(re(e,we.ajaxSettings),t):re(we.ajaxSettings,e)},ajaxPrefilter:te(Ht),ajaxTransport:te(Jt),ajax:function(e,t){function r(e,t,n,r){var u,l,h,p,_,k=t;f||(f=!0,c&&o.clearTimeout(c),i=void 0,a=r||"",S.readyState=e>0?4:0,u=e>=200&&e<300||304===e,n&&(p=ie(v,S,n)),p=oe(v,p,S,u),u?(v.ifModified&&(_=S.getResponseHeader("Last-Modified"),_&&(we.lastModified[s]=_),_=S.getResponseHeader("etag"),_&&(we.etag[s]=_)),204===e||"HEAD"===v.type?k="nocontent":304===e?k="notmodified":(k=p.state,l=p.data,h=p.error,u=!h)):(h=k,!e&&k||(k="error",e<0&&(e=0))),S.status=e,S.statusText=(t||k)+"",u?m.resolveWith(g,[l,k,S]):m.rejectWith(g,[S,k,h]),S.statusCode(w),w=void 0,d&&y.trigger(u?"ajaxSuccess":"ajaxError",[S,v,u?l:h]),b.fireWith(g,[S,k]),d&&(y.trigger("ajaxComplete",[S,v]),--we.active||we.event.trigger("ajaxStop")))}"object"===("undefined"==typeof e?"undefined":n(e))&&(t=e,e=void 0),t=t||{};var i,s,a,u,c,l,f,d,h,p,v=we.ajaxSetup({},t),g=v.context||v,y=v.context&&(g.nodeType||g.jquery)?we(g):we.event,m=we.Deferred(),b=we.Callbacks("once memory"),w=v.statusCode||{},_={},k={},C="canceled",S={readyState:0,getResponseHeader:function(e){var t;if(f){if(!u)for(u={};t=Mt.exec(a);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return f?a:null},setRequestHeader:function(e,t){return null==f&&(e=k[e.toLowerCase()]=k[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==f&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(f)S.always(e[S.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||C;return i&&i.abort(t),r(0,t),this}};if(m.promise(S),v.url=((e||v.url||Pt.href)+"").replace(Bt,Pt.protocol+"//"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(Fe)||[""],null==v.crossDomain){l=ae.createElement("a");try{l.href=v.url,l.href=l.href,v.crossDomain=Vt.protocol+"//"+Vt.host!=l.protocol+"//"+l.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=we.param(v.data,v.traditional)),ne(Ht,v,t,S),f)return S;d=we.event&&v.global,d&&0===we.active++&&we.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Lt.test(v.type),s=v.url.replace(zt,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(jt,"+")):(p=v.url.slice(s.length),v.data&&(s+=(It.test(s)?"&":"?")+v.data,delete v.data),v.cache===!1&&(s=s.replace(Wt,"$1"),p=(It.test(s)?"&":"?")+"_="+At++ +p),v.url=s+p),v.ifModified&&(we.lastModified[s]&&S.setRequestHeader("If-Modified-Since",we.lastModified[s]),we.etag[s]&&S.setRequestHeader("If-None-Match",we.etag[s])),(v.data&&v.hasContent&&v.contentType!==!1||t.contentType)&&S.setRequestHeader("Content-Type",v.contentType),S.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+Yt+"; q=0.01":""):v.accepts["*"]);for(h in v.headers)S.setRequestHeader(h,v.headers[h]);if(v.beforeSend&&(v.beforeSend.call(g,S,v)===!1||f))return S.abort();if(C="abort",b.add(v.complete),S.done(v.success),S.fail(v.error),i=ne(Jt,v,t,S)){if(S.readyState=1,d&&y.trigger("ajaxSend",[S,v]),f)return S;v.async&&v.timeout>0&&(c=o.setTimeout(function(){S.abort("timeout")},v.timeout));try{f=!1,i.send(_,r)}catch(e){if(f)throw e;r(-1,e)}}else r(-1,"No Transport");return S},getJSON:function(e,t,n){return we.get(e,t,n,"json")},getScript:function(e,t){return we.get(e,void 0,t,"script")}}),we.each(["get","post"],function(e,t){we[t]=function(e,n,r,i){return we.isFunction(n)&&(i=i||r,r=n,n=void 0),we.ajax(we.extend({url:e,type:t,dataType:i,data:n,success:r},we.isPlainObject(e)&&e))}}),we._evalUrl=function(e){return we.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},we.fn.extend({wrapAll:function(e){var t;return this[0]&&(we.isFunction(e)&&(e=e.call(this[0])),t=we(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return we.isFunction(e)?this.each(function(t){we(this).wrapInner(e.call(this,t))}):this.each(function(){var t=we(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=we.isFunction(e);return this.each(function(n){
we(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){we(this).replaceWith(this.childNodes)}),this}}),we.expr.pseudos.hidden=function(e){return!we.expr.pseudos.visible(e)},we.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},we.ajaxSettings.xhr=function(){try{return new o.XMLHttpRequest}catch(e){}};var Zt={0:200,1223:204},Gt=we.ajaxSettings.xhr();me.cors=!!Gt&&"withCredentials"in Gt,me.ajax=Gt=!!Gt,we.ajaxTransport(function(e){var t,n;if(me.cors||Gt&&!e.crossDomain)return{send:function(r,i){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest");for(s in r)a.setRequestHeader(s,r[s]);t=function(e){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?i(0,"error"):i(a.status,a.statusText):i(Zt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=t("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&o.setTimeout(function(){t&&n()})},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}}),we.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),we.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return we.globalEval(e),e}}}),we.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),we.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(r,i){t=we("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),ae.head.appendChild(t[0])},abort:function(){n&&n()}}}});var $t=[],Xt=/(=)\?(?=&|$)|\?\?/;we.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=$t.pop()||we.expando+"_"+At++;return this[e]=!0,e}}),we.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,s,a=e.jsonp!==!1&&(Xt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Xt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=we.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Xt,"$1"+r):e.jsonp!==!1&&(e.url+=(It.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return s||we.error(r+" was not called"),s[0]},e.dataTypes[0]="json",i=o[r],o[r]=function(){s=arguments},n.always(function(){void 0===i?we(o).removeProp(r):o[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,$t.push(r)),s&&we.isFunction(i)&&i(s[0]),s=i=void 0}),"script"}),me.createHTMLDocument=function(){var e=ae.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),we.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var r,i,o;return t||(me.createHTMLDocument?(t=ae.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=ae.location.href,t.head.appendChild(r)):t=ae),i=Ee.exec(e),o=!n&&[],i?[t.createElement(i[1])]:(i=x([e],t,o),o&&o.length&&we(o).remove(),we.merge([],i.childNodes))},we.fn.load=function(e,t,r){var i,o,s,a=this,u=e.indexOf(" ");return u>-1&&(i=K(e.slice(u)),e=e.slice(0,u)),we.isFunction(t)?(r=t,t=void 0):t&&"object"===("undefined"==typeof t?"undefined":n(t))&&(o="POST"),a.length>0&&we.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){s=arguments,a.html(i?we("<div>").append(we.parseHTML(e)).find(i):e)}).always(r&&function(e,t){a.each(function(){r.apply(this,s||[e.responseText,t,e])})}),this},we.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){we.fn[t]=function(e){return this.on(t,e)}}),we.expr.pseudos.animated=function(e){return we.grep(we.timers,function(t){return e===t.elem}).length},we.offset={setOffset:function(e,t,n){var r,i,o,s,a,u,c,l=we.css(e,"position"),f=we(e),d={};"static"===l&&(e.style.position="relative"),a=f.offset(),o=we.css(e,"top"),u=we.css(e,"left"),c=("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1,c?(r=f.position(),s=r.top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(u)||0),we.isFunction(t)&&(t=t.call(e,n,we.extend({},a))),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+i),"using"in t?t.using.call(e,d):f.css(d)}},we.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){we.offset.setOffset(this,e,t)});var t,n,r,i,o=this[0];if(o)return o.getClientRects().length?(r=o.getBoundingClientRect(),t=o.ownerDocument,n=t.documentElement,i=t.defaultView,{top:r.top+i.pageYOffset-n.clientTop,left:r.left+i.pageXOffset-n.clientLeft}):{top:0,left:0}},position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===we.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),c(e[0],"html")||(r=e.offset()),r={top:r.top+we.css(e[0],"borderTopWidth",!0),left:r.left+we.css(e[0],"borderLeftWidth",!0)}),{top:t.top-r.top-we.css(n,"marginTop",!0),left:t.left-r.left-we.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===we.css(e,"position");)e=e.offsetParent;return e||nt})}}),we.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;we.fn[e]=function(r){return We(this,function(e,r,i){var o;return we.isWindow(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i?o?o[t]:e[r]:void(o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i)},e,r,arguments.length)}}),we.each(["top","left"],function(e,t){we.cssHooks[t]=z(me.pixelPosition,function(e,n){if(n)return n=j(e,t),dt.test(n)?we(e).position()[t]+"px":n})}),we.each({Height:"height",Width:"width"},function(e,t){we.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){we.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(i===!0||o===!0?"margin":"border");return We(this,function(t,n,i){var o;return we.isWindow(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?we.css(t,n,a):we.style(t,n,i,a)},t,s?i:void 0,s)}})}),we.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),we.holdReady=function(e){e?we.readyWait++:we.ready(!0)},we.isArray=Array.isArray,we.parseJSON=JSON.parse,we.nodeName=c,r=[],i=function(){return we}.apply(t,r),!(void 0!==i&&(e.exports=i));var Kt=o.jQuery,Qt=o.$;return we.noConflict=function(e){return o.$===we&&(o.$=Qt),e&&o.jQuery===we&&(o.jQuery=Kt),we},s||(o.jQuery=o.$=we),we})}).call(t,n(157)(e))},function(e,t,n){var r,i,o,i;(function(s){"use strict";var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(o,s){"object"===a(t)&&"undefined"!=typeof e?e.exports=s(o):(r=s,i="function"==typeof r?r.call(t,n,t,e):r,!(void 0!==i&&(e.exports=i)))}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof s?s:void 0,function(r){var s,a=r.Base64,u="2.4.8";if("undefined"!=typeof e&&e.exports)if("undefined"!=typeof navigator&&"ReactNative"==navigator.product);else try{s=n(79).Buffer}catch(e){}var c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=function(e){for(var t={},n=0,r=e.length;n<r;n++)t[e.charAt(n)]=n;return t}(c),f=String.fromCharCode,d=function(e){if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?f(192|t>>>6)+f(128|63&t):f(224|t>>>12&15)+f(128|t>>>6&63)+f(128|63&t)}var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return f(240|t>>>18&7)+f(128|t>>>12&63)+f(128|t>>>6&63)+f(128|63&t)},h=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,p=function(e){return e.replace(h,d)},v=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0),r=[c.charAt(n>>>18),c.charAt(n>>>12&63),t>=2?"=":c.charAt(n>>>6&63),t>=1?"=":c.charAt(63&n)];return r.join("")},g=r.btoa?function(e){return r.btoa(e)}:function(e){return e.replace(/[\s\S]{1,3}/g,v)},y=s?s.from&&Uint8Array&&s.from!==Uint8Array.from?function(e){return(e.constructor===s.constructor?e:s.from(e)).toString("base64")}:function(e){return(e.constructor===s.constructor?e:new s(e)).toString("base64")}:function(e){return g(p(e))},m=function(e,t){return t?y(String(e)).replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,""):y(String(e))},b=function(e){return m(e,!0)},w=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),_=function(e){switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return f((n>>>10)+55296)+f((1023&n)+56320);case 3:return f((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return f((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},k=function(e){return e.replace(w,_)},C=function(e){var t=e.length,n=t%4,r=(t>0?l[e.charAt(0)]<<18:0)|(t>1?l[e.charAt(1)]<<12:0)|(t>2?l[e.charAt(2)]<<6:0)|(t>3?l[e.charAt(3)]:0),i=[f(r>>>16),f(r>>>8&255),f(255&r)];return i.length-=[0,0,2,1][n],i.join("")},S=r.atob?function(e){return r.atob(e)}:function(e){return e.replace(/[\s\S]{1,4}/g,C)},x=s?s.from&&Uint8Array&&s.from!==Uint8Array.from?function(e){return(e.constructor===s.constructor?e:s.from(e,"base64")).toString()}:function(e){return(e.constructor===s.constructor?e:new s(e,"base64")).toString()}:function(e){return k(S(e))},R=function(e){return x(String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))},q=function(){var e=r.Base64;return r.Base64=a,e};if(r.Base64={VERSION:u,atob:S,btoa:g,fromBase64:R,toBase64:m,utob:p,encode:m,encodeURI:b,btou:k,decode:R,noConflict:q},"function"==typeof Object.defineProperty){var T=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}};r.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",T(function(){return R(this)})),Object.defineProperty(String.prototype,"toBase64",T(function(e){return m(this,e)})),Object.defineProperty(String.prototype,"toBase64URI",T(function(){return m(this,!0)}))}}return r.Meteor&&(Base64=r.Base64),"undefined"!=typeof e&&e.exports?e.exports.Base64=r.Base64:(o=[],i=function(){return r.Base64}.apply(t,o),!(void 0!==i&&(e.exports=i))),{Base64:r.Base64}})}).call(t,function(){return this}())},function(e,t,n){"use strict";var r=n(16).assign,i=n(147),o=n(148),s=n(73),a={};r(a,i,o,s),e.exports=a},function(e,t,n){"use strict";function r(e){if(!(this instanceof r))return new r(e);this.options=u.assign({level:m,method:w,chunkSize:16384,windowBits:15,memLevel:8,strategy:b,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new f,this.strm.avail_out=0;var n=a.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==v)throw new Error(l[n]);if(t.header&&a.deflateSetHeader(this.strm,t.header),t.dictionary){var i;if(i="string"==typeof t.dictionary?c.string2buf(t.dictionary):"[object ArrayBuffer]"===d.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,n=a.deflateSetDictionary(this.strm,i),n!==v)throw new Error(l[n]);this._dict_set=!0}}function i(e,t){var n=new r(t);if(n.push(e,!0),n.err)throw n.msg||l[n.err];return n.result}function o(e,t){return t=t||{},t.raw=!0,i(e,t)}function s(e,t){return t=t||{},t.gzip=!0,i(e,t)}var a=n(149),u=n(16),c=n(71),l=n(48),f=n(75),d=Object.prototype.toString,h=0,p=4,v=0,g=1,y=2,m=-1,b=0,w=8;r.prototype.push=function(e,t){var n,r,i=this.strm,o=this.options.chunkSize;if(this.ended)return!1;r=t===~~t?t:t===!0?p:h,"string"==typeof e?i.input=c.string2buf(e):"[object ArrayBuffer]"===d.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new u.Buf8(o),i.next_out=0,i.avail_out=o),n=a.deflate(i,r),n!==g&&n!==v)return this.onEnd(n),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||r!==p&&r!==y)||("string"===this.options.to?this.onData(c.buf2binstring(u.shrinkBuf(i.output,i.next_out))):this.onData(u.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&n!==g);return r===p?(n=a.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===v):r!==y||(this.onEnd(v),i.avail_out=0,!0)},r.prototype.onData=function(e){this.chunks.push(e)},r.prototype.onEnd=function(e){e===v&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=u.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.Deflate=r,t.deflate=i,t.deflateRaw=o,t.gzip=s},function(e,t,n){"use strict";function r(e){if(!(this instanceof r))return new r(e);this.options=a.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0===(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new f,this.strm.avail_out=0;var n=s.inflateInit2(this.strm,t.windowBits);if(n!==c.Z_OK)throw new Error(l[n]);this.header=new d,s.inflateGetHeader(this.strm,this.header)}function i(e,t){var n=new r(t);if(n.push(e,!0),n.err)throw n.msg||l[n.err];return n.result}function o(e,t){return t=t||{},t.raw=!0,i(e,t)}var s=n(152),a=n(16),u=n(71),c=n(73),l=n(48),f=n(75),d=n(150),h=Object.prototype.toString;r.prototype.push=function(e,t){var n,r,i,o,l,f,d=this.strm,p=this.options.chunkSize,v=this.options.dictionary,g=!1;if(this.ended)return!1;r=t===~~t?t:t===!0?c.Z_FINISH:c.Z_NO_FLUSH,"string"==typeof e?d.input=u.binstring2buf(e):"[object ArrayBuffer]"===h.call(e)?d.input=new Uint8Array(e):d.input=e,d.next_in=0,d.avail_in=d.input.length;do{if(0===d.avail_out&&(d.output=new a.Buf8(p),d.next_out=0,d.avail_out=p),n=s.inflate(d,c.Z_NO_FLUSH),n===c.Z_NEED_DICT&&v&&(f="string"==typeof v?u.string2buf(v):"[object ArrayBuffer]"===h.call(v)?new Uint8Array(v):v,n=s.inflateSetDictionary(this.strm,f)),n===c.Z_BUF_ERROR&&g===!0&&(n=c.Z_OK,g=!1),n!==c.Z_STREAM_END&&n!==c.Z_OK)return this.onEnd(n),this.ended=!0,!1;d.next_out&&(0!==d.avail_out&&n!==c.Z_STREAM_END&&(0!==d.avail_in||r!==c.Z_FINISH&&r!==c.Z_SYNC_FLUSH)||("string"===this.options.to?(i=u.utf8border(d.output,d.next_out),o=d.next_out-i,l=u.buf2string(d.output,i),d.next_out=o,d.avail_out=p-o,o&&a.arraySet(d.output,d.output,i,o,0),this.onData(l)):this.onData(a.shrinkBuf(d.output,d.next_out)))),0===d.avail_in&&0===d.avail_out&&(g=!0)}while((d.avail_in>0||0===d.avail_out)&&n!==c.Z_STREAM_END);return n===c.Z_STREAM_END&&(r=c.Z_FINISH),r===c.Z_FINISH?(n=s.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===c.Z_OK):r!==c.Z_SYNC_FLUSH||(this.onEnd(c.Z_OK),d.avail_out=0,!0)},r.prototype.onData=function(e){this.chunks.push(e)},r.prototype.onEnd=function(e){e===c.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=a.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.Inflate=r,t.inflate=i,t.inflateRaw=o,t.ungzip=i},function(e,t,n){"use strict";function r(e,t){return e.msg=D[t],t}function i(e){return(e<<1)-(e>4?9:0)}function o(e){for(var t=e.length;--t>=0;)e[t]=0}function s(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(P.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))}function a(e,t){A._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,s(e.strm)}function u(e,t){e.pending_buf[e.pending++]=t}function c(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function l(e,t,n,r){var i=e.avail_in;return i>r&&(i=r),0===i?0:(e.avail_in-=i,P.arraySet(t,e.input,e.next_in,i,n),1===e.state.wrap?e.adler=I(e.adler,t,i,n):2===e.state.wrap&&(e.adler=O(e.adler,t,i,n)),e.next_in+=i,e.total_in+=i,i)}function f(e,t){var n,r,i=e.max_chain_length,o=e.strstart,s=e.prev_length,a=e.nice_match,u=e.strstart>e.w_size-fe?e.strstart-(e.w_size-fe):0,c=e.window,l=e.w_mask,f=e.prev,d=e.strstart+le,h=c[o+s-1],p=c[o+s];e.prev_length>=e.good_match&&(i>>=2),a>e.lookahead&&(a=e.lookahead);do if(n=t,c[n+s]===p&&c[n+s-1]===h&&c[n]===c[o]&&c[++n]===c[o+1]){o+=2,n++;do;while(c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&o<d);if(r=le-(d-o),o=d-le,r>s){if(e.match_start=t,s=r,r>=a)break;h=c[o+s-1],p=c[o+s]}}while((t=f[t&l])>u&&0!==--i);return s<=e.lookahead?s:e.lookahead}function d(e){var t,n,r,i,o,s=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-fe)){P.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,n=e.hash_size,t=n;do r=e.head[--t],e.head[t]=r>=s?r-s:0;while(--n);n=s,t=n;do r=e.prev[--t],e.prev[t]=r>=s?r-s:0;while(--n);i+=s}if(0===e.strm.avail_in)break;if(n=l(e.strm,e.window,e.strstart+e.lookahead,i),e.lookahead+=n,e.lookahead+e.insert>=ce)for(o=e.strstart-e.insert,e.ins_h=e.window[o],e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+ce-1])&e.hash_mask,e.prev[o&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=o,o++,e.insert--,!(e.lookahead+e.insert<ce)););}while(e.lookahead<fe&&0!==e.strm.avail_in)}function h(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(d(e),0===e.lookahead&&t===U)return we;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,a(e,!1),0===e.strm.avail_out))return we;if(e.strstart-e.block_start>=e.w_size-fe&&(a(e,!1),0===e.strm.avail_out))return we}return e.insert=0,t===z?(a(e,!0),0===e.strm.avail_out?ke:Ce):e.strstart>e.block_start&&(a(e,!1),0===e.strm.avail_out)?we:we}function p(e,t){for(var n,r;;){if(e.lookahead<fe){if(d(e),e.lookahead<fe&&t===U)return we;if(0===e.lookahead)break}if(n=0,e.lookahead>=ce&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+ce-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-fe&&(e.match_length=f(e,n)),e.match_length>=ce)if(r=A._tr_tally(e,e.strstart-e.match_start,e.match_length-ce),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=ce){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+ce-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(0!==--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=A._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(a(e,!1),0===e.strm.avail_out))return we}return e.insert=e.strstart<ce-1?e.strstart:ce-1,t===z?(a(e,!0),0===e.strm.avail_out?ke:Ce):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?we:_e}function v(e,t){for(var n,r,i;;){if(e.lookahead<fe){if(d(e),e.lookahead<fe&&t===U)return we;if(0===e.lookahead)break}if(n=0,e.lookahead>=ce&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+ce-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=ce-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-fe&&(e.match_length=f(e,n),e.match_length<=5&&(e.strategy===Y||e.match_length===ce&&e.strstart-e.match_start>4096)&&(e.match_length=ce-1)),e.prev_length>=ce&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-ce,r=A._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-ce),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+ce-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(0!==--e.prev_length);if(e.match_available=0,e.match_length=ce-1,e.strstart++,r&&(a(e,!1),0===e.strm.avail_out))return we}else if(e.match_available){if(r=A._tr_tally(e,0,e.window[e.strstart-1]),r&&a(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return we}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=A._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<ce-1?e.strstart:ce-1,t===z?(a(e,!0),0===e.strm.avail_out?ke:Ce):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?we:_e}function g(e,t){for(var n,r,i,o,s=e.window;;){if(e.lookahead<=le){if(d(e),e.lookahead<=le&&t===U)return we;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=ce&&e.strstart>0&&(i=e.strstart-1,r=s[i],r===s[++i]&&r===s[++i]&&r===s[++i])){o=e.strstart+le;do;while(r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&i<o);e.match_length=le-(o-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=ce?(n=A._tr_tally(e,1,e.match_length-ce),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=A._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(a(e,!1),0===e.strm.avail_out))return we}return e.insert=0,t===z?(a(e,!0),0===e.strm.avail_out?ke:Ce):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?we:_e}function y(e,t){for(var n;;){if(0===e.lookahead&&(d(e),0===e.lookahead)){if(t===U)return we;break}if(e.match_length=0,n=A._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(a(e,!1),0===e.strm.avail_out))return we}return e.insert=0,t===z?(a(e,!0),0===e.strm.avail_out?ke:Ce):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?we:_e}function m(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}function b(e){e.window_size=2*e.w_size,o(e.head),e.max_lazy_match=E[e.level].max_lazy,e.good_match=E[e.level].good_length,e.nice_match=E[e.level].nice_length,e.max_chain_length=E[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=ce-1,e.match_available=0,e.ins_h=0}function w(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=K,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new P.Buf16(2*ae),this.dyn_dtree=new P.Buf16(2*(2*oe+1)),this.bl_tree=new P.Buf16(2*(2*se+1)),o(this.dyn_ltree),o(this.dyn_dtree),o(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new P.Buf16(ue+1),this.heap=new P.Buf16(2*ie+1),o(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new P.Buf16(2*ie+1),o(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function _(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=X,t=e.state,t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?he:me,e.adler=2===t.wrap?0:1,t.last_flush=U,A._tr_init(t),M):r(e,L)}function k(e){var t=_(e);return t===M&&b(e.state),t}function C(e,t){return e&&e.state?2!==e.state.wrap?L:(e.state.gzhead=t,M):L}function S(e,t,n,i,o,s){if(!e)return L;var a=1;if(t===J&&(t=6),i<0?(a=0,i=-i):i>15&&(a=2,i-=16),o<1||o>Q||n!==K||i<8||i>15||t<0||t>9||s<0||s>G)return r(e,L);8===i&&(i=9);var u=new w;return e.state=u,u.strm=e,u.wrap=a,u.gzhead=null,u.w_bits=i,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=o+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+ce-1)/ce),u.window=new P.Buf8(2*u.w_size),u.head=new P.Buf16(u.hash_size),u.prev=new P.Buf16(u.w_size),u.lit_bufsize=1<<o+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new P.Buf8(u.pending_buf_size),u.d_buf=1*u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=t,u.strategy=s,u.method=n,k(e)}function x(e,t){return S(e,t,K,ee,te,$)}function R(e,t){var n,a,l,f;if(!e||!e.state||t>W||t<0)return e?r(e,L):L;if(a=e.state,!e.output||!e.input&&0!==e.avail_in||a.status===be&&t!==z)return r(e,0===e.avail_out?H:L);if(a.strm=e,n=a.last_flush,a.last_flush=t,a.status===he)if(2===a.wrap)e.adler=0,u(a,31),u(a,139),u(a,8),a.gzhead?(u(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),u(a,255&a.gzhead.time),u(a,a.gzhead.time>>8&255),u(a,a.gzhead.time>>16&255),u(a,a.gzhead.time>>24&255),u(a,9===a.level?2:a.strategy>=V||a.level<2?4:0),u(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(u(a,255&a.gzhead.extra.length),u(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=O(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=pe):(u(a,0),u(a,0),u(a,0),u(a,0),u(a,0),u(a,9===a.level?2:a.strategy>=V||a.level<2?4:0),u(a,Se),a.status=me);else{var d=K+(a.w_bits-8<<4)<<8,h=-1;h=a.strategy>=V||a.level<2?0:a.level<6?1:6===a.level?2:3,d|=h<<6,0!==a.strstart&&(d|=de),d+=31-d%31,a.status=me,c(a,d),0!==a.strstart&&(c(a,e.adler>>>16),c(a,65535&e.adler)),e.adler=1}if(a.status===pe)if(a.gzhead.extra){for(l=a.pending;a.gzindex<(65535&a.gzhead.extra.length)&&(a.pending!==a.pending_buf_size||(a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),s(e),l=a.pending,a.pending!==a.pending_buf_size));)u(a,255&a.gzhead.extra[a.gzindex]),a.gzindex++;a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=ve)}else a.status=ve;if(a.status===ve)if(a.gzhead.name){l=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),s(e),l=a.pending,a.pending===a.pending_buf_size)){f=1;break}f=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,u(a,f)}while(0!==f);a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),0===f&&(a.gzindex=0,a.status=ge)}else a.status=ge;if(a.status===ge)if(a.gzhead.comment){l=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),s(e),l=a.pending,a.pending===a.pending_buf_size)){f=1;break}f=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,u(a,f)}while(0!==f);a.gzhead.hcrc&&a.pending>l&&(e.adler=O(e.adler,a.pending_buf,a.pending-l,l)),0===f&&(a.status=ye)}else a.status=ye;if(a.status===ye&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&s(e),a.pending+2<=a.pending_buf_size&&(u(a,255&e.adler),u(a,e.adler>>8&255),e.adler=0,a.status=me)):a.status=me),0!==a.pending){if(s(e),0===e.avail_out)return a.last_flush=-1,M}else if(0===e.avail_in&&i(t)<=i(n)&&t!==z)return r(e,H);if(a.status===be&&0!==e.avail_in)return r(e,H);if(0!==e.avail_in||0!==a.lookahead||t!==U&&a.status!==be){var p=a.strategy===V?y(a,t):a.strategy===Z?g(a,t):E[a.level].func(a,t);if(p!==ke&&p!==Ce||(a.status=be),p===we||p===ke)return 0===e.avail_out&&(a.last_flush=-1),M;if(p===_e&&(t===F?A._tr_align(a):t!==W&&(A._tr_stored_block(a,0,0,!1),t===j&&(o(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),s(e),0===e.avail_out))return a.last_flush=-1,M}return t!==z?M:a.wrap<=0?N:(2===a.wrap?(u(a,255&e.adler),u(a,e.adler>>8&255),u(a,e.adler>>16&255),u(a,e.adler>>24&255),u(a,255&e.total_in),u(a,e.total_in>>8&255),u(a,e.total_in>>16&255),u(a,e.total_in>>24&255)):(c(a,e.adler>>>16),c(a,65535&e.adler)),s(e),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?M:N)}function q(e){var t;return e&&e.state?(t=e.state.status,t!==he&&t!==pe&&t!==ve&&t!==ge&&t!==ye&&t!==me&&t!==be?r(e,L):(e.state=null,t===me?r(e,B):M)):L}function T(e,t){var n,r,i,s,a,u,c,l,f=t.length;if(!e||!e.state)return L;if(n=e.state,s=n.wrap,2===s||1===s&&n.status!==he||n.lookahead)return L;for(1===s&&(e.adler=I(e.adler,t,f,0)),n.wrap=0,f>=n.w_size&&(0===s&&(o(n.head),n.strstart=0,n.block_start=0,n.insert=0),l=new P.Buf8(n.w_size),P.arraySet(l,t,f-n.w_size,n.w_size,0),t=l,f=n.w_size),a=e.avail_in,u=e.next_in,c=e.input,e.avail_in=f,e.next_in=0,e.input=t,d(n);n.lookahead>=ce;){r=n.strstart,i=n.lookahead-(ce-1);do n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+ce-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++;while(--i);n.strstart=r,n.lookahead=ce-1,d(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=ce-1,n.match_available=0,e.next_in=u,e.input=c,e.avail_in=a,n.wrap=s,M}var E,P=n(16),A=n(154),I=n(72),O=n(74),D=n(48),U=0,F=1,j=3,z=4,W=5,M=0,N=1,L=-2,B=-3,H=-5,J=-1,Y=1,V=2,Z=3,G=4,$=0,X=2,K=8,Q=9,ee=15,te=8,ne=29,re=256,ie=re+1+ne,oe=30,se=19,ae=2*ie+1,ue=15,ce=3,le=258,fe=le+ce+1,de=32,he=42,pe=69,ve=73,ge=91,ye=103,me=113,be=666,we=1,_e=2,ke=3,Ce=4,Se=3;E=[new m(0,0,0,0,h),new m(4,4,8,4,p),new m(4,5,16,8,p),new m(4,6,32,32,p),new m(4,4,16,16,v),new m(8,16,32,32,v),new m(8,16,128,128,v),new m(8,32,128,256,v),new m(32,128,258,1024,v),new m(32,258,258,4096,v)],t.deflateInit=x,t.deflateInit2=S,t.deflateReset=k,t.deflateResetKeep=_,t.deflateSetHeader=C,t.deflate=R,t.deflateEnd=q,t.deflateSetDictionary=T,t.deflateInfo="pako deflate (from Nodeca project)"},function(e,t){"use strict";function n(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}e.exports=n},function(e,t){"use strict";var n=30,r=12;e.exports=function(e,t){var i,o,s,a,u,c,l,f,d,h,p,v,g,y,m,b,w,_,k,C,S,x,R,q,T;i=e.state,o=e.next_in,q=e.input,s=o+(e.avail_in-5),a=e.next_out,T=e.output,u=a-(t-e.avail_out),c=a+(e.avail_out-257),l=i.dmax,f=i.wsize,d=i.whave,h=i.wnext,p=i.window,v=i.hold,g=i.bits,y=i.lencode,m=i.distcode,b=(1<<i.lenbits)-1,w=(1<<i.distbits)-1;e:do{g<15&&(v+=q[o++]<<g,g+=8,v+=q[o++]<<g,g+=8),_=y[v&b];t:for(;;){if(k=_>>>24,v>>>=k,g-=k,k=_>>>16&255,0===k)T[a++]=65535&_;else{if(!(16&k)){if(0===(64&k)){_=y[(65535&_)+(v&(1<<k)-1)];continue t}if(32&k){i.mode=r;break e}e.msg="invalid literal/length code",i.mode=n;break e}C=65535&_,k&=15,k&&(g<k&&(v+=q[o++]<<g,g+=8),C+=v&(1<<k)-1,v>>>=k,g-=k),g<15&&(v+=q[o++]<<g,g+=8,v+=q[o++]<<g,g+=8),_=m[v&w];n:for(;;){if(k=_>>>24,v>>>=k,g-=k,k=_>>>16&255,!(16&k)){if(0===(64&k)){_=m[(65535&_)+(v&(1<<k)-1)];continue n}e.msg="invalid distance code",i.mode=n;break e}if(S=65535&_,k&=15,g<k&&(v+=q[o++]<<g,g+=8,g<k&&(v+=q[o++]<<g,g+=8)),S+=v&(1<<k)-1,S>l){e.msg="invalid distance too far back",i.mode=n;break e}if(v>>>=k,g-=k,k=a-u,S>k){if(k=S-k,k>d&&i.sane){e.msg="invalid distance too far back",i.mode=n;break e}if(x=0,R=p,0===h){if(x+=f-k,k<C){C-=k;do T[a++]=p[x++];while(--k);x=a-S,R=T}}else if(h<k){if(x+=f+h-k,k-=h,k<C){C-=k;do T[a++]=p[x++];while(--k);if(x=0,h<C){k=h,C-=k;do T[a++]=p[x++];while(--k);x=a-S,R=T}}}else if(x+=h-k,k<C){
C-=k;do T[a++]=p[x++];while(--k);x=a-S,R=T}for(;C>2;)T[a++]=R[x++],T[a++]=R[x++],T[a++]=R[x++],C-=3;C&&(T[a++]=R[x++],C>1&&(T[a++]=R[x++]))}else{x=a-S;do T[a++]=T[x++],T[a++]=T[x++],T[a++]=T[x++],C-=3;while(C>2);C&&(T[a++]=T[x++],C>1&&(T[a++]=T[x++]))}break}}break}}while(o<s&&a<c);C=g>>3,o-=C,g-=C<<3,v&=(1<<g)-1,e.next_in=o,e.next_out=a,e.avail_in=o<s?5+(s-o):5-(o-s),e.avail_out=a<c?257+(c-a):257-(a-c),i.hold=v,i.bits=g}},function(e,t,n){"use strict";function r(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function i(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new m.Buf16(320),this.work=new m.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function o(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=j,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new m.Buf32(ve),t.distcode=t.distdyn=new m.Buf32(ge),t.sane=1,t.back=-1,E):I}function s(e){var t;return e&&e.state?(t=e.state,t.wsize=0,t.whave=0,t.wnext=0,o(e)):I}function a(e,t){var n,r;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=(t>>4)+1,t<48&&(t&=15)),t&&(t<8||t>15)?I:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,s(e))):I}function u(e,t){var n,r;return e?(r=new i,e.state=r,r.window=null,n=a(e,t),n!==E&&(e.state=null),n):I}function c(e){return u(e,me)}function l(e){if(be){var t;for(g=new m.Buf32(512),y=new m.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(k(S,e.lens,0,288,g,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;k(x,e.lens,0,32,y,0,e.work,{bits:5}),be=!1}e.lencode=g,e.lenbits=9,e.distcode=y,e.distbits=5}function f(e,t,n,r){var i,o=e.state;return null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new m.Buf8(o.wsize)),r>=o.wsize?(m.arraySet(o.window,t,n-o.wsize,o.wsize,0),o.wnext=0,o.whave=o.wsize):(i=o.wsize-o.wnext,i>r&&(i=r),m.arraySet(o.window,t,n-r,i,o.wnext),r-=i,r?(m.arraySet(o.window,t,n-r,r,0),o.wnext=r,o.whave=o.wsize):(o.wnext+=i,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=i))),0}function d(e,t){var n,i,o,s,a,u,c,d,h,p,v,g,y,ve,ge,ye,me,be,we,_e,ke,Ce,Se,xe,Re=0,qe=new m.Buf8(4),Te=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return I;n=e.state,n.mode===Z&&(n.mode=G),a=e.next_out,o=e.output,c=e.avail_out,s=e.next_in,i=e.input,u=e.avail_in,d=n.hold,h=n.bits,p=u,v=c,Ce=E;e:for(;;)switch(n.mode){case j:if(0===n.wrap){n.mode=G;break}for(;h<16;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(2&n.wrap&&35615===d){n.check=0,qe[0]=255&d,qe[1]=d>>>8&255,n.check=w(n.check,qe,2,0),d=0,h=0,n.mode=z;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&d)<<8)+(d>>8))%31){e.msg="incorrect header check",n.mode=de;break}if((15&d)!==F){e.msg="unknown compression method",n.mode=de;break}if(d>>>=4,h-=4,ke=(15&d)+8,0===n.wbits)n.wbits=ke;else if(ke>n.wbits){e.msg="invalid window size",n.mode=de;break}n.dmax=1<<ke,e.adler=n.check=1,n.mode=512&d?Y:Z,d=0,h=0;break;case z:for(;h<16;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(n.flags=d,(255&n.flags)!==F){e.msg="unknown compression method",n.mode=de;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=de;break}n.head&&(n.head.text=d>>8&1),512&n.flags&&(qe[0]=255&d,qe[1]=d>>>8&255,n.check=w(n.check,qe,2,0)),d=0,h=0,n.mode=W;case W:for(;h<32;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.head&&(n.head.time=d),512&n.flags&&(qe[0]=255&d,qe[1]=d>>>8&255,qe[2]=d>>>16&255,qe[3]=d>>>24&255,n.check=w(n.check,qe,4,0)),d=0,h=0,n.mode=M;case M:for(;h<16;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.head&&(n.head.xflags=255&d,n.head.os=d>>8),512&n.flags&&(qe[0]=255&d,qe[1]=d>>>8&255,n.check=w(n.check,qe,2,0)),d=0,h=0,n.mode=N;case N:if(1024&n.flags){for(;h<16;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.length=d,n.head&&(n.head.extra_len=d),512&n.flags&&(qe[0]=255&d,qe[1]=d>>>8&255,n.check=w(n.check,qe,2,0)),d=0,h=0}else n.head&&(n.head.extra=null);n.mode=L;case L:if(1024&n.flags&&(g=n.length,g>u&&(g=u),g&&(n.head&&(ke=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),m.arraySet(n.head.extra,i,s,g,ke)),512&n.flags&&(n.check=w(n.check,i,g,s)),u-=g,s+=g,n.length-=g),n.length))break e;n.length=0,n.mode=B;case B:if(2048&n.flags){if(0===u)break e;g=0;do ke=i[s+g++],n.head&&ke&&n.length<65536&&(n.head.name+=String.fromCharCode(ke));while(ke&&g<u);if(512&n.flags&&(n.check=w(n.check,i,g,s)),u-=g,s+=g,ke)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=H;case H:if(4096&n.flags){if(0===u)break e;g=0;do ke=i[s+g++],n.head&&ke&&n.length<65536&&(n.head.comment+=String.fromCharCode(ke));while(ke&&g<u);if(512&n.flags&&(n.check=w(n.check,i,g,s)),u-=g,s+=g,ke)break e}else n.head&&(n.head.comment=null);n.mode=J;case J:if(512&n.flags){for(;h<16;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(d!==(65535&n.check)){e.msg="header crc mismatch",n.mode=de;break}d=0,h=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=Z;break;case Y:for(;h<32;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}e.adler=n.check=r(d),d=0,h=0,n.mode=V;case V:if(0===n.havedict)return e.next_out=a,e.avail_out=c,e.next_in=s,e.avail_in=u,n.hold=d,n.bits=h,A;e.adler=n.check=1,n.mode=Z;case Z:if(t===q||t===T)break e;case G:if(n.last){d>>>=7&h,h-=7&h,n.mode=ce;break}for(;h<3;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}switch(n.last=1&d,d>>>=1,h-=1,3&d){case 0:n.mode=$;break;case 1:if(l(n),n.mode=ne,t===T){d>>>=2,h-=2;break e}break;case 2:n.mode=Q;break;case 3:e.msg="invalid block type",n.mode=de}d>>>=2,h-=2;break;case $:for(d>>>=7&h,h-=7&h;h<32;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if((65535&d)!==(d>>>16^65535)){e.msg="invalid stored block lengths",n.mode=de;break}if(n.length=65535&d,d=0,h=0,n.mode=X,t===T)break e;case X:n.mode=K;case K:if(g=n.length){if(g>u&&(g=u),g>c&&(g=c),0===g)break e;m.arraySet(o,i,s,g,a),u-=g,s+=g,c-=g,a+=g,n.length-=g;break}n.mode=Z;break;case Q:for(;h<14;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(n.nlen=(31&d)+257,d>>>=5,h-=5,n.ndist=(31&d)+1,d>>>=5,h-=5,n.ncode=(15&d)+4,d>>>=4,h-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=de;break}n.have=0,n.mode=ee;case ee:for(;n.have<n.ncode;){for(;h<3;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.lens[Te[n.have++]]=7&d,d>>>=3,h-=3}for(;n.have<19;)n.lens[Te[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,Se={bits:n.lenbits},Ce=k(C,n.lens,0,19,n.lencode,0,n.work,Se),n.lenbits=Se.bits,Ce){e.msg="invalid code lengths set",n.mode=de;break}n.have=0,n.mode=te;case te:for(;n.have<n.nlen+n.ndist;){for(;Re=n.lencode[d&(1<<n.lenbits)-1],ge=Re>>>24,ye=Re>>>16&255,me=65535&Re,!(ge<=h);){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(me<16)d>>>=ge,h-=ge,n.lens[n.have++]=me;else{if(16===me){for(xe=ge+2;h<xe;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(d>>>=ge,h-=ge,0===n.have){e.msg="invalid bit length repeat",n.mode=de;break}ke=n.lens[n.have-1],g=3+(3&d),d>>>=2,h-=2}else if(17===me){for(xe=ge+3;h<xe;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}d>>>=ge,h-=ge,ke=0,g=3+(7&d),d>>>=3,h-=3}else{for(xe=ge+7;h<xe;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}d>>>=ge,h-=ge,ke=0,g=11+(127&d),d>>>=7,h-=7}if(n.have+g>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=de;break}for(;g--;)n.lens[n.have++]=ke}}if(n.mode===de)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=de;break}if(n.lenbits=9,Se={bits:n.lenbits},Ce=k(S,n.lens,0,n.nlen,n.lencode,0,n.work,Se),n.lenbits=Se.bits,Ce){e.msg="invalid literal/lengths set",n.mode=de;break}if(n.distbits=6,n.distcode=n.distdyn,Se={bits:n.distbits},Ce=k(x,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,Se),n.distbits=Se.bits,Ce){e.msg="invalid distances set",n.mode=de;break}if(n.mode=ne,t===T)break e;case ne:n.mode=re;case re:if(u>=6&&c>=258){e.next_out=a,e.avail_out=c,e.next_in=s,e.avail_in=u,n.hold=d,n.bits=h,_(e,v),a=e.next_out,o=e.output,c=e.avail_out,s=e.next_in,i=e.input,u=e.avail_in,d=n.hold,h=n.bits,n.mode===Z&&(n.back=-1);break}for(n.back=0;Re=n.lencode[d&(1<<n.lenbits)-1],ge=Re>>>24,ye=Re>>>16&255,me=65535&Re,!(ge<=h);){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(ye&&0===(240&ye)){for(be=ge,we=ye,_e=me;Re=n.lencode[_e+((d&(1<<be+we)-1)>>be)],ge=Re>>>24,ye=Re>>>16&255,me=65535&Re,!(be+ge<=h);){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}d>>>=be,h-=be,n.back+=be}if(d>>>=ge,h-=ge,n.back+=ge,n.length=me,0===ye){n.mode=ue;break}if(32&ye){n.back=-1,n.mode=Z;break}if(64&ye){e.msg="invalid literal/length code",n.mode=de;break}n.extra=15&ye,n.mode=ie;case ie:if(n.extra){for(xe=n.extra;h<xe;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.length+=d&(1<<n.extra)-1,d>>>=n.extra,h-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=oe;case oe:for(;Re=n.distcode[d&(1<<n.distbits)-1],ge=Re>>>24,ye=Re>>>16&255,me=65535&Re,!(ge<=h);){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(0===(240&ye)){for(be=ge,we=ye,_e=me;Re=n.distcode[_e+((d&(1<<be+we)-1)>>be)],ge=Re>>>24,ye=Re>>>16&255,me=65535&Re,!(be+ge<=h);){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}d>>>=be,h-=be,n.back+=be}if(d>>>=ge,h-=ge,n.back+=ge,64&ye){e.msg="invalid distance code",n.mode=de;break}n.offset=me,n.extra=15&ye,n.mode=se;case se:if(n.extra){for(xe=n.extra;h<xe;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}n.offset+=d&(1<<n.extra)-1,d>>>=n.extra,h-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=de;break}n.mode=ae;case ae:if(0===c)break e;if(g=v-c,n.offset>g){if(g=n.offset-g,g>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=de;break}g>n.wnext?(g-=n.wnext,y=n.wsize-g):y=n.wnext-g,g>n.length&&(g=n.length),ve=n.window}else ve=o,y=a-n.offset,g=n.length;g>c&&(g=c),c-=g,n.length-=g;do o[a++]=ve[y++];while(--g);0===n.length&&(n.mode=re);break;case ue:if(0===c)break e;o[a++]=n.length,c--,n.mode=re;break;case ce:if(n.wrap){for(;h<32;){if(0===u)break e;u--,d|=i[s++]<<h,h+=8}if(v-=c,e.total_out+=v,n.total+=v,v&&(e.adler=n.check=n.flags?w(n.check,o,v,a-v):b(n.check,o,v,a-v)),v=c,(n.flags?d:r(d))!==n.check){e.msg="incorrect data check",n.mode=de;break}d=0,h=0}n.mode=le;case le:if(n.wrap&&n.flags){for(;h<32;){if(0===u)break e;u--,d+=i[s++]<<h,h+=8}if(d!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=de;break}d=0,h=0}n.mode=fe;case fe:Ce=P;break e;case de:Ce=O;break e;case he:return D;case pe:default:return I}return e.next_out=a,e.avail_out=c,e.next_in=s,e.avail_in=u,n.hold=d,n.bits=h,(n.wsize||v!==e.avail_out&&n.mode<de&&(n.mode<ce||t!==R))&&f(e,e.output,e.next_out,v-e.avail_out)?(n.mode=he,D):(p-=e.avail_in,v-=e.avail_out,e.total_in+=p,e.total_out+=v,n.total+=v,n.wrap&&v&&(e.adler=n.check=n.flags?w(n.check,o,v,e.next_out-v):b(n.check,o,v,e.next_out-v)),e.data_type=n.bits+(n.last?64:0)+(n.mode===Z?128:0)+(n.mode===ne||n.mode===X?256:0),(0===p&&0===v||t===R)&&Ce===E&&(Ce=U),Ce)}function h(e){if(!e||!e.state)return I;var t=e.state;return t.window&&(t.window=null),e.state=null,E}function p(e,t){var n;return e&&e.state?(n=e.state,0===(2&n.wrap)?I:(n.head=t,t.done=!1,E)):I}function v(e,t){var n,r,i,o=t.length;return e&&e.state?(n=e.state,0!==n.wrap&&n.mode!==V?I:n.mode===V&&(r=1,r=b(r,t,o,0),r!==n.check)?O:(i=f(e,t,o,o))?(n.mode=he,D):(n.havedict=1,E)):I}var g,y,m=n(16),b=n(72),w=n(74),_=n(151),k=n(153),C=0,S=1,x=2,R=4,q=5,T=6,E=0,P=1,A=2,I=-2,O=-3,D=-4,U=-5,F=8,j=1,z=2,W=3,M=4,N=5,L=6,B=7,H=8,J=9,Y=10,V=11,Z=12,G=13,$=14,X=15,K=16,Q=17,ee=18,te=19,ne=20,re=21,ie=22,oe=23,se=24,ae=25,ue=26,ce=27,le=28,fe=29,de=30,he=31,pe=32,ve=852,ge=592,ye=15,me=ye,be=!0;t.inflateReset=s,t.inflateReset2=a,t.inflateResetKeep=o,t.inflateInit=c,t.inflateInit2=u,t.inflate=d,t.inflateEnd=h,t.inflateGetHeader=p,t.inflateSetDictionary=v,t.inflateInfo="pako inflate (from Nodeca project)"},function(e,t,n){"use strict";var r=n(16),i=15,o=852,s=592,a=0,u=1,c=2,l=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],f=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],d=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],h=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(e,t,n,p,v,g,y,m){var b,w,_,k,C,S,x,R,q,T=m.bits,E=0,P=0,A=0,I=0,O=0,D=0,U=0,F=0,j=0,z=0,W=null,M=0,N=new r.Buf16(i+1),L=new r.Buf16(i+1),B=null,H=0;for(E=0;E<=i;E++)N[E]=0;for(P=0;P<p;P++)N[t[n+P]]++;for(O=T,I=i;I>=1&&0===N[I];I--);if(O>I&&(O=I),0===I)return v[g++]=20971520,v[g++]=20971520,m.bits=1,0;for(A=1;A<I&&0===N[A];A++);for(O<A&&(O=A),F=1,E=1;E<=i;E++)if(F<<=1,F-=N[E],F<0)return-1;if(F>0&&(e===a||1!==I))return-1;for(L[1]=0,E=1;E<i;E++)L[E+1]=L[E]+N[E];for(P=0;P<p;P++)0!==t[n+P]&&(y[L[t[n+P]]++]=P);if(e===a?(W=B=y,S=19):e===u?(W=l,M-=257,B=f,H-=257,S=256):(W=d,B=h,S=-1),z=0,P=0,E=A,C=g,D=O,U=0,_=-1,j=1<<O,k=j-1,e===u&&j>o||e===c&&j>s)return 1;for(;;){x=E-U,y[P]<S?(R=0,q=y[P]):y[P]>S?(R=B[H+y[P]],q=W[M+y[P]]):(R=96,q=0),b=1<<E-U,w=1<<D,A=w;do w-=b,v[C+(z>>U)+w]=x<<24|R<<16|q|0;while(0!==w);for(b=1<<E-1;z&b;)b>>=1;if(0!==b?(z&=b-1,z+=b):z=0,P++,0===--N[E]){if(E===I)break;E=t[n+y[P]]}if(E>O&&(z&k)!==_){for(0===U&&(U=O),C+=A,D=E-U,F=1<<D;D+U<I&&(F-=N[D+U],!(F<=0));)D++,F<<=1;if(j+=1<<D,e===u&&j>o||e===c&&j>s)return 1;_=z&k,v[_]=O<<24|D<<16|C-g|0}}return 0!==z&&(v[C+z]=E-U<<24|64<<16|0),m.bits=O,0}},function(e,t,n){"use strict";function r(e){for(var t=e.length;--t>=0;)e[t]=0}function i(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function o(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function s(e){return e<256?ue[e]:ue[256+(e>>>7)]}function a(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function u(e,t,n){e.bi_valid>G-n?(e.bi_buf|=t<<e.bi_valid&65535,a(e,e.bi_buf),e.bi_buf=t>>G-e.bi_valid,e.bi_valid+=n-G):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function c(e,t,n){u(e,n[2*t],n[2*t+1])}function l(e,t){var n=0;do n|=1&e,e>>>=1,n<<=1;while(--t>0);return n>>>1}function f(e){16===e.bi_valid?(a(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}function d(e,t){var n,r,i,o,s,a,u=t.dyn_tree,c=t.max_code,l=t.stat_desc.static_tree,f=t.stat_desc.has_stree,d=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(o=0;o<=Z;o++)e.bl_count[o]=0;for(u[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<V;n++)r=e.heap[n],o=u[2*u[2*r+1]+1]+1,o>p&&(o=p,v++),u[2*r+1]=o,r>c||(e.bl_count[o]++,s=0,r>=h&&(s=d[r-h]),a=u[2*r],e.opt_len+=a*(o+s),f&&(e.static_len+=a*(l[2*r+1]+s)));if(0!==v){do{for(o=p-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(o=p;0!==o;o--)for(r=e.bl_count[o];0!==r;)i=e.heap[--n],i>c||(u[2*i+1]!==o&&(e.opt_len+=(o-u[2*i+1])*u[2*i],u[2*i+1]=o),r--)}}function h(e,t,n){var r,i,o=new Array(Z+1),s=0;for(r=1;r<=Z;r++)o[r]=s=s+n[r-1]<<1;for(i=0;i<=t;i++){var a=e[2*i+1];0!==a&&(e[2*i]=l(o[a]++,a))}}function p(){var e,t,n,r,o,s=new Array(Z+1);for(n=0,r=0;r<L-1;r++)for(le[r]=n,e=0;e<1<<te[r];e++)ce[n++]=r;for(ce[n-1]=r,o=0,r=0;r<16;r++)for(fe[r]=o,e=0;e<1<<ne[r];e++)ue[o++]=r;for(o>>=7;r<J;r++)for(fe[r]=o<<7,e=0;e<1<<ne[r]-7;e++)ue[256+o++]=r;for(t=0;t<=Z;t++)s[t]=0;for(e=0;e<=143;)se[2*e+1]=8,e++,s[8]++;for(;e<=255;)se[2*e+1]=9,e++,s[9]++;for(;e<=279;)se[2*e+1]=7,e++,s[7]++;for(;e<=287;)se[2*e+1]=8,e++,s[8]++;for(h(se,H+1,s),e=0;e<J;e++)ae[2*e+1]=5,ae[2*e]=l(e,5);de=new i(se,te,B+1,H,Z),he=new i(ae,ne,0,J,Z),pe=new i(new Array(0),re,0,Y,$)}function v(e){var t;for(t=0;t<H;t++)e.dyn_ltree[2*t]=0;for(t=0;t<J;t++)e.dyn_dtree[2*t]=0;for(t=0;t<Y;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*X]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function g(e){e.bi_valid>8?a(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function y(e,t,n,r){g(e),r&&(a(e,n),a(e,~n)),I.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}function m(e,t,n,r){var i=2*t,o=2*n;return e[i]<e[o]||e[i]===e[o]&&r[t]<=r[n]}function b(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&m(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!m(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r}function w(e,t,n){var r,i,o,a,l=0;if(0!==e.last_lit)do r=e.pending_buf[e.d_buf+2*l]<<8|e.pending_buf[e.d_buf+2*l+1],i=e.pending_buf[e.l_buf+l],l++,0===r?c(e,i,t):(o=ce[i],c(e,o+B+1,t),a=te[o],0!==a&&(i-=le[o],u(e,i,a)),r--,o=s(r),c(e,o,n),a=ne[o],0!==a&&(r-=fe[o],u(e,r,a)));while(l<e.last_lit);c(e,X,t)}function _(e,t){var n,r,i,o=t.dyn_tree,s=t.stat_desc.static_tree,a=t.stat_desc.has_stree,u=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=V,n=0;n<u;n++)0!==o[2*n]?(e.heap[++e.heap_len]=c=n,e.depth[n]=0):o[2*n+1]=0;for(;e.heap_len<2;)i=e.heap[++e.heap_len]=c<2?++c:0,o[2*i]=1,e.depth[i]=0,e.opt_len--,a&&(e.static_len-=s[2*i+1]);for(t.max_code=c,n=e.heap_len>>1;n>=1;n--)b(e,o,n);i=u;do n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],b(e,o,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,o[2*i]=o[2*n]+o[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,o[2*n+1]=o[2*r+1]=i,e.heap[1]=i++,b(e,o,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],d(e,t),h(o,c,e.bl_count)}function k(e,t,n){var r,i,o=-1,s=t[1],a=0,u=7,c=4;for(0===s&&(u=138,c=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=s,s=t[2*(r+1)+1],++a<u&&i===s||(a<c?e.bl_tree[2*i]+=a:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[2*K]++):a<=10?e.bl_tree[2*Q]++:e.bl_tree[2*ee]++,a=0,o=i,0===s?(u=138,c=3):i===s?(u=6,c=3):(u=7,c=4))}function C(e,t,n){var r,i,o=-1,s=t[1],a=0,l=7,f=4;for(0===s&&(l=138,f=3),r=0;r<=n;r++)if(i=s,s=t[2*(r+1)+1],!(++a<l&&i===s)){if(a<f){do c(e,i,e.bl_tree);while(0!==--a)}else 0!==i?(i!==o&&(c(e,i,e.bl_tree),a--),c(e,K,e.bl_tree),u(e,a-3,2)):a<=10?(c(e,Q,e.bl_tree),u(e,a-3,3)):(c(e,ee,e.bl_tree),u(e,a-11,7));a=0,o=i,0===s?(l=138,f=3):i===s?(l=6,f=3):(l=7,f=4)}}function S(e){var t;for(k(e,e.dyn_ltree,e.l_desc.max_code),k(e,e.dyn_dtree,e.d_desc.max_code),_(e,e.bl_desc),t=Y-1;t>=3&&0===e.bl_tree[2*ie[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}function x(e,t,n,r){var i;for(u(e,t-257,5),u(e,n-1,5),u(e,r-4,4),i=0;i<r;i++)u(e,e.bl_tree[2*ie[i]+1],3);C(e,e.dyn_ltree,t-1),C(e,e.dyn_dtree,n-1)}function R(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return D;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return U;for(t=32;t<B;t++)if(0!==e.dyn_ltree[2*t])return U;return D}function q(e){ve||(p(),ve=!0),e.l_desc=new o(e.dyn_ltree,de),e.d_desc=new o(e.dyn_dtree,he),e.bl_desc=new o(e.bl_tree,pe),e.bi_buf=0,e.bi_valid=0,v(e)}function T(e,t,n,r){u(e,(j<<1)+(r?1:0),3),y(e,t,n,!0)}function E(e){u(e,z<<1,3),c(e,X,se),f(e)}function P(e,t,n,r){var i,o,s=0;e.level>0?(e.strm.data_type===F&&(e.strm.data_type=R(e)),_(e,e.l_desc),_(e,e.d_desc),s=S(e),i=e.opt_len+3+7>>>3,o=e.static_len+3+7>>>3,o<=i&&(i=o)):i=o=n+5,n+4<=i&&t!==-1?T(e,t,n,r):e.strategy===O||o===i?(u(e,(z<<1)+(r?1:0),3),w(e,se,ae)):(u(e,(W<<1)+(r?1:0),3),x(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),w(e,e.dyn_ltree,e.dyn_dtree)),v(e),r&&g(e)}function A(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(ce[n]+B+1)]++,e.dyn_dtree[2*s(t)]++),e.last_lit===e.lit_bufsize-1}var I=n(16),O=4,D=0,U=1,F=2,j=0,z=1,W=2,M=3,N=258,L=29,B=256,H=B+1+L,J=30,Y=19,V=2*H+1,Z=15,G=16,$=7,X=256,K=16,Q=17,ee=18,te=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],ne=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],re=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ie=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],oe=512,se=new Array(2*(H+2));r(se);var ae=new Array(2*J);r(ae);var ue=new Array(oe);r(ue);var ce=new Array(N-M+1);r(ce);var le=new Array(L);r(le);var fe=new Array(J);r(fe);var de,he,pe,ve=!1;t._tr_init=q,t._tr_stored_block=T,t._tr_flush_block=P,t._tr_tally=A,t._tr_align=E},function(e,t,n){"use strict";function r(e,t,n){var r=t&&n||0,i=t||[];e=e||{};var s=void 0!==e.clockseq?e.clockseq:u,f=void 0!==e.msecs?e.msecs:(new Date).getTime(),d=void 0!==e.nsecs?e.nsecs:l+1,h=f-c+(d-l)/1e4;if(h<0&&void 0===e.clockseq&&(s=s+1&16383),(h<0||f>c)&&void 0===e.nsecs&&(d=0),d>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");c=f,l=d,u=s,f+=122192928e5;var p=(1e4*(268435455&f)+d)%4294967296;i[r++]=p>>>24&255,i[r++]=p>>>16&255,i[r++]=p>>>8&255,i[r++]=255&p;var v=f/4294967296*1e4&268435455;i[r++]=v>>>8&255,i[r++]=255&v,i[r++]=v>>>24&15|16,i[r++]=v>>>16&255,i[r++]=s>>>8|128,i[r++]=255&s;for(var g=e.node||a,y=0;y<6;++y)i[r+y]=g[y];return t?t:o(i)}var i=n(77),o=n(76),s=i(),a=[1|s[0],s[1],s[2],s[3],s[4],s[5]],u=16383&(s[6]<<8|s[7]),c=0,l=0;e.exports=r},function(e,t,n){"use strict";function r(e,t,n){var r=t&&n||0;"string"==typeof e&&(t="binary"==e?new Array(16):null,e=null),e=e||{};var s=e.random||(e.rng||i)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t)for(var a=0;a<16;++a)t[r+a]=s[a];return t||o(s)}var i=n(77),o=n(76);e.exports=r},function(e,t){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children=[],e.webpackPolyfill=1),e}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.ImageHttpRequest=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(49),a=r(s),u=n(146),c=r(u),l=n(29),f=function(){var e=function(){function e(t){i(this,e);var n={iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null};this.oOptions=Object.assign(n,t),this.szHost="http://127.0.0.1",this.szUUID="",this.szVersion="",this.bNormalClose=!1,this.bConnected=!1,this.bInitConnect=!0,this.iGetErrorCount=0,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this,t=a.default.v4(),n={sequence:t,cmd:"system.connect"},r=JSON.stringify(n);e.sendImageHttp(e.szHost+":"+e.oOptions.iPort+"/imghttp/local",r,t,{success:function(t){var n=JSON.parse(t);e.szUUID=n.uuid,e.szVersion=n.version,e.bConnected=!0,e.bInitConnect=!1,setTimeout(function(){e.imageHttpPolling()},100),e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()},error:function(){}})}},{key:"sendImageHttp",value:function(e,t,n,r){var i=this,o={success:null,error:null,abort:null};r=Object.assign(o,r);var s=c.default.deflate(t);""!==(new Uint8Array).toString()&&(l.oUtils.isMacOS()?s=Array.from(s):l.oUtils.browser().msie&&(s=Array.from(s)));for(var a=encodeURIComponent(btoa(s)),u=this.splitStr(a),f=[],d="",h=0,p=u.length;h<p;h++)d=h===p-1?"update="+(new Date).getTime()+"&isLast=true&data="+u[h]+"&sequence="+n:"update="+(new Date).getTime()+"&isLast=false&data="+u[h]+"&sequence="+n,f.push(d);if(f.length>0){var v=function t(){i.imageHttp(e+"?"+f[0],{success:function(e){f.shift(),f.length>0?(i.bInitConnect||i.bConnected)&&t():r.success&&r.success(e)},error:function(){r.error&&r.error()},abort:function(){r.abort&&r.abort()}})};v()}}},{key:"splitStr",value:function(e){for(var t=this.getByteLen(e),n=[],r=1500,i=0,o=Math.ceil(t/r);i<o;i++)n[i]=e.slice(r*i,r*(i+1));return n}},{key:"getByteLen",value:function(e){for(var t=0,n="",r=0,i=e.length;r<i;r++)n=e.charAt(r),t+=/[^\x00-\xff]/.test(n)?2:1;return t}},{key:"imageHttp",value:function(e,t){var n={success:null,error:null,abort:null};t=Object.assign(n,t);var r=new Image;r.onload=function(){if(t.success){var e=document.createElement("canvas"),n=e.getContext("2d"),i=r.width,o=r.height;e.width=i,e.height=o;try{n.drawImage(r,0,0);for(var s=n.getImageData(0,0,i,o).data,a="",u=-1,c=o-1;c>=0;c--)for(var f=0;f<4*i&&(u=c*i*4+f,0!==s[u]);f++)255!==s[u]&&(a+=String.fromCharCode(s[u]));t.success(l.oUtils.utf8to16(a))}catch(e){t.error&&t.error()}}},r.onerror=function(){t.error&&t.error()},r.onabort=function(){t.abort&&t.abort()},r.crossOrigin="anonymous",r.src=e}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){var e=this,t=a.default.v4(),n={sequence:t,uuid:e.szUUID,cmd:"system.disconnect"},r=JSON.stringify(n);e.bConnected&&e.sendImageHttp(e.szHost+":"+e.oOptions.iPort+"/imghttp/local",r,t,{success:function(){e.bNormalClose=!0,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)},error:function(){e.bConnected=!1}})}},{key:"imageHttpPolling",value:function(){var e=this,t=a.default.v4(),n={sequence:t,uuid:e.szUUID,cmd:"system.get"},r=JSON.stringify(n);e.bConnected&&e.sendImageHttp(e.szHost+":"+e.oOptions.iPort+"/imghttp/local",r,t,{success:function(t){if(e.iGetErrorCount=0,"timeout"===t)setTimeout(function(){e.imageHttpPolling()},100);else if("invalid"===t)e.bConnected=!1,e.oOptions.cbConnectError&&e.oOptions.cbConnectError();else if("closed"===t)console.log("connected is disconnected");else{var n=JSON.parse(t);"undefined"!=typeof n.cmd?e.parseCmd(n):console.log("[jsWebControl]imgHttpPolling push message error:"+t),setTimeout(function(){e.imageHttpPolling()},100)}},error:function(){5===e.iGetErrorCount?(console.log("[jsWebControl]imageHttpPolling get polling finished"),e.bNormalClose=!1,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)):setTimeout(function(){console.log("[jsWebControl]imgHttpPolling get polling failed"),e.iGetErrorCount++,e.imageHttpPolling()},100)}})}},{key:"sendRequest",value:function(e){var t=this,n=new Promise(function(n,r){var i=e.cmd.split("."),o="";i.length>1?o="laputa"===i[0]?"laputa":"local":r();var s=a.default.v4();e.sequence=s,e.uuid=t.szUUID,e.timestamp=(new Date).getTime()+"";var u=JSON.stringify(e);t.bConnected?t.sendImageHttp(t.szHost+":"+t.oOptions.iPort+"/imghttp/"+o,u,s,{success:function(e){var t=JSON.parse(e);0===t.errorModule&&0===t.errorCode?n(t):r(t)},error:function(){r()}}):r()});return n}},{key:"parseCmd",value:function(e){var t=e.cmd,n=t.split("."),r=n[1].replace(/^[a-z]{1}/g,function(e){return e.toUpperCase()});"window"===n[0]||"play"===n[0]?this.oWindowControlCallback["cb"+r]&&this.oWindowControlCallback["cb"+r](e):"sadp"===n[0]?this.oSadpCallback["cb"+r]&&this.oSadpCallback["cb"+r](e):"serial"===n[0]?this.oSerialCallback["cb"+r]&&this.oSerialCallback["cb"+r](e):"slice"===n[0]?this.oSliceCallback["cb"+r]&&this.oSliceCallback["cb"+r](e):"ui"===n[0]?this.oUIControlCallback["cb"+r]&&this.oUIControlCallback["cb"+r](e):"upgrade"===n[0]&&this.oUpgradeCallback["cb"+r]&&this.oUpgradeCallback["cb"+r](e)}}]),e}();return e}();t.ImageHttpRequest=f},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.Request=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(161),s=n(158),a=n(160),u=n(29),c=function(){var e=function(){function e(t){r(this,e);var n={szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:-1,iServicePortEnd:-1,szClassId:""};this.oOptions=Object.assign(n,t),this.iPort=-1,this.oRequest=null,this.bInit=!1,this.oCallbacks={},this.init()}return i(e,[{key:"init",value:function(){var e=this;u.oUtils.detectPort(e.oOptions.iServicePortStart,e.oOptions.iServicePortEnd,{success:function(t){if(e.iPort=t,u.oUtils.browser().msie)"11.0"===u.oUtils.browser().version?"https:"===window.location.protocol?e.oRequest=new s.ImageHttpRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new o.WebSocketRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new a.WebActiveXRequest({szPluginContainer:e.oOptions.szPluginContainer,iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose,szClassId:e.oOptions.szClassId});else if("https:"===window.location.protocol)if(u.oUtils.browser().chrome)try{e.oRequest=new o.WebSocketRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}catch(t){e.oRequest=new s.ImageHttpRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}else e.oRequest=new s.ImageHttpRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose});else"WebSocket"in window&&(e.oRequest=new o.WebSocketRequest({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}));e.bInit=!0;for(var n in e.oCallbacks)e.oRequest[n](e.oCallbacks[n])},error:function(){e.oOptions.cbConnectError&&e.oOptions.cbConnectError()}})}},{key:"setWindowControlCallback",value:function(e){this.bInit?this.oRequest.setWindowControlCallback(e):this.oCallbacks.setWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.bInit?this.oRequest.setSadpCallback(e):this.oCallbacks.setSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.bInit?this.oRequest.setSliceCallback(e):this.oCallbacks.setSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.bInit?this.oRequest.setSerialCallback(e):this.oCallbacks.setSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.bInit?this.oRequest.setUIControlCallback(e):this.oCallbacks.setUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.bInit?this.oRequest.setUpgradeCallback(e):this.oCallbacks.setUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.oRequest.getServiceVersion()}},{key:"getRequestUUID",value:function(){return this.oRequest.getRequestUUID()}},{key:"startService",value:function(e,t){var n={cmd:"system.startService",type:e};return"undefined"!=typeof t&&(n.options=t),this.oRequest.sendRequest(n)}},{key:"stopService",value:function(e){var t=this,n=new Promise(function(n,r){null!==t.oRequest?t.oRequest.sendRequest({cmd:"system.stopService",type:e}).then(function(e){n(e)},function(e){r(e)}):r()});return n}},{key:"disconnect",value:function(){var e=this,t=new Promise(function(t,n){null!==e.oRequest?(e.oRequest.disconnect(),t("cbConnectClose callback is really success")):n()});return t}},{key:"openDirectory",value:function(e){return this.oRequest.sendRequest({cmd:"system.openDirectory",path:e})}},{key:"openFile",value:function(e,t,n){return this.oRequest.sendRequest({
cmd:"system.openFile",path:e,relative:t,version:n})}},{key:"selectDirectory",value:function(e,t){var n=this,r=new Promise(function(r,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"system.selectDirectory",caption:"undefined"!=typeof e&&""!==e?u.oUtils.Base64().encode(e):"",dir:"undefined"!=typeof t&&""!==t?u.oUtils.Base64().encode(t):""}).then(function(e){""!==e.path&&(e.path=u.oUtils.Base64().decode(e.path)),r(e)},function(e){i(e)}):i()});return r}},{key:"selectFile",value:function(e,t,n){var r=this,i=new Promise(function(i,o){null!==r.oRequest?r.oRequest.sendRequest({cmd:"system.selectFile",caption:""!==e?u.oUtils.Base64().encode(e):"",dir:""!==t?u.oUtils.Base64().encode(t):"",filter:n}).then(function(e){""!==e.path&&(e.path=u.oUtils.Base64().decode(e.path)),i(e)},function(e){o(e)}):o()});return i}},{key:"getLocalConfig",value:function(e){return this.oRequest.sendRequest({cmd:"system.getLocalConfig",default:e})}},{key:"setLocalConfig",value:function(e){return e.cmd="system.setLocalConfig",this.oRequest.sendRequest(e)}},{key:"createWnd",value:function(e,t,n,r,i,o,s){return this.oRequest.sendRequest({cmd:"window.createWnd",rect:{left:e,top:t,width:n,height:r},className:i,embed:o,activeXParentWnd:s})}},{key:"showWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.showWnd"})}},{key:"hideWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.hideWnd"})}},{key:"destroyWnd",value:function(){var e=this,t=new Promise(function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.destroyWnd"}).then(function(e){t(e)},function(e){n(e)}):n()});return t}},{key:"setWndGeometry",value:function(e,t,n,r){return this.oRequest.sendRequest({cmd:"window.setWndGeometry",rect:{left:e,top:t,width:n,height:r}})}},{key:"setWndCover",value:function(e,t){var n=this,r=new Promise(function(r,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"window.setWndCover",position:e,size:t}).then(function(e){r(e)},function(e){i(e)}):i()});return r}},{key:"cuttingPartWindow",value:function(e,t,n,r,i){var o=this,s=new Promise(function(s,a){null!==o.oRequest?o.oRequest.sendRequest({cmd:"window.cuttingPartWindow",rect:{left:e,top:t,width:n,height:r},round:i}).then(function(e){s(e)},function(e){a(e)}):a()});return s}},{key:"repairPartWindow",value:function(e,t,n,r,i){var o=this,s=new Promise(function(s,a){null!==o.oRequest?o.oRequest.sendRequest({cmd:"window.repairPartWindow",rect:{left:e,top:t,width:n,height:r},round:i}).then(function(e){s(e)},function(e){a(e)}):a()});return s}},{key:"setWndZOrder",value:function(e){return this.oRequest.sendRequest({cmd:"window.setWndZOrder",flag:e})}},{key:"changePlayMode",value:function(e){return this.oRequest.sendRequest({cmd:"window.changePlayMode",type:e})}},{key:"setLanguageType",value:function(e){return this.oRequest.sendRequest({cmd:"window.setLanguageType",type:e})}},{key:"initLoginInfo",value:function(e){return this.oRequest.sendRequest({cmd:"window.initLoginInfo",vsmAddress:e.vsmAddress,vsmPort:e.vsmPort,sessionID:e.sessionID,loginModel:e.loginModel,userType:e.userType,networkType:e.networkType})}},{key:"setTranslateFile",value:function(e){return this.oRequest.sendRequest({cmd:"window.setTranslateFile",url:e})}},{key:"switchToSimple",value:function(e){return this.oRequest.sendRequest({cmd:"window.switchToSimple",simple:e})}},{key:"setVsmToken",value:function(e){return this.oRequest.sendRequest({cmd:"play.setVsmToken",token:e})}},{key:"startPlay",value:function(e,t,n,r,i,o,s,a,c){var l={cmd:"play.startPlay",url:e,username:t,password:n,siteID:r,areaName:u.oUtils.Base64().encode(i),cameraName:u.oUtils.Base64().encode(o),permission:s,wndIndex:a};return"undefined"!=typeof c&&(l.options=c,"undefined"!=typeof l.options.siteName&&(l.options.siteName=u.oUtils.Base64().encode(l.options.siteName))),this.oRequest.sendRequest(l)}},{key:"setPreview3DPosition",value:function(e){return this.oRequest.sendRequest({cmd:"play.setPreview3DPosition",open:e})}},{key:"stopTotal",value:function(){var e=this,t=new Promise(function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"play.stopTotal"}).then(function(e){t(e)},function(e){n(e)}):n()});return t}},{key:"setDragMode",value:function(e){return this.oRequest.sendRequest({cmd:"play.setDragMode",drag:e})}},{key:"showErrorInfoInFullScreen",value:function(e){return this.oRequest.sendRequest({cmd:"play.showErrorInfoInFullScreen",error:u.oUtils.Base64().encode(e)})}},{key:"setNumberOfWindows",value:function(e){return this.oRequest.sendRequest({cmd:"play.setNumberOfWindows",number:e})}},{key:"initCardReader",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitCardReader",param:e})}},{key:"unInitCardReader",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitCardReader"})}},{key:"startAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartAutoMode"})}},{key:"stopAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopAutoMode"})}},{key:"initFingerprint",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitFingerprint",param:e})}},{key:"unInitFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitFingerprint"})}},{key:"startCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartCollectFingerprint"})}},{key:"stopCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectFingerprint"})}},{key:"isCollectingFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSIsCollectingFingerprint"})}},{key:"initVideocapture",value:function(e){return e.majorTitle=u.oUtils.Base64().encode(e.majorTitle),e.tip=u.oUtils.Base64().encode(e.tip),e.captureBtnTxt=u.oUtils.Base64().encode(e.captureBtnTxt),e.USBRemovedTip=u.oUtils.Base64().encode(e.USBRemovedTip),this.oRequest.sendRequest({cmd:"serial.ACSStartCollectImage",param:e})}},{key:"unInitVideocapture",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectImage"})}},{key:"registerDeviceType",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.registDeviceType",deviceType:e})}},{key:"activeOnlineDevice",value:function(e,t){return this.oRequest.sendRequest({cmd:"sadp.activeDevice",serialNumber:e,password:t})}},{key:"refreshDeviceList",value:function(){return this.oRequest.sendRequest({cmd:"sadp.refreshDeviceList"})}},{key:"modifyDeviceNetParam",value:function(e,t,n,r,i,o,s){return this.oRequest.sendRequest({cmd:"sadp.modifyDeviceParam",macAddress:e,password:t,ipv4Address:n,ipv4Gateway:r,ipv4SubnetMask:i,port:o,httpPort:s})}},{key:"exportKeyFile",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.exportKeyFile",serialNumber:e})}},{key:"importKeyFile",value:function(){return this.oRequest.sendRequest({cmd:"sadp.importKeyFile"})}},{key:"resetPassword",value:function(e,t,n,r){return this.oRequest.sendRequest({cmd:"sadp.resetPassword",serialNumber:e,password:t,importFileData:n,szCode:r})}},{key:"uploadPicture",value:function(e){return this.oRequest.sendRequest({cmd:"slice.uploadPicture",path:u.oUtils.Base64().encode(e)})}},{key:"showSelectMenu",value:function(e,t,n,r,i){return this.oRequest.sendRequest({cmd:"ui.showSelectMenu",items:i,rect:{left:e,top:t,width:n,height:r}})}},{key:"hideSelectMenu",value:function(){return this.oRequest.sendRequest({cmd:"ui.hideSelectMenu"})}},{key:"destroySelectMenu",value:function(){var e=this,t=new Promise(function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"ui.destroySelectMenu"}).then(function(e){t(e)},function(e){n(e)}):n()});return t}},{key:"deviceConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.encodingDevice",param:e})}},{key:"cloudStorageConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.cloudStorage",param:e})}},{key:"ezvizRemoteConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ezvizRemote",param:e})}},{key:"showAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"window.showAlarmInfoInFullScreen",alarmTitle:e,alarmMessage:t,alarmId:n})}},{key:"updateParentWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.updateParentWnd"})}},{key:"restoreWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.restoreWnd"})}},{key:"setImmediatePlaybackTime",value:function(e){return this.oRequest.sendRequest({cmd:"play.setImmediatePlaybackTime",specifyTime:e})}},{key:"setDrawStatus",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setDrawStatus",enable:e})}},{key:"clearRegion",value:function(){return this.oRequest.sendRequest({cmd:"draw.clearRegion"})}},{key:"setDrawShapeInfo",value:function(e,t){return this.oRequest.sendRequest({cmd:"draw.setDrawShapeInfo",drawType:e,drawInfo:t})}},{key:"setGridInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setGridInfo",gridInfo:e})}},{key:"getGridInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getGridInfo"})}},{key:"setPolygonInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setPolygonInfo",polygonInfo:e})}},{key:"getPolygonInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getPolygonInfo"})}},{key:"setLineInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setLineInfo",lineInfo:e})}},{key:"getLineInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getLineInfo"})}},{key:"setRectInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setRectInfo",rectInfo:e})}},{key:"getRectInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getRectInfo"})}},{key:"clearShapeByType",value:function(e){return this.oRequest.sendRequest({cmd:"draw.clearShapeByType",type:e})}},{key:"sensitiveEncrypt",value:function(e,t,n){var r={cmd:"laputa.sensitiveEncrypt",encryptType:e,encryptField:t};return"undefined"!=typeof n&&(r.options=n),this.oRequest.sendRequest(r)}},{key:"sendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"requestInterface",value:function(e){var t={};return t.cmd="window.requestInterface",t.requestParams=e,this.oRequest.sendRequest(t)}},{key:"stopPlay",value:function(e){return"undefined"==typeof e&&(e=-1),this.oRequest.sendRequest({cmd:"play.stopPlay",wndIndex:e})}},{key:"showRemoteConfig",value:function(e){var t=this;e.cmd="config.showRemoteConfig";var n=new Promise(function(n,r){null!==t.oRequest?t.oRequest.sendRequest(e).then(function(e){n(e)},function(e){r(e)}):r()});return n}},{key:"video2Picture",value:function(){var e={};return e.cmd="window.video2Picture",this.oRequest.sendRequest(e)}},{key:"picture2Video",value:function(){var e={};return e.cmd="window.picture2Video",this.oRequest.sendRequest(e)}},{key:"ptzControl",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ptzControl",param:e})}},{key:"simMouseClickEvent",value:function(e,t){return this.oRequest.sendRequest({cmd:"window.simMouseClickEvent",pointX:e,pointY:t})}},{key:"us_SetMaxJobCount",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.setMaxJobCount",xml:e})}},{key:"us_GetMaxJobCount",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.getMaxJobCount"})}},{key:"us_AddSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.addSchedule",xml:u.oUtils.Base64().encode(e)})}},{key:"us_DelSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.delSchedule",scheduleId:e})}},{key:"us_GetScheduleList",value:function(e){var t=this,n=new Promise(function(n,r){null!==t.oRequest?t.oRequest.sendRequest({cmd:"upgrade.getScheduleList",xml:e}).then(function(e){""!==e.xml&&(e.xml=u.oUtils.Base64().decode(e.xml)),n(e)},function(e){r(e)}):r()});return n}},{key:"us_GetSchedule",value:function(e,t){var n=this,r=new Promise(function(r,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"upgrade.getSchedule",xml:t,scheduleId:e}).then(function(e){""!==e.xml&&(e.xml=u.oUtils.Base64().decode(e.xml)),r(e)},function(e){i(e)}):i()});return r}},{key:"us_UpgradeAction",value:function(e,t){return this.oRequest.sendRequest({cmd:"upgrade.upgradeAction",xml:t,scheduleId:e})}},{key:"us_CheckUpgradeableDevice",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDevice",param:e})}},{key:"us_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDeviceList",param:e})}},{key:"us_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.isRunningAsyCheckUpgradeable"})}},{key:"us_StopAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.stopAsyCheckUpgradeable"})}},{key:"getFishEyePTZPreset",value:function(e){return this.oRequest.sendRequest({cmd:"play.getFishEyePTZPreset",wndIndex:e})}},{key:"setFishEyePTZPreset",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.setFishEyePTZPreset",wndIndex:e,command:t,presetInfo:n})}},{key:"controlFishEyePTZ",value:function(e,t,n,r){return this.oRequest.sendRequest({cmd:"play.controlFishEyePTZ",wndIndex:e,command:t,stop:n,speed:r})}},{key:"controlFishEyeParol",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.controlFishEyeParol",wndIndex:e,command:t,cruisePointList:n})}},{key:"setFirstDayOfWeek",value:function(e){return this.oRequest.sendRequest({cmd:"window.setFirstDayOfWeek",firstDay:e})}},{key:"setEhomePlayInfo",value:function(e,t,n,r,i,o){return this.oRequest.sendRequest({cmd:"play.setEhomePlayInfo",guid:e,protocal:t,session:n,token:r,ip:i,port:o})}},{key:"startPlayPatch",value:function(e){if(e.length>0)for(var t=0,n=e.length;t<n;t++)e[t].areaName=u.oUtils.Base64().encode(e[t].areaName),e[t].cameraName=u.oUtils.Base64().encode(e[t].cameraName);return this.oRequest.sendRequest({cmd:"play.startPlayPatch",params:e})}},{key:"grabOpen",value:function(){var e=this,t=new Promise(function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.grabOpen"}).then(function(e){t(e)},function(e){n(e)}):n()});return t}},{key:"setWndAutoPanState",value:function(e,t){return this.oRequest.sendRequest({cmd:"play.setWndAutoPanState",wndIndex:e,open:t})}},{key:"enablePrivileges",value:function(){return this.oRequest.sendRequest({cmd:"system.enablePrivileges"})}}]),e}();return e}();t.Request=c},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.WebActiveXRequest=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(49),a=r(s),u=n(29),c=function(){var e=function(){function e(t){i(this,e);var n={szPluginContainer:"",iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,szClassId:""};this.oOptions=Object.assign(n,t),this.oPlugin=null,this.szPluginId="",this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.aMessage=[],this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this;e.initPlugin(),e.oPlugin.object&&e.oPlugin.createSocket("ws://127.0.0.1:"+e.oOptions.iPort)}},{key:"initPlugin",value:function(){var e=this;this.szPluginId="webActiveX_"+(new Date).getTime();var t="<object id='"+this.szPluginId+"' classid='clsid:"+e.oOptions.szClassId+"' codebase='' standby='Waiting...' width='100%' height='100%' align='center' ></object>",n=e.oOptions.szPluginContainer;if(""===n){n=this.szPluginId+"_div";var r=document.createElement("div");r.id=n,document.body.parentNode.appendChild(r)}document.getElementById(n).innerHTML=t,e.oPlugin=document.getElementById(this.szPluginId),window.onConnectMessage=function(t,n){n?(e.aMessage.push(t),e.onConnectMessage(e.aMessage.join("")),e.aMessage.length=0):e.aMessage.push(t)},window.onConnectClose=function(){e.onConnectClose()},window.onConnectError=function(){e.onConnectError()},window.onConnectCloseException=function(){e.onConnectCloseException()},window.onConnectOpen=function(){e.onConnectOpen()},u.oUtils.createEventScript(this.szPluginId,"onConnectMessage(szData, bLast)","onConnectMessage(szData, bLast);"),u.oUtils.createEventScript(this.szPluginId,"onConnectClose()","onConnectClose();"),u.oUtils.createEventScript(this.szPluginId,"onConnectError()","onConnectError();"),u.oUtils.createEventScript(this.szPluginId,"onConnectCloseException()","onConnectCloseException();"),u.oUtils.createEventScript(this.szPluginId,"onConnectOpen()","onConnectOpen();")}},{key:"onConnectMessage",value:function(e){var t=this;if(e){var n=JSON.parse(e),r=n.sequence;"undefined"==typeof r&&"undefined"==typeof n.cmd?(t.szUUID=n.uuid,t.szVersion=n.version,t.oOptions.cbConnectSuccess&&t.oOptions.cbConnectSuccess()):"undefined"!=typeof n.cmd?t.parseCmd(n):"undefined"!=typeof t.oRequestList[r]&&(0===n.errorModule&&0===n.errorCode?t.oRequestList[r].resolve(n):t.oRequestList[r].reject(n),delete t.oRequestList[r])}}},{key:"onConnectClose",value:function(){if(this.oPlugin=null,""!==this.szPluginId){var e=document.getElementById(this.szPluginId);e.parentNode.removeChild(e);var t=document.getElementById(this.szPluginId+"_div");null!==t&&t.parentNode.removeChild(t)}this.oOptions.cbConnectClose&&this.oOptions.cbConnectClose(this.bNormalClose)}},{key:"onConnectCloseException",value:function(){var e=this;setTimeout(function(){e.oPlugin.object&&e.oPlugin.closeSocket()},1e3)}},{key:"onConnectOpen",value:function(){var e=a.default.v4(),t={sequence:e,cmd:"system.connect"},n=JSON.stringify(t);this.oPlugin.object&&this.oPlugin.sendRequest(n)}},{key:"onConnectError",value:function(){}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oPlugin&&this.oPlugin.object&&this.oPlugin.closeSocket()}},{key:"sendRequest",value:function(e){var t=this;"window.hideWnd"===e.cmd?t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="hidden"):"window.showWnd"===e.cmd&&t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="visible");var n=new Promise(function(n,r){var i=a.default.v4();e.sequence=i,t.oRequestList[i]={resolve:n,reject:r},e.uuid=t.szUUID,e.timestamp=(new Date).getTime()+"";var o=JSON.stringify(e);t.oPlugin&&t.oPlugin.object?t.oPlugin.sendRequest(o):r()});return n}},{key:"parseCmd",value:function(e){var t=e.cmd,n=t.split("."),r=n[1].replace(/^[a-z]{1}/g,function(e){return e.toUpperCase()});"window"===n[0]||"play"===n[0]?this.oWindowControlCallback["cb"+r]&&this.oWindowControlCallback["cb"+r](e):"sadp"===n[0]?this.oSadpCallback["cb"+r]&&this.oSadpCallback["cb"+r](e):"serial"===n[0]?this.oSerialCallback["cb"+r]&&this.oSerialCallback["cb"+r](e):"slice"===n[0]?this.oSliceCallback["cb"+r]&&this.oSliceCallback["cb"+r](e):"ui"===n[0]?this.oUIControlCallback["cb"+r]&&this.oUIControlCallback["cb"+r](e):"upgrade"===n[0]&&this.oUpgradeCallback["cb"+r]&&this.oUpgradeCallback["cb"+r](e)}}]),e}();return e}();t.WebActiveXRequest=c},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.WebSocketRequest=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(49),a=r(s),u=n(29),c=function(){var e=function(){function e(t){i(this,e);var n={iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null};this.oOptions=Object.assign(n,t),this.oWebSocket=null,this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this,t=function(){e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose),e.bNormalClose=!1};e.oWebSocket=new WebSocket("ws://127.0.0.1:"+e.oOptions.iPort),e.oWebSocket.onerror=function(){},e.oWebSocket.onopen=function(){var t=a.default.v4(),n={sequence:t,cmd:"system.connect"},r=JSON.stringify(n);e.oWebSocket.send(r)},e.oWebSocket.onmessage=function(t){var n=t.data,r=JSON.parse(n),i=r.sequence;"undefined"==typeof i&&"undefined"==typeof r.cmd?(e.szUUID=r.uuid,e.szVersion=r.version,e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()):"undefined"!=typeof r.cmd?e.parseCmd(r):"undefined"!=typeof e.oRequestList[i]&&(0===r.errorModule&&0===r.errorCode?e.oRequestList[i].resolve(r):e.oRequestList[i].reject(r),delete e.oRequestList[i])},e.oWebSocket.onclose=function(){e.oWebSocket=null,u.oUtils.browser().mozilla?setTimeout(function(){t()},100):t()}}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oWebSocket&&WebSocket.OPEN===this.oWebSocket.readyState&&(this.oWebSocket.close(),delete this.oWebSocket)}},{key:"sendRequest",value:function(e){var t=this,n=new Promise(function(n,r){var i=a.default.v4();e.sequence=i,t.oRequestList[i]={resolve:n,reject:r},e.uuid=t.szUUID,e.timestamp=(new Date).getTime()+"";var o=JSON.stringify(e);t.oWebSocket&&WebSocket.OPEN===t.oWebSocket.readyState?t.oWebSocket.send(o):r()});return n}},{key:"parseCmd",value:function(e){var t=e.cmd,n=t.split("."),r=n[1].replace(/^[a-z]{1}/g,function(e){return e.toUpperCase()});"window"===n[0]||"play"===n[0]?this.oWindowControlCallback["cb"+r]&&this.oWindowControlCallback["cb"+r](e):"sadp"===n[0]?this.oSadpCallback["cb"+r]&&this.oSadpCallback["cb"+r](e):"serial"===n[0]?this.oSerialCallback["cb"+r]&&this.oSerialCallback["cb"+r](e):"slice"===n[0]?this.oSliceCallback["cb"+r]&&this.oSliceCallback["cb"+r](e):"ui"===n[0]?this.oUIControlCallback["cb"+r]&&this.oUIControlCallback["cb"+r](e):"upgrade"===n[0]&&this.oUpgradeCallback["cb"+r]&&this.oUpgradeCallback["cb"+r](e)}}]),e}();return e}();t.WebSocketRequest=c}])});