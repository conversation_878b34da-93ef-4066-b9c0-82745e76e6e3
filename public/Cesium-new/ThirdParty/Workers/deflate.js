!function(e){var t=256,n=256,a=-2,i=-5,r=[0,1,2,3,4,4,5,5,6,6,6,6,7,7,7,7,8,8,8,8,8,8,8,8,9,9,9,9,9,9,9,9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,0,0,16,17,18,18,19,19,20,20,20,20,21,21,21,21,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29];function _(){var e=this;function t(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.build_tree=function(n){var a,i,r,_=e.dyn_tree,o=e.stat_desc.static_tree,u=e.stat_desc.elems,d=-1;for(n.heap_len=0,n.heap_max=573,a=0;a<u;a++)0!==_[2*a]?(n.heap[++n.heap_len]=d=a,n.depth[a]=0):_[2*a+1]=0;for(;n.heap_len<2;)_[2*(r=n.heap[++n.heap_len]=d<2?++d:0)]=1,n.depth[r]=0,n.opt_len--,o&&(n.static_len-=o[2*r+1]);for(e.max_code=d,a=Math.floor(n.heap_len/2);a>=1;a--)n.pqdownheap(_,a);r=u;do{a=n.heap[1],n.heap[1]=n.heap[n.heap_len--],n.pqdownheap(_,1),i=n.heap[1],n.heap[--n.heap_max]=a,n.heap[--n.heap_max]=i,_[2*r]=_[2*a]+_[2*i],n.depth[r]=Math.max(n.depth[a],n.depth[i])+1,_[2*a+1]=_[2*i+1]=r,n.heap[1]=r++,n.pqdownheap(_,1)}while(n.heap_len>=2);n.heap[--n.heap_max]=n.heap[1],function(t){var n,a,i,r,_,o,u=e.dyn_tree,d=e.stat_desc.static_tree,l=e.stat_desc.extra_bits,f=e.stat_desc.extra_base,s=e.stat_desc.max_length,c=0;for(r=0;r<=15;r++)t.bl_count[r]=0;for(u[2*t.heap[t.heap_max]+1]=0,n=t.heap_max+1;n<573;n++)(r=u[2*u[2*(a=t.heap[n])+1]+1]+1)>s&&(r=s,c++),u[2*a+1]=r,a>e.max_code||(t.bl_count[r]++,_=0,a>=f&&(_=l[a-f]),o=u[2*a],t.opt_len+=o*(r+_),d&&(t.static_len+=o*(d[2*a+1]+_)));if(0!==c){do{for(r=s-1;0===t.bl_count[r];)r--;t.bl_count[r]--,t.bl_count[r+1]+=2,t.bl_count[s]--,c-=2}while(c>0);for(r=s;0!==r;r--)for(a=t.bl_count[r];0!==a;)(i=t.heap[--n])>e.max_code||(u[2*i+1]!=r&&(t.opt_len+=(r-u[2*i+1])*u[2*i],u[2*i+1]=r),a--)}}(n),function(e,n,a){var i,r,_,o=[],u=0;for(i=1;i<=15;i++)o[i]=u=u+a[i-1]<<1;for(r=0;r<=n;r++)0!==(_=e[2*r+1])&&(e[2*r]=t(o[_]++,_))}(_,e.max_code,n.bl_count)}}function o(e,t,n,a,i){var r=this;r.static_tree=e,r.extra_bits=t,r.extra_base=n,r.elems=a,r.max_length=i}_._length_code=[0,1,2,3,4,5,6,7,8,8,9,9,10,10,11,11,12,12,12,12,13,13,13,13,14,14,14,14,15,15,15,15,16,16,16,16,16,16,16,16,17,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28],_.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],_.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],_.d_code=function(e){return e<256?r[e]:r[256+(e>>>7)]},_.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],_.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],_.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],_.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],o.static_ltree=[12,8,140,8,76,8,204,8,44,8,172,8,108,8,236,8,28,8,156,8,92,8,220,8,60,8,188,8,124,8,252,8,2,8,130,8,66,8,194,8,34,8,162,8,98,8,226,8,18,8,146,8,82,8,210,8,50,8,178,8,114,8,242,8,10,8,138,8,74,8,202,8,42,8,170,8,106,8,234,8,26,8,154,8,90,8,218,8,58,8,186,8,122,8,250,8,6,8,134,8,70,8,198,8,38,8,166,8,102,8,230,8,22,8,150,8,86,8,214,8,54,8,182,8,118,8,246,8,14,8,142,8,78,8,206,8,46,8,174,8,110,8,238,8,30,8,158,8,94,8,222,8,62,8,190,8,126,8,254,8,1,8,129,8,65,8,193,8,33,8,161,8,97,8,225,8,17,8,145,8,81,8,209,8,49,8,177,8,113,8,241,8,9,8,137,8,73,8,201,8,41,8,169,8,105,8,233,8,25,8,153,8,89,8,217,8,57,8,185,8,121,8,249,8,5,8,133,8,69,8,197,8,37,8,165,8,101,8,229,8,21,8,149,8,85,8,213,8,53,8,181,8,117,8,245,8,13,8,141,8,77,8,205,8,45,8,173,8,109,8,237,8,29,8,157,8,93,8,221,8,61,8,189,8,125,8,253,8,19,9,275,9,147,9,403,9,83,9,339,9,211,9,467,9,51,9,307,9,179,9,435,9,115,9,371,9,243,9,499,9,11,9,267,9,139,9,395,9,75,9,331,9,203,9,459,9,43,9,299,9,171,9,427,9,107,9,363,9,235,9,491,9,27,9,283,9,155,9,411,9,91,9,347,9,219,9,475,9,59,9,315,9,187,9,443,9,123,9,379,9,251,9,507,9,7,9,263,9,135,9,391,9,71,9,327,9,199,9,455,9,39,9,295,9,167,9,423,9,103,9,359,9,231,9,487,9,23,9,279,9,151,9,407,9,87,9,343,9,215,9,471,9,55,9,311,9,183,9,439,9,119,9,375,9,247,9,503,9,15,9,271,9,143,9,399,9,79,9,335,9,207,9,463,9,47,9,303,9,175,9,431,9,111,9,367,9,239,9,495,9,31,9,287,9,159,9,415,9,95,9,351,9,223,9,479,9,63,9,319,9,191,9,447,9,127,9,383,9,255,9,511,9,0,7,64,7,32,7,96,7,16,7,80,7,48,7,112,7,8,7,72,7,40,7,104,7,24,7,88,7,56,7,120,7,4,7,68,7,36,7,100,7,20,7,84,7,52,7,116,7,3,8,131,8,67,8,195,8,35,8,163,8,99,8,227,8],o.static_dtree=[0,5,16,5,8,5,24,5,4,5,20,5,12,5,28,5,2,5,18,5,10,5,26,5,6,5,22,5,14,5,30,5,1,5,17,5,9,5,25,5,5,5,21,5,13,5,29,5,3,5,19,5,11,5,27,5,7,5,23,5],o.static_l_desc=new o(o.static_ltree,_.extra_lbits,257,286,15),o.static_d_desc=new o(o.static_dtree,_.extra_dbits,0,30,15),o.static_bl_desc=new o(null,_.extra_blbits,0,19,7);function u(e,t,n,a,i){var r=this;r.good_length=e,r.max_lazy=t,r.nice_length=n,r.max_chain=a,r.func=i}var d,l=[new u(0,0,0,0,0),new u(4,4,8,4,1),new u(4,5,16,8,1),new u(4,6,32,32,1),new u(4,4,16,16,2),new u(8,16,32,32,2),new u(8,16,128,128,2),new u(8,32,128,256,2),new u(32,128,258,1024,2),new u(32,258,258,4096,2)],f=["need dictionary","stream end","","","stream error","data error","","buffer error","",""],s=113,c=666,h=258,p=262;function x(e,t,n,a){var i=e[2*t],r=e[2*n];return i<r||i==r&&a[t]<=a[n]}function v(){var e,r,u,d,v,b,g,w,m,y,M,A,U,E,k,z,q,D,I,P,S,L,j,B,C,F,G,H,J,K,N,O,Q,R,T,V,W,X,Y,Z,$,ee=this,te=new _,ne=new _,ae=new _;function ie(){var e;for(e=0;e<286;e++)N[2*e]=0;for(e=0;e<30;e++)O[2*e]=0;for(e=0;e<19;e++)Q[2*e]=0;N[512]=1,ee.opt_len=ee.static_len=0,V=X=0}function re(e,t){var n,a,i=-1,r=e[1],_=0,o=7,u=4;for(0===r&&(o=138,u=3),e[2*(t+1)+1]=65535,n=0;n<=t;n++)a=r,r=e[2*(n+1)+1],++_<o&&a==r||(_<u?Q[2*a]+=_:0!==a?(a!=i&&Q[2*a]++,Q[32]++):_<=10?Q[34]++:Q[36]++,_=0,i=a,0===r?(o=138,u=3):a==r?(o=6,u=3):(o=7,u=4))}function _e(e){ee.pending_buf[ee.pending++]=e}function oe(e){_e(255&e),_e(e>>>8&255)}function ue(e,t){var n,a=t;$>16-a?(oe(Z|=(n=e)<<$&65535),Z=n>>>16-$,$+=a-16):(Z|=e<<$&65535,$+=a)}function de(e,t){var n=2*e;ue(65535&t[n],65535&t[n+1])}function le(e,t){var n,a,i=-1,r=e[1],_=0,o=7,u=4;for(0===r&&(o=138,u=3),n=0;n<=t;n++)if(a=r,r=e[2*(n+1)+1],!(++_<o&&a==r)){if(_<u)do{de(a,Q)}while(0!=--_);else 0!==a?(a!=i&&(de(a,Q),_--),de(16,Q),ue(_-3,2)):_<=10?(de(17,Q),ue(_-3,3)):(de(18,Q),ue(_-11,7));_=0,i=a,0===r?(o=138,u=3):a==r?(o=6,u=3):(o=7,u=4)}}function fe(){16==$?(oe(Z),Z=0,$=0):$>=8&&(_e(255&Z),Z>>>=8,$-=8)}function se(e,n){var a,i,r;if(ee.pending_buf[W+2*V]=e>>>8&255,ee.pending_buf[W+2*V+1]=255&e,ee.pending_buf[R+V]=255&n,V++,0===e?N[2*n]++:(X++,e--,N[2*(_._length_code[n]+t+1)]++,O[2*_.d_code(e)]++),0==(8191&V)&&G>2){for(a=8*V,i=S-q,r=0;r<30;r++)a+=O[2*r]*(5+_.extra_dbits[r]);if(a>>>=3,X<Math.floor(V/2)&&a<Math.floor(i/2))return!0}return V==T-1}function ce(e,a){var i,r,o,u,d=0;if(0!==V)do{i=ee.pending_buf[W+2*d]<<8&65280|255&ee.pending_buf[W+2*d+1],r=255&ee.pending_buf[R+d],d++,0===i?de(r,e):(de((o=_._length_code[r])+t+1,e),0!==(u=_.extra_lbits[o])&&ue(r-=_.base_length[o],u),i--,de(o=_.d_code(i),a),0!==(u=_.extra_dbits[o])&&ue(i-=_.base_dist[o],u))}while(d<V);de(n,e),Y=e[513]}function he(){$>8?oe(Z):$>0&&_e(255&Z),Z=0,$=0}function pe(e,t,n){ue(0+(n?1:0),3),function(e,t,n){he(),Y=8,n&&(oe(t),oe(~t)),ee.pending_buf.set(w.subarray(e,e+t),ee.pending),ee.pending+=t}(e,t,!0)}function xe(e,t,n){var a,i,r=0;G>0?(te.build_tree(ee),ne.build_tree(ee),r=function(){var e;for(re(N,te.max_code),re(O,ne.max_code),ae.build_tree(ee),e=18;e>=3&&0===Q[2*_.bl_order[e]+1];e--);return ee.opt_len+=3*(e+1)+5+5+4,e}(),a=ee.opt_len+3+7>>>3,(i=ee.static_len+3+7>>>3)<=a&&(a=i)):a=i=t+5,t+4<=a&&-1!=e?pe(e,t,n):i==a?(ue(2+(n?1:0),3),ce(o.static_ltree,o.static_dtree)):(ue(4+(n?1:0),3),function(e,t,n){var a;for(ue(e-257,5),ue(t-1,5),ue(n-4,4),a=0;a<n;a++)ue(Q[2*_.bl_order[a]+1],3);le(N,e-1),le(O,t-1)}(te.max_code+1,ne.max_code+1,r+1),ce(N,O)),ie(),n&&he()}function ve(t){xe(q>=0?q:-1,S-q,t),q=S,e.flush_pending()}function be(){var t,n,a,i;do{if(0===(i=m-j-S)&&0===S&&0===j)i=v;else if(-1==i)i--;else if(S>=v+v-p){w.set(w.subarray(v,v+v),0),L-=v,S-=v,q-=v,a=t=U;do{n=65535&M[--a],M[a]=n>=v?n-v:0}while(0!=--t);a=t=v;do{n=65535&y[--a],y[a]=n>=v?n-v:0}while(0!=--t);i+=v}if(0===e.avail_in)return;t=e.read_buf(w,S+j,i),(j+=t)>=3&&(A=((A=255&w[S])<<z^255&w[S+1])&k)}while(j<p&&0!==e.avail_in)}function ge(e){var t,n,a=C,i=S,r=B,_=S>v-p?S-(v-p):0,o=K,u=g,d=S+h,l=w[i+r-1],f=w[i+r];B>=J&&(a>>=2),o>j&&(o=j);do{if(w[(t=e)+r]==f&&w[t+r-1]==l&&w[t]==w[i]&&w[++t]==w[i+1]){i+=2,t++;do{}while(w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&w[++i]==w[++t]&&i<d);if(n=h-(d-i),i=d-h,n>r){if(L=e,r=n,n>=o)break;l=w[i+r-1],f=w[i+r]}}}while((e=65535&y[e&u])>_&&0!=--a);return r<=j?r:j}function we(e){return e.total_in=e.total_out=0,e.msg=null,ee.pending=0,ee.pending_out=0,r=s,d=0,te.dyn_tree=N,te.stat_desc=o.static_l_desc,ne.dyn_tree=O,ne.stat_desc=o.static_d_desc,ae.dyn_tree=Q,ae.stat_desc=o.static_bl_desc,Z=0,$=0,Y=8,ie(),function(){var e;for(m=2*v,M[U-1]=0,e=0;e<U-1;e++)M[e]=0;F=l[G].max_lazy,J=l[G].good_length,K=l[G].nice_length,C=l[G].max_chain,S=0,q=0,j=0,D=B=2,P=0,A=0}(),0}ee.depth=[],ee.bl_count=[],ee.heap=[],N=[],O=[],Q=[],ee.pqdownheap=function(e,t){for(var n=ee.heap,a=n[t],i=t<<1;i<=ee.heap_len&&(i<ee.heap_len&&x(e,n[i+1],n[i],ee.depth)&&i++,!x(e,a,n[i],ee.depth));)n[t]=n[i],t=i,i<<=1;n[t]=a},ee.deflateInit=function(e,t,n,i,r,_){return i||(i=8),r||(r=8),_||(_=0),e.msg=null,-1==t&&(t=6),r<1||r>9||8!=i||n<9||n>15||t<0||t>9||_<0||_>2?a:(e.dstate=ee,g=(v=1<<(b=n))-1,k=(U=1<<(E=r+7))-1,z=Math.floor((E+3-1)/3),w=new Uint8Array(2*v),y=[],M=[],T=1<<r+6,ee.pending_buf=new Uint8Array(4*T),u=4*T,W=Math.floor(T/2),R=3*T,G=t,H=_,255&i,we(e))},ee.deflateEnd=function(){return 42!=r&&r!=s&&r!=c?a:(ee.pending_buf=null,M=null,y=null,w=null,ee.dstate=null,r==s?-3:0)},ee.deflateParams=function(e,t,n){var i=0;return-1==t&&(t=6),t<0||t>9||n<0||n>2?a:(l[G].func!=l[t].func&&0!==e.total_in&&(i=e.deflate(1)),G!=t&&(F=l[G=t].max_lazy,J=l[G].good_length,K=l[G].nice_length,C=l[G].max_chain),H=n,i)},ee.deflateSetDictionary=function(e,t,n){var i,_=n,o=0;if(!t||42!=r)return a;if(_<3)return 0;for(_>v-p&&(o=n-(_=v-p)),w.set(t.subarray(o,o+_),0),S=_,q=_,A=((A=255&w[0])<<z^255&w[1])&k,i=0;i<=_-3;i++)A=(A<<z^255&w[i+2])&k,y[i&g]=M[A],M[A]=i;return 0},ee.deflate=function(t,_){var h,x,m,E,C,J;if(_>4||_<0)return a;if(!t.next_out||!t.next_in&&0!==t.avail_in||r==c&&4!=_)return t.msg=f[4],a;if(0===t.avail_out)return t.msg=f[7],i;if(e=t,E=d,d=_,42==r&&(x=8+(b-8<<4)<<8,(m=(G-1&255)>>1)>3&&(m=3),x|=m<<6,0!==S&&(x|=32),r=s,_e((J=x+=31-x%31)>>8&255),_e(255&J)),0!==ee.pending){if(e.flush_pending(),0===e.avail_out)return d=-1,0}else if(0===e.avail_in&&_<=E&&4!=_)return e.msg=f[7],i;if(r==c&&0!==e.avail_in)return t.msg=f[7],i;if(0!==e.avail_in||0!==j||0!=_&&r!=c){switch(C=-1,l[G].func){case 0:C=function(t){var n,a=65535;for(a>u-5&&(a=u-5);;){if(j<=1){if(be(),0===j&&0==t)return 0;if(0===j)break}if(S+=j,j=0,n=q+a,(0===S||S>=n)&&(j=S-n,S=n,ve(!1),0===e.avail_out))return 0;if(S-q>=v-p&&(ve(!1),0===e.avail_out))return 0}return ve(4==t),0===e.avail_out?4==t?2:0:4==t?3:1}(_);break;case 1:C=function(t){for(var n,a=0;;){if(j<p){if(be(),j<p&&0==t)return 0;if(0===j)break}if(j>=3&&(A=(A<<z^255&w[S+2])&k,a=65535&M[A],y[S&g]=M[A],M[A]=S),0!==a&&(S-a&65535)<=v-p&&2!=H&&(D=ge(a)),D>=3)if(n=se(S-L,D-3),j-=D,D<=F&&j>=3){D--;do{S++,A=(A<<z^255&w[S+2])&k,a=65535&M[A],y[S&g]=M[A],M[A]=S}while(0!=--D);S++}else S+=D,D=0,A=((A=255&w[S])<<z^255&w[S+1])&k;else n=se(0,255&w[S]),j--,S++;if(n&&(ve(!1),0===e.avail_out))return 0}return ve(4==t),0===e.avail_out?4==t?2:0:4==t?3:1}(_);break;case 2:C=function(t){for(var n,a,i=0;;){if(j<p){if(be(),j<p&&0==t)return 0;if(0===j)break}if(j>=3&&(A=(A<<z^255&w[S+2])&k,i=65535&M[A],y[S&g]=M[A],M[A]=S),B=D,I=L,D=2,0!==i&&B<F&&(S-i&65535)<=v-p&&(2!=H&&(D=ge(i)),D<=5&&(1==H||3==D&&S-L>4096)&&(D=2)),B>=3&&D<=B){a=S+j-3,n=se(S-1-I,B-3),j-=B-1,B-=2;do{++S<=a&&(A=(A<<z^255&w[S+2])&k,i=65535&M[A],y[S&g]=M[A],M[A]=S)}while(0!=--B);if(P=0,D=2,S++,n&&(ve(!1),0===e.avail_out))return 0}else if(0!==P){if((n=se(0,255&w[S-1]))&&ve(!1),S++,j--,0===e.avail_out)return 0}else P=1,S++,j--}return 0!==P&&(n=se(0,255&w[S-1]),P=0),ve(4==t),0===e.avail_out?4==t?2:0:4==t?3:1}(_)}if(2!=C&&3!=C||(r=c),0==C||2==C)return 0===e.avail_out&&(d=-1),0;if(1==C){if(1==_)ue(2,3),de(n,o.static_ltree),fe(),1+Y+10-$<9&&(ue(2,3),de(n,o.static_ltree),fe()),Y=7;else if(pe(0,0,!1),3==_)for(h=0;h<U;h++)M[h]=0;if(e.flush_pending(),0===e.avail_out)return d=-1,0}}return 4!=_?0:1}}function b(){var e=this;e.next_in_index=0,e.next_out_index=0,e.avail_in=0,e.total_in=0,e.avail_out=0,e.total_out=0}function g(e){var t=new b,n=512,a=new Uint8Array(n);void 0===e&&(e=-1),t.deflateInit(e),t.next_out=a,this.append=function(e,i){var r,_=[],o=0,u=0,d=0;if(e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,0!=t.deflate(0))throw"deflating: "+t.msg;t.next_out_index&&(t.next_out_index==n?_.push(new Uint8Array(a)):_.push(new Uint8Array(a.subarray(0,t.next_out_index)))),d+=t.next_out_index,i&&t.next_in_index>0&&t.next_in_index!=o&&(i(t.next_in_index),o=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return r=new Uint8Array(d),_.forEach((function(e){r.set(e,u),u+=e.length})),r}},this.flush=function(){var e,i,r=[],_=0,o=0;do{if(t.next_out_index=0,t.avail_out=n,1!=(e=t.deflate(4))&&0!=e)throw"deflating: "+t.msg;n-t.avail_out>0&&r.push(new Uint8Array(a.subarray(0,t.next_out_index))),o+=t.next_out_index}while(t.avail_in>0||0===t.avail_out);return t.deflateEnd(),i=new Uint8Array(o),r.forEach((function(e){i.set(e,_),_+=e.length})),i}}b.prototype={deflateInit:function(e,t){var n=this;return n.dstate=new v,t||(t=15),n.dstate.deflateInit(n,e,t)},deflate:function(e){var t=this;return t.dstate?t.dstate.deflate(t,e):a},deflateEnd:function(){var e=this;if(!e.dstate)return a;var t=e.dstate.deflateEnd();return e.dstate=null,t},deflateParams:function(e,t){var n=this;return n.dstate?n.dstate.deflateParams(n,e,t):a},deflateSetDictionary:function(e,t){var n=this;return n.dstate?n.dstate.deflateSetDictionary(n,e,t):a},read_buf:function(e,t,n){var a=this,i=a.avail_in;return i>n&&(i=n),0===i?0:(a.avail_in-=i,e.set(a.next_in.subarray(a.next_in_index,a.next_in_index+i),t),a.next_in_index+=i,a.total_in+=i,i)},flush_pending:function(){var e=this,t=e.dstate.pending;t>e.avail_out&&(t=e.avail_out),0!==t&&(e.next_out.set(e.dstate.pending_buf.subarray(e.dstate.pending_out,e.dstate.pending_out+t),e.next_out_index),e.next_out_index+=t,e.dstate.pending_out+=t,e.total_out+=t,e.avail_out-=t,e.dstate.pending-=t,0===e.dstate.pending&&(e.dstate.pending_out=0))}},e.zip?e.zip.Deflater=g:(d=new g,e.addEventListener("message",(function(t){var n=t.data;n.init&&(d=new g(n.level),e.postMessage({oninit:!0})),n.append&&e.postMessage({onappend:!0,data:d.append(n.data,(function(t){e.postMessage({progress:!0,current:t})}))}),n.flush&&e.postMessage({onflush:!0,data:d.flush()})}),!1))}(this);