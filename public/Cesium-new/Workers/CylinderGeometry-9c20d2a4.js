define(["exports","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./CylinderGeometryLibrary-4c16342b","./when-515d5295","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./VertexFormat-e844760b"],(function(t,e,n,a,o,r,i,s,u,f,p,m,d,b,c,y){"use strict";var l=new a.r,v=new o.a,h=new o.a,w=new o.a,g=new o.a;function A(t){var e=(t=s.e(t,s.e.EMPTY_OBJECT)).length,n=t.topRadius,a=t.bottomRadius,o=s.e(t.vertexFormat,y.n.DEFAULT),r=s.e(t.slices,128);if(!s.t(e))throw new u.t("options.length must be defined.");if(!s.t(n))throw new u.t("options.topRadius must be defined.");if(!s.t(a))throw new u.t("options.bottomRadius must be defined.");if(r<3)throw new u.t("options.slices must be greater than or equal to 3.");if(s.t(t.offsetAttribute)&&t.offsetAttribute===m.I.TOP)throw new u.t("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._length=e,this._topRadius=n,this._bottomRadius=a,this._vertexFormat=y.n.clone(o),this._slices=r,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderGeometry"}A.packedLength=y.n.packedLength+5,A.pack=function(t,e,n){if(!s.t(t))throw new u.t("value is required");if(!s.t(e))throw new u.t("array is required");return n=s.e(n,0),y.n.pack(t._vertexFormat,e,n),n+=y.n.packedLength,e[n++]=t._length,e[n++]=t._topRadius,e[n++]=t._bottomRadius,e[n++]=t._slices,e[n]=s.e(t._offsetAttribute,-1),e};var _,x=new y.n,R={vertexFormat:x,length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,offsetAttribute:void 0};A.unpack=function(t,e,n){if(!s.t(t))throw new u.t("array is required");e=s.e(e,0);var a=y.n.unpack(t,e,x);e+=y.n.packedLength;var o=t[e++],r=t[e++],i=t[e++],f=t[e++],p=t[e];return s.t(n)?(n._vertexFormat=y.n.clone(a,n._vertexFormat),n._length=o,n._topRadius=r,n._bottomRadius=i,n._slices=f,n._offsetAttribute=-1===p?void 0:p,n):(R.length=o,R.topRadius=r,R.bottomRadius=i,R.slices=f,R.offsetAttribute=-1===p?void 0:p,new A(R))},A.createGeometry=function(t){var u=t._length,y=t._topRadius,A=t._bottomRadius,_=t._vertexFormat,x=t._slices;if(!(u<=0||y<0||A<0||0===y&&0===A)){var R,F=x+x,D=x+F,T=F+F,O=i.x.computePositions(u,y,A,x,!0),C=_.st?new Float32Array(2*T):void 0,L=_.normal?new Float32Array(3*T):void 0,P=_.tangent?new Float32Array(3*T):void 0,k=_.bitangent?new Float32Array(3*T):void 0,G=_.normal||_.tangent||_.bitangent;if(G){var I=_.tangent||_.bitangent,M=0,z=0,E=0,N=Math.atan2(A-y,u),U=v;U.z=Math.sin(N);var q=Math.cos(N),S=w,B=h;for(R=0;R<x;R++){var Y=R/x*b.n.TWO_PI,Z=q*Math.cos(Y),J=q*Math.sin(Y);G&&(U.x=Z,U.y=J,I&&(S=o.a.normalize(o.a.cross(o.a.UNIT_Z,U,S),S)),_.normal&&(L[M++]=U.x,L[M++]=U.y,L[M++]=U.z,L[M++]=U.x,L[M++]=U.y,L[M++]=U.z),_.tangent&&(P[z++]=S.x,P[z++]=S.y,P[z++]=S.z,P[z++]=S.x,P[z++]=S.y,P[z++]=S.z),_.bitangent&&(B=o.a.normalize(o.a.cross(U,S,B),B),k[E++]=B.x,k[E++]=B.y,k[E++]=B.z,k[E++]=B.x,k[E++]=B.y,k[E++]=B.z))}for(R=0;R<x;R++)_.normal&&(L[M++]=0,L[M++]=0,L[M++]=-1),_.tangent&&(P[z++]=1,P[z++]=0,P[z++]=0),_.bitangent&&(k[E++]=0,k[E++]=-1,k[E++]=0);for(R=0;R<x;R++)_.normal&&(L[M++]=0,L[M++]=0,L[M++]=1),_.tangent&&(P[z++]=1,P[z++]=0,P[z++]=0),_.bitangent&&(k[E++]=0,k[E++]=1,k[E++]=0)}var V=12*x-12,W=d.IndexDatatype.createTypedArray(T,V),j=0,H=0;for(R=0;R<x-1;R++)W[j++]=H,W[j++]=H+2,W[j++]=H+3,W[j++]=H,W[j++]=H+3,W[j++]=H+1,H+=2;for(W[j++]=F-2,W[j++]=0,W[j++]=1,W[j++]=F-2,W[j++]=1,W[j++]=F-1,R=1;R<x-1;R++)W[j++]=F+R+1,W[j++]=F+R,W[j++]=F;for(R=1;R<x-1;R++)W[j++]=D,W[j++]=D+R,W[j++]=D+R+1;var K=0;if(_.st){var Q=Math.max(y,A);for(R=0;R<T;R++){var X=o.a.fromArray(O,3*R,g);C[K++]=(X.x+Q)/(2*Q),C[K++]=(X.y+Q)/(2*Q)}}var $=new p.t;_.position&&($.position=new f.r({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:O})),_.normal&&($.normal=new f.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),_.tangent&&($.tangent=new f.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:P})),_.bitangent&&($.bitangent=new f.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:k})),_.st&&($.st=new f.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:C})),l.x=.5*u,l.y=Math.max(A,y);var tt=new n.c(o.a.ZERO,a.r.magnitude(l));if(s.t(t._offsetAttribute)){u=O.length;var et=new Uint8Array(u/3),nt=t._offsetAttribute===m.I.NONE?0:1;e.d(et,nt),$.applyOffset=new f.r({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:et})}return new f.T({attributes:$,indices:W,primitiveType:c._0x38df4a.TRIANGLES,boundingSphere:tt,offsetAttribute:t._offsetAttribute})}},A.getUnitCylinder=function(){return s.t(_)||(_=A.createGeometry(new A({topRadius:1,bottomRadius:1,length:1,vertexFormat:y.n.POSITION_ONLY}))),_},t.x=A}));
