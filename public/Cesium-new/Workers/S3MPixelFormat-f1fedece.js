define(["exports"],(function(r){"use strict";var n=function(){var r,n=(r=!0,function(n,t){var u=r?function(){if(t){var r=t.apply(n,arguments);return t=null,r}}:function(){};return r=!1,u}),t=function(){var r=n(this,(function(){return r.toString().search("(((.+)+)+)+$").toString().constructor(r).search("(((.+)+)+)+$")}));r();var t=!0;return function(r,n){var u=t?function(){if(n){var t=n.apply(r,arguments);return n=null,t}}:function(){};return t=!1,u}}(),u=t(this,(function(){return u.toString().search("(((.+)+)+)+$").toString().constructor(u).search("(((.+)+)+)+$")}));u();var i=!0;return function(r,n){var t=i?function(){if(n){var t=n.apply(r,arguments);return n=null,t}}:function(){};return i=!1,t}}(),t=n(void 0,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var u=Object.freeze({LUMINANCE_8:1,LUMINANCE_16:2,ALPHA:3,ALPHA_4_LUMINANCE_4:4,LUMINANCE_ALPHA:5,RGB_565:6,BGR565:7,RGB:10,BGR:11,ARGB:12,ABGR:13,BGRA:14,WEBP:25,RGBA:28,DXT1:17,DXT2:18,DXT3:19,DXT4:20,DXT5:21,CRN_DXT5:26,STANDARD_CRN:27,KTX2:31});r.S3MPixelFormat=u}));
