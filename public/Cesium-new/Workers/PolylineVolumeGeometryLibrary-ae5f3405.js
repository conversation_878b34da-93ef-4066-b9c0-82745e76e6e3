define(["exports","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Cartesian4-034d54d5","./EllipsoidTangentPlane-fd839d7b","./Math-5e38123d","./PrimitiveType-b38a4004","./PolylinePipeline-bf1462fc","./GeometryAttribute-9bc31a7f"],(function(a,r,e,n,t,o,l,i,c){"use strict";var s=Object.freeze({ROUNDED:0,MITERED:1,BEVELED:2}),u=[new e.a,new e.a],y=new e.a,m=new e.a,d=new e.a,p=new e.a,f=new e.a,v=new e.a,g=new e.a,h=new e.a,w=new e.a,B=new e.a,E=new e.a,z={},S=new e.i;function b(a,r){for(var e=new Array(a.length),n=0;n<a.length;n++){var t=a[n];S=r.cartesianToCartographic(t,S),e[n]=S.height,a[n]=r.scaleToGeodeticSurface(t,t)}return e}function P(a,r,n,t){var o,l=a[0],i=a[1],c=e.a.angleBetween(l,i),s=Math.ceil(c/t),u=new Array(s);if(r===n){for(o=0;o<s;o++)u[o]=r;return u.push(n),u}var y=(n-r)/s;for(o=1;o<s;o++){var m=r+o*y;u[o]=m}return u[0]=r,u.push(n),u}var T=new e.a,A=new e.a;function x(a,n,o,l){var i=new t.s(o,l),c=i.projectPointOntoPlane(e.a.add(o,a,T),T),s=i.projectPointOntoPlane(e.a.add(o,n,A),A),u=r.r.angleBetween(c,s);return s.x*c.y-s.y*c.x>=0?-u:u}var D=new e.a(-1,0,0),N=l.c.clone(l.c.IDENTITY),O=new l.c,R=new l.r,V=l.r.IDENTITY.clone(),I=new e.a,F=new n.a,L=new e.a;function G(a,r,n,t,o,i,s,u){var y=I,m=F;N=c.m.eastNorthUpToFixedFrame(a,o,N),y=l.c.multiplyByPointAsVector(N,D,y);var d=x(y=e.a.normalize(y,y),r,a,o);R=l.r.fromRotationZ(d,R),L.z=i,N=l.c.multiplyTransformation(N,l.c.fromRotationTranslation(R,L,O),N);var p=V;p[0]=s;for(var f=0;f<u;f++)for(var v=0;v<n.length;v+=3)m=e.a.fromArray(n,v,m),m=l.r.multiplyByVector(p,m,m),m=l.c.multiplyByPoint(N,m,m),t.push(m.x,m.y,m.z);return t}function M(a,r,n,t,o,i,s,u,y){var m=I,d=F;N=c.m.eastNorthUpToFixedFrame(a,o,N),m=l.c.multiplyByPointAsVector(N,D,m);var p=x(m=e.a.normalize(m,m),r,a,o);R=l.r.fromRotationZ(p,R),L.z=i,N=l.c.multiplyTransformation(N,l.c.fromRotationTranslation(R,L,O),N);var f=V;f[0]=s;for(var v=0;v<u;v++)for(var g=0;g<n.length;g+=3)d=e.a.fromArray(n,g,d),d=l.r.multiplyByVector(f,d,d),d=l.c.multiplyByPoint(N,d,d),d=l.c.multiplyByPoint(y,d,d),t.push(d.x,d.y,d.z);return t}var U=new e.a;function _(a,r,n,t,o,l,i){for(var c=0;c<a.length;c+=3){t=G(e.a.fromArray(a,c,U),r,n,t,o,l[c/3],i,1)}return t}function j(a,r){var e=a.length,n=new Array(6*e),t=0,o=r.x+r.width/2,l=r.y+r.height/2,i=a[0];n[t++]=i.x-o,n[t++]=0,n[t++]=i.y-l;for(var c=1;c<e;c++){var s=(i=a[c]).x-o,u=i.y-l;n[t++]=s,n[t++]=0,n[t++]=u,n[t++]=s,n[t++]=0,n[t++]=u}return i=a[0],n[t++]=i.x-o,n[t++]=0,n[t++]=i.y-l,n}function C(a,r){for(var e=a.length,n=new Array(3*e),t=0,o=r.x+r.width/2,l=r.y+r.height/2,i=0;i<e;i++)n[t++]=a[i].x-o,n[t++]=0,n[t++]=a[i].y-l;return n}var q=new c.a,Q=new e.a,Y=new l.r;function Z(a,r,n,t,i,u,y,m,d,p){var f,v,g=e.a.angleBetween(e.a.subtract(r,a,B),e.a.subtract(n,a,E)),h=t===s.BEVELED?0:Math.ceil(g/o.n.toRadians(5));if(f=i?l.r.fromQuaternion(c.a.fromAxisAngle(e.a.negate(a,B),g/(h+1),q),Y):l.r.fromQuaternion(c.a.fromAxisAngle(a,g/(h+1),q),Y),r=e.a.clone(r,Q),h>0)for(var w=p?2:1,z=0;z<h;z++)r=l.r.multiplyByVector(f,r,r),v=e.a.subtract(r,a,B),v=e.a.normalize(v,v),i||(v=e.a.negate(v,v)),y=G(u.scaleToGeodeticSurface(r,E),v,m,y,u,d,1,w);else v=e.a.subtract(r,a,B),v=e.a.normalize(v,v),i||(v=e.a.negate(v,v)),y=G(u.scaleToGeodeticSurface(r,E),v,m,y,u,d,1,1),n=e.a.clone(n,Q),v=e.a.subtract(n,a,B),v=e.a.normalize(v,v),i||(v=e.a.negate(v,v)),y=G(u.scaleToGeodeticSurface(n,E),v,m,y,u,d,1,1);return y}z.removeDuplicatesFromShape=function(a){for(var e=a.length,n=[],t=e-1,o=0;o<e;t=o++){var l=a[t],i=a[o];r.r.equals(l,i)||n.push(i)}return n},z.angleIsGreaterThanPi=function(a,r,n,o){var l=new t.s(n,o),i=l.projectPointOntoPlane(e.a.add(n,a,T),T),c=l.projectPointOntoPlane(e.a.add(n,r,A),A);return c.x*i.y-c.y*i.x>=0};var K=new e.a,k=new e.a;z.computePositions=function(a,r,n,t,l){var c=t._ellipsoid,E=b(a,c),S=t._granularity,T=t._cornerType,A=l?j(r,n):C(r,n),x=l?C(r,n):void 0,D=n.height/2,N=n.width/2,O=a.length,R=[],V=l?[]:void 0,I=y,F=m,L=d,M=p,U=f,q=v,Q=g,Y=h,H=w,J=a[0],W=a[1];M=c.geodeticSurfaceNormal(J,M),I=e.a.subtract(W,J,I),I=e.a.normalize(I,I),Y=e.a.cross(M,I,Y),Y=e.a.normalize(Y,Y);var X=E[0],$=E[1];l&&(V=G(J,Y,x,V,c,X+D,1,1)),H=e.a.clone(J,H),J=W,F=e.a.negate(I,F);for(var aa,ra=1;ra<O-1;ra++){var ea=l?2:1;W=a[ra+1],I=e.a.subtract(W,J,I),I=e.a.normalize(I,I),L=e.a.add(I,F,L),L=e.a.normalize(L,L),M=c.geodeticSurfaceNormal(J,M);var na=e.a.multiplyByScalar(M,e.a.dot(I,M),K);e.a.subtract(I,na,na),e.a.normalize(na,na);var ta=e.a.multiplyByScalar(M,e.a.dot(F,M),k);if(e.a.subtract(F,ta,ta),e.a.normalize(ta,ta),!o.n.equalsEpsilon(Math.abs(e.a.dot(na,ta)),1,o.n.EPSILON7)){L=e.a.cross(L,M,L),L=e.a.cross(M,L,L),L=e.a.normalize(L,L);var oa=1/Math.max(.25,e.a.magnitude(e.a.cross(L,F,B))),la=z.angleIsGreaterThanPi(I,F,J,c);la?(U=e.a.add(J,e.a.multiplyByScalar(L,oa*N,L),U),q=e.a.add(U,e.a.multiplyByScalar(Y,N,q),q),u[0]=e.a.clone(H,u[0]),u[1]=e.a.clone(q,u[1]),aa=P(u,X+D,$+D,S),R=_(i.v.generateArc({positions:u,granularity:S,ellipsoid:c}),Y,A,R,c,aa,1),Y=e.a.cross(M,I,Y),Y=e.a.normalize(Y,Y),Q=e.a.add(U,e.a.multiplyByScalar(Y,N,Q),Q),T===s.ROUNDED||T===s.BEVELED?Z(U,q,Q,T,la,c,R,A,$+D,l):R=G(J,L=e.a.negate(L,L),A,R,c,$+D,oa,ea),H=e.a.clone(Q,H)):(U=e.a.add(J,e.a.multiplyByScalar(L,oa*N,L),U),q=e.a.add(U,e.a.multiplyByScalar(Y,-N,q),q),u[0]=e.a.clone(H,u[0]),u[1]=e.a.clone(q,u[1]),aa=P(u,X+D,$+D,S),R=_(i.v.generateArc({positions:u,granularity:S,ellipsoid:c}),Y,A,R,c,aa,1),Y=e.a.cross(M,I,Y),Y=e.a.normalize(Y,Y),Q=e.a.add(U,e.a.multiplyByScalar(Y,-N,Q),Q),T===s.ROUNDED||T===s.BEVELED?Z(U,q,Q,T,la,c,R,A,$+D,l):R=G(J,L,A,R,c,$+D,oa,ea),H=e.a.clone(Q,H)),F=e.a.negate(I,F)}else R=G(H,Y,A,R,c,X+D,1,1),H=J;X=$,$=E[ra+1],J=W}u[0]=e.a.clone(H,u[0]),u[1]=e.a.clone(J,u[1]),aa=P(u,X+D,$+D,S),R=_(i.v.generateArc({positions:u,granularity:S,ellipsoid:c}),Y,A,R,c,aa,1),l&&(V=G(J,Y,x,V,c,$+D,1,1)),O=R.length;var ia=l?O+V.length:O,ca=new Float64Array(ia);return ca.set(R),l&&ca.set(V,O),ca},z.computeLocalPositions=function(a,r,n,t,E,S){var T=t._ellipsoid,A=b(a,T),x=t._granularity,D=t._cornerType,N=E?j(r,n):C(r,n),O=E?C(r,n):void 0,R=n.width/2,V=a.length,I=[],F=E?[]:void 0,L=y,G=m,q=d,Q=p,Y=f,H=v,J=g,W=h,X=w,$=c.m.eastNorthUpToFixedFrame(S,T,new l.c),aa=l.c.inverse($,new l.c),ra=a[0],ea=a[1];Q=T.geodeticSurfaceNormal(ra,Q),L=e.a.subtract(ea,ra,L),L=e.a.normalize(L,L),W=e.a.cross(Q,L,W),W=e.a.normalize(W,W);var na=A[0],ta=A[1];E&&(F=M(ra,W,O,F,T,na+0,1,1,aa)),X=e.a.clone(ra,X),ra=ea,G=e.a.negate(L,G);for(var oa,la=1;la<V-1;la++){var ia=E?2:1;ea=a[la+1],L=e.a.subtract(ea,ra,L),L=e.a.normalize(L,L),q=e.a.add(L,G,q),q=e.a.normalize(q,q),Q=T.geodeticSurfaceNormal(ra,Q);var ca=e.a.multiplyByScalar(Q,e.a.dot(L,Q),K);e.a.subtract(L,ca,ca),e.a.normalize(ca,ca);var sa=e.a.multiplyByScalar(Q,e.a.dot(G,Q),k);if(e.a.subtract(G,sa,sa),e.a.normalize(sa,sa),!o.n.equalsEpsilon(Math.abs(e.a.dot(ca,sa)),1,o.n.EPSILON7)){q=e.a.cross(q,Q,q),q=e.a.cross(Q,q,q),q=e.a.normalize(q,q);var ua=1/Math.max(.25,e.a.magnitude(e.a.cross(q,G,B))),ya=z.angleIsGreaterThanPi(L,G,ra,T);ya?(Y=e.a.add(ra,e.a.multiplyByScalar(q,ua*R,q),Y),H=e.a.add(Y,e.a.multiplyByScalar(W,R,H),H),u[0]=e.a.clone(X,u[0]),u[1]=e.a.clone(H,u[1]),oa=P(u,na+0,ta+0,x),I=_(i.v.generateArc({positions:u,granularity:x,ellipsoid:T}),W,N,I,T,oa,1,fromEnu),W=e.a.cross(Q,L,W),W=e.a.normalize(W,W),J=e.a.add(Y,e.a.multiplyByScalar(W,R,J),J),D===s.ROUNDED||D===s.BEVELED?Z(Y,H,J,D,ya,T,I,N,ta+0,E):I=M(ra,q=e.a.negate(q,q),N,I,T,ta+0,ua,ia,aa),X=e.a.clone(J,X)):(Y=e.a.add(ra,e.a.multiplyByScalar(q,ua*R,q),Y),H=e.a.add(Y,e.a.multiplyByScalar(W,-R,H),H),u[0]=e.a.clone(X,u[0]),u[1]=e.a.clone(H,u[1]),oa=P(u,na+0,ta+0,x),I=_(i.v.generateArc({positions:u,granularity:x,ellipsoid:T}),W,N,I,T,oa,1),W=e.a.cross(Q,L,W),W=e.a.normalize(W,W),J=e.a.add(Y,e.a.multiplyByScalar(W,-R,J),J),D===s.ROUNDED||D===s.BEVELED?Z(Y,H,J,D,ya,T,I,N,ta+0,E):I=M(ra,q,N,I,T,ta+0,ua,ia,aa),X=e.a.clone(J,X)),G=e.a.negate(L,G)}else I=M(X,W,N,I,T,na+0,1,1,aa),X=ra;na=ta,ta=A[la+1],ra=ea}u[0]=e.a.clone(X,u[0]),u[1]=e.a.clone(ra,u[1]),oa=P(u,na+0,ta+0,x),I=function(a,r,n,t,o,l,i,c){for(var s=0;s<a.length;s+=3)t=M(e.a.fromArray(a,s,U),r,n,t,o,l[s/3],i,1,c);return t}(i.v.generateArc({positions:u,granularity:x,ellipsoid:T}),W,N,I,T,oa,1,aa),E&&(F=M(ra,W,O,F,T,ta+0,1,1,aa)),V=I.length;var ma=E?V+F.length:V,da=new Float64Array(ma);return da.set(I),E&&da.set(F,V),da},a.K=z,a.O=s}));
