define(["exports","./S3MPixelFormat-f1fedece","./when-515d5295","./Cartographic-1bbcab04","./Buffer-72562b71","./PrimitiveType-b38a4004","./IndexDatatype-eefd5922","./ComponentDatatype-d430c7f7"],(function(t,e,r,n,a,o,i,s){"use strict";function u(t,e,r,n){var a=t|e<<8,o=a>>11&31,i=a>>5&63,s=31&a;return r[n+0]=o<<3|o>>2,r[n+1]=i<<2|i>>4,r[n+2]=s<<3|s>>2,r[n+3]=255,a}function c(t,e,r,n){var a=0;0!=(6&n)&&(a=8),function(t,e,r,n){for(var a=new Uint8Array(16),o=u(e[r+0],e[r+1],a,0),i=u(e[r+2],e[r+3],a,4),s=0;s<3;s++){var c=a[s],f=a[4+s];n&&o<=i?(a[8+s]=(c+f)/2,a[12+s]=0):(a[8+s]=(2*c+f)/3,a[12+s]=(c+2*f)/3)}a[11]=255,a[15]=n&&o<=i?0:255;var y=new Uint8Array(16);for(s=0;s<4;++s){var p=e[r+4+s];y[4*s+0]=3&p,y[4*s+1]=p>>2&3,y[4*s+2]=p>>4&3,y[4*s+3]=p>>6&3}for(s=0;s<16;++s)for(var v=4*y[s],l=0;l<4;++l)t[4*s+l]=a[v+l]}(t,e,r+a,0!=(1&n)),0!=(2&n)?function(t,e,r){for(var n=0;n<8;++n){var a=bytes[r+n],o=15&a,i=240&a;t[8*n+3]=o|o<<4,t[8*n+7]=i|i>>4}}(t,0,r):0!=(4&n)&&function(t,e,r){var n=e[r+0],a=e[r+1],o=new Uint8Array(8);if(o[0]=n,o[1]=a,n<=a){for(var i=1;i<5;++i)o[1+i]=((5-i)*n+i*a)/5;o[6]=0,o[7]=255}else for(i=1;i<7;++i)o[1+i]=((7-i)*n+i*a)/7;var s=new Uint8Array(16),u=(r+=2,0);for(i=0;i<2;++i){for(var c=0,f=0;f<3;++f)c|=e[r++]<<8*f;for(f=0;f<8;++f){var y=c>>3*f&7;s[u++]=y}}for(i=0;i<16;++i)t[4*i+3]=o[s[i]]}(t,e,r)}function f(t){}f.decode=function(t,r,n,a,o){if(null!=t&&null!=a&&0!=n&&0!=r){var i=0;1&(i=o>e.S3MPixelFormat.BGR||o===e.S3MPixelFormat.LUMINANCE_ALPHA?4:33)&&32&i?function(t,e,r,n){for(var a=new Uint16Array(4),o=t,i=0,s=0,u=0,c=0,f=0,y=0,p=0,v=0,l=0,d=e/4,h=r/4,A=0;A<h;A++)for(var x=0;x<d;x++)u=4*((h-A)*d+x),a[0]=n[u],a[1]=n[u+1],c=31&a[0],f=2016&a[0],y=63488&a[0],p=31&a[1],v=2016&a[1],l=63488&a[1],a[2]=5*c+3*p>>3|5*f+3*v>>3&2016|5*y+3*l>>3&63488,a[3]=5*p+3*c>>3|5*v+3*f>>3&2016|5*l+3*y>>3&63488,i=n[u+2],o[s=4*A*e+4*x]=a[3&i],o[s+1]=a[i>>2&3],o[s+2]=a[i>>4&3],o[s+3]=a[i>>6&3],o[s+=e]=a[i>>8&3],o[s+1]=a[i>>10&3],o[s+2]=a[i>>12&3],o[s+3]=a[i>>14],i=n[u+3],o[s+=e]=a[3&i],o[s+1]=a[i>>2&3],o[s+2]=a[i>>4&3],o[s+3]=a[i>>6&3],o[s+=e]=a[i>>8&3],o[s+1]=a[i>>10&3],o[s+2]=a[i>>12&3],o[s+3]=a[i>>14]}
/*! @brief Decompresses an image in memory.

	 @param rgba		Storage for the decompressed pixels.
	 @param width	The width of the source image.
	 @param height	The height of the source image.
	 @param blocks	The compressed DXT blocks.
	 @param flags	Compression flags.

	 The decompressed pixels will be written as a contiguous array of width*height
	 16 rgba values, with each component as 1 byte each. In memory this is:

	 { r1, g1, b1, a1, .... , rn, gn, bn, an } for n = width*height

	 The flags parameter should specify either kDxt1, kDxt3 or kDxt5 compression,
	 however, DXT1 will be used by default if none is specified. All other flags
	 are ignored.

	 Internally this function calls squish::Decompress for each block.
	 */(t,r,n,a):function(t,e,r,n,a){for(var o=0!=(1&a)?8:16,i=0,s=0;s<r;s+=4)for(var u=0;u<e;u+=4){var f=new Uint8Array(64);c(f,n,i,a);for(var y=0,p=0;p<4;++p)for(var v=0;v<4;++v){var l=u+v,d=s+p;if(l<e&&d<r)for(var h=4*(e*(r-d)+l),A=0;A<4;++A)t[h++]=f[y++];else y+=4}i+=o}}(t,r,n,a,i)}};var y=function(){var t,e=(t=!0,function(e,r){var n=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}}(),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));n();var a=!0;return function(t,e){var r=a?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return a=!1,r}}(),p=y(void 0,(function(){return p.toString().search("(((.+)+)+)+$").toString().constructor(p).search("(((.+)+)+)+$")}));p();var v=Object.freeze({SVC_Vertex:1,SVC_Normal:2,SVC_VertexColor:4,SVC_SecondColor:8,SVC_TexutreCoord:16,SVC_TexutreCoordIsW:32}),l=function(){var t,e=(t=!0,function(e,r){var n=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}}(),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));n();var a=!0;return function(t,e){var r=a?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return a=!1,r}}(),d=l(void 0,(function(){return d.toString().search("(((.+)+)+)+$").toString().constructor(d).search("(((.+)+)+)+$")}));function h(){}d(),h.computeNeighbors=function(t,e){for(var r=t.length/3,n=new Uint32Array(e+1),a=new Uint32Array(e+1),o=function(t,e){t<e?n[t+1]++:a[e+1]++},i=0;i<r;i++){var s=t[3*i],u=t[3*i+1],c=t[3*i+2];o(s,u),o(u,c),o(c,s)}for(i=u=s=0;i<e;i++)c=n[i+1],o=a[i+1],n[i+1]=s,a[i+1]=u,s+=c,u+=o;var f=new Uint32Array(6*r),y=n[e];for(o=function(t,e,r){if(t<e){var o=n[t+1]++;f[2*o]=e,f[2*o+1]=r}else o=a[e+1]++,f[2*y+2*o]=t,f[2*y+2*o+1]=r},i=0;i<r;i++)s=t[3*i],u=t[3*i+1],c=t[3*i+2],o(s,u,i),o(u,c,i),o(c,s,i);for(s=function(t,e){var r=2*t;for(t=e-t,e=1;e<t;e++){for(var n=f[r+2*e],a=f[r+2*e+1],o=e-1;0<=o&&f[r+2*o]>n;o--)f[r+2*o+2]=f[r+2*o],f[r+2*o+3]=f[r+2*o+1];f[r+2*o+2]=n,f[r+2*o+3]=a}},i=0;i<e;i++)s(n[i],n[i+1]),s(y+a[i],y+a[i+1]);var p=new Int32Array(3*r),v=function(e,r){return e===t[3*r]?0:e===t[3*r+1]?1:e===t[3*r+2]?2:-1};for(r=function(t,e){t=v(t,e),p[3*e+t]=-1},s=function(t,e,r,n){t=v(t,e),p[3*e+t]=n,r=v(r,n),p[3*n+r]=e},i=0;i<e;i++){u=n[i],c=n[i+1],o=a[i];for(var l=a[i+1];u<c&&o<l;){var d=f[2*u],h=f[2*y+2*o];d===h?(s(i,f[2*u+1],h,f[2*y+2*o+1]),u++,o++):d<h?(r(i,f[2*u+1]),u++):(r(h,f[2*y+2*o+1]),o++)}for(;u<c;)r(i,f[2*u+1]),u++;for(;o<l;)r(h=f[2*y+2*o],f[2*y+2*o+1]),o++}return p};var A=null;function x(){}function m(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=function(){var t=r(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var e=!0;return function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}}(),a=function(){var t=n(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var e=!0;return function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}}(),o=a(this,(function(){return o.toString().search("(((.+)+)+)+$").toString().constructor(o).search("(((.+)+)+)+$")}));return o(),t*Math.PI/180}h.deduplicate=function(t,e,r,n,a){void 0===r&&(r=0),void 0===n&&(n=0),void 0===a&&(a=t.byteLength/(4*e)),t=new Uint32Array(t,n,a*e),n=new Uint32Array(a);var o=Math.floor(1.1*a)+1;(null==A||A.length<2*o)&&(A=new Uint32Array(function(t){--t;for(var e=1;32>e;e<<=1)t|=t>>e;return t+1}(2*o)));for(var i=0;i<2*o;i++)A[i]=0;var s=0,u=0!==r?Math.ceil(7.84*1.96/(r*r)*r*(1-r)):a;for(i=0;i<a;i++){if(i===u){if((f=1-s/i)******Math.sqrt(f*(1-f)/i)<r)return null;u*=2}for(var c,f=i*e,y=c=0;y<e;y++)c=(c=t[f+y]+c|0)+(c<<11)+(c>>>2)|0;y=(c>>>=0)%o;for(var p=s;0!==A[2*y+1];){if(A[2*y]===c){var v=A[2*y+1]-1,l=v*e;t:{for(var d=0;d<e;d++)if(t[f+d]!==t[l+d]){l=!1;break t}l=!0}if(l){p=n[v];break}}++y>=o&&(y-=o)}p===s&&(A[2*y]=c,A[2*y+1]=i+1,s++),n[i]=p}if(0!==r&&1-s/a<r)return null;for(r=new Uint32Array(e*s),i=s=0;i<a;i++)if(n[i]===s){for(o=t,u=i*e,f=r,c=s*e,y=e,p=0;p<y;p++)f[c+p]=o[u+p];s++}return{buffer:r.buffer,indices:n,uniqueCount:s}};var g=m(4),b=m(35),S=Math.cos(b),D=Math.cos(g);var B={position0:new n.a,position1:new n.a,faceNormal0:new n.a,faceNormal1:new n.a,cosAngle:0},I=new n.a,z=new n.a;function C(t,e){var r,a,o,i,s,u,c=(r=t.cosAngle,Math.acos(1<r?1:-1>r?-1:r));return a=z,o=t.position1,i=t.position0,s=i.x-o.x,u=i.y-o.y,(i=s*s+u*u+(o=i.z-o.z)*o)?(i=1/Math.sqrt(i),a.x=s*i,a.y=u*i,a.z=o*i):(a.x=0,a.y=0,a.z=0),n.a.cross(t.faceNormal0,t.faceNormal1,I),c*(0<n.a.dot(I,z)?-1:1)>e}var T=new n.a,w=new n.a,V=new n.a;x.extractEdges=function(t){var e=t.vertices,r=t.dim,a=B,o=a.position0,i=a.position1,s=a.faceNormal0,u=a.faceNormal1,c=function(t){for(var e=t.faces.length/3,r=t.vertices,a=t.dim,o=t.faces,i=new Float32Array(3*e),s=0;s<e;s++){var u=o[3*s+0],c=o[3*s+1],f=o[3*s+2];T.x=r[a*u],T.y=r[a*u+1],T.z=r[a*u+2],w.x=r[a*c],w.y=r[a*c+1],w.z=r[a*c+2],V.x=r[a*f],V.y=r[a*f+1],V.z=r[a*f+2],n.a.subtract(w,T,w),n.a.subtract(V,T,V),n.a.cross(w,V,T),p=void 0,(p=(y=T).x*y.x+y.y*y.y+y.z*y.z)>0&&(p=1/Math.sqrt(p),y.x*=p,y.y*=p,y.z*=p),i[3*s+0]=T.x,i[3*s+1]=T.y,i[3*s+2]=T.z}var y,p;return i}(t),f=function(t){var e=t.faces.length/3,r=t.faces,n=t.neighbors,a=0,o=0;for(o=0;o<e;o++){var i=n[3*o+0],s=n[3*o+1],u=n[3*o+2],c=r[3*o+0],f=r[3*o+1],y=r[3*o+2];a+=-1===i||c<f?1:0,a+=-1===s||f<y?1:0,a+=-1===u||y<c?1:0}var p=new Int32Array(4*a),v=0;for(o=0;o<e;o++)i=n[3*o+0],s=n[3*o+1],u=n[3*o+2],c=r[3*o+0],f=r[3*o+1],y=r[3*o+2],(-1===i||c<f)&&(p[v++]=c,p[v++]=f,p[v++]=o,p[v++]=i),(-1===s||f<y)&&(p[v++]=f,p[v++]=y,p[v++]=o,p[v++]=s),(-1===u||y<c)&&(p[v++]=y,p[v++]=c,p[v++]=o,p[v++]=u);return p}(t),y=f.length/4,p=new Float32Array(9*y),v=0,l=new Float32Array(12*y),d=0,h=0,A=0,x=function(t,e){0===e&&(e=t,t=0);for(var r=Array(e-t),n=t;n<e;n++)r[n-t]=n;return r}(0,y),m=new Float32Array(y);m.forEach((function(t,a,s){var u=f[4*a+0],c=f[4*a+1];o.x=e[u*r],o.y=e[u*r+1],o.z=e[u*r+2],i.x=e[c*r],i.y=e[c*r+1],i.z=e[c*r+2],s[a]=n.a.distance(o,i)})),x.sort((function(t,e){return m[e]-m[t]}));for(var b=[],I=[],z=0;z<y;z++){var P=x[z],L=m[P],O=f[4*P+0],F=f[4*P+1],N=f[4*P+2],$=f[4*P+3],E=-1===$;if(o.x=e[O*r],o.y=e[O*r+1],o.z=e[O*r+2],i.x=e[F*r],i.y=e[F*r+1],i.z=e[F*r+2],E)s.x=c[3*N],s.y=c[3*N+1],s.z=c[3*N+2],u.x=s.x,u.y=s.y,u.z=s.z,a.cosAngle=n.a.dot(s,u);else if(s.x=c[3*N],s.y=c[3*N+1],s.z=c[3*N+2],u.x=c[3*$],u.y=c[3*$+1],u.z=c[3*$+2],a.cosAngle=n.a.dot(s,u),a.cosAngle>D)continue;h+=L,A++,E||a.cosAngle<S?(p[v++]=a.position0.x,p[v++]=a.position0.y,p[v++]=a.position0.z,p[v++]=a.position1.x,p[v++]=a.position1.y,p[v++]=a.position1.z,p[v++]=a.faceNormal0.x,p[v++]=a.faceNormal0.y,p[v++]=a.faceNormal0.z,b.push(L)):C(a,g)&&(l[d++]=a.position0.x,l[d++]=a.position0.y,l[d++]=a.position0.z,l[d++]=a.position1.x,l[d++]=a.position1.y,l[d++]=a.position1.z,l[d++]=a.faceNormal0.x,l[d++]=a.faceNormal0.y,l[d++]=a.faceNormal0.z,l[d++]=a.faceNormal1.x,l[d++]=a.faceNormal1.y,l[d++]=a.faceNormal1.z,I.push(L))}p=p.slice(0,v),l=l.slice(0,d);var U=h/A,_=b.length,M=I.length;return{regular:{instancesData:p,instanceCount:_,edgeLength:_*U},silhouette:{instancesData:l,instanceCount:M,edgeLength:M*U},averageEdgeLength:U}};var P=function(){var t,e=(t=!0,function(e,r){var n=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}}(),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));n();var a=!0;return function(t,e){var r=a?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return a=!1,r}}(),L=P(void 0,(function(){return L.toString().search("(((.+)+)+)+$").toString().constructor(L).search("(((.+)+)+)+$")}));function O(t){}function F(t){if(r.t(t.cachedSidenessVertexBuffer))return t.cachedSidenessVertexBuffer;var e=new Float32Array(8),n=0;return e[n++]=0,e[n++]=0,e[n++]=0,e[n++]=1,e[n++]=1,e[n++]=1,e[n++]=1,e[n++]=0,t.cachedSidenessVertexBuffer=a.u.createVertexBuffer({context:t,typedArray:e,usage:a.A.STATIC_DRAW}),t.cachedSidenessVertexBuffer.vertexArrayDestroyable=!1,t.cachedSidenessVertexBuffer}function N(t,e){for(var r,a,o,i=e.componentsPerAttribute,s=t.vertCompressConstant,u=new n.a(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),c=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),f=new Float32Array(3*t.verticesCount),y=0;y<t.verticesCount;y++)r=c[i*y]*s+u.x,a=c[i*y+1]*s+u.y,o=c[i*y+2]*s+u.z,f[3*y]=r,f[3*y+1]=a,f[3*y+2]=o;return f}L(),O.RegularInstanceStride=12,O.SilhouetteInstanceStride=15,O.createEdgeData=function(t,e,n){if(0==e.length)return null;var a,o=e[0];a=0===o.indexType?new Uint16Array(o.indicesTypedArray.buffer,o.indicesTypedArray.byteOffset,o.indicesTypedArray.byteLength/2):new Uint32Array(o.indicesTypedArray.buffer,o.indicesTypedArray.byteOffset,o.indicesTypedArray.byteLength/4);var i=O.extractEdgeInformation(t,!1,a),s=x.extractEdges(i);return r.t(n)&&(r.t(s.regular.instancesData)&&n.push(s.regular.instancesData.buffer),r.t(s.silhouette.instancesData)&&n.push(s.silhouette.instancesData.buffer)),s},O.createIndexBuffer=function(t){return r.t(t.cachedSidenessIndexBuffer)||(t.cachedSidenessIndexBuffer=a.u.createIndexBuffer({context:t,typedArray:(e=new Uint16Array(6),n=0,e[n++]=2,e[n++]=1,e[n++]=0,e[n++]=3,e[n++]=2,e[n++]=0,e),usage:a.A.STATIC_DRAW,indexDatatype:i.IndexDatatype.UNSIGNED_SHORT}),t.cachedSidenessIndexBuffer.vertexArrayDestroyable=!1),t.cachedSidenessIndexBuffer;var e,n},O.createRegularEdgeAttributes=function(t,e){if(r.t(e.instancesData)&&0!=e.instancesData.length){var n={},o=[];e.attributeLocations=n,e.attributes=o;var i=a.u.createVertexBuffer({context:t,typedArray:e.instancesData,usage:a.A.STATIC_DRAW});e.instancesData=null;var u=s.ComponentDatatype.getSizeInBytes(s.ComponentDatatype.FLOAT),c=F(t),f=0;n.aSideness=f++,o.push({index:n.aSideness,vertexBuffer:c,componentsPerAttribute:2,componentDatatype:s.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*s.ComponentDatatype.getSizeInBytes(s.ComponentDatatype.FLOAT),normalize:!1});var y=O.RegularInstanceStride,p=0;n.aPosition0=f++,o.push({index:n.aPosition0,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=3,n.aPosition1=f++,o.push({index:n.aPosition1,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=3,n.aNormal=f++,o.push({index:n.aNormal,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=3,n.batchId=f++,o.push({index:n.batchId,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=1,n.aVariantStroke=f++,o.push({index:n.aVariantStroke,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=1,n.aVariantExtension=f++,o.push({index:n.aVariantExtension,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*p,strideInBytes:u*y,instanceDivisor:1}),p+=1}},O.createSilhouetteEdgeAttributes=function(t,e){if(r.t(e.instancesData)&&0!=e.instancesData.length){var n={},o=[];e.attributeLocations=n,e.attributes=o;var i=a.u.createVertexBuffer({context:t,typedArray:e.instancesData,usage:a.A.STATIC_DRAW});e.instancesData=null;var u=s.ComponentDatatype.getSizeInBytes(s.ComponentDatatype.FLOAT),c=0;n.aSideness=c++,o.push({index:n.aSideness,vertexBuffer:F(t),componentsPerAttribute:2,componentDatatype:s.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*u,normalize:!1});var f=O.SilhouetteInstanceStride,y=0;n.aPosition0=c++,o.push({index:n.aPosition0,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=3,n.aPosition1=c++,o.push({index:n.aPosition1,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=3,n.aNormalA=c++,o.push({index:n.aNormalA,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=3,n.aNormalB=c++,o.push({index:n.aNormalB,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=3,n.batchId=c++,o.push({index:n.batchId,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=1,n.aVariantStroke=c++,o.push({index:n.aVariantStroke,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=1,n.aVariantExtension=c++,o.push({index:n.aVariantExtension,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:s.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:u*y,strideInBytes:u*f,instanceDivisor:1}),y+=1}},O.extractEdgeInformation=function(t,e,n){var a,o=t.attrLocation.aPosition,i=t.vertexAttributes[o],s=r.t(t.nCompressOptions)&&(t.nCompressOptions&v.SVC_Vertex)===v.SVC_Vertex,u=i.componentsPerAttribute;s?(u=3,a=N(t,i)):a=new Float32Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/4);var c=a.length/u;if(e&&n)return{faces:n,neighbors:h.computeNeighbors(n,c),vertices:a,dim:u};var f,y=i.typedArray.buffer;f=s?a.buffer:y.slice(i.typedArray.byteOffset,i.typedArray.byteOffset+i.typedArray.byteLength);var p=h.deduplicate(f,u),l=O.selectIndexData(p.indices,n);return{faces:l,neighbors:h.computeNeighbors(l,p.uniqueCount),vertices:new Float32Array(p.buffer),dim:u}},O.selectIndexData=function(t,e){if(e){e=e.slice();for(var r=0;r<e.length;r++)e[r]=t[e[r]];return e}return t};var $=new n.a,E=new n.a,U=new n.a,_=new n.a,M=new n.a,R=new n.a,q=new n.a,k=new n.a;function W(t,e){function n(t,e,r){var n=48217*t%2147483647,a=e+n/2147483647*(r-=e);return{seed:n,result:Math.round(a)}}var a=function(t,e){var r=new Float32Array(6),n=new Uint32Array(r.buffer),a=new Uint32Array(1);r[0]=t.x,r[1]=t.y,r[2]=t.z,r[3]=e.x,r[4]=e.y,r[5]=e.z,a[0]=5381;for(var o=0;o<n.length;o++)a[0]=31*a[0]+n[o];return a[0]}(t,e);r.t(a)||(a=2147483647*Math.random());var o=n(a,0,255);a=o.seed,o.result,a=(o=n(a,0,5)).seed;var i,s=o.result;a=(o={seed:i=48217*a%2147483647,result:i/2147483646}).seed;var u=o.result;return u=-(1-Math.min(u/.7,1))+Math.max(0,u-.7)/(1-.7),{variantStroke:s,variantExtension:u=255*(Math.abs(u)**1.2*(0>u?-1:1)*.5+.5)}}O.createEdgeDataByIndices=function(t,e){var a,o,i=t.attrLocation.aPosition,s=t.vertexAttributes[i],u=r.t(t.nCompressOptions)&&(t.nCompressOptions&v.SVC_Vertex)===v.SVC_Vertex,c=s.componentsPerAttribute;u?(c=3,a=N(t,s)):a=new Float32Array(s.typedArray.buffer,s.typedArray.byteOffset,s.typedArray.byteLength/4);for(var f=[],y=[],p=(o=0===e.indexType?new Uint16Array(e.indicesTypedArray.buffer,e.indicesTypedArray.byteOffset,e.indicesTypedArray.byteLength/2):new Uint32Array(e.indicesTypedArray.buffer,e.indicesTypedArray.byteOffset,e.indicesTypedArray.byteLength/4)).length,l=0,d=0,h=4*Math.floor(p/4);d<h;d+=4){var A=o[d],x=o[d+1],m=o[d+2],g=o[d+3];if($.x=a[c*A],$.y=a[c*A+1],$.z=a[c*A+2],E.x=a[c*x],E.y=a[c*x+1],E.z=a[c*x+2],U.x=a[c*m],U.y=a[c*m+1],U.z=a[c*m+2],_.x=a[c*g],_.y=a[c*g+1],_.z=a[c*g+2],!(n.a.equals(E,U)||n.a.equals(E,_)||n.a.equals(E,$)||n.a.equals(U,$)||n.a.equals(_,$))){if(m===g){if(n.a.subtract(E,$,M),n.a.subtract(U,$,R),n.a.cross(M,R,M),n.a.equals(M,n.a.ZERO))continue;n.a.normalize(M,M),f.push($.x),f.push($.y),f.push($.z),f.push(E.x),f.push(E.y),f.push(E.z),f.push(M.x),f.push(M.y),f.push(M.z),f.push(A);var b=(D=W($,E)).variantStroke,S=D.variantExtension;f.push(b),f.push(S)}else{if(n.a.subtract(E,$,M),n.a.subtract(U,$,R),n.a.cross(M,R,M),n.a.equals(M,n.a.ZERO)||(n.a.normalize(M,M),n.a.subtract(E,$,q),n.a.subtract(_,$,k),n.a.cross(k,q,q),n.a.equals(q,n.a.ZERO)))continue;n.a.normalize(q,q),y.push($.x),y.push($.y),y.push($.z),y.push(E.x),y.push(E.y),y.push(E.z),y.push(M.x),y.push(M.y),y.push(M.z),y.push(q.x),y.push(q.y),y.push(q.z),y.push(A);var D;b=(D=W($,E)).variantStroke,S=D.variantExtension;y.push(b),y.push(S)}l+=n.a.distance($,E)}}var B=l/(p/4),I=f.length/O.RegularInstanceStride,z=y.length/O.SilhouetteInstanceStride;return{regular:{instancesData:new Float32Array(f),instanceCount:I,edgeLength:I*B},silhouette:{instancesData:new Float32Array(y),instanceCount:z,edgeLength:z},averageEdgeLength:B}};var Z=function(){var t,e=(t=!0,function(e,r){var n=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}}(),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));n();var a=!0;return function(t,e){var r=a?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return a=!1,r}}(),j=Z(void 0,(function(){return j.toString().search("(((.+)+)+)+$").toString().constructor(j).search("(((.+)+)+)+$")}));j();var G=Object.freeze({encNONE:0,enrS3TCDXTN:14,enrPVRTPF_PVRTC2:19,enrPVRTPF_PVRTC:20,enrPVRTPF_PVRTC_4bpp:21,enrPVRTPF_ETC1:22});t.S3MCompressType=G,t.VertexCompressOption=v,t._0x5d8d50=O,t.d=f}));
