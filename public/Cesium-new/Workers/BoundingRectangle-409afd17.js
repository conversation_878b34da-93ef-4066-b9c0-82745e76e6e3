define(["exports","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Intersect-53434a77","./Rectangle-e170be8b"],(function(t,e,n,h,i,r,a){"use strict";function o(t,e,n,h){this.x=i.e(t,0),this.y=i.e(e,0),this.width=i.e(n,0),this.height=i.e(h,0)}o.packedLength=4,o.pack=function(t,e,n){return h.n.typeOf.object("value",t),h.n.defined("array",e),n=i.e(n,0),e[n++]=t.x,e[n++]=t.y,e[n++]=t.width,e[n]=t.height,e},o.unpack=function(t,e,n){return h.n.defined("array",t),e=i.e(e,0),i.t(n)||(n=new o),n.x=t[e++],n.y=t[e++],n.width=t[e++],n.height=t[e],n},o.fromPoints=function(t,e){if(i.t(e)||(e=new o),!i.t(t)||0===t.length)return e.x=0,e.y=0,e.width=0,e.height=0,e;for(var n=t.length,h=t[0].x,r=t[0].y,a=t[0].x,c=t[0].y,y=1;y<n;y++){var u=t[y],x=u.x,f=u.y;h=Math.min(x,h),a=Math.max(x,a),r=Math.min(f,r),c=Math.max(f,c)}return e.x=h,e.y=r,e.width=a-h,e.height=c-r,e};var c=new r.s,y=new n.i,u=new n.i;o.fromRectangle=function(t,n,h){if(i.t(h)||(h=new o),!i.t(t))return h.x=0,h.y=0,h.width=0,h.height=0,h;var r=(n=i.e(n,c)).project(a.s.southwest(t,y)),x=n.project(a.s.northeast(t,u));return e.r.subtract(x,r,x),h.x=r.x,h.y=r.y,h.width=x.x,h.height=x.y,h},o.clone=function(t,e){if(i.t(t))return i.t(e)?(e.x=t.x,e.y=t.y,e.width=t.width,e.height=t.height,e):new o(t.x,t.y,t.width,t.height)},o.union=function(t,e,n){h.n.typeOf.object("left",t),h.n.typeOf.object("right",e),i.t(n)||(n=new o);var r=Math.min(t.x,e.x),a=Math.min(t.y,e.y),c=Math.max(t.x+t.width,e.x+e.width),y=Math.max(t.y+t.height,e.y+e.height);return n.x=r,n.y=a,n.width=c-r,n.height=y-a,n},o.expand=function(t,e,n){h.n.typeOf.object("rectangle",t),h.n.typeOf.object("point",e),n=o.clone(t,n);var i=e.x-n.x,r=e.y-n.y;return i>n.width?n.width=i:i<0&&(n.width-=i,n.x=e.x),r>n.height?n.height=r:r<0&&(n.height-=r,n.y=e.y),n},o.intersect=function(t,e){h.n.typeOf.object("left",t),h.n.typeOf.object("right",e);var n=t.x,i=t.y,a=e.x,o=e.y;return n>a+e.width||n+t.width<a||i+t.height<o||i>o+e.height?r.S.OUTSIDE:r.S.INTERSECTING},o.equals=function(t,e){return t===e||i.t(t)&&i.t(e)&&t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},o.prototype.clone=function(t){return o.clone(this,t)},o.prototype.intersect=function(t){return o.intersect(this,t)},o.prototype.equals=function(t){return o.equals(this,t)},t.n=o}));
