define(["exports","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./Math-5e38123d"],(function(t,a,n,i,e,s){"use strict";function h(t,a,n,i,e,s,h){var r=function(t,a){return t*a*(4+t*(4-3*a))/16}(t,n);return(1-r)*t*a*(i+r*e*(h+r*s*(2*h*h-1)))}var r=new a.a,d=new a.a;function o(t,i,e,o){var c=a.a.normalize(o.cartographicToCartesian(i,d),r),u=a.a.normalize(o.cartographicToCartesian(e,d),d);n.n.typeOf.number.greaterThanOrEquals("value",Math.abs(Math.abs(a.a.angleBetween(c,u))-Math.PI),.0125),function(t,a,n,i,e,r,d){var o,c,u,M,_,g=(a-n)/a,l=r-i,f=Math.atan((1-g)*Math.tan(e)),p=Math.atan((1-g)*Math.tan(d)),v=Math.cos(f),m=Math.sin(f),H=Math.cos(p),b=Math.sin(p),O=v*H,q=v*b,S=m*b,w=m*H,U=l,A=s.n.TWO_PI,R=Math.cos(U),y=Math.sin(U);do{R=Math.cos(U),y=Math.sin(U);var P,C=q-w*R;u=Math.sqrt(H*H*y*y+C*C),c=S+O*R,o=Math.atan2(u,c),0===u?(P=0,M=1):M=1-(P=O*y/u)*P,A=U,_=c-2*S/M,isNaN(_)&&(_=0),U=l+h(g,P,M,o,u,c,_)}while(Math.abs(U-A)>s.n.EPSILON12);var D=M*(a*a-n*n)/(n*n),T=D*(256+D*(D*(74-47*D)-128))/1024,x=_*_,E=n*(1+D*(4096+D*(D*(320-175*D)-768))/16384)*(o-T*u*(_+T*(c*(2*x-1)-T*_*(4*u*u-3)*(4*x-3)/6)/4)),I=Math.atan2(H*y,q-w*R),N=Math.atan2(v*y,q*R-w);t._distance=E,t._startHeading=I,t._endHeading=N,t._uSquared=D}(t,o.maximumRadius,o.minimumRadius,i.longitude,i.latitude,e.longitude,e.latitude),t._start=a.i.clone(i,t._start),t._end=a.i.clone(e,t._end),t._start.height=0,t._end.height=0,function(t){var a=t._uSquared,n=t._ellipsoid.maximumRadius,i=t._ellipsoid.minimumRadius,e=(n-i)/n,s=Math.cos(t._startHeading),h=Math.sin(t._startHeading),r=(1-e)*Math.tan(t._start.latitude),d=1/Math.sqrt(1+r*r),o=d*r,c=Math.atan2(r,s),u=d*h,M=u*u,_=1-M,g=Math.sqrt(_),l=a/4,f=l*l,p=f*l,v=f*f,m=1+l-3*f/4+5*p/4-175*v/64,H=1-l+15*f/8-35*p/8,b=1-3*l+35*f/4,O=1-5*l,q=m*c-H*Math.sin(2*c)*l/2-b*Math.sin(4*c)*f/16-O*Math.sin(6*c)*p/48-5*Math.sin(8*c)*v/512,S=t._constants;S.a=n,S.b=i,S.f=e,S.cosineHeading=s,S.sineHeading=h,S.tanU=r,S.cosineU=d,S.sineU=o,S.sigma=c,S.sineAlpha=u,S.sineSquaredAlpha=M,S.cosineSquaredAlpha=_,S.cosineAlpha=g,S.u2Over4=l,S.u4Over16=f,S.u6Over64=p,S.u8Over256=v,S.a0=m,S.a1=H,S.a2=b,S.a3=O,S.distanceRatio=q}(t)}function c(t,n,s){var h=i.e(s,e.n.WGS84);this._ellipsoid=h,this._start=new a.i,this._end=new a.i,this._constants={},this._startHeading=void 0,this._endHeading=void 0,this._distance=void 0,this._uSquared=void 0,i.t(t)&&i.t(n)&&o(this,t,n,h)}Object.defineProperties(c.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return n.n.defined("distance",this._distance),this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},startHeading:{get:function(){return n.n.defined("distance",this._distance),this._startHeading}},endHeading:{get:function(){return n.n.defined("distance",this._distance),this._endHeading}}}),c.prototype.setEndPoints=function(t,a){n.n.defined("start",t),n.n.defined("end",a),o(this,t,a,this._ellipsoid)},c.prototype.interpolateUsingFraction=function(t,a){return this.interpolateUsingSurfaceDistance(this._distance*t,a)},c.prototype.interpolateUsingSurfaceDistance=function(t,e){n.n.defined("distance",this._distance);var s=this._constants,r=s.distanceRatio+t/s.b,d=Math.cos(2*r),o=Math.cos(4*r),c=Math.cos(6*r),u=Math.sin(2*r),M=Math.sin(4*r),_=Math.sin(6*r),g=Math.sin(8*r),l=r*r,f=r*l,p=s.u8Over256,v=s.u2Over4,m=s.u6Over64,H=s.u4Over16,b=2*f*p*d/3+r*(1-v+7*H/4-15*m/4+579*p/64-(H-15*m/4+187*p/16)*d-(5*m/4-115*p/16)*o-29*p*c/16)+(v/2-H+71*m/32-85*p/16)*u+(5*H/16-5*m/4+383*p/96)*M-l*((m-11*p/2)*u+5*p*M/2)+(29*m/96-29*p/16)*_+539*p*g/1536,O=Math.asin(Math.sin(b)*s.cosineAlpha),q=Math.atan(s.a/s.b*Math.tan(O));b-=s.sigma;var S=Math.cos(2*s.sigma+b),w=Math.sin(b),U=Math.cos(b),A=s.cosineU*U,R=s.sineU*w,y=Math.atan2(w*s.sineHeading,A-R*s.cosineHeading)-h(s.f,s.sineAlpha,s.cosineSquaredAlpha,b,w,U,S);return i.t(e)?(e.longitude=this._start.longitude+y,e.latitude=q,e.height=0,e):new a.i(this._start.longitude+y,q,0)},t.D=c}));
