define(["exports","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./PrimitiveType-b38a4004","./FeatureDetection-7fae0d5a","./Math-5e38123d","./Rectangle-e170be8b","./Cartesian4-034d54d5","./buildModuleUrl-dba4ec07","./RuntimeError-350acae3","./Intersect-53434a77"],(function(e,t,n,r,a,o,i,s,u,l,c,f,d){"use strict";var p=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});function y(e,t,n,r){this[0]=a.e(e,0),this[1]=a.e(n,0),this[2]=a.e(t,0),this[3]=a.e(r,0)}y.packedLength=4,y.pack=function(e,t,n){return r.n.typeOf.object("value",e),r.n.defined("array",t),n=a.e(n,0),t[n++]=e[0],t[n++]=e[1],t[n++]=e[2],t[n++]=e[3],t},y.unpack=function(e,t,n){return r.n.defined("array",e),t=a.e(t,0),a.t(n)||(n=new y),n[0]=e[t++],n[1]=e[t++],n[2]=e[t++],n[3]=e[t++],n},y.clone=function(e,t){if(a.t(e))return a.t(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new y(e[0],e[2],e[1],e[3])},y.fromArray=function(e,t,n){return r.n.defined("array",e),t=a.e(t,0),a.t(n)||(n=new y),n[0]=e[t],n[1]=e[t+1],n[2]=e[t+2],n[3]=e[t+3],n},y.fromColumnMajorArray=function(e,t){return r.n.defined("values",e),y.clone(e,t)},y.fromRowMajorArray=function(e,t){return r.n.defined("values",e),a.t(t)?(t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3],t):new y(e[0],e[1],e[2],e[3])},y.fromScale=function(e,t){return r.n.typeOf.object("scale",e),a.t(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=e.y,t):new y(e.x,0,0,e.y)},y.fromUniformScale=function(e,t){return r.n.typeOf.number("scale",e),a.t(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=e,t):new y(e,0,0,e)},y.fromRotation=function(e,t){r.n.typeOf.number("angle",e);var n=Math.cos(e),o=Math.sin(e);return a.t(t)?(t[0]=n,t[1]=o,t[2]=-o,t[3]=n,t):new y(n,-o,o,n)},y.toArray=function(e,t){return r.n.typeOf.object("matrix",e),a.t(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):[e[0],e[1],e[2],e[3]]},y.getElementIndex=function(e,t){return r.n.typeOf.number.greaterThanOrEquals("row",t,0),r.n.typeOf.number.lessThanOrEquals("row",t,1),r.n.typeOf.number.greaterThanOrEquals("column",e,0),r.n.typeOf.number.lessThanOrEquals("column",e,1),2*e+t},y.getColumn=function(e,t,n){r.n.typeOf.object("matrix",e),r.n.typeOf.number.greaterThanOrEquals("index",t,0),r.n.typeOf.number.lessThanOrEquals("index",t,1),r.n.typeOf.object("result",n);var a=2*t,o=e[a],i=e[a+1];return n.x=o,n.y=i,n},y.setColumn=function(e,t,n,a){r.n.typeOf.object("matrix",e),r.n.typeOf.number.greaterThanOrEquals("index",t,0),r.n.typeOf.number.lessThanOrEquals("index",t,1),r.n.typeOf.object("cartesian",n),r.n.typeOf.object("result",a);var o=2*t;return(a=y.clone(e,a))[o]=n.x,a[o+1]=n.y,a},y.getRow=function(e,t,n){r.n.typeOf.object("matrix",e),r.n.typeOf.number.greaterThanOrEquals("index",t,0),r.n.typeOf.number.lessThanOrEquals("index",t,1),r.n.typeOf.object("result",n);var a=e[t],o=e[t+2];return n.x=a,n.y=o,n},y.setRow=function(e,t,n,a){return r.n.typeOf.object("matrix",e),r.n.typeOf.number.greaterThanOrEquals("index",t,0),r.n.typeOf.number.lessThanOrEquals("index",t,1),r.n.typeOf.object("cartesian",n),r.n.typeOf.object("result",a),(a=y.clone(e,a))[t]=n.x,a[t+2]=n.y,a};var h=new t.r;y.getScale=function(e,n){return r.n.typeOf.object("matrix",e),r.n.typeOf.object("result",n),n.x=t.r.magnitude(t.r.fromElements(e[0],e[1],h)),n.y=t.r.magnitude(t.r.fromElements(e[2],e[3],h)),n};var m=new t.r;function w(e,t,n,r){this.x=a.e(e,0),this.y=a.e(t,0),this.z=a.e(n,0),this.w=a.e(r,0)}y.getMaximumScale=function(e){return y.getScale(e,m),t.r.maximumComponent(m)},y.multiply=function(e,t,n){r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n);var a=e[0]*t[0]+e[2]*t[1],o=e[0]*t[2]+e[2]*t[3],i=e[1]*t[0]+e[3]*t[1],s=e[1]*t[2]+e[3]*t[3];return n[0]=a,n[1]=i,n[2]=o,n[3]=s,n},y.add=function(e,t,n){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n),n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n},y.subtract=function(e,t,n){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n),n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n},y.multiplyByVector=function(e,t,n){r.n.typeOf.object("matrix",e),r.n.typeOf.object("cartesian",t),r.n.typeOf.object("result",n);var a=e[0]*t.x+e[2]*t.y,o=e[1]*t.x+e[3]*t.y;return n.x=a,n.y=o,n},y.multiplyByScalar=function(e,t,n){return r.n.typeOf.object("matrix",e),r.n.typeOf.number("scalar",t),r.n.typeOf.object("result",n),n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n},y.multiplyByScale=function(e,t,n){return r.n.typeOf.object("matrix",e),r.n.typeOf.object("scale",t),r.n.typeOf.object("result",n),n[0]=e[0]*t.x,n[1]=e[1]*t.x,n[2]=e[2]*t.y,n[3]=e[3]*t.y,n},y.negate=function(e,t){return r.n.typeOf.object("matrix",e),r.n.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},y.transpose=function(e,t){r.n.typeOf.object("matrix",e),r.n.typeOf.object("result",t);var n=e[0],a=e[2],o=e[1],i=e[3];return t[0]=n,t[1]=a,t[2]=o,t[3]=i,t},y.abs=function(e,t){return r.n.typeOf.object("matrix",e),r.n.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t},y.equals=function(e,t){return e===t||a.t(e)&&a.t(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},y.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]},y.equalsEpsilon=function(e,t,n){return r.n.typeOf.number("epsilon",n),e===t||a.t(e)&&a.t(t)&&Math.abs(e[0]-t[0])<=n&&Math.abs(e[1]-t[1])<=n&&Math.abs(e[2]-t[2])<=n&&Math.abs(e[3]-t[3])<=n},y.IDENTITY=Object.freeze(new y(1,0,0,1)),y.ZERO=Object.freeze(new y(0,0,0,0)),y.COLUMN0ROW0=0,y.COLUMN0ROW1=1,y.COLUMN1ROW0=2,y.COLUMN1ROW1=3,Object.defineProperties(y.prototype,{length:{get:function(){return y.packedLength}}}),y.prototype.clone=function(e){return y.clone(this,e)},y.prototype.equals=function(e){return y.equals(this,e)},y.prototype.equalsEpsilon=function(e,t){return y.equalsEpsilon(this,e,t)},y.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"};var O=new n.a;w.fromAxisAngle=function(e,t,o){r.n.typeOf.object("axis",e),r.n.typeOf.number("angle",t);var i=t/2,s=Math.sin(i),u=(O=n.a.normalize(e,O)).x*s,l=O.y*s,c=O.z*s,f=Math.cos(i);return a.t(o)?(o.x=u,o.y=l,o.z=c,o.w=f,o):new w(u,l,c,f)};var b=[1,2,0],x=new Array(3);w.fromRotationMatrix=function(e,t){r.n.typeOf.object("matrix",e);var n,i,s,u,l,c=e[o.r.COLUMN0ROW0],f=e[o.r.COLUMN1ROW1],d=e[o.r.COLUMN2ROW2],p=c+f+d;if(p>0)l=.5*(n=Math.sqrt(p+1)),n=.5/n,i=(e[o.r.COLUMN1ROW2]-e[o.r.COLUMN2ROW1])*n,s=(e[o.r.COLUMN2ROW0]-e[o.r.COLUMN0ROW2])*n,u=(e[o.r.COLUMN0ROW1]-e[o.r.COLUMN1ROW0])*n;else{var y=0;f>c&&(y=1),d>c&&d>f&&(y=2);var h=b[y],m=b[h];n=Math.sqrt(e[o.r.getElementIndex(y,y)]-e[o.r.getElementIndex(h,h)]-e[o.r.getElementIndex(m,m)]+1);var O=x;O[y]=.5*n,n=.5/n,l=(e[o.r.getElementIndex(m,h)]-e[o.r.getElementIndex(h,m)])*n,O[h]=(e[o.r.getElementIndex(h,y)]+e[o.r.getElementIndex(y,h)])*n,O[m]=(e[o.r.getElementIndex(m,y)]+e[o.r.getElementIndex(y,m)])*n,i=-O[0],s=-O[1],u=-O[2]}return a.t(t)?(t.x=i,t.y=s,t.z=u,t.w=l,t):new w(i,s,u,l)};var E=new w,_=new w,v=new w,T=new w;w.fromHeadingPitchRoll=function(e,t){return r.n.typeOf.object("headingPitchRoll",e),T=w.fromAxisAngle(n.a.UNIT_X,e.roll,E),v=w.fromAxisAngle(n.a.UNIT_Y,-e.pitch,t),t=w.multiply(v,T,v),_=w.fromAxisAngle(n.a.UNIT_Z,-e.heading,E),w.multiply(_,t,t)};var S=new n.a,g=new n.a,D=new w,P=new w,N=new w;w.packedLength=4,w.pack=function(e,t,n){return r.n.typeOf.object("value",e),r.n.defined("array",t),n=a.e(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},w.unpack=function(e,t,n){return r.n.defined("array",e),t=a.e(t,0),a.t(n)||(n=new w),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},w.packedInterpolationLength=3,w.convertPackedArrayForInterpolation=function(e,t,n,r){w.unpack(e,4*n,N),w.conjugate(N,N);for(var a=0,o=n-t+1;a<o;a++){var i=3*a;w.unpack(e,4*(t+a),D),w.multiply(D,N,D),D.w<0&&w.negate(D,D),w.computeAxis(D,S);var s=w.computeAngle(D);r[i]=S.x*s,r[i+1]=S.y*s,r[i+2]=S.z*s}},w.unpackInterpolationResult=function(e,t,r,o,i){a.t(i)||(i=new w),n.a.fromArray(e,0,g);var s=n.a.magnitude(g);return w.unpack(t,4*o,P),0===s?w.clone(w.IDENTITY,D):w.fromAxisAngle(g,s,D),w.multiply(D,P,i)},w.clone=function(e,t){if(a.t(e))return a.t(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new w(e.x,e.y,e.z,e.w)},w.conjugate=function(e,t){return r.n.typeOf.object("quaternion",e),r.n.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},w.magnitudeSquared=function(e){return r.n.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},w.magnitude=function(e){return Math.sqrt(w.magnitudeSquared(e))},w.normalize=function(e,t){r.n.typeOf.object("result",t);var n=1/w.magnitude(e),a=e.x*n,o=e.y*n,i=e.z*n,s=e.w*n;return t.x=a,t.y=o,t.z=i,t.w=s,t},w.inverse=function(e,t){r.n.typeOf.object("result",t);var n=w.magnitudeSquared(e);return t=w.conjugate(e,t),w.multiplyByScalar(t,1/n,t)},w.add=function(e,t,n){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},w.subtract=function(e,t,n){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},w.negate=function(e,t){return r.n.typeOf.object("quaternion",e),r.n.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},w.dot=function(e,t){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},w.multiply=function(e,t,n){r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",n);var a=e.x,o=e.y,i=e.z,s=e.w,u=t.x,l=t.y,c=t.z,f=t.w,d=s*u+a*f+o*c-i*l,p=s*l-a*c+o*f+i*u,y=s*c+a*l-o*u+i*f,h=s*f-a*u-o*l-i*c;return n.x=d,n.y=p,n.z=y,n.w=h,n},w.multiplyByVec=function(e,t,r){var a=new n.a,o=new n.a,i=new n.a(e.x,e.y,e.z);a=n.a.cross(i,t,a),o=n.a.cross(i,a,o);var s=new n.a;s=n.a.multiplyByScalar(a,2*e.w,s);var u=new n.a;return u=n.a.multiplyByScalar(a,2,u),r=n.a.add(t,s,r),r=n.a.add(r,u,r)},w.multiplyByScalar=function(e,t,n){return r.n.typeOf.object("quaternion",e),r.n.typeOf.number("scalar",t),r.n.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},w.divideByScalar=function(e,t,n){return r.n.typeOf.object("quaternion",e),r.n.typeOf.number("scalar",t),r.n.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},w.computeAxis=function(e,t){r.n.typeOf.object("quaternion",e),r.n.typeOf.object("result",t);var n=e.w;if(Math.abs(n-1)<s.n.EPSILON6)return t.x=t.y=t.z=0,t;var a=1/Math.sqrt(1-n*n);return t.x=e.x*a,t.y=e.y*a,t.z=e.z*a,t},w.computeAngle=function(e){return r.n.typeOf.object("quaternion",e),Math.abs(e.w-1)<s.n.EPSILON6?0:2*Math.acos(e.w)};var C=new w;w.lerp=function(e,t,n,a){return r.n.typeOf.object("start",e),r.n.typeOf.object("end",t),r.n.typeOf.number("t",n),r.n.typeOf.object("result",a),C=w.multiplyByScalar(t,n,C),a=w.multiplyByScalar(e,1-n,a),w.add(C,a,a)};var I=new w,j=new w,R=new w;w.slerp=function(e,t,n,a){r.n.typeOf.object("start",e),r.n.typeOf.object("end",t),r.n.typeOf.number("t",n),r.n.typeOf.object("result",a);var o=w.dot(e,t),i=t;if(o<0&&(o=-o,i=I=w.negate(t,I)),1-o<s.n.EPSILON6)return w.lerp(e,i,n,a);var u=Math.acos(o);return j=w.multiplyByScalar(e,Math.sin((1-n)*u),j),R=w.multiplyByScalar(i,Math.sin(n*u),R),a=w.add(j,R,a),w.multiplyByScalar(a,1/Math.sin(u),a)},w.log=function(e,t){r.n.typeOf.object("quaternion",e),r.n.typeOf.object("result",t);var a=s.n.acosClamped(e.w),o=0;return 0!==a&&(o=a/Math.sin(a)),n.a.multiplyByScalar(e,o,t)},w.exp=function(e,t){r.n.typeOf.object("cartesian",e),r.n.typeOf.object("result",t);var a=n.a.magnitude(e),o=0;return 0!==a&&(o=Math.sin(a)/a),t.x=e.x*o,t.y=e.y*o,t.z=e.z*o,t.w=Math.cos(a),t};var M=new n.a,A=new n.a,q=new w,U=new w;w.computeInnerQuadrangle=function(e,t,a,o){r.n.typeOf.object("q0",e),r.n.typeOf.object("q1",t),r.n.typeOf.object("q2",a),r.n.typeOf.object("result",o);var i=w.conjugate(t,q);w.multiply(i,a,U);var s=w.log(U,M);w.multiply(i,e,U);var u=w.log(U,A);return n.a.add(s,u,s),n.a.multiplyByScalar(s,.25,s),n.a.negate(s,s),w.exp(s,q),w.multiply(t,q,o)},w.squad=function(e,t,n,a,o,i){r.n.typeOf.object("q0",e),r.n.typeOf.object("q1",t),r.n.typeOf.object("s0",n),r.n.typeOf.object("s1",a),r.n.typeOf.number("t",o),r.n.typeOf.object("result",i);var s=w.slerp(e,t,o,q),u=w.slerp(n,a,o,U);return w.slerp(s,u,2*o*(1-o),i)};for(var z=new w,F=1.9011074535173003,W=i.o.supportsTypedArrays()?new Float32Array(8):[],L=i.o.supportsTypedArrays()?new Float32Array(8):[],k=i.o.supportsTypedArrays()?new Float32Array(8):[],Y=i.o.supportsTypedArrays()?new Float32Array(8):[],B=0;B<7;++B){var G=B+1,Z=2*G+1;W[B]=1/(G*Z),L[B]=G/Z}function V(e,t,n){r.n.defined("array",e),r.n.defined("itemToFind",t),r.n.defined("comparator",n);for(var a,o,i=0,s=e.length-1;i<=s;)if((o=n(e[a=~~((i+s)/2)],t))<0)i=a+1;else{if(!(o>0))return a;s=a-1}return~(s+1)}function J(e,t,n,r,a){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=r,this.ut1MinusUtc=a
/**
	@license
	sprintf.js from the php.js project - https://github.com/kvz/phpjs
	Directly from https://github.com/kvz/phpjs/blob/master/functions/strings/sprintf.js

	php.js is copyright 2012 Kevin van Zonneveld.

	Portions copyright Brett Zamir (http://brett-zamir.me), Kevin van Zonneveld
	(http://kevin.vanzonneveld.net), Onno Marsman, Theriault, Michael White
	(http://getsprink.com), Waldo Malqui Silva, Paulo Freitas, Jack, Jonas
	Raoni Soares Silva (http://www.jsfromhell.com), Philip Peterson, Legaev
	Andrey, Ates Goral (http://magnetiq.com), Alex, Ratheous, Martijn Wieringa,
	Rafa? Kukawski (http://blog.kukawski.pl), lmeyrick
	(https://sourceforge.net/projects/bcmath-js/), Nate, Philippe Baumann,
	Enrique Gonzalez, Webtoolkit.info (http://www.webtoolkit.info/), Carlos R.
	L. Rodrigues (http://www.jsfromhell.com), Ash Searle
	(http://hexmen.com/blog/), Jani Hartikainen, travc, Ole Vrijenhoek,
	Erkekjetter, Michael Grier, Rafa? Kukawski (http://kukawski.pl), Johnny
	Mast (http://www.phpvrouwen.nl), T.Wild, d3x,
	http://stackoverflow.com/questions/57803/how-to-convert-decimal-to-hex-in-javascript,
	Rafa? Kukawski (http://blog.kukawski.pl/), stag019, pilus, WebDevHobo
	(http://webdevhobo.blogspot.com/), marrtins, GeekFG
	(http://geekfg.blogspot.com), Andrea Giammarchi
	(http://webreflection.blogspot.com), Arpad Ray (mailto:<EMAIL>),
	gorthaur, Paul Smith, Tim de Koning (http://www.kingsquare.nl), Joris, Oleg
	Eremeev, Steve Hilder, majak, gettimeofday, KELAN, Josh Fraser
	(http://onlineaspect.com/2007/06/08/auto-detect-a-time-zone-with-javascript/),
	Marc Palau, Martin
	(http://www.erlenwiese.de/), Breaking Par Consulting Inc
	(http://www.breakingpar.com/bkp/home.nsf/0/87256B280015193F87256CFB006C45F7),
	Chris, Mirek Slugen, saulius, Alfonso Jimenez
	(http://www.alfonsojimenez.com), Diplom@t (http://difane.com/), felix,
	Mailfaker (http://www.weedem.fr/), Tyler Akins (http://rumkin.com), Caio
	Ariede (http://caioariede.com), Robin, Kankrelune
	(http://www.webfaktory.info/), Karol Kowalski, Imgen Tata
	(http://www.myipdf.com/), mdsjack (http://www.mdsjack.bo.it), Dreamer,
	Felix Geisendoerfer (http://www.debuggable.com/felix), Lars Fischer, AJ,
	David, Aman Gupta, Michael White, Public Domain
	(http://www.json.org/json2.js), Steven Levithan
	(http://blog.stevenlevithan.com), Sakimori, Pellentesque Malesuada,
	Thunder.m, Dj (http://phpjs.org/functions/htmlentities:425#comment_134018),
	Steve Clay, David James, Francois, class_exists, nobbler, T. Wild, Itsacon
	(http://www.itsacon.net/), date, Ole Vrijenhoek (http://www.nervous.nl/),
	Fox, Raphael (Ao RUDLER), Marco, noname, Mateusz "loonquawl" Zalega, Frank
	Forte, Arno, ger, mktime, john (http://www.jd-tech.net), Nick Kolosov
	(http://sammy.ru), marc andreu, Scott Cariss, Douglas Crockford
	(http://javascript.crockford.com), madipta, Slawomir Kaniecki,
	ReverseSyntax, Nathan, Alex Wilson, kenneth, Bayron Guevara, Adam Wallner
	(http://web2.bitbaro.hu/), paulo kuong, jmweb, Lincoln Ramsay, djmix,
	Pyerre, Jon Hohle, Thiago Mata (http://thiagomata.blog.com), lmeyrick
	(https://sourceforge.net/projects/bcmath-js/this.), Linuxworld, duncan,
	Gilbert, Sanjoy Roy, Shingo, sankai, Oskar Larsson H?gfeldt
	(http://oskar-lh.name/), Denny Wardhana, 0m3r, Everlasto, Subhasis Deb,
	josh, jd, Pier Paolo Ramon (http://www.mastersoup.com/), P, merabi, Soren
	Hansen, Eugene Bulkin (http://doubleaw.com/), Der Simon
	(http://innerdom.sourceforge.net/), echo is bad, Ozh, XoraX
	(http://www.xorax.info), EdorFaus, JB, J A R, Marc Jansen, Francesco, LH,
	Stoyan Kyosev (http://www.svest.org/), nord_ua, omid
	(http://phpjs.org/functions/380:380#comment_137122), Brad Touesnard, MeEtc
	(http://yass.meetcweb.com), Peter-Paul Koch
	(http://www.quirksmode.org/js/beat.html), Olivier Louvignes
	(http://mg-crea.com/), T0bsn, Tim Wiel, Bryan Elliott, Jalal Berrami,
	Martin, JT, David Randall, Thomas Beaucourt (http://www.webapp.fr), taith,
	vlado houba, Pierre-Luc Paour, Kristof Coomans (SCK-CEN Belgian Nucleair
	Research Centre), Martin Pool, Kirk Strobeck, Rick Waldron, Brant Messenger
	(http://www.brantmessenger.com/), Devan Penner-Woelk, Saulo Vallory, Wagner
	B. Soares, Artur Tchernychev, Valentina De Rosa, Jason Wong
	(http://carrot.org/), Christoph, Daniel Esteban, strftime, Mick@el, rezna,
	Simon Willison (http://simonwillison.net), Anton Ongson, Gabriel Paderni,
	Marco van Oort, penutbutterjelly, Philipp Lenssen, Bjorn Roesbeke
	(http://www.bjornroesbeke.be/), Bug?, Eric Nagel, Tomasz Wesolowski,
	Evertjan Garretsen, Bobby Drake, Blues (http://tech.bluesmoon.info/), Luke
	Godfrey, Pul, uestla, Alan C, Ulrich, Rafal Kukawski, Yves Sucaet,
	sowberry, Norman "zEh" Fuchs, hitwork, Zahlii, johnrembo, Nick Callen,
	Steven Levithan (stevenlevithan.com), ejsanders, Scott Baker, Brian Tafoya
	(http://www.premasolutions.com/), Philippe Jausions
	(http://pear.php.net/user/jausions), Aidan Lister
	(http://aidanlister.com/), Rob, e-mike, HKM, ChaosNo1, metjay, strcasecmp,
	strcmp, Taras Bogach, jpfle, Alexander Ermolaev
	(http://snippets.dzone.com/user/AlexanderErmolaev), DxGx, kilops, Orlando,
	dptr1988, Le Torbi, James (http://www.james-bell.co.uk/), Pedro Tainha
	(http://www.pedrotainha.com), James, Arnout Kazemier
	(http://www.3rd-Eden.com), Chris McMacken, gabriel paderni, Yannoo,
	FGFEmperor, baris ozdil, Tod Gentille, Greg Frazier, jakes, 3D-GRAF, Allan
	Jensen (http://www.winternet.no), Howard Yeend, Benjamin Lupton, davook,
	daniel airton wermann (http://wermann.com.br), Atli T¨®r, Maximusya, Ryan
	W Tenney (http://ryan.10e.us), Alexander M Beedie, fearphage
	(http://http/my.opera.com/fearphage/), Nathan Sepulveda, Victor, Matteo,
	Billy, stensi, Cord, Manish, T.J. Leahy, Riddler
	(http://www.frontierwebdev.com/), Rafa? Kukawski, FremyCompany, Matt
	Bradley, Tim de Koning, Luis Salazar (http://www.freaky-media.com/), Diogo
	Resende, Rival, Andrej Pavlovic, Garagoth, Le Torbi
	(http://www.letorbi.de/), Dino, Josep Sanz (http://www.ws3.es/), rem,
	Russell Walker (http://www.nbill.co.uk/), Jamie Beck
	(http://www.terabit.ca/), setcookie, Michael, YUI Library:
	http://developer.yahoo.com/yui/docs/YAHOO.util.DateLocale.html, Blues at
	http://hacks.bluesmoon.info/strftime/strftime.js, Ben
	(http://benblume.co.uk/), DtTvB
	(http://dt.in.th/2008-09-16.string-length-in-bytes.html), Andreas, William,
	meo, incidence, Cagri Ekin, Amirouche, Amir Habibi
	(http://www.residence-mixte.com/), Luke Smith (http://lucassmith.name),
	Kheang Hok Chin (http://www.distantia.ca/), Jay Klehr, Lorenzo Pisani,
	Tony, Yen-Wei Liu, Greenseed, mk.keck, Leslie Hoare, dude, booeyOH, Ben
	Bryan

	Licensed under the MIT (MIT-LICENSE.txt) license.

	Permission is hereby granted, free of charge, to any person obtaining a
	copy of this software and associated documentation files (the
	"Software"), to deal in the Software without restriction, including
	without limitation the rights to use, copy, modify, merge, publish,
	distribute, sublicense, and/or sell copies of the Software, and to
	permit persons to whom the Software is furnished to do so, subject to
	the following conditions:

	The above copyright notice and this permission notice shall be included
	in all copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
	OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
	MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
	IN NO EVENT SHALL KEVIN VAN ZONNEVELD BE LIABLE FOR ANY CLAIM, DAMAGES
	OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
	ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
	OTHER DEALINGS IN THE SOFTWARE.
	*/}function X(){var e=/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,t=arguments,n=0,r=t[n++],a=function(e,t,n,r){n||(n=" ");var a=e.length>=t?"":Array(1+t-e.length>>>0).join(n);return r?e+a:a+e},o=function(e,t,n,r,o,i){var s=r-e.length;return s>0&&(e=n||!o?a(e,r,i,n):e.slice(0,t.length)+a("",s,"0",!0)+e.slice(t.length)),e},i=function(e,t,n,r,i,s,u){var l=e>>>0;return e=(n=n&&l&&{2:"0b",8:"0",16:"0x"}[t]||"")+a(l.toString(t),s||0,"0",!1),o(e,n,r,i,u)},s=function(e,t,n,r,a,i){return null!=r&&(e=e.slice(0,r)),o(e,"",t,n,a,i)},u=function(e,r,u,l,c,f,d){var p,y,h,m,w;if("%%"==e)return"%";for(var O=!1,b="",x=!1,E=!1,_=" ",v=u.length,T=0;u&&T<v;T++)switch(u.charAt(T)){case" ":b=" ";break;case"+":b="+";break;case"-":O=!0;break;case"'":_=u.charAt(T+1);break;case"0":x=!0;break;case"#":E=!0}if((l=l?"*"==l?+t[n++]:"*"==l.charAt(0)?+t[l.slice(1,-1)]:+l:0)<0&&(l=-l,O=!0),!isFinite(l))throw new Error("sprintf: (minimum-)width must be finite");switch(f=f?"*"==f?+t[n++]:"*"==f.charAt(0)?+t[f.slice(1,-1)]:+f:"fFeE".indexOf(d)>-1?6:"d"==d?0:void 0,w=r?t[r.slice(0,-1)]:t[n++],d){case"s":return s(String(w),O,l,f,x,_);case"c":return s(String.fromCharCode(+w),O,l,f,x);case"b":return i(w,2,E,O,l,f,x);case"o":return i(w,8,E,O,l,f,x);case"x":return i(w,16,E,O,l,f,x);case"X":return i(w,16,E,O,l,f,x).toUpperCase();case"u":return i(w,10,E,O,l,f,x);case"i":case"d":return p=+w||0,w=(y=(p=Math.round(p-p%1))<0?"-":b)+a(String(Math.abs(p)),f,"0",!1),o(w,y,O,l,x);case"e":case"E":case"f":case"F":case"g":case"G":return y=(p=+w)<0?"-":b,h=["toExponential","toFixed","toPrecision"]["efg".indexOf(d.toLowerCase())],m=["toString","toUpperCase"]["eEfFgG".indexOf(d)%2],w=y+Math.abs(p)[h](f),o(w,y,O,l,x)[m]();default:return e}};return r.replace(e,u)}function H(e,t,n,r,a,o,i,s){this.year=e,this.month=t,this.day=n,this.hour=r,this.minute=a,this.second=o,this.millisecond=i,this.isLeapSecond=s}function $(e){if(null===e||isNaN(e))throw new r.t("year is required and must be a number.");return e%4==0&&e%100!=0||e%400==0}function Q(e,t){this.julianDate=e,this.offset=t}W[7]=F/136,L[7]=8*F/17,w.fastSlerp=function(e,t,n,a){r.n.typeOf.object("start",e),r.n.typeOf.object("end",t),r.n.typeOf.number("t",n),r.n.typeOf.object("result",a);var o,i=w.dot(e,t);i>=0?o=1:(o=-1,i=-i);for(var s=i-1,u=1-n,l=n*n,c=u*u,f=7;f>=0;--f)k[f]=(W[f]*l-L[f])*s,Y[f]=(W[f]*c-L[f])*s;var d=o*n*(1+k[0]*(1+k[1]*(1+k[2]*(1+k[3]*(1+k[4]*(1+k[5]*(1+k[6]*(1+k[7])))))))),p=u*(1+Y[0]*(1+Y[1]*(1+Y[2]*(1+Y[3]*(1+Y[4]*(1+Y[5]*(1+Y[6]*(1+Y[7])))))))),y=w.multiplyByScalar(e,p,z);return w.multiplyByScalar(t,d,a),w.add(y,a,a)},w.fastSquad=function(e,t,n,a,o,i){r.n.typeOf.object("q0",e),r.n.typeOf.object("q1",t),r.n.typeOf.object("s0",n),r.n.typeOf.object("s1",a),r.n.typeOf.number("t",o),r.n.typeOf.object("result",i);var s=w.fastSlerp(e,t,o,q),u=w.fastSlerp(n,a,o,U);return w.fastSlerp(s,u,2*o*(1-o),i)},w.equals=function(e,t){return e===t||a.t(e)&&a.t(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},w.equalsEpsilon=function(e,t,n){return r.n.typeOf.number("epsilon",n),e===t||a.t(e)&&a.t(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},w.ZERO=Object.freeze(new w(0,0,0,0)),w.IDENTITY=Object.freeze(new w(0,0,0,1)),w.prototype.clone=function(e){return w.clone(this,e)},w.prototype.equals=function(e){return w.equals(this,e)},w.prototype.equalsEpsilon=function(e,t){return w.equalsEpsilon(this,e,t)},w.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var K=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5}),ee=Object.freeze({UTC:0,TAI:1}),te=new H,ne=[31,28,31,30,31,30,31,31,30,31,30,31];function re(e,t){return be.compare(e.julianDate,t.julianDate)}var ae=new Q;function oe(e){ae.julianDate=e;var t=be.leapSeconds,n=V(t,ae,re);n<0&&(n=~n),n>=t.length&&(n=t.length-1);var r=t[n].offset;n>0&&(be.secondsDifference(t[n].julianDate,e)>r&&(r=t[--n].offset));be.addSeconds(e,r,e)}function ie(e,t){ae.julianDate=e;var n=be.leapSeconds,r=V(n,ae,re);if(r<0&&(r=~r),0===r)return be.addSeconds(e,-n[0].offset,t);if(r>=n.length)return be.addSeconds(e,-n[r-1].offset,t);var a=be.secondsDifference(n[r].julianDate,e);return 0===a?be.addSeconds(e,-n[r].offset,t):a<=1?void 0:be.addSeconds(e,-n[--r].offset,t)}function se(e,t,n){var r=t/K.SECONDS_PER_DAY|0;return e+=r,(t-=K.SECONDS_PER_DAY*r)<0&&(e--,t+=K.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function ue(e,t,n,r,a,o,i){var s=(t-14)/12|0,u=e+4800+s,l=(1461*u/4|0)+(367*(t-2-12*s)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(r-=12)<0&&(r+=24);var c=o+(r*K.SECONDS_PER_HOUR+a*K.SECONDS_PER_MINUTE+i*K.SECONDS_PER_MILLISECOND);return c>=43200&&(l-=1),[l,c]}var le=/^(\d{4})$/,ce=/^(\d{4})-(\d{2})$/,fe=/^(\d{4})-?(\d{3})$/,de=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,pe=/^(\d{4})-?(\d{2})-?(\d{2})$/,ye=/([Z+\-])?(\d{2})?:?(\d{2})?$/,he=/^(\d{2})(\.\d+)?/.source+ye.source,me=/^(\d{2}):?(\d{2})(\.\d+)?/.source+ye.source,we=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+ye.source,Oe="Invalid ISO 8601 date.";function be(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=a.e(e,0),t=a.e(t,0),n=a.e(n,ee.UTC);var r=0|e;se(r,t+=(e-r)*K.SECONDS_PER_DAY,this),n===ee.UTC&&oe(this)}be.fromGregorianDate=function(e,t){if(!(e instanceof H))throw new r.t("date must be a valid GregorianDate.");var n=ue(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return a.t(t)?(se(n[0],n[1],t),oe(t),t):new be(n[0],n[1],ee.UTC)},be.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new r.t("date must be a valid JavaScript Date.");var n=ue(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return a.t(t)?(se(n[0],n[1],t),oe(t),t):new be(n[0],n[1],ee.UTC)},be.fromIso8601=function(e,t){if("string"!=typeof e)throw new r.t(Oe);var n,o,i,s,u,l=(e=e.replace(",",".")).split("T"),c=1,f=1,d=0,p=0,y=0,h=0,m=l[0],w=l[1];if(!a.t(m))throw new r.t(Oe);if(null!==(l=m.match(pe))){if((s=m.split("-").length-1)>0&&2!==s)throw new r.t(Oe);n=+l[1],c=+l[2],f=+l[3]}else if(null!==(l=m.match(ce)))n=+l[1],c=+l[2];else if(null!==(l=m.match(le)))n=+l[1];else{var O;if(null!==(l=m.match(fe))){if(n=+l[1],O=+l[2],i=$(n),O<1||i&&O>366||!i&&O>365)throw new r.t(Oe)}else{if(null===(l=m.match(de)))throw new r.t(Oe);n=+l[1];var b=+l[2],x=+l[3]||0;if((s=m.split("-").length-1)>0&&(!a.t(l[3])&&1!==s||a.t(l[3])&&2!==s))throw new r.t(Oe);O=7*b+x-new Date(Date.UTC(n,0,4)).getUTCDay()-3}(o=new Date(Date.UTC(n,0,1))).setUTCDate(O),c=o.getUTCMonth()+1,f=o.getUTCDate()}if(i=$(n),c<1||c>12||f<1||(2!==c||!i)&&f>ne[c-1]||i&&2===c&&f>29)throw new r.t(Oe);if(a.t(w)){if(null!==(l=w.match(we))){if((s=w.split(":").length-1)>0&&2!==s&&3!==s)throw new r.t(Oe);d=+l[1],p=+l[2],y=+l[3],h=1e3*+(l[4]||0),u=5}else if(null!==(l=w.match(me))){if((s=w.split(":").length-1)>2)throw new r.t(Oe);d=+l[1],p=+l[2],y=60*+(l[3]||0),u=4}else{if(null===(l=w.match(he)))throw new r.t(Oe);d=+l[1],p=60*+(l[2]||0),u=3}if(p>=60||y>=61||d>24||24===d&&(p>0||y>0||h>0))throw new r.t(Oe);var E=l[u],_=+l[u+1],v=+(l[u+2]||0);switch(E){case"+":d-=_,p-=v;break;case"-":d+=_,p+=v;break;case"Z":break;default:p+=new Date(Date.UTC(n,c-1,f,d,p)).getTimezoneOffset()}}var T=60===y;for(T&&y--;p>=60;)p-=60,d++;for(;d>=24;)d-=24,f++;for(o=i&&2===c?29:ne[c-1];f>o;)f-=o,++c>12&&(c-=12,n++),o=i&&2===c?29:ne[c-1];for(;p<0;)p+=60,d--;for(;d<0;)d+=24,f--;for(;f<1;)--c<1&&(c+=12,n--),f+=o=i&&2===c?29:ne[c-1];var S=ue(n,c,f,d,p,y,h);return a.t(t)?(se(S[0],S[1],t),oe(t)):t=new be(S[0],S[1],ee.UTC),T&&be.addSeconds(t,1,t),t},be.now=function(e){return be.fromDate(new Date,e)};var xe=new be(0,0,ee.TAI);function Ee(e){if(e=a.e(e,a.e.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=a.e(e.addNewLeapSeconds,!0),a.t(e.data))ve(this,e.data);else if(a.t(e.url)){var t=c.t.createIfNeeded(e.url),n=this;this._downloadPromise=a.c(t.fetchJson(),(function(e){ve(n,e)}),(function(){n._dataError="An error occurred while retrieving the EOP data from the URL "+t.url+"."}))}else ve(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function _e(e,t){return be.compare(e.julianDate,t)}function ve(e,t){if(a.t(t.columnNames))if(a.t(t.samples)){var n=t.columnNames.indexOf("modifiedJulianDateUtc"),r=t.columnNames.indexOf("xPoleWanderRadians"),o=t.columnNames.indexOf("yPoleWanderRadians"),i=t.columnNames.indexOf("ut1MinusUtcSeconds"),s=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||r<0||o<0||i<0||s<0||u<0||l<0)e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns";else{var c=e._samples=t.samples,f=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=r,e._yPoleWanderRadiansColumn=o,e._ut1MinusUtcSecondsColumn=i,e._xCelestialPoleOffsetRadiansColumn=s,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=l,e._columnCount=t.columnNames.length,e._lastIndex=void 0;for(var d,p=e._addNewLeapSeconds,y=0,h=c.length;y<h;y+=e._columnCount){var m=c[y+n],w=c[y+l],O=new be(m+K.MODIFIED_JULIAN_DATE_DIFFERENCE,w,ee.TAI);if(f.push(O),p){if(w!==d&&a.t(d)){var b=be.leapSeconds,x=V(b,O,_e);if(x<0){var E=new Q(O,w);b.splice(~x,0,E)}}d=w}}}}else e._dataError="Error in loaded EOP data: The samples property is required.";else e._dataError="Error in loaded EOP data: The columnNames property is required."}function Te(e,t,n,r,a){var o=n*r;a.xPoleWander=t[o+e._xPoleWanderRadiansColumn],a.yPoleWander=t[o+e._yPoleWanderRadiansColumn],a.xPoleOffset=t[o+e._xCelestialPoleOffsetRadiansColumn],a.yPoleOffset=t[o+e._yCelestialPoleOffsetRadiansColumn],a.ut1MinusUtc=t[o+e._ut1MinusUtcSecondsColumn]}function Se(e,t,n){return t+e*(n-t)}function ge(e,t,n,r,a,o,i){var s=e._columnCount;if(o>t.length-1)return i.xPoleWander=0,i.yPoleWander=0,i.xPoleOffset=0,i.yPoleOffset=0,i.ut1MinusUtc=0,i;var u=t[a],l=t[o];if(u.equals(l)||r.equals(u))return Te(e,n,a,s,i),i;if(r.equals(l))return Te(e,n,o,s,i),i;var c=be.secondsDifference(r,u)/be.secondsDifference(l,u),f=a*s,d=o*s,p=n[f+e._ut1MinusUtcSecondsColumn],y=n[d+e._ut1MinusUtcSecondsColumn],h=y-p;if(h>.5||h<-.5){var m=n[f+e._taiMinusUtcSecondsColumn],w=n[d+e._taiMinusUtcSecondsColumn];m!==w&&(l.equals(r)?p=y:y-=w-m)}return i.xPoleWander=Se(c,n[f+e._xPoleWanderRadiansColumn],n[d+e._xPoleWanderRadiansColumn]),i.yPoleWander=Se(c,n[f+e._yPoleWanderRadiansColumn],n[d+e._yPoleWanderRadiansColumn]),i.xPoleOffset=Se(c,n[f+e._xCelestialPoleOffsetRadiansColumn],n[d+e._xCelestialPoleOffsetRadiansColumn]),i.yPoleOffset=Se(c,n[f+e._yCelestialPoleOffsetRadiansColumn],n[d+e._yCelestialPoleOffsetRadiansColumn]),i.ut1MinusUtc=Se(c,p,y),i}function De(e,t,n){this.heading=a.e(e,0),this.pitch=a.e(t,0),this.roll=a.e(n,0)}function Pe(e,t,n){this.x=e,this.y=t,this.s=n}function Ne(e){e=a.e(e,a.e.EMPTY_OBJECT),this._xysFileUrlTemplate=c.t.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=a.e(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=a.e(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new be(this._sampleZeroJulianEphemerisDate,0,ee.TAI),this._stepSizeDays=a.e(e.stepSizeDays,1),this._samplesPerXysFile=a.e(e.samplesPerXysFile,1e3),this._totalSamples=a.e(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];for(var t=this._interpolationOrder,n=this._denominators=new Array(t+1),r=this._xTable=new Array(t+1),o=Math.pow(this._stepSizeDays,t),i=0;i<=t;++i){n[i]=o,r[i]=i*this._stepSizeDays;for(var s=0;s<=t;++s)s!==i&&(n[i]*=i-s);n[i]=1/n[i]}this._work=new Array(t+1),this._coef=new Array(t+1)}be.toGregorianDate=function(e,t){if(!a.t(e))throw new r.t("julianDate is required.");var n=!1,o=ie(e,xe);a.t(o)||(be.addSeconds(e,-1,xe),o=ie(xe,xe),n=!0);var i=o.dayNumber,s=o.secondsOfDay;s>=43200&&(i+=1);var u=i+68569|0,l=4*u/146097|0,c=4e3*((u=u-((146097*l+3)/4|0)|0)+1)/1461001|0,f=80*(u=u-(1461*c/4|0)+31|0)/2447|0,d=u-(2447*f/80|0)|0,p=f+2-12*(u=f/11|0)|0,y=100*(l-49)+c+u|0,h=s/K.SECONDS_PER_HOUR|0,m=s-h*K.SECONDS_PER_HOUR,w=m/K.SECONDS_PER_MINUTE|0,O=0|(m-=w*K.SECONDS_PER_MINUTE),b=(m-O)/K.SECONDS_PER_MILLISECOND;return(h+=12)>23&&(h-=24),n&&(O+=1),a.t(t)?(t.year=y,t.month=p,t.day=d,t.hour=h,t.minute=w,t.second=O,t.millisecond=b,t.isLeapSecond=n,t):new H(y,p,d,h,w,O,b,n)},be.toDate=function(e){if(!a.t(e))throw new r.t("julianDate is required.");var t=be.toGregorianDate(e,te),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},be.toIso8601=function(e,t){if(!a.t(e))throw new r.t("julianDate is required.");var n=be.toGregorianDate(e,te),o=n.year,i=n.month,s=n.day,u=n.hour,l=n.minute,c=n.second,f=n.millisecond;return 1e4===o&&1===i&&1===s&&0===u&&0===l&&0===c&&0===f&&(o=9999,i=12,s=31,u=24),a.t(t)||0===f?a.t(t)&&0!==t?X("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",o,i,s,u,l,c,(.01*f).toFixed(t).replace(".","").slice(0,t)):X("%04d-%02d-%02dT%02d:%02d:%02dZ",o,i,s,u,l,c):X("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",o,i,s,u,l,c,(.01*f).toString().replace(".",""))},be.clone=function(e,t){if(a.t(e))return a.t(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new be(e.dayNumber,e.secondsOfDay,ee.TAI)},be.compare=function(e,t){if(!a.t(e))throw new r.t("left is required.");if(!a.t(t))throw new r.t("right is required.");var n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},be.equals=function(e,t){return e===t||a.t(e)&&a.t(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},be.equalsEpsilon=function(e,t,n){if(!a.t(n))throw new r.t("epsilon is required.");return e===t||a.t(e)&&a.t(t)&&Math.abs(be.secondsDifference(e,t))<=n},be.totalDays=function(e){if(!a.t(e))throw new r.t("julianDate is required.");return e.dayNumber+e.secondsOfDay/K.SECONDS_PER_DAY},be.secondsDifference=function(e,t){if(!a.t(e))throw new r.t("left is required.");if(!a.t(t))throw new r.t("right is required.");return(e.dayNumber-t.dayNumber)*K.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},be.daysDifference=function(e,t){if(!a.t(e))throw new r.t("left is required.");if(!a.t(t))throw new r.t("right is required.");return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/K.SECONDS_PER_DAY},be.computeTaiMinusUtc=function(e){ae.julianDate=e;var t=be.leapSeconds,n=V(t,ae,re);return n<0&&(n=~n,--n<0&&(n=0)),t[n].offset},be.addSeconds=function(e,t,n){if(!a.t(e))throw new r.t("julianDate is required.");if(!a.t(t))throw new r.t("seconds is required.");if(!a.t(n))throw new r.t("result is required.");return se(e.dayNumber,e.secondsOfDay+t,n)},be.addMinutes=function(e,t,n){if(!a.t(e))throw new r.t("julianDate is required.");if(!a.t(t))throw new r.t("minutes is required.");if(!a.t(n))throw new r.t("result is required.");var o=e.secondsOfDay+t*K.SECONDS_PER_MINUTE;return se(e.dayNumber,o,n)},be.addHours=function(e,t,n){if(!a.t(e))throw new r.t("julianDate is required.");if(!a.t(t))throw new r.t("hours is required.");if(!a.t(n))throw new r.t("result is required.");var o=e.secondsOfDay+t*K.SECONDS_PER_HOUR;return se(e.dayNumber,o,n)},be.addDays=function(e,t,n){if(!a.t(e))throw new r.t("julianDate is required.");if(!a.t(t))throw new r.t("days is required.");if(!a.t(n))throw new r.t("result is required.");return se(e.dayNumber+t,e.secondsOfDay,n)},be.lessThan=function(e,t){return be.compare(e,t)<0},be.lessThanOrEquals=function(e,t){return be.compare(e,t)<=0},be.greaterThan=function(e,t){return be.compare(e,t)>0},be.greaterThanOrEquals=function(e,t){return be.compare(e,t)>=0},be.prototype.clone=function(e){return be.clone(this,e)},be.prototype.equals=function(e){return be.equals(this,e)},be.prototype.equalsEpsilon=function(e,t){return be.equalsEpsilon(this,e,t)},be.prototype.toString=function(){return be.toIso8601(this)},be.leapSeconds=[new Q(new be(2441317,43210,ee.TAI),10),new Q(new be(2441499,43211,ee.TAI),11),new Q(new be(2441683,43212,ee.TAI),12),new Q(new be(2442048,43213,ee.TAI),13),new Q(new be(2442413,43214,ee.TAI),14),new Q(new be(2442778,43215,ee.TAI),15),new Q(new be(2443144,43216,ee.TAI),16),new Q(new be(2443509,43217,ee.TAI),17),new Q(new be(2443874,43218,ee.TAI),18),new Q(new be(2444239,43219,ee.TAI),19),new Q(new be(2444786,43220,ee.TAI),20),new Q(new be(2445151,43221,ee.TAI),21),new Q(new be(2445516,43222,ee.TAI),22),new Q(new be(2446247,43223,ee.TAI),23),new Q(new be(2447161,43224,ee.TAI),24),new Q(new be(2447892,43225,ee.TAI),25),new Q(new be(2448257,43226,ee.TAI),26),new Q(new be(2448804,43227,ee.TAI),27),new Q(new be(2449169,43228,ee.TAI),28),new Q(new be(2449534,43229,ee.TAI),29),new Q(new be(2450083,43230,ee.TAI),30),new Q(new be(2450630,43231,ee.TAI),31),new Q(new be(2451179,43232,ee.TAI),32),new Q(new be(2453736,43233,ee.TAI),33),new Q(new be(2454832,43234,ee.TAI),34),new Q(new be(2456109,43235,ee.TAI),35),new Q(new be(2457204,43236,ee.TAI),36),new Q(new be(2457754,43237,ee.TAI),37)],Ee.NONE=Object.freeze({getPromiseToLoad:function(){return a.c()},compute:function(e,t){return a.t(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new J(0,0,0,0,0),t}}),Ee.prototype.getPromiseToLoad=function(){return a.c(this._downloadPromise)},Ee.prototype.compute=function(e,t){if(a.t(this._samples)){if(a.t(t)||(t=new J(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;var n=this._dates,r=this._lastIndex,o=0,i=0;if(a.t(r)){var s=n[r],u=n[r+1],l=be.lessThanOrEquals(s,e),c=!a.t(u),d=c||be.greaterThanOrEquals(u,e);if(l&&d)return o=r,!c&&u.equals(e)&&++o,i=o+1,ge(this,n,this._samples,e,o,i,t),t}var p=V(n,e,be.compare,this._dateColumn);return p>=0?(p<n.length-1&&n[p+1].equals(e)&&++p,o=p,i=p):(o=(i=~p)-1)<0&&(o=0),this._lastIndex=o,ge(this,n,this._samples,e,o,i,t),t}if(a.t(this._dataError))throw new f.t(this._dataError)},De.fromQuaternion=function(e,t){if(!a.t(e))throw new r.t("quaternion is required");a.t(t)||(t=new De);var n=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),u=1-2*(e.y*e.y+e.z*e.z),l=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(l,u),t.roll=Math.atan2(i,o),t.pitch=-s.n.asinClamped(n),t},De.fromDegrees=function(e,t,n,o){if(!a.t(e))throw new r.t("heading is required");if(!a.t(t))throw new r.t("pitch is required");if(!a.t(n))throw new r.t("roll is required");return a.t(o)||(o=new De),o.heading=e*s.n.RADIANS_PER_DEGREE,o.pitch=t*s.n.RADIANS_PER_DEGREE,o.roll=n*s.n.RADIANS_PER_DEGREE,o},De.clone=function(e,t){if(a.t(e))return a.t(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new De(e.heading,e.pitch,e.roll)},De.equals=function(e,t){return e===t||a.t(e)&&a.t(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},De.equalsEpsilon=function(e,t,n,r){return e===t||a.t(e)&&a.t(t)&&s.n.equalsEpsilon(e.heading,t.heading,n,r)&&s.n.equalsEpsilon(e.pitch,t.pitch,n,r)&&s.n.equalsEpsilon(e.roll,t.roll,n,r)},De.prototype.clone=function(e){return De.clone(this,e)},De.prototype.equals=function(e){return De.equals(this,e)},De.prototype.equalsEpsilon=function(e,t,n){return De.equalsEpsilon(this,e,t,n)},De.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var Ce=new be(0,0,ee.TAI);function Ie(e,t,n){var r=Ce;return r.dayNumber=t,r.secondsOfDay=n,be.daysDifference(r,e._sampleZeroDateTT)}function je(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];var n=a.c.defer();e._chunkDownloadsInProgress[t]=n;var r,o=e._xysFileUrlTemplate;return r=a.t(o)?o.getDerivedResource({templateValues:{0:t}}):new c.t({url:c.o("Assets/IAU2006_XYS/IAU2006_XYS_"+t+".json")}),a.c(r.fetchJson(),(function(r){e._chunkDownloadsInProgress[t]=!1;for(var a=e._samples,o=r.samples,i=t*e._samplesPerXysFile*3,s=0,u=o.length;s<u;++s)a[i+s]=o[s];n.resolve()})),n.promise}Ne.prototype.preload=function(e,t,n,r){var o=Ie(this,e,t),i=Ie(this,n,r),s=o/this._stepSizeDays-this._interpolationOrder/2|0;s<0&&(s=0);var u=i/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);for(var l=s/this._samplesPerXysFile|0,c=u/this._samplesPerXysFile|0,f=[],d=l;d<=c;++d)f.push(je(this,d));return a.c.all(f)},Ne.prototype.computeXysRadians=function(e,t,n){var r=Ie(this,e,t);if(!(r<0)){var o=r/this._stepSizeDays|0;if(!(o>=this._totalSamples)){var i=this._interpolationOrder,s=o-(i/2|0);s<0&&(s=0);var u=s+i;u>=this._totalSamples&&((s=(u=this._totalSamples-1)-i)<0&&(s=0));var l=!1,c=this._samples;if(a.t(c[3*s])||(je(this,s/this._samplesPerXysFile|0),l=!0),a.t(c[3*u])||(je(this,u/this._samplesPerXysFile|0),l=!0),!l){a.t(n)?(n.x=0,n.y=0,n.s=0):n=new Pe(0,0,0);var f,d,p=r-s*this._stepSizeDays,y=this._work,h=this._denominators,m=this._coef,w=this._xTable;for(f=0;f<=i;++f)y[f]=p-w[f];for(f=0;f<=i;++f){for(m[f]=1,d=0;d<=i;++d)d!==f&&(m[f]*=y[d]);m[f]*=h[f];var O=3*(s+f);n.x+=m[f]*c[O++],n.y+=m[f]*c[O++],n.s+=m[f]*c[O]}return n}}}};var Re={},Me={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Ae={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},qe={},Ue={east:new n.a,north:new n.a,up:new n.a,west:new n.a,south:new n.a,down:new n.a},ze=new n.a,Fe=new n.a,We=new n.a;Re.localFrameToFixedFrameGenerator=function(e,t){if(!Me.hasOwnProperty(e)||!Me[e].hasOwnProperty(t))throw new r.t("firstAxis and secondAxis must be east, north, up, west, south or down.");var i,l=Me[e][t],c=e+t;return a.t(qe[c])?i=qe[c]:(i=function(i,c,f){if(!a.t(i))throw new r.t("origin is required.");if(a.t(f)||(f=new o.c),n.a.equalsEpsilon(i,n.a.ZERO,s.n.EPSILON14))n.a.unpack(Ae[e],0,ze),n.a.unpack(Ae[t],0,Fe),n.a.unpack(Ae[l],0,We);else if(s.n.equalsEpsilon(i.x,0,s.n.EPSILON14)&&s.n.equalsEpsilon(i.y,0,s.n.EPSILON14)){var d=s.n.sign(i.z);n.a.unpack(Ae[e],0,ze),"east"!==e&&"west"!==e&&n.a.multiplyByScalar(ze,d,ze),n.a.unpack(Ae[t],0,Fe),"east"!==t&&"west"!==t&&n.a.multiplyByScalar(Fe,d,Fe),n.a.unpack(Ae[l],0,We),"east"!==l&&"west"!==l&&n.a.multiplyByScalar(We,d,We)}else{(c=a.e(c,u.n.WGS84)).geodeticSurfaceNormal(i,Ue.up);var p=Ue.up,y=Ue.east;y.x=-i.y,y.y=i.x,y.z=0,n.a.normalize(y,Ue.east),n.a.cross(p,y,Ue.north),n.a.multiplyByScalar(Ue.up,-1,Ue.down),n.a.multiplyByScalar(Ue.east,-1,Ue.west),n.a.multiplyByScalar(Ue.north,-1,Ue.south),ze=Ue[e],Fe=Ue[t],We=Ue[l]}return f[0]=ze.x,f[1]=ze.y,f[2]=ze.z,f[3]=0,f[4]=Fe.x,f[5]=Fe.y,f[6]=Fe.z,f[7]=0,f[8]=We.x,f[9]=We.y,f[10]=We.z,f[11]=0,f[12]=i.x,f[13]=i.y,f[14]=i.z,f[15]=1,f},qe[c]=i),i},Re.eastNorthUpToFixedFrame=Re.localFrameToFixedFrameGenerator("east","north"),Re.northEastDownToFixedFrame=Re.localFrameToFixedFrameGenerator("north","east"),Re.northUpEastToFixedFrame=Re.localFrameToFixedFrameGenerator("north","up"),Re.northWestUpToFixedFrame=Re.localFrameToFixedFrameGenerator("north","west");var Le=new w,ke=new n.a(1,1,1),Ye=new o.c;Re.headingPitchRollToFixedFrame=function(e,t,i,s,u){r.n.typeOf.object("HeadingPitchRoll",t),s=a.e(s,Re.eastNorthUpToFixedFrame);var l=w.fromHeadingPitchRoll(t,Le),c=o.c.fromTranslationQuaternionRotationScale(n.a.ZERO,l,ke,Ye);return u=s(e,i,u),o.c.multiply(u,c,u)};var Be=new o.c,Ge=new o.r;Re.headingPitchRollQuaternion=function(e,t,n,a,i){r.n.typeOf.object("HeadingPitchRoll",t);var s=Re.headingPitchRollToFixedFrame(e,t,n,a,Be),u=o.c.getMatrix3(s,Ge);return w.fromRotationMatrix(u,i)};var Ze=new n.a(1,1,1),Ve=new n.a,Je=new o.c,Xe=new o.c,He=new o.r,$e=new w;Re.fixedFrameToHeadingPitchRoll=function(e,t,i,s){r.n.defined("transform",e),t=a.e(t,u.n.WGS84),i=a.e(i,Re.eastNorthUpToFixedFrame),a.t(s)||(s=new De);var l=o.c.getTranslation(e,Ve);if(n.a.equals(l,n.a.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;var c=o.c.inverseTransformation(i(l,t,Je),Je),f=o.c.setScale(e,Ze,Xe);f=o.c.setTranslation(f,n.a.ZERO,f),c=o.c.multiply(c,f,c);var d=w.fromRotationMatrix(o.c.getMatrix3(c,He),$e);return d=w.normalize(d,d),De.fromQuaternion(d,s)};var Qe=s.n.TWO_PI/86400,Ke=new be;Re.computeTemeToPseudoFixedMatrix=function(e,t){if(!a.t(e))throw new r.t("date is required.");var n,i=(Ke=be.addSeconds(e,-be.computeTaiMinusUtc(e),Ke)).dayNumber,u=Ke.secondsOfDay,l=i-2451545,c=(24110.54841+(n=u>=43200?(l+.5)/K.DAYS_PER_JULIAN_CENTURY:(l-.5)/K.DAYS_PER_JULIAN_CENTURY)*(8640184.812866+n*(.093104+-62e-7*n)))*Qe%s.n.TWO_PI+(72921158553e-15+11772758384668e-32*(i-2451545.5))*((u+.5*K.SECONDS_PER_DAY)%K.SECONDS_PER_DAY),f=Math.cos(c),d=Math.sin(c);return a.t(t)?(t[0]=f,t[1]=-d,t[2]=0,t[3]=d,t[4]=f,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new o.r(f,d,0,-d,f,0,0,0,1)},Re.iau2006XysData=new Ne,Re.earthOrientationParameters=Ee.NONE;var et=32.184;Re.preloadIcrfFixed=function(e){var t=e.start.dayNumber,n=e.start.secondsOfDay+et,r=e.stop.dayNumber,o=e.stop.secondsOfDay+et,i=Re.iau2006XysData.preload(t,n,r,o),s=Re.earthOrientationParameters.getPromiseToLoad();return a.c.all([i,s])},Re.computeIcrfToFixedMatrix=function(e,t){if(!a.t(e))throw new r.t("date is required.");a.t(t)||(t=new o.r);var n=Re.computeFixedToIcrfMatrix(e,t);if(a.t(n))return o.r.transpose(n,t)};var tt=new Pe(0,0,0),nt=new J(0,0,0,0,0,0),rt=new o.r,at=new o.r;Re.computeFixedToIcrfMatrix=function(e,t){if(!a.t(e))throw new r.t("date is required.");a.t(t)||(t=new o.r);var n=Re.earthOrientationParameters.compute(e,nt);if(a.t(n)){var i=e.dayNumber,u=e.secondsOfDay+et,l=Re.iau2006XysData.computeXysRadians(i,u,tt);if(a.t(l)){var c=l.x+n.xPoleOffset,f=l.y+n.yPoleOffset,d=1/(1+Math.sqrt(1-c*c-f*f)),p=rt;p[0]=1-d*c*c,p[3]=-d*c*f,p[6]=c,p[1]=-d*c*f,p[4]=1-d*f*f,p[7]=f,p[2]=-c,p[5]=-f,p[8]=1-d*(c*c+f*f);var y=o.r.fromRotationZ(-l.s,at),h=o.r.multiply(p,y,rt),m=e.dayNumber-2451545,w=(e.secondsOfDay-be.computeTaiMinusUtc(e)+n.ut1MinusUtc)/K.SECONDS_PER_DAY,O=.779057273264+w+.00273781191135448*(m+w);O=O%1*s.n.TWO_PI;var b=o.r.fromRotationZ(O,at),x=o.r.multiply(h,b,rt),E=Math.cos(n.xPoleWander),_=Math.cos(n.yPoleWander),v=Math.sin(n.xPoleWander),T=Math.sin(n.yPoleWander),S=i-2451545+u/K.SECONDS_PER_DAY,g=-47e-6*(S/=36525)*s.n.RADIANS_PER_DEGREE/3600,D=Math.cos(g),P=Math.sin(g),N=at;return N[0]=E*D,N[1]=E*P,N[2]=v,N[3]=-_*P+T*v*D,N[4]=_*D+T*v*P,N[5]=-T*E,N[6]=-T*P-_*v*D,N[7]=T*D-_*v*P,N[8]=_*E,o.r.multiply(x,N,t)}}};var ot=new l.a;Re.pointToWindowCoordinates=function(e,t,n,r){return(r=Re.pointToGLWindowCoordinates(e,t,n,r)).y=2*t[5]-r.y,r},Re.pointToGLWindowCoordinates=function(e,n,i,s){if(!a.t(e))throw new r.t("modelViewProjectionMatrix is required.");if(!a.t(n))throw new r.t("viewportTransformation is required.");if(!a.t(i))throw new r.t("point is required.");a.t(s)||(s=new t.r);var u=ot;return o.c.multiplyByVector(e,l.a.fromElements(i.x,i.y,i.z,1,u),u),l.a.multiplyByScalar(u,1/u.w,u),o.c.multiplyByVector(n,u,u),t.r.fromCartesian4(u,s)};var it=new n.a,st=new n.a,ut=new n.a;Re.rotationMatrixFromPositionVelocity=function(e,t,i,l){if(!a.t(e))throw new r.t("position is required.");if(!a.t(t))throw new r.t("velocity is required.");var c=a.e(i,u.n.WGS84).geodeticSurfaceNormal(e,it),f=n.a.cross(t,c,st);n.a.equalsEpsilon(f,n.a.ZERO,s.n.EPSILON6)&&(f=n.a.clone(n.a.UNIT_X,f));var d=n.a.cross(f,t,ut);return n.a.normalize(d,d),n.a.cross(t,d,f),n.a.negate(f,f),n.a.normalize(f,f),a.t(l)||(l=new o.r),l[0]=t.x,l[1]=t.y,l[2]=t.z,l[3]=f.x,l[4]=f.y,l[5]=f.z,l[6]=d.x,l[7]=d.y,l[8]=d.z,l};var lt=new o.c(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),ct=new n.i,ft=new n.a,dt=new n.a,pt=new o.r,yt=new o.c,ht=new o.c;function mt(e){e=a.e(e,a.e.EMPTY_OBJECT),r.n.typeOf.object("options.attributes",e.attributes),this.attributes=e.attributes,this.indices=e.indices,this.primitiveType=a.e(e.primitiveType,o._0x38df4a.TRIANGLES),this.boundingSphere=e.boundingSphere,this.geometryType=a.e(e.geometryType,p.NONE),this.boundingSphereCV=e.boundingSphereCV,this.offsetAttribute=e.offsetAttribute}Re.basisTo2D=function(e,t,i){if(!a.t(e))throw new r.t("projection is required.");if(!a.t(t))throw new r.t("matrix is required.");if(!a.t(i))throw new r.t("result is required.");var s=o.c.getTranslation(t,dt),u=e.ellipsoid,l=u.cartesianToCartographic(s,ct),c=e.project(l,ft);n.a.fromElements(c.z,c.x,c.y,c);var f=Re.eastNorthUpToFixedFrame(s,u,yt),d=o.c.inverseTransformation(f,ht),p=o.c.getMatrix3(t,pt),y=o.c.multiplyByMatrix3(d,p,i);return o.c.multiply(lt,y,i),o.c.setTranslation(i,c,i),i},Re.wgs84To2DModelMatrix=function(e,t,i){if(!a.t(e))throw new r.t("projection is required.");if(!a.t(t))throw new r.t("center is required.");if(!a.t(i))throw new r.t("result is required.");var s=e.ellipsoid,u=Re.eastNorthUpToFixedFrame(t,s,yt),l=o.c.inverseTransformation(u,ht),c=s.cartesianToCartographic(t,ct),f=e.project(c,ft);n.a.fromElements(f.z,f.x,f.y,f);var d=o.c.fromTranslation(f,yt);return o.c.multiply(lt,l,i),o.c.multiply(d,i,i),i},Re.buildUp=function(e,t){var r=t.clone(),a=e.clone();a=n.a.normalize(a,a),Math.abs(n.a.dot(a,r))>=1&&(a=Math.abs(n.a.dot(r,n.a.UNIT_Y))<1?n.a.clone(n.a.UNIT_Y,a):n.a.clone(n.a.UNIT_Z,a));var o=new n.a;return n.a.cross(a,r,o),o=n.a.normalize(o,o),n.a.cross(r,o,a),a=n.a.normalize(a,a)},Re.getHeading=function(e,t){var n;return n=s.n.equalsEpsilon(Math.abs(e.z),1,s.n.EPSILON3)?Math.atan2(t.y,t.x)-s.n.PI_OVER_TWO:Math.atan2(e.y,e.x)-s.n.PI_OVER_TWO,s.n.TWO_PI-s.n.zeroToTwoPi(n)},Re.convertToColumbusCartesian=function(e){var t=new d.s,r=t.ellipsoid,a=new n.a,o=new n.i;return r.cartesianToCartographic(e,o),t.project(o,a),n.a.fromElements(a.z,a.x,a.y)},Re.convertTo3DCartesian=function(e){var t=new d.s,r=t.ellipsoid,a=new n.a,o=new n.i;return a=n.a.fromElements(e.y,e.z,e.x),t.unproject(a,o),r.cartographicToCartesian(o,a)},mt.computeNumberOfVertices=function(e){r.n.typeOf.object("geometry",e);var t=-1;for(var n in e.attributes)if(e.attributes.hasOwnProperty(n)&&a.t(e.attributes[n])&&a.t(e.attributes[n].values)){var o=e.attributes[n];if(o.isInstanceAttribute)continue;var i=o.values.length/o.componentsPerAttribute;if(t!==i&&-1!==t)throw new r.t("All attribute lists must have the same number of attributes.");t=i}return t};var wt=new n.i,Ot=new n.a,bt=new o.c,xt=[new n.i,new n.i,new n.i],Et=[new t.r,new t.r,new t.r],_t=[new t.r,new t.r,new t.r],vt=new n.a,Tt=new w,St=new o.c,gt=new y;mt._textureCoordinateRotationPoints=function(e,r,a,i){var s,l=u.s.center(i,wt),c=n.i.toCartesian(l,a,Ot),f=Re.eastNorthUpToFixedFrame(c,a,bt),d=o.c.inverse(f,bt),p=Et,h=xt;h[0].longitude=i.west,h[0].latitude=i.south,h[1].longitude=i.west,h[1].latitude=i.north,h[2].longitude=i.east,h[2].latitude=i.south;var m=vt;for(s=0;s<3;s++)n.i.toCartesian(h[s],a,m),m=o.c.multiplyByPointAsVector(d,m,m),p[s].x=m.x,p[s].y=m.y;var O=w.fromAxisAngle(n.a.UNIT_Z,-r,Tt),b=o.r.fromQuaternion(O,St),x=e.length,E=Number.POSITIVE_INFINITY,_=Number.POSITIVE_INFINITY,v=Number.NEGATIVE_INFINITY,T=Number.NEGATIVE_INFINITY;for(s=0;s<x;s++)m=o.c.multiplyByPointAsVector(d,e[s],m),m=o.r.multiplyByVector(b,m,m),E=Math.min(E,m.x),_=Math.min(_,m.y),v=Math.max(v,m.x),T=Math.max(T,m.y);var S=y.fromRotation(r,gt),g=_t;g[0].x=E,g[0].y=_,g[1].x=E,g[1].y=T,g[2].x=v,g[2].y=_;var D=p[0],P=p[2].x-D.x,N=p[1].y-D.y;for(s=0;s<3;s++){var C=g[s];y.multiplyByVector(S,C,C),C.x=(C.x-D.x)/P,C.y=(C.y-D.y)/N}var I=g[0],j=g[1],R=g[2],M=new Array(6);return t.r.pack(I,M),t.r.pack(j,M,2),t.r.pack(R,M,4),M},e.Sr=p,e.T=mt,e.a=w,e.m=Re,e.r=function(e){if(e=a.e(e,a.e.EMPTY_OBJECT),!a.t(e.componentDatatype))throw new r.t("options.componentDatatype is required.");if(!a.t(e.componentsPerAttribute))throw new r.t("options.componentsPerAttribute is required.");if(e.componentsPerAttribute<1||e.componentsPerAttribute>4)throw new r.t("options.componentsPerAttribute must be between 1 and 4.");if(!a.t(e.values))throw new r.t("options.values is required.");this.componentDatatype=e.componentDatatype,this.componentsPerAttribute=e.componentsPerAttribute,this.normalize=a.e(e.normalize,!1),this.values=e.values},e.u=y}));
