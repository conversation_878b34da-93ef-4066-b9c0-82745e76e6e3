define(["./Check-3aa71481","./RuntimeError-350acae3","./when-515d5295","./pako_inflate-f73548c4","./createTaskProcessorWorker"],(function(t,r,e,n,i){"use strict";function a(e,n){if(a.passThroughDataForTesting)return n;t.n.typeOf.object("key",e),t.n.typeOf.object("data",n);var i=e.byteLength;if(0===i||i%4!=0)throw new r.t("The length of key must be greater than 0 and a multiple of 4.");var o=new DataView(n),s=o.getUint32(0,!0);if(**********===s||**********===s)return n;for(var f,u=new DataView(e),h=0,v=n.byteLength,c=v-v%8,g=i,d=8;h<c;)for(f=d=(d+8)%24;h<c&&f<g;)o.setUint32(h,o.getUint32(h,!0)^u.getUint32(f,!0),!0),o.setUint32(h+4,o.getUint32(h+4,!0)^u.getUint32(f+4,!0),!0),h+=8,f+=24;if(h<v)for(f>=g&&(f=d=(d+8)%24);h<v;)o.setUint8(h,o.getUint8(h)^u.getUint8(f)),h++,f++}function o(t,r){return 0!=(t&r)}a.passThroughDataForTesting=!1;var s=[1,2,4,8];function f(t,r,e,n,i,a){this._bits=t,this.cnodeVersion=r,this.imageryVersion=e,this.terrainVersion=n,this.imageryProvider=i,this.terrainProvider=a,this.ancestorHasTerrain=!1,this.terrainState=void 0}f.clone=function(t,r){return e.t(r)?(r._bits=t._bits,r.cnodeVersion=t.cnodeVersion,r.imageryVersion=t.imageryVersion,r.terrainVersion=t.terrainVersion,r.imageryProvider=t.imageryProvider,r.terrainProvider=t.terrainProvider):r=new f(t._bits,t.cnodeVersion,t.imageryVersion,t.terrainVersion,t.imageryProvider,t.terrainProvider),r.ancestorHasTerrain=t.ancestorHasTerrain,r.terrainState=t.terrainState,r},f.prototype.setParent=function(t){this.ancestorHasTerrain=t.ancestorHasTerrain||this.hasTerrain()},f.prototype.hasSubtree=function(){return o(this._bits,16)},f.prototype.hasImagery=function(){return o(this._bits,64)},f.prototype.hasTerrain=function(){return o(this._bits,128)},f.prototype.hasChildren=function(){return o(this._bits,15)},f.prototype.hasChild=function(t){return o(this._bits,s[t])},f.prototype.getChildBitmask=function(){return 15&this._bits};var u=Uint16Array.BYTES_PER_ELEMENT,h=Int32Array.BYTES_PER_ELEMENT,v=Uint32Array.BYTES_PER_ELEMENT,c={METADATA:0,TERRAIN:1,DBROOT:2};c.fromString=function(t){return"Metadata"===t?c.METADATA:"Terrain"===t?c.TERRAIN:"DbRoot"===t?c.DBROOT:void 0};var g=**********,d=**********;return i((function(t,e){var i=c.fromString(t.type),o=t.buffer;a(t.key,o);var s=function(t){var e=new DataView(t),i=0,a=e.getUint32(i,!0);if(i+=v,a!==g&&a!==d)throw new r.t("Invalid magic");var o=e.getUint32(i,a===g);i+=v;var s=new Uint8Array(t,i),f=n.pako.inflate(s);if(f.length!==o)throw new r.t("Size of packet doesn't match header");return f}(o);o=s.buffer;var T=s.length;switch(i){case c.METADATA:return function(t,e,n){var i=new DataView(t),a=0,o=i.getUint32(a,!0);if(a+=v,32301!==o)throw new r.t("Invalid magic");var s=i.getUint32(a,!0);if(a+=v,1!==s)throw new r.t("Invalid data type. Must be 1 for QuadTreePacket");var c=i.getUint32(a,!0);if(a+=v,2!==c)throw new r.t("Invalid QuadTreePacket version. Only version 2 is supported.");var g=i.getInt32(a,!0);a+=h;var d=i.getInt32(a,!0);if(a+=h,32!==d)throw new r.t("Invalid instance size.");var T=i.getInt32(a,!0);a+=h;var w=i.getInt32(a,!0);a+=h;var p=i.getInt32(a,!0);if(T!==g*d+(a+=h))throw new r.t("Invalid dataBufferOffset");if(T+w+p!==e)throw new r.t("Invalid packet offsets");for(var y=[],l=0;l<g;++l){var U=i.getUint8(a);++a,++a;var b=i.getUint16(a,!0);a+=u;var E=i.getUint16(a,!0);a+=u;var m=i.getUint16(a,!0);a+=u,a+=u,a+=u,a+=h,a+=h,a+=8;var I=i.getUint8(a++),V=i.getUint8(a++);a+=u,y.push(new f(U,b,E,m,I,V))}var _=[],A=0;function P(t,r,e){var n=!1;if(4===e){if(r.hasSubtree())return;n=!0}for(var i=0;i<4;++i){var a=t+i.toString();if(n)_[a]=null;else if(e<4)if(r.hasChild(i)){if(A===g)return void console.log("Incorrect number of instances");var o=y[A++];_[a]=o,P(a,o,e+1)}else _[a]=null}}var D=0,R=y[A++];""===n?++D:_[n]=R;return P(n,R,D),_}(o,T,t.quadKey);case c.TERRAIN:return function(t,r,e){var n=new DataView(t),i=0,a=[];for(;i<r;){for(var o=i,s=0;s<4;++s){var f=n.getUint32(i,!0);i+=v,i+=f}var u=t.slice(o,i);e.push(u),a.push(u)}return a}(o,T,e);case c.DBROOT:return e.push(o),{buffer:o}}}))}));
