define(["exports","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./PrimitiveType-b38a4004","./OrientedBoundingBox-57407e6e"],(function(n,e,t,r,a,i){"use strict";var o={},u=new t.a,c=new t.a,d=new t.a,m=new t.a,l=new i.b;function s(n,r,a,i,o){var c=t.a.subtract(n,r,u),d=t.a.dot(a,c),m=t.a.dot(i,c);return e.r.fromElements(d,m,o)}o.validOutline=function(n){r.n.defined("positions",n);var e=i.b.fromPoints(n,l).halfAxes,o=a.r.getColumn(e,0,c),u=a.r.getColumn(e,1,d),s=a.r.getColumn(e,2,m),f=t.a.magnitude(o),g=t.a.magnitude(u),b=t.a.magnitude(s);return!(0===f&&(0===g||0===b)||0===g&&0===b)},o.computeProjectTo2DArguments=function(n,e,o,u){r.n.defined("positions",n),r.n.defined("centerResult",e),r.n.defined("planeAxis1Result",o),r.n.defined("planeAxis2Result",u);var s,f,g=i.b.fromPoints(n,l),b=g.halfAxes,p=a.r.getColumn(b,0,c),C=a.r.getColumn(b,1,d),P=a.r.getColumn(b,2,m),h=t.a.magnitude(p),v=t.a.magnitude(C),w=t.a.magnitude(P),x=Math.min(h,v,w);return(0!==h||0!==v&&0!==w)&&(0!==v||0!==w)&&((x===v||x===w)&&(s=p),x===h?s=C:x===w&&(f=C),(x===h||x===v)&&(f=P),t.a.normalize(s,o),t.a.normalize(f,u),t.a.clone(g.center,e),!0)},o.createProjectPointsTo2DFunction=function(n,e,t){return function(r){for(var a=new Array(r.length),i=0;i<r.length;i++)a[i]=s(r[i],n,e,t);return a}},o.createProjectPointTo2DFunction=function(n,e,t){return function(r,a){return s(r,n,e,t,a)}},n.p=o}));
