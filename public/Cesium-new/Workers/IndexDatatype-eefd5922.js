define(["exports","./when-515d5295","./Check-3aa71481","./Math-5e38123d","./WebGLConstants-77a84876"],(function(e,t,r,n,a){"use strict";var i={UNSIGNED_BYTE:a.t.UNSIGNED_BYTE,UNSIGNED_SHORT:a.t.UNSIGNED_SHORT,UNSIGNED_INT:a.t.UNSIGNED_INT,getSizeInBytes:function(e){switch(e){case i.UNSIGNED_BYTE:return Uint8Array.BYTES_PER_ELEMENT;case i.UNSIGNED_SHORT:return Uint16Array.BYTES_PER_ELEMENT;case i.UNSIGNED_INT:return Uint32Array.BYTES_PER_ELEMENT}throw new r.t("indexDatatype is required and must be a valid IndexDatatype constant.")},fromSizeInBytes:function(e){switch(e){case 2:return i.UNSIGNED_SHORT;case 4:return i.UNSIGNED_INT;case 1:return i.UNSIGNED_BYTE;default:throw new r.t("Size in bytes cannot be mapped to an IndexDatatype")}},validate:function(e){return t.t(e)&&(e===i.UNSIGNED_BYTE||e===i.UNSIGNED_SHORT||e===i.UNSIGNED_INT)},createTypedArray:function(e,a){if(!t.t(e))throw new r.t("numberOfVertices is required.");return e>=n.n.SIXTY_FOUR_KILOBYTES?new Uint32Array(a):new Uint16Array(a)},createTypedArrayFromArrayBuffer:function(e,a,i,N){if(!t.t(e))throw new r.t("numberOfVertices is required.");if(!t.t(a))throw new r.t("sourceArray is required.");if(!t.t(i))throw new r.t("byteOffset is required.");return e>=n.n.SIXTY_FOUR_KILOBYTES?new Uint32Array(a,i,N):new Uint16Array(a,i,N)}},N=Object.freeze(i);e.IndexDatatype=N}));
