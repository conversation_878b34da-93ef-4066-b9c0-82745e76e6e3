define(["exports","./arrayRemoveDuplicates-a4c6347e","./Cartographic-1bbcab04","./when-515d5295","./Math-5e38123d","./PolylinePipeline-bf1462fc","./GeometryAttribute-9bc31a7f","./PrimitiveType-b38a4004"],(function(e,t,r,i,n,o,a,s){"use strict";var h={};function l(e,t){return n.n.equalsEpsilon(e.latitude,t.latitude,n.n.EPSILON10)&&n.n.equalsEpsilon(e.longitude,t.longitude,n.n.EPSILON10)}var g=new r.i,v=new r.i;var c=new Array(2),u=new Array(2),p={positions:void 0,height:void 0,granularity:void 0,ellipsoid:void 0};function y(e,t){for(var i=new Array(e.length),n=0;n<e.length;n+=3){var o=new r.a(e[n],e[n+1],e[n+2]);s.c.multiplyByPoint(t,o,o),i[n]=o.x,i[n+1]=o.y,i[n+2]=o.z}return i}h.computePositions=function(e,h,A,w,f,m,d){var P=function(e,n,o,a){var s=(n=t.u(n,r.a.equalsEpsilon)).length;if(!(s<2)){var h=i.t(a),c=i.t(o),u=!0,p=new Array(s),y=new Array(s),A=new Array(s),w=n[0];p[0]=w;var f=e.cartesianToCartographic(w,g);c&&(f.height=o[0]),u=u&&0==f.height,y[0]=f.height,A[0]=h?a[0]:0;for(var m=1,d=1;d<s;++d){var P=n[d],b=e.cartesianToCartographic(P,v);c&&(b.height=o[d]),u=u&&0==b.height,l(f,b)?f.height<b.height&&(y[m-1]=b.height):(p[m]=P,y[m]=b.height,A[m]=h?a[d]:0,r.i.clone(b,f),++m)}if(!(u||m<2))return p.length=m,y.length=m,A.length=m,{positions:p,topHeights:y,bottomHeights:A}}}(e,h,A,w);if(i.t(P)){var b=a.m.eastNorthUpToFixedFrame(P.positions[0],e,new s.c),F=s.c.inverse(b,new s.c);h=P.positions,A=P.topHeights,w=P.bottomHeights;var C,E,x,H,T=h.length,q=T-2,L=n.n.chordLength(f,e.maximumRadius),N=p;if(N.minDistance=L,N.ellipsoid=e,m){var O,B=0;for(O=0;O<T-1;O++)B+=o.v.numberOfPoints(h[O],h[O+1],L)+1;C=new Float64Array(3*B),E=new Float64Array(3*B),i.t(d)&&(x=new Float64Array(3*B),H=new Float64Array(3*B));var D=c,I=u;N.positions=D,N.height=I;var R=0;for(O=0;O<T-1;O++){D[0]=h[O],D[1]=h[O+1],I[0]=A[O],I[1]=A[O+1];var S=o.v.generateArc(N);C.set(S,R),i.t(d)&&x.set(y(S,F),R),I[0]=w[O],I[1]=w[O+1],E.set(o.v.generateArc(N),R),i.t(d)&&H.set(y(o.v.generateArc(N),F),R),R+=S.length}}else N.positions=h,N.height=A,C=new Float64Array(o.v.generateArc(N)),i.t(d)&&(x=new Float64Array(y(o.v.generateArc(N)))),N.height=w,E=new Float64Array(o.v.generateArc(N)),i.t(d)&&(H=new Float64Array(y(o.v.generateArc(N))));var z={pos:{bottomPositions:E,topPositions:C,numCorners:q}};return i.t(d)&&(z.localPos={bottomPositions:H,topPositions:x,numCorners:q}),z}},e.B=h}));
