define(["./AttributeCompression-f9ee669b","./Cartographic-1bbcab04","./Rectangle-e170be8b","./IndexDatatype-eefd5922","./Math-5e38123d","./createTaskProcessorWorker","./Cartesian2-1b9b0d8a","./Check-3aa71481","./when-515d5295","./WebGLConstants-77a84876"],(function(a,r,e,n,t,u,s,c,i,f){"use strict";var o=32767,p=new r.i,b=new r.a;var d=new e.s,w=new e.n,y=new r.a,k={min:void 0,max:void 0};var v=new r.a,h=new r.a,l=new r.a,A=new r.a,g=new r.a;return u((function(u,s){var c=new Uint16Array(u.positions),i=new Uint16Array(u.widths),f=new Uint32Array(u.counts),x=new Uint16Array(u.batchIds);!function(a){a=new Float64Array(a);var n=0;k.min=a[n++],k.max=a[n++],e.s.unpack(a,n,d),n+=e.s.packedLength,e.n.unpack(a,n,w),n+=e.n.packedLength,r.a.unpack(a,n,y)}(u.packedBuffer);var D,I=w,m=y,E=function(e,n,u,s,c){var i=e.length/3,f=e.subarray(0,i),d=e.subarray(i,2*i),w=e.subarray(2*i,3*i);a.r.zigZagDeltaDecode(f,d,w);for(var y=new Float32Array(e.length),k=0;k<i;++k){var v=f[k],h=d[k],l=w[k],A=t.n.lerp(n.west,n.east,v/o),g=t.n.lerp(n.south,n.north,h/o),x=t.n.lerp(u,s,l/o),D=r.i.fromRadians(A,g,x,p),I=c.cartographicToCartesian(D,b);r.a.pack(I,y,3*k)}return y}(c,d,k.min,k.max,I),T=E.length/3,U=4*T-4,C=new Float32Array(3*U),F=new Float32Array(3*U),N=new Float32Array(3*U),P=new Float32Array(2*U),L=new Uint16Array(U),R=0,S=0,_=0,G=0,W=f.length;for(D=0;D<W;++D){for(var B=f[D],M=i[D],z=x[D],H=0;H<B;++H){var O;if(0===H){var Y=r.a.unpack(E,3*G,v),Z=r.a.unpack(E,3*(G+1),h);O=r.a.subtract(Y,Z,l),r.a.add(Y,O,O)}else O=r.a.unpack(E,3*(G+H-1),l);var j,q=r.a.unpack(E,3*(G+H),A);if(H===B-1){var J=r.a.unpack(E,3*(G+B-1),v),K=r.a.unpack(E,3*(G+B-2),h);j=r.a.subtract(J,K,g),r.a.add(J,j,j)}else j=r.a.unpack(E,3*(G+H+1),g);r.a.subtract(O,m,O),r.a.subtract(q,m,q),r.a.subtract(j,m,j);for(var Q=H===B-1?2:4,V=0===H?2:0;V<Q;++V){r.a.pack(q,C,R),r.a.pack(O,F,R),r.a.pack(j,N,R),R+=3;var X=V-2<0?-1:1;P[S++]=V%2*2-1,P[S++]=X*M,L[_++]=z}}G+=B}var $=n.IndexDatatype.createTypedArray(U,6*T-6),aa=0,ra=0;for(W=T-1,D=0;D<W;++D)$[ra++]=aa,$[ra++]=aa+2,$[ra++]=aa+1,$[ra++]=aa+1,$[ra++]=aa+2,$[ra++]=aa+3,aa+=4;return s.push(C.buffer,F.buffer,N.buffer),s.push(P.buffer,L.buffer,$.buffer),{indexDatatype:2===$.BYTES_PER_ELEMENT?n.IndexDatatype.UNSIGNED_SHORT:n.IndexDatatype.UNSIGNED_INT,currentPositions:C.buffer,previousPositions:F.buffer,nextPositions:N.buffer,expandAndWidth:P.buffer,batchIds:L.buffer,indices:$.buffer}}))}));
