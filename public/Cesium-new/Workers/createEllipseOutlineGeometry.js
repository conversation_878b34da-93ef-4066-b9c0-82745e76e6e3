define(["./Cartographic-1bbcab04","./when-515d5295","./EllipseOutlineGeometry-c1a1f716","./Rectangle-e170be8b","./Check-3aa71481","./Math-5e38123d","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./EllipseGeometryLibrary-497ff3d7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922"],(function(e,t,a,r,n,i,c,d,o,l,b,f,u,s,y,p,m,G,C,h,E,_){"use strict";return function(n,i){return t.t(i)&&(n=a.w.unpack(n,i)),n._center=e.a.clone(n._center),n._ellipsoid=r.n.clone(n._ellipsoid),a.w.createGeometry(n)}}));
