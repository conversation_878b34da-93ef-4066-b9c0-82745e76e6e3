define(["exports","./when-515d5295","./Check-3aa71481"],(function(t,n,e){"use strict";function o(t){t=n.e(t,n.e.EMPTY_OBJECT),this.position=n.e(t.position,!1),this.normal=n.e(t.normal,!1),this.st=n.e(t.st,!1),this.bitangent=n.e(t.bitangent,!1),this.tangent=n.e(t.tangent,!1),this.color=n.e(t.color,!1)}o.POSITION_ONLY=Object.freeze(new o({position:!0})),o.POSITION_AND_NORMAL=Object.freeze(new o({position:!0,normal:!0})),o.POSITION_NORMAL_AND_ST=Object.freeze(new o({position:!0,normal:!0,st:!0})),o.POSITION_AND_ST=Object.freeze(new o({position:!0,st:!0})),o.POSITION_AND_COLOR=Object.freeze(new o({position:!0,color:!0})),o.ALL=Object.freeze(new o({position:!0,normal:!0,st:!0,tangent:!0,bitangent:!0})),o.DEFAULT=o.POSITION_NORMAL_AND_ST,o.packedLength=6,o.pack=function(t,o,i){if(!n.t(t))throw new e.t("value is required");if(!n.t(o))throw new e.t("array is required");return i=n.e(i,0),o[i++]=t.position?1:0,o[i++]=t.normal?1:0,o[i++]=t.st?1:0,o[i++]=t.tangent?1:0,o[i++]=t.bitangent?1:0,o[i]=t.color?1:0,o},o.unpack=function(t,i,r){if(!n.t(t))throw new e.t("array is required");return i=n.e(i,0),n.t(r)||(r=new o),r.position=1===t[i++],r.normal=1===t[i++],r.st=1===t[i++],r.tangent=1===t[i++],r.bitangent=1===t[i++],r.color=1===t[i],r},o.clone=function(t,e){if(n.t(t))return n.t(e)||(e=new o),e.position=t.position,e.normal=t.normal,e.st=t.st,e.tangent=t.tangent,e.bitangent=t.bitangent,e.color=t.color,e},t.n=o}));
