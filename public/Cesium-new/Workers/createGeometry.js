define(["./when-515d5295","./PrimitivePipeline-67f346ce","./createTaskProcessorWorker","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./Math-5e38123d","./Rectangle-e170be8b","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryPipeline-137aa28e","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./IndexDatatype-eefd5922","./IntersectionTests-5fa33dbd","./Plane-92c15089","./WebMercatorProjection-aa5a37a5"],(function(e,t,r,a,n,o,i,c,s,u,b,d,f,l,m,p,y,C,v,k,P,h,G,W,g,A){"use strict";var T={};function D(t){var r=T[t];return e.t(r)||("object"==typeof exports?T[r]=r=require("Workers/"+t):require(["Workers/"+t],(function(e){T[r=e]=e}))),r}return r((function(r,a){for(var n=r.subTasks,o=n.length,i=new Array(o),c=0;c<o;c++){var s=n[c],u=s.geometry,b=s.moduleName;if(e.t(b)){var d=D(b);i[c]=d(u,s.offset)}else i[c]=u}return e.c.all(i,(function(e){return t.S.packCreateGeometryResults(e,a)}))}))}));
