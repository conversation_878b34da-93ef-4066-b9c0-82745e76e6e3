define(["exports","./when-515d5295","./Check-3aa71481","./RuntimeError-350acae3"],(function(e,t,n,r){"use strict";function o(e,r,i,d){if(!t.t(e))throw new n.t("uint8Array is required.");if(r<0)throw new n.t("byteOffset cannot be negative.");if(i<0)throw new n.t("byteLength cannot be negative.");if(r+i>e.byteLength)throw new n.t("sub-region exceeds array bounds.");return r=t.e(r,0),i=t.e(i,e.byteLength-r),d=t.e(d,"utf-8"),e=e.subarray(r,r+i),o.decode(e,d)}function i(e,t,n){return t<=e&&e<=n}o.decodeWithTextDecoder=function(e,t){return new TextDecoder(t).decode(e)},o.decodeWithFromCharCode=function(e){for(var t="",n=function(e){for(var t=0,n=0,o=0,d=128,c=191,u=[],f=e.length,a=0;a<f;++a){var h=e[a];if(0===o){if(i(h,0,127)){u.push(h);continue}if(i(h,194,223)){o=1,t=31&h;continue}if(i(h,224,239)){224===h&&(d=160),237===h&&(c=159),o=2,t=15&h;continue}if(i(h,240,244)){240===h&&(d=144),244===h&&(c=143),o=3,t=7&h;continue}throw new r.t("String decoding failed.")}i(h,d,c)?(d=128,c=191,t=t<<6|63&h,++n===o&&(u.push(t),t=o=n=0)):(t=o=n=0,d=128,c=191,--a)}return u}(e),o=n.length,d=0;d<o;++d){var c=n[d];c<=65535?t+=String.fromCharCode(c):(c-=65536,t+=String.fromCharCode(55296+(c>>10),56320+(1023&c)))}return t},typeof TextDecoder<"u"?o.decode=o.decodeWithTextDecoder:o.decode=o.decodeWithFromCharCode,e.c=o}));
