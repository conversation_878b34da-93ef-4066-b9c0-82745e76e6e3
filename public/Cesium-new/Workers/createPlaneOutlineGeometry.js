define(["./when-515d5295","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./PrimitiveType-b38a4004","./Rectangle-e170be8b","./Math-5e38123d","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5"],(function(e,t,n,a,r,i,o,c,u,d,y,b,f,p,s,m,w){"use strict";function l(){this._workerName="createPlaneOutlineGeometry"}l.packedLength=0,l.pack=function(e,t){return a.n.defined("value",e),a.n.defined("array",t),t},l.unpack=function(t,n,r){return a.n.defined("array",t),e.t(r)?r:new l};var h=new n.a(-.5,-.5,0),v=new n.a(.5,.5,0);return l.createGeometry=function(){var e=new o.t,a=new Uint16Array(8),u=new Float64Array(12);return u[0]=h.x,u[1]=h.y,u[2]=h.z,u[3]=v.x,u[4]=h.y,u[5]=h.z,u[6]=v.x,u[7]=v.y,u[8]=h.z,u[9]=h.x,u[10]=v.y,u[11]=h.z,e.position=new i.r({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:u}),a[0]=0,a[1]=1,a[2]=1,a[3]=2,a[4]=2,a[5]=3,a[6]=3,a[7]=0,new i.T({attributes:e,indices:a,primitiveType:c._0x38df4a.LINES,boundingSphere:new t.c(n.a.ZERO,Math.sqrt(2))})},function(t,n){return e.t(n)&&(t=l.unpack(t,n)),l.createGeometry(t)}}));
