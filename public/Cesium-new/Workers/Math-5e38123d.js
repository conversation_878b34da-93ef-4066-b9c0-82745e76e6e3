define(["exports","./Check-3aa71481","./when-515d5295"],(function(t,e,r){"use strict";
/**
   @license
   mersenne-twister.js - https://gist.github.com/banksean/300494

      Copyright (C) 1997 - 2002, <PERSON><PERSON> and <PERSON><PERSON><PERSON>,
      All rights reserved.

      Redistribution and use in source and binary forms, with or without
      modification, are permitted provided that the following conditions
      are met:

        1. Redistributions of source code must retain the above copyright
           notice, this list of conditions and the following disclaimer.

        2. Redistributions in binary form must reproduce the above copyright
           notice, this list of conditions and the following disclaimer in the
           documentation and/or other materials provided with the distribution.

        3. The names of its contributors may not be used to endorse or promote
           products derived from this software without specific prior written
           permission.

      THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
      "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
      LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
      A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
      CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
      EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
      PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
      PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
      LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
      NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
      SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   */var i=function(t){null==t&&(t=(new Date).getTime()),this.N=624,this.M=397,this.MATRIX_A=2567483615,this.UPPER_MASK=2147483648,this.LOWER_MASK=2147483647,this.mt=new Array(this.N),this.mti=this.N+1,this.init_genrand(t)};i.prototype.init_genrand=function(t){for(this.mt[0]=t>>>0,this.mti=1;this.mti<this.N;this.mti++){t=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30;this.mt[this.mti]=(1812433253*((4294901760&t)>>>16)<<16)+1812433253*(65535&t)+this.mti,this.mt[this.mti]>>>=0}},i.prototype.genrand_int32=function(){var t,e=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var r;for(this.mti==this.N+1&&this.init_genrand(5489),r=0;r<this.N-this.M;r++)t=this.mt[r]&this.UPPER_MASK|this.mt[r+1]&this.LOWER_MASK,this.mt[r]=this.mt[r+this.M]^t>>>1^e[1&t];for(;r<this.N-1;r++)t=this.mt[r]&this.UPPER_MASK|this.mt[r+1]&this.LOWER_MASK,this.mt[r]=this.mt[r+(this.M-this.N)]^t>>>1^e[1&t];t=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^t>>>1^e[1&t],this.mti=0}return t=this.mt[this.mti++],t^=t>>>11,t^=t<<7&2636928640,t^=t<<15&4022730752,(t^=t>>>18)>>>0},i.prototype.random=function(){return this.genrand_int32()*(1/4294967296)};var n={Radius:6378137,EPSILON1:.1,EPSILON2:.01,EPSILON3:.001,EPSILON4:1e-4,EPSILON5:1e-5,EPSILON6:1e-6,EPSILON7:1e-7,EPSILON8:1e-8,EPSILON9:1e-9,EPSILON10:1e-10,EPSILON11:1e-11,EPSILON12:1e-12,EPSILON13:1e-13,EPSILON14:1e-14,EPSILON15:1e-15,EPSILON16:1e-16,EPSILON17:1e-17,EPSILON18:1e-18,EPSILON19:1e-19,EPSILON20:1e-20,EPSILON21:1e-21,GRAVITATIONALPARAMETER:3986004418e5,SOLAR_RADIUS:6955e5,LUNAR_RADIUS:1737400,SIXTY_FOUR_KILOBYTES:65536};n.sign=r.e(Math.sign,(function(t){return 0===(t=+t)||t!=t?t:t>0?1:-1})),n.signNotZero=function(t){return t<0?-1:1},n.toSNorm=function(t,e){return e=r.e(e,255),Math.round((.5*n.clamp(t,-1,1)+.5)*e)},n.fromSNorm=function(t,e){return e=r.e(e,255),n.clamp(t,0,e)/e*2-1},n.normalize=function(t,e,r){return 0===(r=Math.max(r-e,0))?0:n.clamp((t-e)/r,0,1)},n.sinh=r.e(Math.sinh,(function(t){return(Math.exp(t)-Math.exp(-t))/2})),n.cosh=r.e(Math.cosh,(function(t){return(Math.exp(t)+Math.exp(-t))/2})),n.lerp=function(t,e,r){return(1-r)*t+r*e},n.PI=Math.PI,n.ONE_OVER_PI=1/Math.PI,n.PI_OVER_TWO=Math.PI/2,n.PI_OVER_THREE=Math.PI/3,n.PI_OVER_FOUR=Math.PI/4,n.PI_OVER_SIX=Math.PI/6,n.THREE_PI_OVER_TWO=3*Math.PI/2,n.TWO_PI=2*Math.PI,n.ONE_OVER_TWO_PI=1/(2*Math.PI),n.RADIANS_PER_DEGREE=Math.PI/180,n.DEGREES_PER_RADIAN=180/Math.PI,n.RADIANS_PER_ARCSECOND=n.RADIANS_PER_DEGREE/3600,n.toRadians=function(t){if(!r.t(t))throw new e.t("degrees is required.");return t*n.RADIANS_PER_DEGREE},n.toDegrees=function(t){if(!r.t(t))throw new e.t("radians is required.");return t*n.DEGREES_PER_RADIAN},n.convertLongitudeRange=function(t){if(!r.t(t))throw new e.t("angle is required.");var i=n.TWO_PI,a=t-Math.floor(t/i)*i;return a<-Math.PI?a+i:a>=Math.PI?a-i:a},n.clampToLatitudeRange=function(t){if(!r.t(t))throw new e.t("angle is required.");return n.clamp(t,-1*n.PI_OVER_TWO,n.PI_OVER_TWO)},n.negativePiToPi=function(t){if(!r.t(t))throw new e.t("angle is required.");return n.zeroToTwoPi(t+n.PI)-n.PI},n.zeroToTwoPi=function(t){if(!r.t(t))throw new e.t("angle is required.");var i=n.mod(t,n.TWO_PI);return Math.abs(i)<n.EPSILON14&&Math.abs(t)>n.EPSILON14?n.TWO_PI:i},n.mod=function(t,i){if(!r.t(t))throw new e.t("m is required.");if(!r.t(i))throw new e.t("n is required.");return(t%i+i)%i},n.equalsEpsilon=function(t,i,n,a){if(!r.t(t))throw new e.t("left is required.");if(!r.t(i))throw new e.t("right is required.");if(!r.t(n))throw new e.t("relativeEpsilon is required.");a=r.e(a,n);var h=Math.abs(t-i);return h<=a||h<=n*Math.max(Math.abs(t),Math.abs(i))},n.lessThan=function(t,i,n){if(!r.t(t))throw new e.t("first is required.");if(!r.t(i))throw new e.t("second is required.");if(!r.t(n))throw new e.t("relativeEpsilon is required.");return t-i<-n},n.lessThanOrEquals=function(t,i,n){if(!r.t(t))throw new e.t("first is required.");if(!r.t(i))throw new e.t("second is required.");if(!r.t(n))throw new e.t("relativeEpsilon is required.");return t-i<n},n.greaterThan=function(t,i,n){if(!r.t(t))throw new e.t("first is required.");if(!r.t(i))throw new e.t("second is required.");if(!r.t(n))throw new e.t("relativeEpsilon is required.");return t-i>n},n.greaterThanOrEquals=function(t,i,n){if(!r.t(t))throw new e.t("first is required.");if(!r.t(i))throw new e.t("second is required.");if(!r.t(n))throw new e.t("relativeEpsilon is required.");return t-i>-n};var a=[1];n.factorial=function(t){if("number"!=typeof t||t<0)throw new e.t("A number greater than or equal to 0 is required.");var r=a.length;if(t>=r)for(var i=a[r-1],n=r;n<=t;n++){var h=i*n;a.push(h),i=h}return a[t]},n.incrementWrap=function(t,i,n){if(n=r.e(n,0),!r.t(t))throw new e.t("n is required.");if(i<=n)throw new e.t("maximumValue must be greater than minimumValue.");return++t>i&&(t=n),t},n.isPowerOfTwo=function(t){if("number"!=typeof t||t<0)throw new e.t("A number greater than or equal to 0 is required.");return 0!==t&&0==(t&t-1)},n.nextPowerOfTwo=function(t){if("number"!=typeof t||t<0)throw new e.t("A number greater than or equal to 0 is required.");return--t,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,++t},n.clamp=function(t,i,n){if(!r.t(t))throw new e.t("value is required");if(!r.t(i))throw new e.t("min is required.");if(!r.t(n))throw new e.t("max is required.");return t<i?i:t>n?n:t};var h=new i;n.setRandomNumberSeed=function(t){if(!r.t(t))throw new e.t("seed is required.");h=new i(t)},n.nextRandomNumber=function(){return h.random()},n.randomBetween=function(t,e){return n.nextRandomNumber()*(e-t)+t},n.acosClamped=function(t){if(!r.t(t))throw new e.t("value is required.");return Math.acos(n.clamp(t,-1,1))},n.asinClamped=function(t){if(!r.t(t))throw new e.t("value is required.");return Math.asin(n.clamp(t,-1,1))},n.chordLength=function(t,i){if(!r.t(t))throw new e.t("angle is required.");if(!r.t(i))throw new e.t("radius is required.");return 2*i*Math.sin(.5*t)},n.logBase=function(t,i){if(!r.t(t))throw new e.t("number is required.");if(!r.t(i))throw new e.t("base is required.");return Math.log(t)/Math.log(i)},n.cbrt=r.e(Math.cbrt,(function(t){var e=Math.pow(Math.abs(t),.3333333333333333);return t<0?-e:e})),n.log2=r.e(Math.log2,(function(t){return Math.log(t)*Math.LOG2E})),n.fog=function(t,e){var r=t*e;return 1-Math.exp(-r*r)},n.fastApproximateAtan=function(t){return e.n.typeOf.number("x",t),t*(-.1784*Math.abs(t)-.0663*t*t+1.0301)},n.fastApproximateAtan2=function(t,r){e.n.typeOf.number("x",t),e.n.typeOf.number("y",r);var i,a,h=Math.abs(t);i=Math.abs(r),a=Math.max(h,i);var o=(i=Math.min(h,i))/a;if(isNaN(o))throw new e.t("either x or y must be nonzero");return h=n.fastApproximateAtan(o),h=Math.abs(r)>Math.abs(t)?n.PI_OVER_TWO-h:h,h=t<0?n.PI-h:h,h=r<0?-h:h},t.n=n}));
