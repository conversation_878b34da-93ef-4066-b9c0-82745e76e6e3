define(["./EllipsoidTangentPlane-fd839d7b","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./when-515d5295","./Rectangle-e170be8b","./TerrainEncoding-29e3257b","./Math-5e38123d","./PrimitiveType-b38a4004","./OrientedBoundingBox-57407e6e","./RuntimeError-350acae3","./GeometryAttribute-9bc31a7f","./WebMercatorProjection-aa5a37a5","./createTaskProcessorWorker","./Check-3aa71481","./Intersect-53434a77","./Cartesian4-034d54d5","./IntersectionTests-5fa33dbd","./Plane-92c15089","./Event-9821f5d9","./AttributeCompression-f9ee669b","./ComponentDatatype-d430c7f7","./WebGLConstants-77a84876","./PolygonPipeline-b8b35011","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6","./FeatureDetection-7fae0d5a"],(function(e,t,n,i,r,a,o,u,s,d,c,h,l,g,m,p,v,I,E,T,f,b,N,x,S,w,P){"use strict";var y=Uint16Array.BYTES_PER_ELEMENT,R=Int32Array.BYTES_PER_ELEMENT,A=Uint32Array.BYTES_PER_ELEMENT,M=Float32Array.BYTES_PER_ELEMENT,B=Float64Array.BYTES_PER_ELEMENT;function _(e,t,n){n=r.e(n,u.n);for(var i=e.length,a=0;a<i;++a)if(n.equalsEpsilon(e[a],t,u.n.EPSILON12))return a;return-1}var C=new i.i,F=new i.a,W=new i.a,U=new i.a,Y=new s.c;function k(e,t,a,o,d,c,h,l,g,m){for(var p=h.length,v=0;v<p;++v){var I=h[v],E=I.cartographic,T=I.index,f=e.length,b=E.longitude,N=E.latitude;N=u.n.clamp(N,-u.n.PI_OVER_TWO,u.n.PI_OVER_TWO);var x=E.height-c.skirtHeight;c.hMin=Math.min(c.hMin,x),i.i.fromRadians(b,N,x,C),g&&(C.longitude+=l),g?v===p-1?C.latitude+=m:0===v&&(C.latitude-=m):C.latitude+=l;var S=c.ellipsoid.cartographicToCartesian(C);e.push(S),t.push(x),a.push(n.r.clone(a[T])),o.length>0&&o.push(o[T]),s.c.multiplyByPoint(c.toENU,S,F);var w=c.minimum,P=c.maximum;i.a.minimumByComponent(F,w,w),i.a.maximumByComponent(F,P,P);var y=c.lastBorderPoint;if(r.t(y)){var R=y.index;d.push(R,f-1,f,f,T,R)}c.lastBorderPoint=I}}return g((function(g,m){g.ellipsoid=a.n.clone(g.ellipsoid),g.rectangle=a.s.clone(g.rectangle);var p=function(a,g,m,p,v,I,E,T,f,b){var N,x,S,w,P,O;r.t(p)?(N=p.west,x=p.south,S=p.east,w=p.north,P=p.width,O=p.height):(N=u.n.toRadians(v.west),x=u.n.toRadians(v.south),S=u.n.toRadians(v.east),w=u.n.toRadians(v.north),P=u.n.toRadians(p.width),O=u.n.toRadians(p.height));var L,V,H=[x,w],D=[N,S],G=h.m.eastNorthUpToFixedFrame(g,m),z=s.c.inverseTransformation(G,Y);T&&(L=l.e.geodeticLatitudeToMercatorAngle(x),V=1/(l.e.geodeticLatitudeToMercatorAngle(w)-L));var j=new DataView(a),q=Number.POSITIVE_INFINITY,J=Number.NEGATIVE_INFINITY,K=W;K.x=Number.POSITIVE_INFINITY,K.y=Number.POSITIVE_INFINITY,K.z=Number.POSITIVE_INFINITY;var Q=U;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;var X,Z,$=0,ee=0,te=0;for(Z=0;Z<4;++Z){var ne=$;X=j.getUint32(ne,!0),ne+=A;var ie=u.n.toRadians(180*j.getFloat64(ne,!0));ne+=B,-1===_(D,ie)&&D.push(ie);var re=u.n.toRadians(180*j.getFloat64(ne,!0));ne+=B,-1===_(H,re)&&H.push(re),ne+=2*B;var ae=j.getInt32(ne,!0);ne+=R,ee+=ae,te+=3*(ae=j.getInt32(ne,!0)),$+=X+A}var oe=[],ue=[],se=new Array(ee),de=new Array(ee),ce=new Array(ee),he=T?new Array(ee):[],le=new Array(te),ge=[],me=[],pe=[],ve=[],Ie=0,Ee=0;for($=0,Z=0;Z<4;++Z){X=j.getUint32($,!0);var Te=$+=A,fe=u.n.toRadians(180*j.getFloat64($,!0));$+=B;var be=u.n.toRadians(180*j.getFloat64($,!0));$+=B;var Ne=u.n.toRadians(180*j.getFloat64($,!0)),xe=.5*Ne;$+=B;var Se=u.n.toRadians(180*j.getFloat64($,!0)),we=.5*Se;$+=B;var Pe=j.getInt32($,!0);$+=R;var ye=j.getInt32($,!0);$+=R,$+=R;for(var Re=new Array(Pe),Ae=0;Ae<Pe;++Ae){var Me=fe+j.getUint8($++)*Ne;C.longitude=Me;var Be=be+j.getUint8($++)*Se;C.latitude=Be;var _e=j.getFloat32($,!0);if($+=M,0!==_e&&_e<b&&(_e*=-Math.pow(2,f)),_e*=6371010*I,C.height=_e,-1!==_(D,Me)||-1!==_(H,Be)){var Ce=_(oe,C,i.i);if(-1!==Ce){Re[Ae]=ue[Ce];continue}oe.push(i.i.clone(C)),ue.push(Ie)}Re[Ae]=Ie,Math.abs(Me-N)<xe?ge.push({index:Ie,cartographic:i.i.clone(C)}):Math.abs(Me-S)<xe?pe.push({index:Ie,cartographic:i.i.clone(C)}):Math.abs(Be-x)<we?me.push({index:Ie,cartographic:i.i.clone(C)}):Math.abs(Be-w)<we&&ve.push({index:Ie,cartographic:i.i.clone(C)}),q=Math.min(_e,q),J=Math.max(_e,J),ce[Ie]=_e;var Fe=m.cartographicToCartesian(C);se[Ie]=Fe,T&&(he[Ie]=(l.e.geodeticLatitudeToMercatorAngle(Be)-L)*V),s.c.multiplyByPoint(z,Fe,F),i.a.minimumByComponent(F,K,K),i.a.maximumByComponent(F,Q,Q);var We=(Me-N)/(S-N);We=u.n.clamp(We,0,1);var Ue=(Be-x)/(w-x);Ue=u.n.clamp(Ue,0,1),de[Ie]=new n.r(We,Ue),++Ie}for(var Ye=3*ye,ke=0;ke<Ye;++ke,++Ee)le[Ee]=Re[j.getUint16($,!0)],$+=y;if(X!==$-Te)throw new c.t("Invalid terrain tile.")}se.length=Ie,de.length=Ie,ce.length=Ie,T&&(he.length=Ie);var Oe=Ie,Le=Ee,Ve={hMin:q,lastBorderPoint:void 0,skirtHeight:E,toENU:z,ellipsoid:m,minimum:K,maximum:Q};ge.sort((function(e,t){return t.cartographic.latitude-e.cartographic.latitude})),me.sort((function(e,t){return e.cartographic.longitude-t.cartographic.longitude})),pe.sort((function(e,t){return e.cartographic.latitude-t.cartographic.latitude})),ve.sort((function(e,t){return t.cartographic.longitude-e.cartographic.longitude}));var He=1e-5;if(k(se,ce,de,he,le,Ve,ge,-He*P,!0,-He*O),k(se,ce,de,he,le,Ve,me,-He*O,!1),k(se,ce,de,he,le,Ve,pe,He*P,!0,He*O),k(se,ce,de,he,le,Ve,ve,He*O,!1),ge.length>0&&ve.length>0){var De=ge[0].index,Ge=Oe,ze=ve[ve.length-1].index,je=se.length-1;le.push(ze,je,Ge,Ge,De,ze)}ee=se.length;var qe,Je=t.c.fromPoints(se);r.t(p)&&(qe=d.b.fromRectangle(p,q,J,m));for(var Ke=new o.c(m).computeHorizonCullingPointPossiblyUnderEllipsoid(g,se,q),Qe=new e.e(K,Q,g),Xe=new o.u(Qe,Ve.hMin,J,G,!1,T),Ze=new Float32Array(ee*Xe.getStride()),$e=0,et=0;et<ee;++et)$e=Xe.encode(Ze,$e,se[et],de[et],ce[et],void 0,he[et]);var tt=ge.map((function(e){return e.index})).reverse(),nt=me.map((function(e){return e.index})).reverse(),it=pe.map((function(e){return e.index})).reverse(),rt=ve.map((function(e){return e.index})).reverse();return nt.unshift(it[it.length-1]),nt.push(tt[0]),rt.unshift(tt[tt.length-1]),rt.push(it[0]),{vertices:Ze,indices:new Uint16Array(le),maximumHeight:J,minimumHeight:q,encoding:Xe,boundingSphere3D:Je,orientedBoundingBox:qe,occludeePointInScaledSpace:Ke,vertexCountWithoutSkirts:Oe,indexCountWithoutSkirts:Le,westIndicesSouthToNorth:tt,southIndicesEastToWest:nt,eastIndicesNorthToSouth:it,northIndicesWestToEast:rt}}(g.buffer,g.relativeToCenter,g.ellipsoid,g.rectangle,g.nativeRectangle,g.exaggeration,g.skirtHeight,g.includeWebMercatorT,g.negativeAltitudeExponentBias,g.negativeElevationThreshold),v=p.vertices;m.push(v.buffer);var I=p.indices;return m.push(I.buffer),{vertices:v.buffer,indices:I.buffer,numberOfAttributes:p.encoding.getStride(),minimumHeight:p.minimumHeight,maximumHeight:p.maximumHeight,boundingSphere3D:p.boundingSphere3D,orientedBoundingBox:p.orientedBoundingBox,occludeePointInScaledSpace:p.occludeePointInScaledSpace,encoding:p.encoding,vertexCountWithoutSkirts:p.vertexCountWithoutSkirts,indexCountWithoutSkirts:p.indexCountWithoutSkirts,westIndicesSouthToNorth:p.westIndicesSouthToNorth,southIndicesEastToWest:p.southIndicesEastToWest,eastIndicesNorthToSouth:p.eastIndicesNorthToSouth,northIndicesWestToEast:p.northIndicesWestToEast}}))}));
