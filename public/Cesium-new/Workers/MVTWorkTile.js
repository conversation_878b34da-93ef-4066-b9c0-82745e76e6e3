define(["./createTaskProcessorWorker","./when-515d5295","./ComponentDatatype-d430c7f7","./IndexDatatype-eefd5922","./WebGLConstants-77a84876","./Buffer-72562b71","./BoundingRectangle-409afd17","./Color-39e7bd91","./Check-3aa71481","./WindingOrder-8479ef05","./Cartesian4-034d54d5","./Math-5e38123d","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Intersect-53434a77","./Rectangle-e170be8b","./FeatureDetection-7fae0d5a"],(function(t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m){"use strict";var g=function(t,e,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,l=u>>1,c=-7,p=r?i-1:0,f=r?-1:1,h=t[e+p];for(p+=f,o=h&(1<<-c)-1,h>>=-c,c+=s;c>0;o=256*o+t[e+p],p+=f,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=n;c>0;a=256*a+t[e+p],p+=f,c-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,n),o-=l}return(h?-1:1)*a*Math.pow(2,o-n)},v=function(t,e,r,n,i,o){var a,s,u,l=8*o-i-1,c=(1<<l)-1,p=c>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:o-1,y=n?1:-1,d=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=c):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),(e+=a+p>=1?f/u:f*Math.pow(2,1-p))*u>=2&&(a++,u/=2),a+p>=c?(s=0,a=c):a+p>=1?(s=(e*u-1)*Math.pow(2,i),a+=p):(s=e*Math.pow(2,p-1)*Math.pow(2,i),a=0));i>=8;t[r+h]=255&s,h+=y,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;t[r+h]=255&a,h+=y,a/=256,l-=8);t[r+h-y]|=128*d},x={read:g,write:v};function b(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}b.Varint=0,b.Fixed64=1,b.Bytes=2,b.Fixed32=5;var _=4294967296,w=1/_;function S(t,e,r){var n,i,o=r.buf;if(n=(112&(i=o[r.pos++]))>>4,i<128||(n|=(127&(i=o[r.pos++]))<<3,i<128)||(n|=(127&(i=o[r.pos++]))<<10,i<128)||(n|=(127&(i=o[r.pos++]))<<17,i<128)||(n|=(127&(i=o[r.pos++]))<<24,i<128)||(n|=(1&(i=o[r.pos++]))<<31,i<128))return E(t,n,e);throw new Error("Expected varint not more than 10 bytes")}function A(t){return t.type===b.Bytes?t.readVarint()+t.pos:t.pos+1}function E(t,e,r){return r?4294967296*e+(t>>>0):4294967296*(e>>>0)+(t>>>0)}function T(t,e){var r,n;if(t>=0?(r=t%4294967296|0,n=t/4294967296|0):(n=~(-t/4294967296),4294967295^(r=~(-t%4294967296))?r=r+1|0:(r=0,n=n+1|0)),t>=0x10000000000000000||t<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");e.realloc(10),I(r,n,e),O(n,e)}function I(t,e,r){r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos]=127&t}function O(t,e){var r=(7&t)<<4;e.buf[e.pos++]|=r|((t>>>=3)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t)))))}function k(t,e,r){var n=e<=16383?1:e<=2097151?2:e<=268435455?3:Math.ceil(Math.log(e)/(7*Math.LN2));r.realloc(n);for(var i=r.pos-1;i>=t;i--)r.buf[i+n]=r.buf[i]}function F(t,e){for(var r=0;r<t.length;r++)e.writeVarint(t[r])}function R(t,e){for(var r=0;r<t.length;r++)e.writeSVarint(t[r])}function M(t,e){for(var r=0;r<t.length;r++)e.writeFloat(t[r])}function C(t,e){for(var r=0;r<t.length;r++)e.writeDouble(t[r])}function P(t,e){for(var r=0;r<t.length;r++)e.writeBoolean(t[r])}function z(t,e){for(var r=0;r<t.length;r++)e.writeFixed32(t[r])}function B(t,e){for(var r=0;r<t.length;r++)e.writeSFixed32(t[r])}function D(t,e){for(var r=0;r<t.length;r++)e.writeFixed64(t[r])}function U(t,e){for(var r=0;r<t.length;r++)e.writeSFixed64(t[r])}function N(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+16777216*t[e+3]}function V(t,e,r){t[r]=e,t[r+1]=e>>>8,t[r+2]=e>>>16,t[r+3]=e>>>24}function L(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+(t[e+3]<<24)}function $(t,e,r){for(var n="",i=e;i<r;){var o,a,s,u=t[i],l=null,c=u>239?4:u>223?3:u>191?2:1;if(i+c>r)break;1===c?u<128&&(l=u):2===c?128==(192&(o=t[i+1]))&&((l=(31&u)<<6|63&o)<=127&&(l=null)):3===c?(o=t[i+1],a=t[i+2],128==(192&o)&&128==(192&a)&&(((l=(15&u)<<12|(63&o)<<6|63&a)<=2047||l>=55296&&l<=57343)&&(l=null))):4===c&&(o=t[i+1],a=t[i+2],s=t[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(((l=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)<=65535||l>=1114112)&&(l=null))),null===l?(l=65533,c=1):l>65535&&(l-=65536,n+=String.fromCharCode(l>>>10&1023|55296),l=56320|1023&l),n+=String.fromCharCode(l),i+=c}return n}function X(t,e,r){for(var n,i,o=0;o<e.length;o++){if((n=e.charCodeAt(o))>55295&&n<57344){if(!i){n>56319||o+1===e.length?(t[r++]=239,t[r++]=191,t[r++]=189):i=n;continue}if(n<56320){t[r++]=239,t[r++]=191,t[r++]=189,i=n;continue}n=i-55296<<10|n-56320|65536,i=null}else i&&(t[r++]=239,t[r++]=191,t[r++]=189,i=null);n<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:(n<65536?t[r++]=n>>12|224:(t[r++]=n>>18|240,t[r++]=n>>12&63|128),t[r++]=n>>6&63|128),t[r++]=63&n|128)}return r}b.prototype={destroy:function(){this.buf=null},readFields:function(t,e,r){for(r=r||this.length;this.pos<r;){var n=this.readVarint(),i=n>>3,o=this.pos;this.type=7&n,t(i,e,this),this.pos===o&&this.skip(n)}return e},readMessage:function(t,e){return this.readFields(t,e,this.readVarint()+this.pos)},readFixed32:function(){var t=N(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=L(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=N(this.buf,this.pos)+N(this.buf,this.pos+4)*_;return this.pos+=8,t},readSFixed64:function(){var t=N(this.buf,this.pos)+L(this.buf,this.pos+4)*_;return this.pos+=8,t},readFloat:function(){var t=x.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=x.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var e,r,n=this.buf;return e=127&(r=n[this.pos++]),r<128||(e|=(127&(r=n[this.pos++]))<<7,r<128)||(e|=(127&(r=n[this.pos++]))<<14,r<128)||(e|=(127&(r=n[this.pos++]))<<21,r<128)?e:S(e|=(15&(r=n[this.pos]))<<28,t,this)},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,e=$(this.buf,this.pos,t);return this.pos=t,e},readBytes:function(){var t=this.readVarint()+this.pos,e=this.buf.subarray(this.pos,t);return this.pos=t,e},readPackedVarint:function(t,e){var r=A(this);for(t=t||[];this.pos<r;)t.push(this.readVarint(e));return t},readPackedSVarint:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readBoolean());return t},readPackedFloat:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readFloat());return t},readPackedDouble:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readDouble());return t},readPackedFixed32:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){var e=A(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed64());return t},skip:function(t){var e=7&t;if(e===b.Varint)for(;this.buf[this.pos++]>127;);else if(e===b.Bytes)this.pos=this.readVarint()+this.pos;else if(e===b.Fixed32)this.pos+=4;else{if(e!==b.Fixed64)throw new Error("Unimplemented type: "+e);this.pos+=8}},writeTag:function(t,e){this.writeVarint(t<<3|e)},realloc:function(t){for(var e=this.length||16;e<this.pos+t;)e*=2;if(e!==this.length){var r=new Uint8Array(e);r.set(this.buf),this.buf=r,this.length=e}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),V(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),V(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),V(this.buf,-1&t,this.pos),V(this.buf,Math.floor(t*w),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),V(this.buf,-1&t,this.pos),V(this.buf,Math.floor(t*w),this.pos+4),this.pos+=8},writeVarint:function(t){(t=+t||0)>268435455||t<0?T(t,this):(this.realloc(4),this.buf[this.pos++]=127&t|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=t>>>7&127))))},writeSVarint:function(t){this.writeVarint(t<0?2*-t-1:2*t)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(4*t.length),this.pos++;var e=this.pos;this.pos=X(this.buf,t,this.pos);var r=this.pos-e;r>=128&&k(e,r,this),this.pos=e-1,this.writeVarint(r),this.pos+=r},writeFloat:function(t){this.realloc(4),x.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),x.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var e=t.length;this.writeVarint(e),this.realloc(e);for(var r=0;r<e;r++)this.buf[this.pos++]=t[r]},writeRawMessage:function(t,e){this.pos++;var r=this.pos;t(e,this);var n=this.pos-r;n>=128&&k(r,n,this),this.pos=r-1,this.writeVarint(n),this.pos+=n},writeMessage:function(t,e,r){this.writeTag(t,b.Bytes),this.writeRawMessage(e,r)},writePackedVarint:function(t,e){this.writeMessage(t,F,e)},writePackedSVarint:function(t,e){this.writeMessage(t,R,e)},writePackedBoolean:function(t,e){this.writeMessage(t,P,e)},writePackedFloat:function(t,e){this.writeMessage(t,M,e)},writePackedDouble:function(t,e){this.writeMessage(t,C,e)},writePackedFixed32:function(t,e){this.writeMessage(t,z,e)},writePackedSFixed32:function(t,e){this.writeMessage(t,B,e)},writePackedFixed64:function(t,e){this.writeMessage(t,D,e)},writePackedSFixed64:function(t,e){this.writeMessage(t,U,e)},writeBytesField:function(t,e){this.writeTag(t,b.Bytes),this.writeBytes(e)},writeFixed32Field:function(t,e){this.writeTag(t,b.Fixed32),this.writeFixed32(e)},writeSFixed32Field:function(t,e){this.writeTag(t,b.Fixed32),this.writeSFixed32(e)},writeFixed64Field:function(t,e){this.writeTag(t,b.Fixed64),this.writeFixed64(e)},writeSFixed64Field:function(t,e){this.writeTag(t,b.Fixed64),this.writeSFixed64(e)},writeVarintField:function(t,e){this.writeTag(t,b.Varint),this.writeVarint(e)},writeSVarintField:function(t,e){this.writeTag(t,b.Varint),this.writeSVarint(e)},writeStringField:function(t,e){this.writeTag(t,b.Bytes),this.writeString(e)},writeFloatField:function(t,e){this.writeTag(t,b.Fixed32),this.writeFloat(e)},writeDoubleField:function(t,e){this.writeTag(t,b.Fixed64),this.writeDouble(e)},writeBooleanField:function(t,e){this.writeVarintField(t,Boolean(e))}};var j,q=(j=!0,function(t,e){var r=j?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return j=!1,r}),H=q(void 0,(function(){return H.toString().search("(((.+)+)+)+$").toString().constructor(H).search("(((.+)+)+)+$")}));function Y(t){this._stringToNumber={},this._numberToString=[];for(var e=0;e<t.length;e++){var r=t[e];this._stringToNumber[r]=e,this._numberToString[e]=r}}H(),Y.prototype.encode=function(t){return this._stringToNumber[t]},Y.prototype.decode=function(t){return this._numberToString[t]};var W,G=(W=!0,function(t,e){var r=W?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return W=!1,r}),Q=G(void 0,(function(){return Q.toString().search("(((.+)+)+)+$").toString().constructor(Q).search("(((.+)+)+)+$")}));Q();var K={Int8:Int8Array,Uint8:Uint8Array,Int16:Int16Array,Uint16:Uint16Array,Int32:Int32Array,Uint32:Uint32Array,Float32:Float32Array};function J(t,e){void 0===e&&(e=1);var r=0,n=0;return{members:t.map((function(t){var i=Z(t.type),o=r=tt(r,Math.max(e,i)),a=t.components||1;return n=Math.max(n,i),r+=i*a,{name:t.name,type:t.type,components:a,offset:o}})),size:tt(r,Math.max(n,e)),alignment:e}}function Z(t){return K[t].BYTES_PER_ELEMENT}function tt(t,e){return Math.ceil(t/e)*e}var et,rt=(et=!0,function(t,e){var r=et?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return et=!1,r}),nt=rt(void 0,(function(){return nt.toString().search("(((.+)+)+)+$").toString().constructor(nt).search("(((.+)+)+)+$")}));nt();var it=1,ot=function(t,e){var r=e.pixelRatio,n=e.version,i=e.stretchX,o=e.stretchY,a=e.content;this.paddedRect=t,this.pixelRatio=r,this.stretchX=i,this.stretchY=o,this.content=a,this.version=n},at={tl:{configurable:!0},br:{configurable:!0},tlbr:{configurable:!0},displaySize:{configurable:!0}};at.tl.get=function(){return[this.paddedRect.x+it,this.paddedRect.y+it]},at.br.get=function(){return[this.paddedRect.x+this.paddedRect.w-it,this.paddedRect.y+this.paddedRect.h-it]},at.tlbr.get=function(){return this.tl.concat(this.br)},at.displaySize.get=function(){return[(this.paddedRect.w-2*it)/this.pixelRatio,(this.paddedRect.h-2*it)/this.pixelRatio]},Object.defineProperties(ot.prototype,at);var st,ut=(st=!0,function(t,e){var r=st?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return st=!1,r}),lt=ut(void 0,(function(){return lt.toString().search("(((.+)+)+)+$").toString().constructor(lt).search("(((.+)+)+)+$")}));lt();var ct=function(t,e){pt(this,t,4,e)};function pt(t,e,r,n){var i=e.width,o=e.height;if(n){if(n instanceof Uint8ClampedArray)n=new Uint8Array(n.buffer);else if(n.length!==i*o*r)throw new RangeError("mismatched image size")}else n=new Uint8Array(i*o*r);return t.width=i,t.height=o,t.data=n,t}function ft(t,e,r){var n=e.width,i=e.height;if(n!==t.width||i!==t.height){var o=pt({},{width:n,height:i},r);ht(t,o,{x:0,y:0},{x:0,y:0},{width:Math.min(t.width,n),height:Math.min(t.height,i)},r),t.width=n,t.height=i,t.data=o.data}}function ht(t,e,r,n,i,o){if(0===i.width||0===i.height)return e;if(i.width>t.width||i.height>t.height||r.x>t.width-i.width||r.y>t.height-i.height)return console.log("out of range source coordinates for image copy"),e;if(i.width>e.width||i.height>e.height||n.x>e.width-i.width||n.y>e.height-i.height)return console.log("out of range destination coordinates for image copy"),e;for(var a=t.data,s=e.data,u=0;u<i.height;u++)for(var l=((r.y+u)*t.width+r.x)*o,c=((n.y+u)*e.width+n.x)*o,p=0;p<i.width*o;p++)s[c+p]=a[l+p];return e}function yt(t){for(var e=0,r=0,n=0,i=t;n<i.length;n+=1){var o=i[n];e+=o.w*o.h,r=Math.max(r,o.w)}t.sort((function(t,e){return e.h-t.h}));for(var a=[{x:0,y:0,w:Math.max(Math.ceil(Math.sqrt(e/.95)),r),h:1/0}],s=0,u=0,l=0,c=t;l<c.length;l+=1)for(var p=c[l],f=a.length-1;f>=0;f--){var h=a[f];if(!(p.w>h.w||p.h>h.h)){if(p.x=h.x,p.y=h.y,u=Math.max(u,p.y+p.h),s=Math.max(s,p.x+p.w),p.w===h.w&&p.h===h.h){var y=a.pop();f<a.length&&(a[f]=y)}else p.h===h.h?(h.x+=p.w,h.w-=p.w):p.w===h.w?(h.y+=p.h,h.h-=p.h):(a.push({x:h.x+p.w,y:h.y,w:h.w-p.w,h:p.h}),h.y+=p.h,h.h-=p.h);break}}return{w:s,h:u,fill:e/(s*u)||0}}ct.prototype.resize=function(t){ft(this,t,4)},ct.prototype.replace=function(t,e){e?this.data.set(t):t instanceof Uint8ClampedArray?this.data=new Uint8Array(t.buffer):this.data=t},ct.prototype.clone=function(){return new ct({width:this.width,height:this.height},new Uint8Array(this.data))},ct.copy=function(t,e,r,n,i){ht(t,e,r,n,i,4)};var dt,mt=(dt=!0,function(t,e){var r=dt?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return dt=!1,r}),gt=mt(void 0,(function(){return gt.toString().search("(((.+)+)+)+$").toString().constructor(gt).search("(((.+)+)+)+$")}));gt();var vt=1,xt=function(t,e){var r={},n={};this.haveRenderCallbacks=[];var i=[];this.addImages(t,r,i),this.addImages(e,n,i);var o=yt(i),a=o.w,s=o.h,u=new ct({width:a||1,height:s||1});for(var l in t){var c=t[l],p=r[l].paddedRect;ct.copy(c.data,u,{x:0,y:0},{x:p.x+vt,y:p.y+vt},c.data)}for(var f in e){var h=e[f],y=n[f].paddedRect,d=y.x+vt,m=y.y+vt,g=h.data.width,v=h.data.height;ct.copy(h.data,u,{x:0,y:0},{x:d,y:m},h.data),ct.copy(h.data,u,{x:0,y:v-1},{x:d,y:m-1},{width:g,height:1}),ct.copy(h.data,u,{x:0,y:0},{x:d,y:m+v},{width:g,height:1}),ct.copy(h.data,u,{x:g-1,y:0},{x:d-1,y:m},{width:1,height:v}),ct.copy(h.data,u,{x:0,y:0},{x:d+g,y:m},{width:1,height:v})}this.image=u,this.iconPositions=r,this.patternPositions=n};xt.prototype.addImages=function(t,e,r){for(var n in t){var i=t[n],o={x:0,y:0,w:i.data.width+2*vt,h:i.data.height+2*vt};r.push(o),e[n]=new ot(o,i),i.hasRenderCallback&&this.haveRenderCallbacks.push(n)}},xt.prototype.patchUpdatedImages=function(t,e){for(var r in t.dispatchRenderCallbacks(this.haveRenderCallbacks),t.updatedImages)this.patchUpdatedImage(this.iconPositions[r],t.getImage(r),e),this.patchUpdatedImage(this.patternPositions[r],t.getImage(r),e)},xt.prototype.patchUpdatedImage=function(t,e,r){if(t&&e&&t.version!==e.version){t.version=e.version;var n=t.tl,i=n[0],o=n[1];r.update(e.data,void 0,{x:i,y:o})}};var bt,_t=(bt=!0,function(t,e){var r=bt?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return bt=!1,r}),wt=_t(void 0,(function(){return wt.toString().search("(((.+)+)+)+$").toString().constructor(wt).search("(((.+)+)+)+$")}));wt();var St={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],rebeccapurple:[102,51,153,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function At(t){return(t=Math.round(t))<0?0:t>255?255:t}function Et(t){return t<0?0:t>1?1:t}function Tt(t){return"%"===t[t.length-1]?At(parseFloat(t)/100*255):At(parseInt(t))}function It(t){return"%"===t[t.length-1]?Et(parseFloat(t)/100):Et(parseFloat(t))}function Ot(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function kt(t){var e,r=t.replace(/ /g,"").toLowerCase();if(r in St)return St[r].slice();if("#"===r[0])return 4===r.length?(e=parseInt(r.substr(1),16))>=0&&e<=4095?[(3840&e)>>4|(3840&e)>>8,240&e|(240&e)>>4,15&e|(15&e)<<4,1]:null:7===r.length&&(e=parseInt(r.substr(1),16))>=0&&e<=16777215?[(16711680&e)>>16,(65280&e)>>8,255&e,1]:null;var n=r.indexOf("("),i=r.indexOf(")");if(-1!==n&&i+1===r.length){var o=r.substr(0,n),a=r.substr(n+1,i-(n+1)).split(","),s=1;switch(o){case"rgba":if(4!==a.length)return null;s=It(a.pop());case"rgb":return 3!==a.length?null:[Tt(a[0]),Tt(a[1]),Tt(a[2]),s];case"hsla":if(4!==a.length)return null;s=It(a.pop());case"hsl":if(3!==a.length)return null;var u=(parseFloat(a[0])%360+360)%360/360,l=It(a[1]),c=It(a[2]),p=c<=.5?c*(l+1):c+l-c*l,f=2*c-p;return[At(255*Ot(f,p,u+1/3)),At(255*Ot(f,p,u)),At(255*Ot(f,p,u-1/3)),s];default:return null}}return null}const Ft=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Rt=Ft(void 0,(function(){return Rt.toString().search("(((.+)+)+)+$").toString().constructor(Rt).search("(((.+)+)+)+$")}));Rt();class Mt{constructor(t,e,r,n=1){this.r=t,this.g=e,this.b=r,this.a=n}static parse(t){if(!t)return;if(t instanceof Mt)return t;if("string"!=typeof t)return;const e=kt(t);return e?new Mt(e[0]/255*e[3],e[1]/255*e[3],e[2]/255*e[3],e[3]):void 0}toString(){const[t,e,r,n]=this.toArray();return"rgba("+Math.round(t)+","+Math.round(e)+","+Math.round(r)+","+n+")"}toArray(){const{r:t,g:e,b:r,a:n}=this;return 0===n?[0,0,0,0]:[255*t/n,255*e/n,255*r/n,n]}}Mt.black=new Mt(0,0,0,1),Mt.white=new Mt(1,1,1,1),Mt.transparent=new Mt(0,0,0,0),Mt.red=new Mt(1,0,0,1);var Ct={kind:"null"},Pt={kind:"number"},zt={kind:"string"},Bt={kind:"boolean"},Dt={kind:"color"},Ut={kind:"object"},Nt={kind:"value"},Vt={kind:"formatted"},Lt={kind:"resolvedImage"};function $t(t,e){var r,n=(r=!0,function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}),i=n(this,(function(){return i.toString().search("(((.+)+)+)+$").toString().constructor(i).search("(((.+)+)+)+$")}));return i(),{kind:"array",itemType:t,N:e}}function Xt(t){if("array"===t.kind){var e=Xt(t.itemType);return"number"==typeof t.N?"array<"+e+", "+t.N+">":"value"===t.itemType.kind?"array":"array<"+e+">"}return t.kind}var jt=[Ct,Pt,zt,Bt,Dt,Vt,Ut,$t(Nt),Lt];function qt(t,e){if("error"===e.kind)return null;if("array"===t.kind){if("array"===e.kind&&(0===e.N&&"value"===e.itemType.kind||!qt(t.itemType,e.itemType))&&("number"!=typeof t.N||t.N===e.N))return null}else{if(t.kind===e.kind)return null;if("value"===t.kind)for(var r=0,n=jt;r<n.length;r+=1){if(!qt(n[r],e))return null}}return"Expected "+Xt(t)+" but found "+Xt(e)+" instead."}var Ht,Yt=(Ht=!0,function(t,e){var r=Ht?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ht=!1,r}),Wt=Yt(void 0,(function(){return Wt.toString().search("(((.+)+)+)+$").toString().constructor(Wt).search("(((.+)+)+)+$")}));Wt();var Gt=function(t,e,r){this.sensitivity=t?e?"variant":"case":e?"accent":"base",this.locale=r,this.collator=new Intl.Collator(this.locale?this.locale:[],{sensitivity:this.sensitivity,usage:"search"})};Gt.prototype.compare=function(t,e){return this.collator.compare(t,e)},Gt.prototype.resolvedLocale=function(){return new Intl.Collator(this.locale?this.locale:[]).resolvedOptions().locale};var Qt,Kt=(Qt=!0,function(t,e){var r=Qt?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Qt=!1,r}),Jt=Kt(void 0,(function(){return Jt.toString().search("(((.+)+)+)+$").toString().constructor(Jt).search("(((.+)+)+)+$")}));Jt();var Zt,te=function(t,e,r,n,i){this.text=t,this.image=e,this.scale=r,this.fontStack=n,this.textColor=i},ee=(Zt=!0,function(t,e){var r=Zt?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Zt=!1,r}),re=ee(void 0,(function(){return re.toString().search("(((.+)+)+)+$").toString().constructor(re).search("(((.+)+)+)+$")}));re();var ne=function(t){this.sections=t};ne.fromString=function(t){return new ne([new te(t,null,null,null,null)])},ne.prototype.isEmpty=function(){return 0===this.sections.length||!this.sections.some((function(t){return 0!==t.text.length||t.image&&0!==t.image.name.length}))},ne.factory=function(t){return t instanceof ne?t:ne.fromString(t)},ne.prototype.toString=function(){return 0===this.sections.length?"":this.sections.map((function(t){return t.text})).join("")},ne.prototype.serialize=function(){for(var t=["format"],e=0,r=this.sections;e<r.length;e+=1){var n=r[e];if(n.image)t.push(["image",n.image.name]);else{t.push(n.text);var i={};n.fontStack&&(i["text-font"]=["literal",n.fontStack.split(",")]),n.scale&&(i["font-scale"]=n.scale),n.textColor&&(i["text-color"]=["rgba"].concat(n.textColor.toArray())),t.push(i)}}return t};var ie,oe=(ie=!0,function(t,e){var r=ie?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ie=!1,r}),ae=oe(void 0,(function(){return ae.toString().search("(((.+)+)+)+$").toString().constructor(ae).search("(((.+)+)+)+$")}));ae();var se=function(t){this.name=t.name,this.available=t.available};se.prototype.toString=function(){return this.name},se.fromString=function(t){return new se({name:t,available:!1})},se.prototype.serialize=function(){return["image",this.name]};var ue,le=(ue=!0,function(t,e){var r=ue?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ue=!1,r}),ce=le(void 0,(function(){return ce.toString().search("(((.+)+)+)+$").toString().constructor(ce).search("(((.+)+)+)+$")}));ce();var pe={kind:"null"},fe={kind:"number"},he={kind:"string"},ye={kind:"boolean"},de={kind:"color"},me={kind:"object"},ge={kind:"value"},ve={kind:"collator"},xe={kind:"formatted"},be={kind:"resolvedImage"};function _e(t,e){return{kind:"array",itemType:t,N:e}}function we(){}we.validateRGBA=function(t,e,r,n){return"number"==typeof t&&t>=0&&t<=255&&"number"==typeof e&&e>=0&&e<=255&&"number"==typeof r&&r>=0&&r<=255?void 0===n||"number"==typeof n&&n>=0&&n<=1?null:"Invalid rgba value ["+[t,e,r,n].join(", ")+"]: 'a' must be between 0 and 1.":"Invalid rgba value ["+("number"==typeof n?[t,e,r,n]:[t,e,r]).join(", ")+"]: 'r', 'g', and 'b' must be between 0 and 255."},we.isValue=function(t){if(null===t)return!0;if("string"==typeof t)return!0;if("boolean"==typeof t)return!0;if("number"==typeof t)return!0;if(t instanceof Mt)return!0;if(t instanceof Gt)return!0;if(t instanceof ne)return!0;if(t instanceof se)return!0;if(Array.isArray(t)){for(var e=0,r=t;e<r.length;e+=1){var n=r[e];if(!we.isValue(n))return!1}return!0}if("object"==typeof t){for(var i in t)if(!we.isValue(t[i]))return!1;return!0}return!1},we.typeOf=function(t){if(null===t)return pe;if("string"==typeof t)return he;if("boolean"==typeof t)return ye;if("number"==typeof t)return fe;if(t instanceof Mt)return de;if(t instanceof Gt)return ve;if(t instanceof ne)return xe;if(t instanceof se)return be;if(Array.isArray(t)){for(var e,r=t.length,n=0,i=t;n<i.length;n+=1){var o=i[n],a=we.typeOf(o);if(e){if(e===a)continue;e=ge;break}e=a}return _e(e||ge,r)}return me},we.toString$1=function(t){var e=typeof t;return null===t?"":"string"===e||"number"===e||"boolean"===e?String(t):t instanceof Mt||t instanceof ne||t instanceof se?t.toString():JSON.stringify(t)};var Se,Ae=(Se=!0,function(t,e){var r=Se?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Se=!1,r}),Ee=Ae(void 0,(function(){return Ee.toString().search("(((.+)+)+)+$").toString().constructor(Ee).search("(((.+)+)+)+$")}));Ee();var Te={kind:"number"},Ie={kind:"string"},Oe={kind:"boolean"},ke={kind:"object"},Fe={kind:"value"};function Re(t,e){return{kind:"array",itemType:t,N:e}}var Me={string:Ie,number:Te,boolean:Oe,object:ke},Ce=function(t,e){this.type=t,this.args=e};Ce.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r,n=1,i=t[0];if("array"===i){var o,a;if(t.length>2){var s=t[1];if("string"!=typeof s||!(s in Me)||"object"===s)return e.error('The item type argument of "array" must be one of string, number, boolean',1);o=Me[s],n++}else o=Fe;if(t.length>3){if(null!==t[2]&&("number"!=typeof t[2]||t[2]<0||t[2]!==Math.floor(t[2])))return e.error('The length argument to "array" must be a positive integer literal',2);a=t[2],n++}r=Re(o,a)}else r=Me[i];for(var u=[];n<t.length;n++){var l=e.parse(t[n],n,Fe);if(!l)return null;u.push(l)}return new Ce(r,u)},Ce.prototype.evaluate=function(t){for(var e=0;e<this.args.length;e++){var r=this.args[e].evaluate(t);if(!qt(this.type,we.typeOf(r)))return r;if(e===this.args.length-1)throw new RuntimeError("Expected value to be of type "+toString(this.type)+", but found "+toString(we.typeOf(r))+" instead.")}return null},Ce.prototype.eachChild=function(t){this.args.forEach(t)},Ce.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},Ce.prototype.serialize=function(){var t=this.type,e=[t.kind];if("array"===t.kind){var r=t.itemType;if("string"===r.kind||"number"===r.kind||"boolean"===r.kind){e.push(r.kind);var n=t.N;("number"==typeof n||this.args.length>1)&&e.push(n)}}return e.concat(this.args.map((function(t){return t.serialize()})))};var Pe,ze=(Pe=!0,function(t,e){var r=Pe?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Pe=!1,r}),Be=ze(void 0,(function(){return Be.toString().search("(((.+)+)+)+$").toString().constructor(Be).search("(((.+)+)+)+$")}));Be();var De={kind:"number"},Ue={kind:"value"};function Ne(t,e){return{kind:"array",itemType:t,N:e}}var Ve=function(t,e,r){this.type=t,this.index=e,this.input=r};Ve.parse=function(t,e){if(3!==t.length)return e.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1,De),n=e.parse(t[2],2,Ne(e.expectedType||Ue));if(!r||!n)return null;var i=n.type;return new Ve(i.itemType,r,n)},Ve.prototype.evaluate=function(t){var e=this.index.evaluate(t),r=this.input.evaluate(t);if(e<0)throw new RuntimeError("Array index out of bounds: "+e+" < 0.");if(e>=r.length)throw new RuntimeError("Array index out of bounds: "+e+" > "+(r.length-1)+".");if(e!==Math.floor(e))throw new RuntimeError("Array index must be an integer, but found "+e+" instead.");return r[e]},Ve.prototype.eachChild=function(t){t(this.index),t(this.input)},Ve.prototype.possibleOutputs=function(){return[void 0]},Ve.prototype.serialize=function(){return["at",this.index.serialize(),this.input.serialize()]};var Le,$e=(Le=!0,function(t,e){var r=Le?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Le=!1,r}),Xe=$e(void 0,(function(){return Xe.toString().search("(((.+)+)+)+$").toString().constructor(Xe).search("(((.+)+)+)+$")}));Xe();var je={kind:"boolean"},qe=function(t,e,r){this.type=t,this.branches=e,this.otherwise=r};qe.parse=function(t,e){if(t.length<4)return e.error("Expected at least 3 arguments, but found only "+(t.length-1)+".");if(t.length%2!=0)return e.error("Expected an odd number of arguments.");var r;e.expectedType&&"value"!==e.expectedType.kind&&(r=e.expectedType);for(var n=[],i=1;i<t.length-1;i+=2){var o=e.parse(t[i],i,je);if(!o)return null;var a=e.parse(t[i+1],i+1,r);if(!a)return null;n.push([o,a]),r=r||a.type}var s=e.parse(t[t.length-1],t.length-1,r);return s?new qe(r,n,s):null},qe.prototype.evaluate=function(t){for(var e=0,r=this.branches;e<r.length;e+=1){var n=r[e],i=n[0],o=n[1];if(i.evaluate(t))return o.evaluate(t)}return this.otherwise.evaluate(t)},qe.prototype.eachChild=function(t){for(var e=0,r=this.branches;e<r.length;e+=1){var n=r[e],i=n[0],o=n[1];t(i),t(o)}t(this.otherwise)},qe.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.branches.map((function(t){return t[0],t[1].possibleOutputs()}))).concat(this.otherwise.possibleOutputs())},qe.prototype.serialize=function(){var t=["case"];return this.eachChild((function(e){t.push(e.serialize())})),t};var He,Ye=(He=!0,function(t,e){var r=He?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return He=!1,r}),We=Ye(void 0,(function(){return We.toString().search("(((.+)+)+)+$").toString().constructor(We).search("(((.+)+)+)+$")}));We();var Ge={kind:"value"},Qe=function(t,e){this.type=t,this.args=e};Qe.parse=function(t,e){if(t.length<2)return e.error("Expectected at least one argument.");var r=null,n=e.expectedType;n&&"value"!==n.kind&&(r=n);for(var i=[],o=0,a=t.slice(1);o<a.length;o+=1){var s=a[o],u=e.parse(s,1+i.length,r,void 0,{typeAnnotation:"omit"});if(!u)return null;r=r||u.type,i.push(u)}var l=n&&i.some((function(t){return qt(n,t.type)}));return new Qe(l?Ge:r,i)},Qe.prototype.evaluate=function(t){for(var e,r=null,n=0,i=0,o=this.args;i<o.length;i+=1){if(n++,(r=o[i].evaluate(t))&&r instanceof se&&!r.available&&(!e&&(e=r.name),r=null,n===this.args.length&&(r=e)),null!==r)break}return r},Qe.prototype.eachChild=function(t){this.args.forEach(t)},Qe.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},Qe.prototype.serialize=function(){var t=["coalesce"];return this.eachChild((function(e){t.push(e.serialize())})),t};var Ke,Je=(Ke=!0,function(t,e){var r=Ke?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ke=!1,r}),Ze=Je(void 0,(function(){return Ze.toString().search("(((.+)+)+)+$").toString().constructor(Ze).search("(((.+)+)+)+$")}));Ze();var tr={kind:"number"},er={kind:"string"},rr={kind:"boolean"},nr={kind:"color"},ir={kind:"value"},or={"to-boolean":rr,"to-color":nr,"to-number":tr,"to-string":er},ar=function(t,e){this.type=t,this.args=e};ar.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r=t[0];if(("to-boolean"===r||"to-string"===r)&&2!==t.length)return e.error("Expected one argument.");for(var n=or[r],i=[],o=1;o<t.length;o++){var a=e.parse(t[o],o,ir);if(!a)return null;i.push(a)}return new ar(n,i)},ar.prototype.evaluate=function(t){if("boolean"===this.type.kind)return Boolean(this.args[0].evaluate(t));if("color"===this.type.kind){for(var e,r,n=0,i=this.args;n<i.length;n+=1){if(r=null,(e=i[n].evaluate(t))instanceof Mt)return e;if("string"==typeof e){var o=t.parseColor(e);if(o)return o}else if(Array.isArray(e)&&!(r=e.length<3||e.length>4?"Invalid rbga value "+JSON.stringify(e)+": expected an array containing either three or four numeric values.":validateRGBA(e[0],e[1],e[2],e[3])))return new Mt(e[0]/255,e[1]/255,e[2]/255,e[3])}throw new RuntimeError(r||"Could not parse color from value '"+("string"==typeof e?e:String(JSON.stringify(e)))+"'")}if("number"===this.type.kind){for(var a=null,s=0,u=this.args;s<u.length;s+=1){if(null===(a=u[s].evaluate(t)))return 0;var l=Number(a);if(!isNaN(l))return l}throw new RuntimeError("Could not convert "+JSON.stringify(a)+" to number.")}return"formatted"===this.type.kind?Formatted.fromString(we.toString$1(this.args[0].evaluate(t))):"resolvedImage"===this.type.kind?se.fromString(we.toString$1(this.args[0].evaluate(t))):we.toString$1(this.args[0].evaluate(t))},ar.prototype.eachChild=function(t){this.args.forEach(t)},ar.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},ar.prototype.serialize=function(){if("formatted"===this.type.kind)return new FormatExpression([{content:this.args[0],scale:null,font:null,textColor:null}]).serialize();if("resolvedImage"===this.type.kind)return new ImageExpression(this.args[0]).serialize();var t=["to-"+this.type.kind];return this.eachChild((function(e){t.push(e.serialize())})),t};var sr,ur=(sr=!0,function(t,e){var r=sr?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return sr=!1,r}),lr=ur(void 0,(function(){return lr.toString().search("(((.+)+)+)+$").toString().constructor(lr).search("(((.+)+)+)+$")}));lr();var cr={kind:"string"},pr={kind:"boolean"},fr={kind:"collator"},hr=function(t,e,r){this.type=fr,this.locale=r,this.caseSensitive=t,this.diacriticSensitive=e};hr.parse=function(t,e){if(2!==t.length)return e.error("Expected one argument.");var r=t[1];if("object"!=typeof r||Array.isArray(r))return e.error("Collator options argument must be an object.");var n=e.parse(void 0!==r["case-sensitive"]&&r["case-sensitive"],1,pr);if(!n)return null;var i=e.parse(void 0!==r["diacritic-sensitive"]&&r["diacritic-sensitive"],1,pr);if(!i)return null;var o=null;return r.locale&&!(o=e.parse(r.locale,1,cr))?null:new hr(n,i,o)},hr.prototype.evaluate=function(t){return new Gt(this.caseSensitive.evaluate(t),this.diacriticSensitive.evaluate(t),this.locale?this.locale.evaluate(t):null)},hr.prototype.eachChild=function(t){t(this.caseSensitive),t(this.diacriticSensitive),this.locale&&t(this.locale)},hr.prototype.possibleOutputs=function(){return[void 0]},hr.prototype.serialize=function(){var t={};return t["case-sensitive"]=this.caseSensitive.serialize(),t["diacritic-sensitive"]=this.diacriticSensitive.serialize(),this.locale&&(t.locale=this.locale.serialize()),["collator",t]};var yr={kind:"boolean"},dr={kind:"value"},mr={kind:"collator"};function gr(t,e){return"=="===t||"!="===t?"boolean"===e.kind||"string"===e.kind||"number"===e.kind||"null"===e.kind||"value"===e.kind:"string"===e.kind||"number"===e.kind||"value"===e.kind}function vr(t,e,r){return e===r}function xr(t,e,r){return e!==r}function br(t,e,r){return e<r}function _r(t,e,r){return e>r}function wr(t,e,r){return e<=r}function Sr(t,e,r){return e>=r}function Ar(t,e,r,n){return 0===n.compare(e,r)}function Er(t,e,r,n){return!Ar(0,e,r,n)}function Tr(t,e,r,n){return n.compare(e,r)<0}function Ir(t,e,r,n){return n.compare(e,r)>0}function Or(t,e,r,n){return n.compare(e,r)<=0}function kr(t,e,r,n){return n.compare(e,r)>=0}function Fr(t,e,r){var n,i=(n=!0,function(t,e){var r=n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return n=!1,r}),o="=="!==t&&"!="!==t;return function(){var n=i(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function a(t,e,r){this.type=yr,this.lhs=t,this.rhs=e,this.collator=r,this.hasUntypedArgument="value"===t.type.kind||"value"===e.type.kind}return n(),a.parse=function(t,e){if(3!==t.length&&4!==t.length)return e.error("Expected two or three arguments.");var r=t[0],n=e.parse(t[1],1,dr);if(!n)return null;if(!gr(r,n.type))return e.concat(1).error('"'+r+"\" comparisons are not supported for type '"+toString(n.type)+"'.");var i=e.parse(t[2],2,dr);if(!i)return null;if(!gr(r,i.type))return e.concat(2).error('"'+r+"\" comparisons are not supported for type '"+toString(i.type)+"'.");if(n.type.kind!==i.type.kind&&"value"!==n.type.kind&&"value"!==i.type.kind)return e.error("Cannot compare types '"+toString(n.type)+"' and '"+toString(i.type)+"'.");o&&("value"===n.type.kind&&"value"!==i.type.kind?n=new Ce(i.type,[n]):"value"!==n.type.kind&&"value"===i.type.kind&&(i=new Ce(n.type,[i])));var s=null;if(4===t.length){if("string"!==n.type.kind&&"string"!==i.type.kind&&"value"!==n.type.kind&&"value"!==i.type.kind)return e.error("Cannot use collator to compare non-string types.");if(!(s=e.parse(t[3],3,mr)))return null}return new a(n,i,s)},a.prototype.evaluate=function(n){var i=this.lhs.evaluate(n),a=this.rhs.evaluate(n);if(o&&this.hasUntypedArgument){var s=we.typeOf(i),u=we.typeOf(a);if(s.kind!==u.kind||"string"!==s.kind&&"number"!==s.kind)throw new RuntimeError('Expected arguments for "'+t+'" to be (string, string) or (number, number), but found ('+s.kind+", "+u.kind+") instead.")}if(this.collator&&!o&&this.hasUntypedArgument){var l=we.typeOf(i),c=we.typeOf(a);if("string"!==l.kind||"string"!==c.kind)return e(n,i,a)}return this.collator?r(n,i,a,this.collator.evaluate(n)):e(n,i,a)},a.prototype.eachChild=function(t){t(this.lhs),t(this.rhs),this.collator&&t(this.collator)},a.prototype.possibleOutputs=function(){return[!0,!1]},a.prototype.serialize=function(){var e=[t];return this.eachChild((function(t){e.push(t.serialize())})),e},a}()}var Rr={};Rr.Equals=Fr("==",vr,Ar),Rr.NotEquals=Fr("!=",xr,Er),Rr.LessThan=Fr("<",br,Tr),Rr.GreaterThan=Fr(">",_r,Ir),Rr.LessThanOrEqual=Fr("<=",wr,Or),Rr.GreaterThanOrEqual=Fr(">=",Sr,kr);var Mr,Cr=(Mr=!0,function(t,e){var r=Mr?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Mr=!1,r}),Pr=Cr(void 0,(function(){return Pr.toString().search("(((.+)+)+)+$").toString().constructor(Pr).search("(((.+)+)+)+$")}));Pr();var zr={kind:"number"},Br={kind:"string"},Dr={kind:"color"},Ur={kind:"value"},Nr={kind:"formatted"},Vr={kind:"resolvedImage"};function Lr(t,e){return{kind:"array",itemType:t,N:e}}var $r=function(t){this.type=Nr,this.sections=t};$r.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r=t[1];if(!Array.isArray(r)&&"object"==typeof r)return e.error("First argument must be an image or text section.");for(var n=[],i=!1,o=1;o<=t.length-1;++o){var a=t[o];if(i&&"object"==typeof a&&!Array.isArray(a)){i=!1;var s=null;if(a["font-scale"]&&!(s=e.parse(a["font-scale"],1,zr)))return null;var u=null;if(a["text-font"]&&!(u=e.parse(a["text-font"],1,Lr(Br))))return null;var l=null;if(a["text-color"]&&!(l=e.parse(a["text-color"],1,Dr)))return null;var c=n[n.length-1];c.scale=s,c.font=u,c.textColor=l}else{var p=e.parse(t[o],1,Ur);if(!p)return null;var f=p.type.kind;if("string"!==f&&"value"!==f&&"null"!==f&&"resolvedImage"!==f)return e.error("Formatted text type must be 'string', 'value', 'image' or 'null'.");i=!0,n.push({content:p,scale:null,font:null,textColor:null})}}return new $r(n)},$r.prototype.evaluate=function(t){return new ne(this.sections.map((function(e){var r=e.content.evaluate(t);return we.typeOf(r)===Vr?new te("",r,null,null,null):new te(we.toString$1(r),null,e.scale?e.scale.evaluate(t):null,e.font?e.font.evaluate(t).join(","):null,e.textColor?e.textColor.evaluate(t):null)})))},$r.prototype.eachChild=function(t){for(var e=0,r=this.sections;e<r.length;e+=1){var n=r[e];t(n.content),n.scale&&t(n.scale),n.font&&t(n.font),n.textColor&&t(n.textColor)}},$r.prototype.possibleOutputs=function(){return[void 0]},$r.prototype.serialize=function(){for(var t=["format"],e=0,r=this.sections;e<r.length;e+=1){var n=r[e];t.push(n.content.serialize());var i={};n.scale&&(i["font-scale"]=n.scale.serialize()),n.font&&(i["text-font"]=n.font.serialize()),n.textColor&&(i["text-color"]=n.textColor.serialize()),t.push(i)}return t};var Xr,jr=(Xr=!0,function(t,e){var r=Xr?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Xr=!1,r}),qr=jr(void 0,(function(){return qr.toString().search("(((.+)+)+)+$").toString().constructor(qr).search("(((.+)+)+)+$")}));qr();var Hr={kind:"string"},Yr={kind:"resolvedImage"},Wr=function(t){this.type=Yr,this.input=t};Wr.parse=function(t,e){if(2!==t.length)return e.error("Expected two arguments.");var r=e.parse(t[1],1,Hr);return r?new Wr(r):e.error("No image name provided.")},Wr.prototype.evaluate=function(t){var e=this.input.evaluate(t),r=!1;return t.availableImages&&t.availableImages.indexOf(e)>-1&&(r=!0),new se({name:e,available:r})},Wr.prototype.eachChild=function(t){t(this.input)},Wr.prototype.possibleOutputs=function(){return[void 0]},Wr.prototype.serialize=function(){return["image",this.input.serialize()]};var Gr,Qr=(Gr=!0,function(t,e){var r=Gr?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Gr=!1,r}),Kr=Qr(void 0,(function(){return Kr.toString().search("(((.+)+)+)+$").toString().constructor(Kr).search("(((.+)+)+)+$")}));Kr();var Jr=function(t,e,r,n,i){this.type=t,this.operator=e,this.interpolation=r,this.input=n,this.labels=[],this.outputs=[];for(var o=0,a=i;o<a.length;o+=1){var s=a[o],u=s[0],l=s[1];this.labels.push(u),this.outputs.push(l)}},Zr={kind:"number"},tn={kind:"color"};function en(t,e,r,n){var i=n-r,o=t-r;return 0===i?0:1===e?o/i:(Math.pow(e,o)-1)/(Math.pow(e,i)-1)}Jr.interpolationFactor=function(t,e,r,n){var i=0;if("exponential"===t.name)i=en(e,t.base,r,n);else if("linear"===t.name)i=en(e,1,r,n);else if("cubic-bezier"===t.name){var o=t.controlPoints;i=new unitbezier(o[0],o[1],o[2],o[3]).solve(en(e,1,r,n))}return i},Jr.parse=function(t,e){var r=t[0],n=t[1],i=t[2],o=t.slice(3);if(!Array.isArray(n)||0===n.length)return e.error("Expected an interpolation type expression.",1);if("linear"===n[0])n={name:"linear"};else if("exponential"===n[0]){var a=n[1];if("number"!=typeof a)return e.error("Exponential interpolation requires a numeric base.",1,1);n={name:"exponential",base:a}}else{if("cubic-bezier"!==n[0])return e.error("Unknown interpolation type "+String(n[0]),1,0);var s=n.slice(1);if(4!==s.length||s.some((function(t){return"number"!=typeof t||t<0||t>1})))return e.error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1.",1);n={name:"cubic-bezier",controlPoints:s}}if(t.length-1<4)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return e.error("Expected an even number of arguments.");if(!(i=e.parse(i,2,Zr)))return null;var u=[],l=null;"interpolate-hcl"===r||"interpolate-lab"===r?l=tn:e.expectedType&&"value"!==e.expectedType.kind&&(l=e.expectedType);for(var c=0;c<o.length;c+=2){var p=o[c],f=o[c+1],h=c+3,y=c+4;if("number"!=typeof p)return e.error('Input/output pairs for "interpolate" expressions must be defined using literal numeric values (not computed expressions) for the input values.',h);if(u.length&&u[u.length-1][0]>=p)return e.error('Input/output pairs for "interpolate" expressions must be arranged with input values in strictly ascending order.',h);var d=e.parse(f,y,l);if(!d)return null;l=l||d.type,u.push([p,d])}return"number"===l.kind||"color"===l.kind||"array"===l.kind&&"number"===l.itemType.kind&&"number"==typeof l.N?new Jr(l,r,n,i,u):e.error("Type "+toString(l)+" is not interpolatable.")},Jr.prototype.evaluate=function(t){var e=this.labels,r=this.outputs;if(1===e.length)return r[0].evaluate(t);var n=this.input.evaluate(t);if(n<=e[0])return r[0].evaluate(t);var i=e.length;if(n>=e[i-1])return r[i-1].evaluate(t);var o=findStopLessThanOrEqualTo(e,n),a=e[o],s=e[o+1],u=Jr.interpolationFactor(this.interpolation,n,a,s),l=r[o].evaluate(t),c=r[o+1].evaluate(t);return"interpolate"===this.operator?interpolate[this.type.kind.toLowerCase()](l,c,u):"interpolate-hcl"===this.operator?hcl.reverse(hcl.interpolate(hcl.forward(l),hcl.forward(c),u)):lab.reverse(lab.interpolate(lab.forward(l),lab.forward(c),u))},Jr.prototype.eachChild=function(t){t(this.input);for(var e=0,r=this.outputs;e<r.length;e+=1){t(r[e])}},Jr.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()})))},Jr.prototype.serialize=function(){var t;t="linear"===this.interpolation.name?["linear"]:"exponential"===this.interpolation.name?1===this.interpolation.base?["linear"]:["exponential",this.interpolation.base]:["cubic-bezier"].concat(this.interpolation.controlPoints);for(var e=[this.operator,t,this.input.serialize()],r=0;r<this.labels.length;r++)e.push(this.labels[r],this.outputs[r].serialize());return e};var rn,nn=(rn=!0,function(t,e){var r=rn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return rn=!1,r}),on=nn(void 0,(function(){return on.toString().search("(((.+)+)+)+$").toString().constructor(on).search("(((.+)+)+)+$")}));on();var an={kind:"boolean"},sn={kind:"value"};function un(t){return"boolean"===t.kind||"string"===t.kind||"number"===t.kind||"null"===t.kind||"value"===t.kind}function ln(t){return"boolean"==typeof t||"string"==typeof t||"number"==typeof t}function cn(t){return Array.isArray(t)||"string"==typeof t}var pn=function(t,e){this.type=an,this.needle=t,this.haystack=e};pn.parse=function(t,e){if(3!==t.length)return e.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1,sn),n=e.parse(t[2],2,sn);return r&&n?un(r.type)?new pn(r,n):e.error("Expected first argument to be of type boolean, string, number or null, but found "+toString(r.type)+" instead"):null},pn.prototype.evaluate=function(t){var e=this.needle.evaluate(t),r=this.haystack.evaluate(t);if(!e||!r)return!1;if(!ln(e))throw new RuntimeError("Expected first argument to be of type boolean, string or number, but found "+toString(typeOf(e))+" instead.");if(!cn(r))throw new RuntimeError("Expected second argument to be of type array or string, but found "+toString(typeOf(r))+" instead.");return r.indexOf(e)>=0},pn.prototype.eachChild=function(t){t(this.needle),t(this.haystack)},pn.prototype.possibleOutputs=function(){return[!0,!1]},pn.prototype.serialize=function(){return["in",this.needle.serialize(),this.haystack.serialize()]};var fn,hn=(fn=!0,function(t,e){var r=fn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return fn=!1,r}),yn=hn(void 0,(function(){return yn.toString().search("(((.+)+)+)+$").toString().constructor(yn).search("(((.+)+)+)+$")}));yn();var dn=function(t,e){this.type=e.type,this.bindings=[].concat(t),this.result=e};dn.prototype.evaluate=function(t){return this.result.evaluate(t)},dn.prototype.eachChild=function(t){for(var e=0,r=this.bindings;e<r.length;e+=1){t(r[e][1])}t(this.result)},dn.parse=function(t,e){if(t.length<4)return e.error("Expected at least 3 arguments, but found "+(t.length-1)+" instead.");for(var r=[],n=1;n<t.length-1;n+=2){var i=t[n];if("string"!=typeof i)return e.error("Expected string, but found "+typeof i+" instead.",n);if(/[^a-zA-Z0-9_]/.test(i))return e.error("Variable names must contain only alphanumeric characters or '_'.",n);var o=e.parse(t[n+1],n+1);if(!o)return null;r.push([i,o])}var a=e.parse(t[t.length-1],t.length-1,e.expectedType,r);return a?new dn(r,a):null},dn.prototype.possibleOutputs=function(){return this.result.possibleOutputs()},dn.prototype.serialize=function(){for(var t=["let"],e=0,r=this.bindings;e<r.length;e+=1){var n=r[e],i=n[0],o=n[1];t.push(i,o.serialize())}return t.push(this.result.serialize()),t};var mn,gn=(mn=!0,function(t,e){var r=mn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return mn=!1,r}),vn=gn(void 0,(function(){return vn.toString().search("(((.+)+)+)+$").toString().constructor(vn).search("(((.+)+)+)+$")}));vn();var xn={kind:"number"},bn=function(t){this.type=xn,this.input=t};bn.parse=function(t,e){if(2!==t.length)return e.error("Expected 1 argument, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1);return r?"array"!==r.type.kind&&"string"!==r.type.kind&&"value"!==r.type.kind?e.error("Expected argument of type string or array, but found "+toString(r.type)+" instead."):new bn(r):null},bn.prototype.evaluate=function(t){var e=this.input.evaluate(t);if("string"==typeof e)return e.length;if(Array.isArray(e))return e.length;throw new RuntimeError("Expected value to be of type string or array, but found "+toString(typeOf(e))+" instead.")},bn.prototype.eachChild=function(t){t(this.input)},bn.prototype.possibleOutputs=function(){return[void 0]},bn.prototype.serialize=function(){var t=["length"];return this.eachChild((function(e){t.push(e.serialize())})),t};var _n,wn=(_n=!0,function(t,e){var r=_n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return _n=!1,r}),Sn=wn(void 0,(function(){return Sn.toString().search("(((.+)+)+)+$").toString().constructor(Sn).search("(((.+)+)+)+$")}));Sn();var An=function(t,e){this.type=t,this.value=e};An.parse=function(t,e){if(2!==t.length)return e.error("'literal' expression requires exactly one argument, but found "+(t.length-1)+" instead.");if(!we.isValue(t[1]))return e.error("invalid value");var r=t[1],n=we.typeOf(r),i=e.expectedType;return"array"===n.kind&&0===n.N&&i&&"array"===i.kind&&("number"!=typeof i.N||0===i.N)&&(n=i),new An(n,r)},An.prototype.evaluate=function(){return this.value},An.prototype.eachChild=function(){},An.prototype.possibleOutputs=function(){return[this.value]},An.prototype.serialize=function(){return"array"===this.type.kind||"object"===this.type.kind?["literal",this.value]:this.value instanceof Color?["rgba"].concat(this.value.toArray()):this.value instanceof ne?this.value.serialize():this.value};var En,Tn=(En=!0,function(t,e){var r=En?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return En=!1,r}),In=Tn(void 0,(function(){return In.toString().search("(((.+)+)+)+$").toString().constructor(In).search("(((.+)+)+)+$")}));In();var On={kind:"value"},kn=function(t,e,r,n,i,o){this.inputType=t,this.type=e,this.input=r,this.cases=n,this.outputs=i,this.otherwise=o};kn.parse=function(t,e){if(t.length<5)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if(t.length%2!=1)return e.error("Expected an even number of arguments.");var r,n;e.expectedType&&"value"!==e.expectedType.kind&&(n=e.expectedType);for(var i={},o=[],a=2;a<t.length-1;a+=2){var s=t[a],u=t[a+1];!Array.isArray(s)&&(s=[s]);var l=e.concat(a);if(0===s.length)return l.error("Expected at least one branch label.");for(var c=0,p=s;c<p.length;c+=1){var f=p[c];if("number"!=typeof f&&"string"!=typeof f)return l.error("Branch labels must be numbers or strings.");if("number"==typeof f&&Math.abs(f)>Number.MAX_SAFE_INTEGER)return l.error("Branch labels must be integers no larger than "+Number.MAX_SAFE_INTEGER+".");if("number"==typeof f&&Math.floor(f)!==f)return l.error("Numeric branch labels must be integer values.");if(r){if(l.checkSubtype(r,we.typeOf(f)))return null}else r=we.typeOf(f);if(void 0!==i[String(f)])return l.error("Branch labels must be unique.");i[String(f)]=o.length}var h=e.parse(u,a,n);if(!h)return null;n=n||h.type,o.push(h)}var y=e.parse(t[1],1,On);if(!y)return null;var d=e.parse(t[t.length-1],t.length-1,n);return d?"value"!==y.type.kind&&e.concat(1).checkSubtype(r,y.type)?null:new kn(r,n,y,i,o,d):null},kn.prototype.evaluate=function(t){var e=this.input.evaluate(t);return(we.typeOf(e)===this.inputType&&this.outputs[this.cases[e]]||this.otherwise).evaluate(t)},kn.prototype.eachChild=function(t){t(this.input),this.outputs.forEach(t),t(this.otherwise)},kn.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()}))).concat(this.otherwise.possibleOutputs())},kn.prototype.serialize=function(){for(var t=this,e=["match",this.input.serialize()],r=[],n={},i=0,o=Object.keys(this.cases).sort();i<o.length;i+=1){var a=o[i];void 0===(p=n[this.cases[a]])?(n[this.cases[a]]=r.length,r.push([this.cases[a],[a]])):r[p][1].push(a)}for(var s=function(e){return"number"===t.inputType.kind?Number(e):e},u=0,l=r;u<l.length;u+=1){var c=l[u],p=c[0],f=c[1];1===f.length?e.push(s(f[0])):e.push(f.map(s)),e.push(this.outputs[outputIndex$1].serialize())}return e.push(this.otherwise.serialize()),e};var Fn,Rn=(Fn=!0,function(t,e){var r=Fn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Fn=!1,r}),Mn=Rn(void 0,(function(){return Mn.toString().search("(((.+)+)+)+$").toString().constructor(Mn).search("(((.+)+)+)+$")}));Mn();var Cn={kind:"number"},Pn={kind:"string"},zn=function(t,e,r,n,i){this.type=Pn,this.number=t,this.locale=e,this.currency=r,this.minFractionDigits=n,this.maxFractionDigits=i};zn.parse=function(t,e){if(3!==t.length)return e.error("Expected two arguments.");var r=e.parse(t[1],1,Cn);if(!r)return null;var n=t[2];if("object"!=typeof n||Array.isArray(n))return e.error("NumberFormat options argument must be an object.");var i=null;if(n.locale&&!(i=e.parse(n.locale,1,Pn)))return null;var o=null;if(n.currency&&!(o=e.parse(n.currency,1,Pn)))return null;var a=null;if(n["min-fraction-digits"]&&!(a=e.parse(n["min-fraction-digits"],1,Cn)))return null;var s=null;return n["max-fraction-digits"]&&!(s=e.parse(n["max-fraction-digits"],1,Cn))?null:new zn(r,i,o,a,s)},zn.prototype.evaluate=function(t){return new Intl.NumberFormat(this.locale?this.locale.evaluate(t):[],{style:this.currency?"currency":"decimal",currency:this.currency?this.currency.evaluate(t):void 0,minimumFractionDigits:this.minFractionDigits?this.minFractionDigits.evaluate(t):void 0,maximumFractionDigits:this.maxFractionDigits?this.maxFractionDigits.evaluate(t):void 0}).format(this.number.evaluate(t))},zn.prototype.eachChild=function(t){t(this.number),this.locale&&t(this.locale),this.currency&&t(this.currency),this.minFractionDigits&&t(this.minFractionDigits),this.maxFractionDigits&&t(this.maxFractionDigits)},zn.prototype.possibleOutputs=function(){return[void 0]},zn.prototype.serialize=function(){var t={};return this.locale&&(t.locale=this.locale.serialize()),this.currency&&(t.currency=this.currency.serialize()),this.minFractionDigits&&(t["min-fraction-digits"]=this.minFractionDigits.serialize()),this.maxFractionDigits&&(t["max-fraction-digits"]=this.maxFractionDigits.serialize()),["number-format",this.number.serialize(),t]};var Bn,Dn=(Bn=!0,function(t,e){var r=Bn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Bn=!1,r}),Un=Dn(void 0,(function(){return Un.toString().search("(((.+)+)+)+$").toString().constructor(Un).search("(((.+)+)+)+$")}));function Nn(t,e){for(var r,n,i=t.length-1,o=0,a=i,s=0;o<=a;)if(r=t[s=Math.floor((o+a)/2)],n=t[s+1],r<=e){if(s===i||e<n)return s;o=s+1}else{if(!(r>e))throw new RuntimeError("Input is not a number.");a=s-1}return 0}Un();var Vn,Ln=(Vn=!0,function(t,e){var r=Vn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Vn=!1,r}),$n=Ln(void 0,(function(){return $n.toString().search("(((.+)+)+)+$").toString().constructor($n).search("(((.+)+)+)+$")}));$n();var Xn={kind:"number"},jn=function(t,e,r){this.type=t,this.input=e,this.labels=[],this.outputs=[];for(var n=0,i=r;n<i.length;n+=1){var o=i[n],a=o[0],s=o[1];this.labels.push(a),this.outputs.push(s)}};jn.parse=function(t,e){if(t.length-1<4)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return e.error("Expected an even number of arguments.");var r=e.parse(t[1],1,Xn);if(!r)return null;var n=[],i=null;e.expectedType&&"value"!==e.expectedType.kind&&(i=e.expectedType);for(var o=1;o<t.length;o+=2){var a=1===o?-1/0:t[o],s=t[o+1],u=o,l=o+1;if("number"!=typeof a)return e.error('Input/output pairs for "step" expressions must be defined using literal numeric values (not computed expressions) for the input values.',u);if(n.length&&n[n.length-1][0]>=a)return e.error('Input/output pairs for "step" expressions must be arranged with input values in strictly ascending order.',u);var c=e.parse(s,l,i);if(!c)return null;i=i||c.type,n.push([a,c])}return new jn(i,r,n)},jn.prototype.evaluate=function(t){var e=this.labels,r=this.outputs;if(1===e.length)return r[0].evaluate(t);var n=this.input.evaluate(t);if(n<=e[0])return r[0].evaluate(t);var i=e.length;return n>=e[i-1]?r[i-1].evaluate(t):r[Nn(e,n)].evaluate(t)},jn.prototype.eachChild=function(t){t(this.input);for(var e=0,r=this.outputs;e<r.length;e+=1){t(r[e])}},jn.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()})))},jn.prototype.serialize=function(){for(var t=["step",this.input.serialize()],e=0;e<this.labels.length;e++)e>0&&t.push(this.labels[e]),t.push(this.outputs[e].serialize());return t};var qn,Hn=(qn=!0,function(t,e){var r=qn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return qn=!1,r}),Yn=Hn(void 0,(function(){return Yn.toString().search("(((.+)+)+)+$").toString().constructor(Yn).search("(((.+)+)+)+$")}));Yn();var Wn=function(t,e){this.type=e.type,this.name=t,this.boundExpression=e};Wn.parse=function(t,e){if(2!==t.length||"string"!=typeof t[1])return e.error("'var' expression requires exactly one string literal argument.");var r=t[1];return e.scope.has(r)?new Wn(r,e.scope.get(r)):e.error('Unknown variable "'+r+'". Make sure "'+r+'" has been bound in an enclosing "let" expression before using it.',1)},Wn.prototype.evaluate=function(t){return this.boundExpression.evaluate(t)},Wn.prototype.eachChild=function(){},Wn.prototype.possibleOutputs=function(){return[void 0]},Wn.prototype.serialize=function(){return["var",this.name]};var Gn,Qn=(Gn=!0,function(t,e){var r=Gn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Gn=!1,r}),Kn=Qn(void 0,(function(){return Kn.toString().search("(((.+)+)+)+$").toString().constructor(Kn).search("(((.+)+)+)+$")}));Kn();var Jn,Zn={"==":Rr.Equals,"!=":Rr.NotEquals,">":Rr.GreaterThan,"<":Rr.LessThan,">=":Rr.GreaterThanOrEqual,"<=":Rr.LessThanOrEqual,array:Ce,at:Ve,boolean:Ce,case:qe,coalesce:Qe,collator:hr,format:$r,image:Wr,in:pn,interpolate:Jr,"interpolate-hcl":Jr,"interpolate-lab":Jr,length:bn,let:dn,literal:An,match:kn,number:Ce,"number-format":zn,object:Ce,step:jn,string:Ce,"to-boolean":ar,"to-color":ar,"to-number":ar,"to-string":ar,var:Wn},ti=(Jn=!0,function(t,e){var r=Jn?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Jn=!1,r}),ei=ti(void 0,(function(){return ei.toString().search("(((.+)+)+)+$").toString().constructor(ei).search("(((.+)+)+)+$")}));function ri(){}ei();var ni={};for(var ii in ri.register=function(t,e,r){void 0===r&&(r={}),Object.defineProperty(e,"_classRegistryKey",{value:t,writeable:!1}),ni[t]={klass:e,omit:r.omit||[],shallow:r.shallow||[]}},ri.register("Object",Object),ri.register("Color",Mt),ri.register("Error",Error),ri.register("ResolvedImage",se),ri.register("ImageAtlas",xt),ri.register("ImagePosition",ot),ri.register("RGBAImage",ct),ri.register("Formatted",ne),ri.register("FormattedSection",te),Zn)Zn[ii]._classRegistryKey||ri.register("Expression_"+ii,Zn[ii]);function oi(t){return t&&"undefined"!=typeof ArrayBuffer&&(t instanceof ArrayBuffer||t.constructor&&"ArrayBuffer"===t.constructor.name)}ri.serialize=function(t,e){if(null==t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||t instanceof Boolean||t instanceof Number||t instanceof String||t instanceof Date||t instanceof RegExp)return t;if(oi(t))return e&&e.push(t),t;if(ArrayBuffer.isView(t)){var r=t;return e&&e.push(r.buffer),r}if(t instanceof ImageData)return e&&e.push(t.data.buffer),t;if(Array.isArray(t)){for(var n=[],i=0,o=t;i<o.length;i+=1){var a=o[i];n.push(ri.serialize(a,e))}return n}if("object"==typeof t){var s=t.constructor,u=s._classRegistryKey;if(!u)throw new Error("can't serialize object of unregistered class");var l=s.serialize?s.serialize(t,e):{};if(!s.serialize){for(var c in t)if(t.hasOwnProperty(c)&&!(ni[u].omit.indexOf(c)>=0)){var p=t[c];"function"!=typeof p&&(l[c]=ni[u].shallow.indexOf(c)>=0?p:ri.serialize(p,e))}t instanceof Error&&(l.message=t.message)}if(l.$name)throw new Error("$name property is reserved for worker serialization logic.");return"Object"!==u&&(l.$name=u),l}throw new Error("can't serialize object of type "+typeof t)},ri.deserialize=function(t){if(null==t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||t instanceof Boolean||t instanceof Number||t instanceof String||t instanceof Date||t instanceof RegExp||oi(t)||ArrayBuffer.isView(t)||t instanceof ImageData)return t;if(Array.isArray(t))return t.map(ri.deserialize);if("object"==typeof t){var e=t.$name||"Object",r=ni[e].klass;if(!r)throw new Error("can't deserialize unregistered class "+e);if(r.deserialize)return r.deserialize(t);for(var n=Object.create(r.prototype),i=0,o=Object.keys(t);i<o.length;i+=1){var a=o[i];if("$name"!==a){var s=t[a];n[a]=ni[e].shallow.indexOf(a)>=0?s:ri.deserialize(s)}}return n}throw new Error("can't deserialize object of type "+typeof t)};var ai,si=(ai=!0,function(t,e){var r=ai?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ai=!1,r}),ui=si(void 0,(function(){return ui.toString().search("(((.+)+)+)+$").toString().constructor(ui).search("(((.+)+)+)+$")}));ui();var li=function(){this.first=!0};li.prototype.update=function(t,e){var r=Math.floor(t);return this.first?(this.first=!1,this.lastIntegerZoom=r,this.lastIntegerZoomTime=0,this.lastZoom=t,this.lastFloorZoom=r,!0):(this.lastFloorZoom>r?(this.lastIntegerZoom=r+1,this.lastIntegerZoomTime=e):this.lastFloorZoom<r&&(this.lastIntegerZoom=r,this.lastIntegerZoomTime=e),t!==this.lastZoom&&(this.lastZoom=t,this.lastFloorZoom=r,!0))},ri.register("ZoomHistory",li);var ci,pi=(ci=!0,function(t,e){var r=ci?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ci=!1,r}),fi=pi(void 0,(function(){return fi.toString().search("(((.+)+)+)+$").toString().constructor(fi).search("(((.+)+)+)+$")}));fi();var hi=function(t,e){this.zoom=t,e?(this.now=e.now,this.fadeDuration=e.fadeDuration,this.zoomHistory=e.zoomHistory,this.transition=e.transition):(this.now=0,this.fadeDuration=0,this.zoomHistory=new li,this.transition={})};hi.prototype.isSupportedScript=function(t){return!1},hi.prototype.crossFadingFactor=function(){return 0===this.fadeDuration?1:Math.min((this.now-this.zoomHistory.lastIntegerZoomTime)/this.fadeDuration,1)},hi.prototype.getCrossfadeParameters=function(){var t=this.zoom,e=t-Math.floor(t),r=this.crossFadingFactor();return t>this.zoomHistory.lastIntegerZoom?{fromScale:2,toScale:1,t:e+(1-e)*r}:{fromScale:.5,toScale:1,t:1-(1-r)*e}},ri.register("EvaluationParameters",hi);var yi,di=(yi=!0,function(t,e){var r=yi?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return yi=!1,r}),mi=di(void 0,(function(){return mi.toString().search("(((.+)+)+)+$").toString().constructor(mi).search("(((.+)+)+)+$")}));mi();var gi=8192;function vi(t,e,r){return Math.min(r,Math.max(e,t))}function xi(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));return n(),{min:-1*Math.pow(2,t-1),max:Math.pow(2,t-1)-1}}var bi=xi(15);function _i(t){for(var e=gi/t.extent,r=t.loadGeometry(),n=0;n<r.length;n++)for(var i=r[n],o=0;o<i.length;o++){var a=i[o];a.x=Math.round(a.x*e),a.y=gi-Math.round(a.y*e),(a.x<bi.min||a.x>bi.max||a.y<bi.min||a.y>bi.max)&&(a.x=vi(a.x,bi.min,bi.max),a.y=vi(a.y,bi.min,bi.max))}return r}var wi,Si=(wi=!0,function(t,e){var r=wi?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return wi=!1,r}),Ai=Si(void 0,(function(){return Ai.toString().search("(((.+)+)+)+$").toString().constructor(Ai).search("(((.+)+)+)+$")}));Ai();var Ei=function(t){void 0===t&&(t=[]),this.segments=t};Ei.prototype.prepareSegment=function(t,e,r,n){var i=this.segments[this.segments.length-1];return(!i||i.vertexLength+t>Ei.MAX_VERTEX_ARRAY_LENGTH||i.sortKey!==n)&&(i={vertexOffset:e.length,primitiveOffset:r.length,vertexLength:0,primitiveLength:0},void 0!==n&&(i.sortKey=n),this.segments.push(i)),i},Ei.prototype.get=function(){return this.segments},Ei.prototype.destroy=function(){for(var t=0,e=this.segments;t<e.length;t+=1){var r=e[t];for(var n in r.cesiumVaos)r.cesiumVaos[n].destroy()}},Ei.simpleSegment=function(t,e,r,n){return new Ei([{vertexOffset:t,primitiveOffset:e,vertexLength:r,primitiveLength:n,vaos:{},cesiumVaos:{},drawCommands:{},sortKey:0}])},Ei.MAX_VERTEX_ARRAY_LENGTH=Math.pow(2,16)-1,ri.register("SegmentVector",Ei);const Ti=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Ii=Ti(void 0,(function(){return Ii.toString().search("(((.+)+)+)+$").toString().constructor(Ii).search("(((.+)+)+)+$")}));Ii();var Oi=function(t,e){this._structArray=t,this._pos1=e*this.size,this._pos2=this._pos1/2,this._pos4=this._pos1/4,this._pos8=this._pos1/8};const ki=128,Fi=5;var Ri=function(){this.isTransferred=!1,this.capacity=-1,this.resize(0)};Ri.serialize=function(t,e){return t._trim(),e&&(t.isTransferred=!0,e.push(t.arrayBuffer)),{length:t.length,arrayBuffer:t.arrayBuffer}},Ri.deserialize=function(t){var e=Object.create(this.prototype);return e.arrayBuffer=t.arrayBuffer,e.length=t.length,e.capacity=t.arrayBuffer.byteLength/e.bytesPerElement,e._refreshViews(),e},Ri.prototype._trim=function(){this.length!==this.capacity&&(this.capacity=this.length,this.arrayBuffer=this.arrayBuffer.slice(0,this.length*this.bytesPerElement),this._refreshViews())},Ri.prototype.clear=function(){this.length=0},Ri.prototype.resize=function(t){this.reserve(t),this.length=t},Ri.prototype.reserve=function(t){if(t>this.capacity){this.capacity=Math.max(t,Math.floor(this.capacity*Fi),ki),this.arrayBuffer=new ArrayBuffer(this.capacity*this.bytesPerElement);var e=this.uint8;this._refreshViews(),e&&(0==e.length&&console.log("oldUint8Array.length == 0"),this.uint8.set(e))}},Ri.prototype._refreshViews=function(){throw new Error("_refreshViews() must be implemented by each concrete StructArray layout")};var Mi,Ci=(Mi=!0,function(t,e){var r=Mi?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Mi=!1,r}),Pi=Ci(void 0,(function(){return Pi.toString().search("(((.+)+)+)+$").toString().constructor(Pi).search("(((.+)+)+)+$")}));function zi(t,e){this.x=t,this.y=e}Pi(),zi.prototype={clone:function(){return new zi(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,e){return this.clone()._rotateAround(t,e)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var e=t.x-this.x,r=t.y-this.y;return e*e+r*r},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,e){return Math.atan2(this.x*e-this.y*t,this.x*t+this.y*e)},_matMult:function(t){var e=t[0]*this.x+t[1]*this.y,r=t[2]*this.x+t[3]*this.y;return this.x=e,this.y=r,this},_add:function(t){return this.x+=t.x,this.y+=t.y,this},_sub:function(t){return this.x-=t.x,this.y-=t.y,this},_mult:function(t){return this.x*=t,this.y*=t,this},_div:function(t){return this.x/=t,this.y/=t,this},_multByPoint:function(t){return this.x*=t.x,this.y*=t.y,this},_divByPoint:function(t){return this.x/=t.x,this.y/=t.y,this},_unit:function(){return this._div(this.mag()),this},_perp:function(){var t=this.y;return this.y=this.x,this.x=-t,this},_rotate:function(t){var e=Math.cos(t),r=Math.sin(t),n=e*this.x-r*this.y,i=r*this.x+e*this.y;return this.x=n,this.y=i,this},_rotateAround:function(t,e){var r=Math.cos(t),n=Math.sin(t),i=e.x+r*(this.x-e.x)-n*(this.y-e.y),o=e.y+n*(this.x-e.x)+r*(this.y-e.y);return this.x=i,this.y=o,this},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}},zi.convert=function(t){return t instanceof zi?t:Array.isArray(t)?new zi(t[0],t[1]):t};var Bi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.int16[n+0]=e,this.int16[n+1]=r,t},e}(Ri);Bi.prototype.bytesPerElement=4,ri.register("StructArrayLayout2i4",Bi);var Di=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n){var i=this.length;return this.resize(i+1),this.emplace(i,t,e,r,n)},e.prototype.emplace=function(t,e,r,n,i){var o=4*t;return this.int16[o+0]=e,this.int16[o+1]=r,this.int16[o+2]=n,this.int16[o+3]=i,t},e}(Ri);Di.prototype.bytesPerElement=8,ri.register("StructArrayLayout4i8",Di);var Ui=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o){var a=this.length;return this.resize(a+1),this.emplace(a,t,e,r,n,i,o)},e.prototype.emplace=function(t,e,r,n,i,o,a){var s=6*t;return this.int16[s+0]=e,this.int16[s+1]=r,this.int16[s+2]=n,this.int16[s+3]=i,this.int16[s+4]=o,this.int16[s+5]=a,t},e}(Ri);Ui.prototype.bytesPerElement=12,ri.register("StructArrayLayout2i4i12",Ui);var Ni=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o){var a=this.length;return this.resize(a+1),this.emplace(a,t,e,r,n,i,o)},e.prototype.emplace=function(t,e,r,n,i,o,a){var s=4*t,u=8*t;return this.int16[s+0]=e,this.int16[s+1]=r,this.uint8[u+4]=n,this.uint8[u+5]=i,this.uint8[u+6]=o,this.uint8[u+7]=a,t},e}(Ri);Ni.prototype.bytesPerElement=8,ri.register("StructArrayLayout2i4ub8",Ni);var Vi=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(){t.apply(this,arguments)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},i.prototype.emplaceBack=function(t,e,r,n,i,o,a,s){var u=this.length;return this.resize(u+1),this.emplace(u,t,e,r,n,i,o,a,s)},i.prototype.emplace=function(t,e,r,n,i,o,a,s,u){var l=8*t;return this.uint16[l+0]=e,this.uint16[l+1]=r,this.uint16[l+2]=n,this.uint16[l+3]=i,this.uint16[l+4]=o,this.uint16[l+5]=a,this.uint16[l+6]=s,this.uint16[l+7]=u,t},i}(Ri);Vi.prototype.bytesPerElement=16,ri.register("StructArrayLayout8ui16",Vi);var Li=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s,u,l,c,p){var f=this.length;return this.resize(f+1),this.emplace(f,t,e,r,n,i,o,a,s,u,l,c,p)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u,l,c,p,f){var h=12*t;return this.int16[h+0]=e,this.int16[h+1]=r,this.int16[h+2]=n,this.int16[h+3]=i,this.uint16[h+4]=o,this.uint16[h+5]=a,this.uint16[h+6]=s,this.uint16[h+7]=u,this.int16[h+8]=l,this.int16[h+9]=c,this.int16[h+10]=p,this.int16[h+11]=f,t},e}(Ri);Li.prototype.bytesPerElement=24,ri.register("StructArrayLayout4i4ui4i24",Li);var $i=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=3*t;return this.float32[i+0]=e,this.float32[i+1]=r,this.float32[i+2]=n,t},e}(Ri);$i.prototype.bytesPerElement=12,ri.register("StructArrayLayout3f12",$i);var Xi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t){var e=this.length;return this.resize(e+1),this.emplace(e,t)},e.prototype.emplace=function(t,e){var r=1*t;return this.uint32[r+0]=e,t},e}(Ri);Xi.prototype.bytesPerElement=4,ri.register("StructArrayLayout1ul4",Xi);var ji=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s,u,l,c){var p=this.length;return this.resize(p+1),this.emplace(p,t,e,r,n,i,o,a,s,u,l,c)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u,l,c,p){var f=12*t,h=6*t;return this.int16[f+0]=e,this.int16[f+1]=r,this.int16[f+2]=n,this.int16[f+3]=i,this.int16[f+4]=o,this.int16[f+5]=a,this.uint32[h+3]=s,this.uint16[f+8]=u,this.uint16[f+9]=l,this.int16[f+10]=c,this.int16[f+11]=p,t},e}(Ri);ji.prototype.bytesPerElement=24,ri.register("StructArrayLayout6i1ul2ui2i24",ji);var qi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o){var a=this.length;return this.resize(a+1),this.emplace(a,t,e,r,n,i,o)},e.prototype.emplace=function(t,e,r,n,i,o,a){var s=6*t;return this.int16[s+0]=e,this.int16[s+1]=r,this.int16[s+2]=n,this.int16[s+3]=i,this.int16[s+4]=o,this.int16[s+5]=a,t},e}(Ri);qi.prototype.bytesPerElement=12,ri.register("StructArrayLayout2i2i2i12",qi);var Hi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n){var i=this.length;return this.resize(i+1),this.emplace(i,t,e,r,n)},e.prototype.emplace=function(t,e,r,n,i){var o=12*t,a=3*t;return this.uint8[o+0]=e,this.uint8[o+1]=r,this.float32[a+1]=n,this.float32[a+2]=i,t},e}(Ri);Hi.prototype.bytesPerElement=12,ri.register("StructArrayLayout2ub2f12",Hi);var Yi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m){var g=this.length;return this.resize(g+1),this.emplace(g,t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m,g){var v=24*t,x=12*t,b=48*t;return this.int16[v+0]=e,this.int16[v+1]=r,this.uint16[v+2]=n,this.uint16[v+3]=i,this.uint32[x+2]=o,this.uint32[x+3]=a,this.uint32[x+4]=s,this.uint16[v+10]=u,this.uint16[v+11]=l,this.uint16[v+12]=c,this.float32[x+7]=p,this.float32[x+8]=f,this.uint8[b+36]=h,this.uint8[b+37]=y,this.uint8[b+38]=d,this.uint32[x+10]=m,this.int16[v+22]=g,t},e}(Ri);Yi.prototype.bytesPerElement=48,ri.register("StructArrayLayout2i2ui3ul3ui2f3ub1ul1i48",Yi);var Wi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m,g,v,x,b,_,w,S,A,E){var T=this.length;return this.resize(T+1),this.emplace(T,t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m,g,v,x,b,_,w,S,A,E)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u,l,c,p,f,h,y,d,m,g,v,x,b,_,w,S,A,E,T){var I=30*t,O=15*t;return this.int16[I+0]=e,this.int16[I+1]=r,this.int16[I+2]=n,this.int16[I+3]=i,this.int16[I+4]=o,this.int16[I+5]=a,this.int16[I+6]=s,this.int16[I+7]=u,this.uint16[I+8]=l,this.uint16[I+9]=c,this.uint16[I+10]=p,this.uint16[I+11]=f,this.uint16[I+12]=h,this.uint16[I+13]=y,this.uint16[I+14]=d,this.uint16[I+15]=m,this.uint16[I+16]=g,this.uint16[I+17]=v,this.uint16[I+18]=x,this.uint16[I+19]=b,this.uint16[I+20]=_,this.uint16[I+21]=w,this.uint32[O+11]=S,this.float32[O+12]=A,this.float32[O+13]=E,this.float32[O+14]=T,t},e}(Ri);Wi.prototype.bytesPerElement=60,ri.register("StructArrayLayout8i14ui1ul3f60",Wi);var Gi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t){var e=this.length;return this.resize(e+1),this.emplace(e,t)},e.prototype.emplace=function(t,e){var r=1*t;return this.float32[r+0]=e,t},e}(Ri);Gi.prototype.bytesPerElement=4,ri.register("StructArrayLayout1f4",Gi);var Qi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=3*t;return this.int16[i+0]=e,this.int16[i+1]=r,this.int16[i+2]=n,t},e}(Ri);Qi.prototype.bytesPerElement=6,ri.register("StructArrayLayout3i6",Qi);var Ki=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=2*t,o=4*t;return this.uint32[i+0]=e,this.uint16[o+2]=r,this.uint16[o+3]=n,t},e}(Ri);Ki.prototype.bytesPerElement=8,ri.register("StructArrayLayout1ul2ui8",Ki);var Ji=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=3*t;return this.uint16[i+0]=e,this.uint16[i+1]=r,this.uint16[i+2]=n,t},e}(Ri);Ji.prototype.bytesPerElement=6,ri.register("StructArrayLayout3ui6",Ji);var Zi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.uint16[n+0]=e,this.uint16[n+1]=r,t},e}(Ri);Zi.prototype.bytesPerElement=4,ri.register("StructArrayLayout2ui4",Zi);var to=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t){var e=this.length;return this.resize(e+1),this.emplace(e,t)},e.prototype.emplace=function(t,e){var r=1*t;return this.uint16[r+0]=e,t},e}(Ri);to.prototype.bytesPerElement=2,ri.register("StructArrayLayout1ui2",to);var eo=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.float32[n+0]=e,this.float32[n+1]=r,t},e}(Ri);eo.prototype.bytesPerElement=8,ri.register("StructArrayLayout2f8",eo);var ro=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n){var i=this.length;return this.resize(i+1),this.emplace(i,t,e,r,n)},e.prototype.emplace=function(t,e,r,n,i){var o=4*t;return this.float32[o+0]=e,this.float32[o+1]=r,this.float32[o+2]=n,this.float32[o+3]=i,t},e}(Ri);ro.prototype.bytesPerElement=16,ri.register("StructArrayLayout4f16",ro);var no=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o){var a=this.length;return this.resize(a+1),this.emplace(a,t,e,r,n,i,o)},e.prototype.emplace=function(t,e,r,n,i,o,a){var s=6*t;return this.float32[s+0]=e,this.float32[s+1]=r,this.float32[s+2]=n,this.float32[s+3]=i,this.float32[s+4]=o,this.float32[s+5]=a,t},e}(Ri);no.prototype.bytesPerElement=24,ri.register("StructArrayLayout6f24",no);var io=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s){var u=this.length;return this.resize(u+1),this.emplace(u,t,e,r,n,i,o,a,s)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u){var l=8*t;return this.float32[l+0]=e,this.float32[l+1]=r,this.float32[l+2]=n,this.float32[l+3]=i,this.float32[l+4]=o,this.float32[l+5]=a,this.float32[l+6]=s,this.float32[l+7]=u,t},e}(Ri);io.prototype.bytesPerElement=32,ri.register("StructArrayLayout8f32",io);var oo=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,o,a,s,u,l,c,p){var f=this.length;return this.resize(f+1),this.emplace(f,t,e,r,n,i,o,a,s,u,l,c,p)},e.prototype.emplace=function(t,e,r,n,i,o,a,s,u,l,c,p,f){var h=12*t;return this.float32[h+0]=e,this.float32[h+1]=r,this.float32[h+2]=n,this.float32[h+3]=i,this.float32[h+4]=o,this.float32[h+5]=a,this.float32[h+6]=s,this.float32[h+7]=u,this.float32[h+8]=l,this.float32[h+9]=c,this.float32[h+10]=p,this.float32[h+11]=f,t},e}(Ri);oo.prototype.bytesPerElement=48,ri.register("StructArrayLayout12f48",oo);var ao=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={anchorPointX:{configurable:!0},anchorPointY:{configurable:!0},x1:{configurable:!0},y1:{configurable:!0},x2:{configurable:!0},y2:{configurable:!0},featureIndex:{configurable:!0},sourceLayerIndex:{configurable:!0},bucketIndex:{configurable:!0},radius:{configurable:!0},signedDistanceFromAnchor:{configurable:!0},anchorPoint:{configurable:!0}};return r.anchorPointX.get=function(){return this._structArray.int16[this._pos2+0]},r.anchorPointX.set=function(t){this._structArray.int16[this._pos2+0]=t},r.anchorPointY.get=function(){return this._structArray.int16[this._pos2+1]},r.anchorPointY.set=function(t){this._structArray.int16[this._pos2+1]=t},r.x1.get=function(){return this._structArray.int16[this._pos2+2]},r.x1.set=function(t){this._structArray.int16[this._pos2+2]=t},r.y1.get=function(){return this._structArray.int16[this._pos2+3]},r.y1.set=function(t){this._structArray.int16[this._pos2+3]=t},r.x2.get=function(){return this._structArray.int16[this._pos2+4]},r.x2.set=function(t){this._structArray.int16[this._pos2+4]=t},r.y2.get=function(){return this._structArray.int16[this._pos2+5]},r.y2.set=function(t){this._structArray.int16[this._pos2+5]=t},r.featureIndex.get=function(){return this._structArray.uint32[this._pos4+3]},r.featureIndex.set=function(t){this._structArray.uint32[this._pos4+3]=t},r.sourceLayerIndex.get=function(){return this._structArray.uint16[this._pos2+8]},r.sourceLayerIndex.set=function(t){this._structArray.uint16[this._pos2+8]=t},r.bucketIndex.get=function(){return this._structArray.uint16[this._pos2+9]},r.bucketIndex.set=function(t){this._structArray.uint16[this._pos2+9]=t},r.radius.get=function(){return this._structArray.int16[this._pos2+10]},r.radius.set=function(t){this._structArray.int16[this._pos2+10]=t},r.signedDistanceFromAnchor.get=function(){return this._structArray.int16[this._pos2+11]},r.signedDistanceFromAnchor.set=function(t){this._structArray.int16[this._pos2+11]=t},r.anchorPoint.get=function(){return new pointGeometry(this.anchorPointX,this.anchorPointY)},Object.defineProperties(e.prototype,r),e}(Oi);ao.prototype.size=24;var so=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t){return new ao(this,t)},e}(ji);ri.register("CollisionBoxArray",so);var uo=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={anchorX:{configurable:!0},anchorY:{configurable:!0},glyphStartIndex:{configurable:!0},numGlyphs:{configurable:!0},vertexStartIndex:{configurable:!0},lineStartIndex:{configurable:!0},lineLength:{configurable:!0},segment:{configurable:!0},lowerSize:{configurable:!0},upperSize:{configurable:!0},lineOffsetX:{configurable:!0},lineOffsetY:{configurable:!0},writingMode:{configurable:!0},placedOrientation:{configurable:!0},hidden:{configurable:!0},crossTileID:{configurable:!0},associatedIconIndex:{configurable:!0}};return r.anchorX.get=function(){return this._structArray.int16[this._pos2+0]},r.anchorX.set=function(t){this._structArray.int16[this._pos2+0]=t},r.anchorY.get=function(){return this._structArray.int16[this._pos2+1]},r.anchorY.set=function(t){this._structArray.int16[this._pos2+1]=t},r.glyphStartIndex.get=function(){return this._structArray.uint16[this._pos2+2]},r.glyphStartIndex.set=function(t){this._structArray.uint16[this._pos2+2]=t},r.numGlyphs.get=function(){return this._structArray.uint16[this._pos2+3]},r.numGlyphs.set=function(t){this._structArray.uint16[this._pos2+3]=t},r.vertexStartIndex.get=function(){return this._structArray.uint32[this._pos4+2]},r.vertexStartIndex.set=function(t){this._structArray.uint32[this._pos4+2]=t},r.lineStartIndex.get=function(){return this._structArray.uint32[this._pos4+3]},r.lineStartIndex.set=function(t){this._structArray.uint32[this._pos4+3]=t},r.lineLength.get=function(){return this._structArray.uint32[this._pos4+4]},r.lineLength.set=function(t){this._structArray.uint32[this._pos4+4]=t},r.segment.get=function(){return this._structArray.uint16[this._pos2+10]},r.segment.set=function(t){this._structArray.uint16[this._pos2+10]=t},r.lowerSize.get=function(){return this._structArray.uint16[this._pos2+11]},r.lowerSize.set=function(t){this._structArray.uint16[this._pos2+11]=t},r.upperSize.get=function(){return this._structArray.uint16[this._pos2+12]},r.upperSize.set=function(t){this._structArray.uint16[this._pos2+12]=t},r.lineOffsetX.get=function(){return this._structArray.float32[this._pos4+7]},r.lineOffsetX.set=function(t){this._structArray.float32[this._pos4+7]=t},r.lineOffsetY.get=function(){return this._structArray.float32[this._pos4+8]},r.lineOffsetY.set=function(t){this._structArray.float32[this._pos4+8]=t},r.writingMode.get=function(){return this._structArray.uint8[this._pos1+36]},r.writingMode.set=function(t){this._structArray.uint8[this._pos1+36]=t},r.placedOrientation.get=function(){return this._structArray.uint8[this._pos1+37]},r.placedOrientation.set=function(t){this._structArray.uint8[this._pos1+37]=t},r.hidden.get=function(){return this._structArray.uint8[this._pos1+38]},r.hidden.set=function(t){this._structArray.uint8[this._pos1+38]=t},r.crossTileID.get=function(){return this._structArray.uint32[this._pos4+10]},r.crossTileID.set=function(t){this._structArray.uint32[this._pos4+10]=t},r.associatedIconIndex.get=function(){return this._structArray.int16[this._pos2+22]},r.associatedIconIndex.set=function(t){this._structArray.int16[this._pos2+22]=t},Object.defineProperties(e.prototype,r),e}(Oi);uo.prototype.size=48;var lo=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t){return new uo(this,t)},e}(Yi);ri.register("PlacedSymbolArray",lo);var co=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={anchorX:{configurable:!0},anchorY:{configurable:!0},rightJustifiedTextSymbolIndex:{configurable:!0},centerJustifiedTextSymbolIndex:{configurable:!0},leftJustifiedTextSymbolIndex:{configurable:!0},verticalPlacedTextSymbolIndex:{configurable:!0},placedIconSymbolIndex:{configurable:!0},verticalPlacedIconSymbolIndex:{configurable:!0},key:{configurable:!0},textBoxStartIndex:{configurable:!0},textBoxEndIndex:{configurable:!0},verticalTextBoxStartIndex:{configurable:!0},verticalTextBoxEndIndex:{configurable:!0},iconBoxStartIndex:{configurable:!0},iconBoxEndIndex:{configurable:!0},verticalIconBoxStartIndex:{configurable:!0},verticalIconBoxEndIndex:{configurable:!0},featureIndex:{configurable:!0},numHorizontalGlyphVertices:{configurable:!0},numVerticalGlyphVertices:{configurable:!0},numIconVertices:{configurable:!0},numVerticalIconVertices:{configurable:!0},crossTileID:{configurable:!0},textBoxScale:{configurable:!0},textOffset0:{configurable:!0},textOffset1:{configurable:!0}};return r.anchorX.get=function(){return this._structArray.int16[this._pos2+0]},r.anchorX.set=function(t){this._structArray.int16[this._pos2+0]=t},r.anchorY.get=function(){return this._structArray.int16[this._pos2+1]},r.anchorY.set=function(t){this._structArray.int16[this._pos2+1]=t},r.rightJustifiedTextSymbolIndex.get=function(){return this._structArray.int16[this._pos2+2]},r.rightJustifiedTextSymbolIndex.set=function(t){this._structArray.int16[this._pos2+2]=t},r.centerJustifiedTextSymbolIndex.get=function(){return this._structArray.int16[this._pos2+3]},r.centerJustifiedTextSymbolIndex.set=function(t){this._structArray.int16[this._pos2+3]=t},r.leftJustifiedTextSymbolIndex.get=function(){return this._structArray.int16[this._pos2+4]},r.leftJustifiedTextSymbolIndex.set=function(t){this._structArray.int16[this._pos2+4]=t},r.verticalPlacedTextSymbolIndex.get=function(){return this._structArray.int16[this._pos2+5]},r.verticalPlacedTextSymbolIndex.set=function(t){this._structArray.int16[this._pos2+5]=t},r.placedIconSymbolIndex.get=function(){return this._structArray.int16[this._pos2+6]},r.placedIconSymbolIndex.set=function(t){this._structArray.int16[this._pos2+6]=t},r.verticalPlacedIconSymbolIndex.get=function(){return this._structArray.int16[this._pos2+7]},r.verticalPlacedIconSymbolIndex.set=function(t){this._structArray.int16[this._pos2+7]=t},r.key.get=function(){return this._structArray.uint16[this._pos2+8]},r.key.set=function(t){this._structArray.uint16[this._pos2+8]=t},r.textBoxStartIndex.get=function(){return this._structArray.uint16[this._pos2+9]},r.textBoxStartIndex.set=function(t){this._structArray.uint16[this._pos2+9]=t},r.textBoxEndIndex.get=function(){return this._structArray.uint16[this._pos2+10]},r.textBoxEndIndex.set=function(t){this._structArray.uint16[this._pos2+10]=t},r.verticalTextBoxStartIndex.get=function(){return this._structArray.uint16[this._pos2+11]},r.verticalTextBoxStartIndex.set=function(t){this._structArray.uint16[this._pos2+11]=t},r.verticalTextBoxEndIndex.get=function(){return this._structArray.uint16[this._pos2+12]},r.verticalTextBoxEndIndex.set=function(t){this._structArray.uint16[this._pos2+12]=t},r.iconBoxStartIndex.get=function(){return this._structArray.uint16[this._pos2+13]},r.iconBoxStartIndex.set=function(t){this._structArray.uint16[this._pos2+13]=t},r.iconBoxEndIndex.get=function(){return this._structArray.uint16[this._pos2+14]},r.iconBoxEndIndex.set=function(t){this._structArray.uint16[this._pos2+14]=t},r.verticalIconBoxStartIndex.get=function(){return this._structArray.uint16[this._pos2+15]},r.verticalIconBoxStartIndex.set=function(t){this._structArray.uint16[this._pos2+15]=t},r.verticalIconBoxEndIndex.get=function(){return this._structArray.uint16[this._pos2+16]},r.verticalIconBoxEndIndex.set=function(t){this._structArray.uint16[this._pos2+16]=t},r.featureIndex.get=function(){return this._structArray.uint16[this._pos2+17]},r.featureIndex.set=function(t){this._structArray.uint16[this._pos2+17]=t},r.numHorizontalGlyphVertices.get=function(){return this._structArray.uint16[this._pos2+18]},r.numHorizontalGlyphVertices.set=function(t){this._structArray.uint16[this._pos2+18]=t},r.numVerticalGlyphVertices.get=function(){return this._structArray.uint16[this._pos2+19]},r.numVerticalGlyphVertices.set=function(t){this._structArray.uint16[this._pos2+19]=t},r.numIconVertices.get=function(){return this._structArray.uint16[this._pos2+20]},r.numIconVertices.set=function(t){this._structArray.uint16[this._pos2+20]=t},r.numVerticalIconVertices.get=function(){return this._structArray.uint16[this._pos2+21]},r.numVerticalIconVertices.set=function(t){this._structArray.uint16[this._pos2+21]=t},r.crossTileID.get=function(){return this._structArray.uint32[this._pos4+11]},r.crossTileID.set=function(t){this._structArray.uint32[this._pos4+11]=t},r.textBoxScale.get=function(){return this._structArray.float32[this._pos4+12]},r.textBoxScale.set=function(t){this._structArray.float32[this._pos4+12]=t},r.textOffset0.get=function(){return this._structArray.float32[this._pos4+13]},r.textOffset0.set=function(t){this._structArray.float32[this._pos4+13]=t},r.textOffset1.get=function(){return this._structArray.float32[this._pos4+14]},r.textOffset1.set=function(t){this._structArray.float32[this._pos4+14]=t},Object.defineProperties(e.prototype,r),e}(Oi);co.prototype.size=60;var po=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t){return new co(this,t)},e}(Wi);ri.register("SymbolInstanceArray",po);var fo=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={offsetX:{configurable:!0}};return r.offsetX.get=function(){return this._structArray.float32[this._pos4+0]},r.offsetX.set=function(t){this._structArray.float32[this._pos4+0]=t},Object.defineProperties(e.prototype,r),e}(Oi);fo.prototype.size=4;var ho=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.getoffsetX=function(t){return this.float32[1*t+0]},e.prototype.get=function(t){return new fo(this,t)},e}(Gi);ri.register("GlyphOffsetArray",ho);var yo=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={x:{configurable:!0},y:{configurable:!0},tileUnitDistanceFromAnchor:{configurable:!0}};return r.x.get=function(){return this._structArray.int16[this._pos2+0]},r.x.set=function(t){this._structArray.int16[this._pos2+0]=t},r.y.get=function(){return this._structArray.int16[this._pos2+1]},r.y.set=function(t){this._structArray.int16[this._pos2+1]=t},r.tileUnitDistanceFromAnchor.get=function(){return this._structArray.int16[this._pos2+2]},r.tileUnitDistanceFromAnchor.set=function(t){this._structArray.int16[this._pos2+2]=t},Object.defineProperties(e.prototype,r),e}(Oi);yo.prototype.size=6;var mo=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.getx=function(t){return this.int16[3*t+0]},e.prototype.gety=function(t){return this.int16[3*t+1]},e.prototype.gettileUnitDistanceFromAnchor=function(t){return this.int16[3*t+2]},e.prototype.get=function(t){return new yo(this,t)},e}(Qi);ri.register("SymbolLineVertexArray",mo);var go=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={featureIndex:{configurable:!0},sourceLayerIndex:{configurable:!0},bucketIndex:{configurable:!0}};return r.featureIndex.get=function(){return this._structArray.uint32[this._pos4+0]},r.featureIndex.set=function(t){this._structArray.uint32[this._pos4+0]=t},r.sourceLayerIndex.get=function(){return this._structArray.uint16[this._pos2+2]},r.sourceLayerIndex.set=function(t){this._structArray.uint16[this._pos2+2]=t},r.bucketIndex.get=function(){return this._structArray.uint16[this._pos2+3]},r.bucketIndex.set=function(t){this._structArray.uint16[this._pos2+3]=t},Object.defineProperties(e.prototype,r),e}(Oi);go.prototype.size=8;var vo=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t){return new go(this,t)},e}(Ki);ri.register("FeatureIndexArray",vo);var xo,bo=(xo=!0,function(t,e){var r=xo?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return xo=!1,r}),_o=bo(void 0,(function(){return _o.toString().search("(((.+)+)+)+$").toString().constructor(_o).search("(((.+)+)+)+$")}));_o();var wo=function(t,e,r){this.property=t,this.value=e,this.parameters=r};wo.prototype.isConstant=function(){return"constant"===this.value.kind},wo.prototype.constantOr=function(t){return"constant"===this.value.kind?this.value.value:t},wo.prototype.evaluate=function(t,e,r){return this.property.evaluate(this.value,this.parameters,t,e,r)};var So,Ao=(So=!0,function(t,e){var r=So?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return So=!1,r}),Eo=Ao(void 0,(function(){return Eo.toString().search("(((.+)+)+)+$").toString().constructor(Eo).search("(((.+)+)+)+$")}));function To(t){return"data-driven"===t["property-type"]||"cross-faded-data-driven"===t["property-type"]}function Io(t){return!!t.expression&&t.expression.parameters.indexOf("zoom")>-1}function Oo(t){return!!t.expression&&t.expression.interpolated}Eo();var ko={},Fo=function(t,e){this.gl=t.gl,this.location=e};ko.Uniform1i=function(t){function e(e,r){t.call(this,e,r),this.current=0}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.current!==t&&(this.current=t,this.gl.uniform1i(this.location,t))},e}(Fo),ko.Uniform1f=function(t){function e(e,r){t.call(this,e,r),this.current=0}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.current!==t&&(this.current=t,this.gl.uniform1f(this.location,t))},e}(Fo),ko.Uniform2f=function(t){function e(e,r){t.call(this,e,r),this.current=[0,0]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){(t[0]!==this.current[0]||t[1]!==this.current[1])&&(this.current=t,this.gl.uniform2f(this.location,t[0],t[1]))},e}(Fo),ko.Uniform3f=function(t){function e(e,r){t.call(this,e,r),this.current=[0,0,0]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){(t[0]!==this.current[0]||t[1]!==this.current[1]||t[2]!==this.current[2])&&(this.current=t,this.gl.uniform3f(this.location,t[0],t[1],t[2]))},e}(Fo),ko.Uniform4f=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e,r){t.call(this,e,r),this.current=[0,0,0,0]}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.set=function(t){(t[0]!==this.current[0]||t[1]!==this.current[1]||t[2]!==this.current[2]||t[3]!==this.current[3])&&(this.current=t,this.gl.uniform4f(this.location,t[0],t[1],t[2],t[3]))},i}(Fo),ko.UniformColor=function(t){function e(e,r){t.call(this,e,r),this.current=Mt.transparent}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){(t.r!==this.current.r||t.g!==this.current.g||t.b!==this.current.b||t.a!==this.current.a)&&(this.current=t,this.gl.uniform4f(this.location,t.r,t.g,t.b,t.a))},e}(Fo);var Ro=new Float32Array(16);ko.UniformMatrix4f=function(t){function e(e,r){t.call(this,e,r),this.current=Ro}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.gl.uniformMatrix4fv(this.location,!1,t)},e}(Fo);var Mo={_maximumCombinedTextureImageUnits:0,_maximumCubeMapSize:0,_maximumFragmentUniformVectors:0,_maximumTextureImageUnits:0,_maximumRenderbufferSize:0,_maximumTextureSize:0,_maximumVaryingVectors:0,_maximumVertexAttributes:0,_maximumVertexTextureImageUnits:0,_maximumVertexUniformVectors:0,_minimumAliasedLineWidth:0,_maximumAliasedLineWidth:0,_minimumAliasedPointSize:0,_maximumAliasedPointSize:0,_maximumViewportWidth:0,_maximumViewportHeight:0,_maximumTextureFilterAnisotropy:0,_maximumDrawBuffers:0,_maximumColorAttachments:0,_highpFloatSupported:!1,_highpIntSupported:!1,_uniformBufferOffsetAlignment:256,_maxUniformBufferBinding:36,_maxArrayTextureLayers:256};function Co(t){if("object"!=typeof t||null===t)return t;for(var e,r=Object.keys(t),n=0;n<r.length;n++)e=r[n],t.hasOwnProperty(e)&&"_applyFunctions"!==e&&(t[e]=Co(t[e]));return Object.freeze(t)}function Po(t){return t===i.t.FUNC_ADD||t===i.t.FUNC_SUBTRACT||t===i.t.FUNC_REVERSE_SUBTRACT||t===i.t.MIN||t===i.t.MAX}function zo(t){return t===i.t.ZERO||t===i.t.ONE||t===i.t.SRC_COLOR||t===i.t.ONE_MINUS_SRC_COLOR||t===i.t.DST_COLOR||t===i.t.ONE_MINUS_DST_COLOR||t===i.t.SRC_ALPHA||t===i.t.ONE_MINUS_SRC_ALPHA||t===i.t.DST_ALPHA||t===i.t.ONE_MINUS_DST_ALPHA||t===i.t.CONSTANT_COLOR||t===i.t.ONE_MINUS_CONSTANT_COLOR||t===i.t.CONSTANT_ALPHA||t===i.t.ONE_MINUS_CONSTANT_ALPHA||t===i.t.SRC_ALPHA_SATURATE}function Bo(t){return t===i.t.FRONT||t===i.t.BACK||t===i.t.FRONT_AND_BACK}function Do(t){return t===i.t.NEVER||t===i.t.LESS||t===i.t.EQUAL||t===i.t.LEQUAL||t===i.t.GREATER||t===i.t.NOTEQUAL||t===i.t.GEQUAL||t===i.t.ALWAYS}function Uo(t){return t===i.t.NEVER||t===i.t.LESS||t===i.t.EQUAL||t===i.t.LEQUAL||t===i.t.GREATER||t===i.t.NOTEQUAL||t===i.t.GEQUAL||t===i.t.ALWAYS}function No(t){return t===i.t.ZERO||t===i.t.KEEP||t===i.t.REPLACE||t===i.t.INCR||t===i.t.DECR||t===i.t.INVERT||t===i.t.INCR_WRAP||t===i.t.DECR_WRAP}function Vo(t){var r=e.e(t,{}),n=e.e(r.cull,{}),o=e.e(r.polygonOffset,{}),c=e.e(r.scissorTest,{}),p=e.e(c.rectangle,{}),f=e.e(r.depthRange,{}),h=e.e(r.depthTest,{}),y=e.e(r.colorMask,{}),d=e.e(r.blending,{}),m=e.e(d.color,{}),g=e.e(r.stencilTest,{}),v=e.e(g.frontOperation,{}),x=e.e(g.backOperation,{}),b=e.e(r.sampleCoverage,{}),_=r.viewport;if(this.frontFace=e.e(r.frontFace,l.F.COUNTER_CLOCKWISE),this.cull={enabled:e.e(n.enabled,!1),face:e.e(n.face,i.t.BACK)},this.lineWidth=e.e(r.lineWidth,1),this.polygonOffset={enabled:e.e(o.enabled,!1),factor:e.e(o.factor,0),units:e.e(o.units,0)},this.scissorTest={enabled:e.e(c.enabled,!1),rectangle:a.n.clone(p)},this.depthRange={near:e.e(f.near,0),far:e.e(f.far,1)},this.depthTest={enabled:e.e(h.enabled,!1),func:e.e(h.func,i.t.LESS)},this.colorMask={red:e.e(y.red,!0),green:e.e(y.green,!0),blue:e.e(y.blue,!0),alpha:e.e(y.alpha,!0)},this.depthMask=e.e(r.depthMask,!0),this.stencilMask=e.e(r.stencilMask,-1),this.blending={enabled:e.e(d.enabled,!1),color:new s.e(e.e(m.red,0),e.e(m.green,0),e.e(m.blue,0),e.e(m.alpha,0)),equationRgb:e.e(d.equationRgb,i.t.FUNC_ADD),equationAlpha:e.e(d.equationAlpha,i.t.FUNC_ADD),functionSourceRgb:e.e(d.functionSourceRgb,i.t.ONE),functionSourceAlpha:e.e(d.functionSourceAlpha,i.t.ONE),functionDestinationRgb:e.e(d.functionDestinationRgb,i.t.ZERO),functionDestinationAlpha:e.e(d.functionDestinationAlpha,i.t.ZERO)},this.stencilTest={enabled:e.e(g.enabled,!1),frontFunction:e.e(g.frontFunction,i.t.ALWAYS),backFunction:e.e(g.backFunction,i.t.ALWAYS),reference:e.e(g.reference,0),mask:e.e(g.mask,-1),frontOperation:{fail:e.e(v.fail,i.t.KEEP),zFail:e.e(v.zFail,i.t.KEEP),zPass:e.e(v.zPass,i.t.KEEP)},backOperation:{fail:e.e(x.fail,i.t.KEEP),zFail:e.e(x.zFail,i.t.KEEP),zPass:e.e(x.zPass,i.t.KEEP)}},this.sampleCoverage={enabled:e.e(b.enabled,!1),value:e.e(b.value,1),invert:e.e(b.invert,!1)},this.viewport=e.t(_)?new a.n(_.x,_.y,_.width,_.height):void 0,this.lineWidth<Mo.minimumAliasedLineWidth||this.lineWidth>Mo.maximumAliasedLineWidth)throw new u.t("renderState.lineWidth is out of range.  Check minimumAliasedLineWidth and maximumAliasedLineWidth.");if(!l.F.validate(this.frontFace))throw new u.t("Invalid renderState.frontFace.");if(!Bo(this.cull.face))throw new u.t("Invalid renderState.cull.face.");if(this.scissorTest.rectangle.width<0||this.scissorTest.rectangle.height<0)throw new u.t("renderState.scissorTest.rectangle.width and renderState.scissorTest.rectangle.height must be greater than or equal to zero.");if(this.depthRange.near>this.depthRange.far)throw new u.t("renderState.depthRange.near can not be greater than renderState.depthRange.far.");if(this.depthRange.near<0)throw new u.t("renderState.depthRange.near must be greater than or equal to zero.");if(this.depthRange.far>1)throw new u.t("renderState.depthRange.far must be less than or equal to one.");if(!Do(this.depthTest.func))throw new u.t("Invalid renderState.depthTest.func.");if(this.blending.color.red<0||this.blending.color.red>1||this.blending.color.green<0||this.blending.color.green>1||this.blending.color.blue<0||this.blending.color.blue>1||this.blending.color.alpha<0||this.blending.color.alpha>1)throw new u.t("renderState.blending.color components must be greater than or equal to zero and less than or equal to one.");if(!Po(this.blending.equationRgb))throw new u.t("Invalid renderState.blending.equationRgb.");if(!Po(this.blending.equationAlpha))throw new u.t("Invalid renderState.blending.equationAlpha.");if(!zo(this.blending.functionSourceRgb))throw new u.t("Invalid renderState.blending.functionSourceRgb.");if(!zo(this.blending.functionSourceAlpha))throw new u.t("Invalid renderState.blending.functionSourceAlpha.");if(!zo(this.blending.functionDestinationRgb))throw new u.t("Invalid renderState.blending.functionDestinationRgb.");if(!zo(this.blending.functionDestinationAlpha))throw new u.t("Invalid renderState.blending.functionDestinationAlpha.");if(!Uo(this.stencilTest.frontFunction))throw new u.t("Invalid renderState.stencilTest.frontFunction.");if(!Uo(this.stencilTest.backFunction))throw new u.t("Invalid renderState.stencilTest.backFunction.");if(!No(this.stencilTest.frontOperation.fail))throw new u.t("Invalid renderState.stencilTest.frontOperation.fail.");if(!No(this.stencilTest.frontOperation.zFail))throw new u.t("Invalid renderState.stencilTest.frontOperation.zFail.");if(!No(this.stencilTest.frontOperation.zPass))throw new u.t("Invalid renderState.stencilTest.frontOperation.zPass.");if(!No(this.stencilTest.backOperation.fail))throw new u.t("Invalid renderState.stencilTest.backOperation.fail.");if(!No(this.stencilTest.backOperation.zFail))throw new u.t("Invalid renderState.stencilTest.backOperation.zFail.");if(!No(this.stencilTest.backOperation.zPass))throw new u.t("Invalid renderState.stencilTest.backOperation.zPass.");if(e.t(this.viewport)){if(this.viewport.width<0)throw new u.t("renderState.viewport.width must be greater than or equal to zero.");if(this.viewport.height<0)throw new u.t("renderState.viewport.height must be greater than or equal to zero.");if(this.viewport.width>Mo.maximumViewportWidth)throw new u.t("renderState.viewport.width must be less than or equal to the maximum viewport width ("+Mo.maximumViewportWidth.toString()+").  Check maximumViewportWidth.");if(this.viewport.height>Mo.maximumViewportHeight)throw new u.t("renderState.viewport.height must be less than or equal to the maximum viewport height ("+Mo.maximumViewportHeight.toString()+").  Check maximumViewportHeight.")}this.id=0,this._applyFunctions=[]}Object.defineProperties(Mo,{maximumCombinedTextureImageUnits:{get:function(){return Mo._maximumCombinedTextureImageUnits}},maximumCubeMapSize:{get:function(){return Mo._maximumCubeMapSize}},maximumFragmentUniformVectors:{get:function(){return Mo._maximumFragmentUniformVectors}},maximumTextureImageUnits:{get:function(){return Mo._maximumTextureImageUnits}},maximumRenderbufferSize:{get:function(){return Mo._maximumRenderbufferSize}},maximumTextureSize:{get:function(){return Mo._maximumTextureSize}},maximumVaryingVectors:{get:function(){return Mo._maximumVaryingVectors}},maximumVertexAttributes:{get:function(){return Mo._maximumVertexAttributes}},maximumVertexTextureImageUnits:{get:function(){return Mo._maximumVertexTextureImageUnits}},maximumVertexUniformVectors:{get:function(){return Mo._maximumVertexUniformVectors}},minimumAliasedLineWidth:{get:function(){return Mo._minimumAliasedLineWidth}},maximumAliasedLineWidth:{get:function(){return Mo._maximumAliasedLineWidth}},minimumAliasedPointSize:{get:function(){return Mo._minimumAliasedPointSize}},maximumAliasedPointSize:{get:function(){return Mo._maximumAliasedPointSize}},maximumViewportWidth:{get:function(){return Mo._maximumViewportWidth}},maximumViewportHeight:{get:function(){return Mo._maximumViewportHeight}},maximumTextureFilterAnisotropy:{get:function(){return Mo._maximumTextureFilterAnisotropy}},maximumDrawBuffers:{get:function(){return Mo._maximumDrawBuffers}},maximumColorAttachments:{get:function(){return Mo._maximumColorAttachments}},highpFloatSupported:{get:function(){return Mo._highpFloatSupported}},highpIntSupported:{get:function(){return Mo._highpIntSupported}},uniformBufferOffsetAlignment:{get:function(){return Mo._uniformBufferOffsetAlignment}},maxUniformBufferBinding:{get:function(){return Mo._maxUniformBufferBinding}},maxArrayTextureLayers:{get:function(){return Mo._maxArrayTextureLayers}}});var Lo=0,$o={};function Xo(t,e,r){r?t.enable(e):t.disable(e)}function jo(t,e){t.frontFace(e.frontFace)}function qo(t,e){var r=e.cull,n=r.enabled;Xo(t,t.CULL_FACE,n),n&&t.cullFace(r.face)}function Ho(t,e){t.lineWidth(e.lineWidth)}function Yo(t,e){var r=e.polygonOffset,n=r.enabled;Xo(t,t.POLYGON_OFFSET_FILL,n),n&&t.polygonOffset(r.factor,r.units)}function Wo(t,r,n){var i=r.scissorTest,o=e.t(n.scissorTest)?n.scissorTest.enabled:i.enabled;if(Xo(t,t.SCISSOR_TEST,o),o){var a=e.t(n.scissorTest)?n.scissorTest.rectangle:i.rectangle;t.scissor(a.x,a.y,a.width,a.height)}}function Go(t,e){var r=e.depthRange;t.depthRange(r.near,r.far)}function Qo(t,e){var r=e.depthTest,n=r.enabled;Xo(t,t.DEPTH_TEST,n),n&&t.depthFunc(r.func)}function Ko(t,e){var r=e.colorMask;t.colorMask(r.red,r.green,r.blue,r.alpha)}function Jo(t,e){t.depthMask(e.depthMask)}function Zo(t,e){t.stencilMask(e.stencilMask)}function ta(t,e){t.blendColor(e.red,e.green,e.blue,e.alpha)}function ea(t,r,n){var i=r.blending,o=e.t(n.blendingEnabled)?n.blendingEnabled:i.enabled;Xo(t,t.BLEND,o),o&&(ta(t,i.color),t.blendEquationSeparate(i.equationRgb,i.equationAlpha),t.blendFuncSeparate(i.functionSourceRgb,i.functionDestinationRgb,i.functionSourceAlpha,i.functionDestinationAlpha))}function ra(t,e){var r=e.stencilTest,n=r.enabled;if(Xo(t,t.STENCIL_TEST,n),n){var i=r.frontFunction,o=r.backFunction,a=r.reference,s=r.mask;t.stencilFunc(i,a,s),t.stencilFuncSeparate(t.BACK,o,a,s),t.stencilFuncSeparate(t.FRONT,i,a,s);var u=r.frontOperation,l=u.fail,c=u.zFail,p=u.zPass;t.stencilOpSeparate(t.FRONT,l,c,p);var f=r.backOperation,h=f.fail,y=f.zFail,d=f.zPass;t.stencilOpSeparate(t.BACK,h,y,d)}}function na(t,e){var r=e.sampleCoverage,n=r.enabled;Xo(t,t.SAMPLE_COVERAGE,n),n&&t.sampleCoverage(r.value,r.invert)}Vo.fromCache=function(t){var r=JSON.stringify(t),n=$o[r];if(e.t(n))return++n.referenceCount,n.state;var i=new Vo(t),o=JSON.stringify(i);return n=$o[o],e.t(n)||(i.id=Lo++,n={referenceCount:0,state:i=Co(i)},$o[o]=n),++n.referenceCount,$o[r]={referenceCount:1,state:n.state},n.state},Vo.removeFromCache=function(t){var r=new Vo(t),n=JSON.stringify(r),i=$o[n],o=JSON.stringify(t),a=$o[o];e.t(a)&&(--a.referenceCount,0===a.referenceCount&&(delete $o[o],e.t(i)&&--i.referenceCount)),e.t(i)&&0===i.referenceCount&&delete $o[n]},Vo.getCache=function(){return $o},Vo.clearCache=function(){$o={}};var ia=new a.n;function oa(t,r,n){var i=e.e(r.viewport,n.viewport);e.t(i)||((i=ia).width=n.context.drawingBufferWidth,i.height=n.context.drawingBufferHeight),n.context.uniformState.viewport=i,t.viewport(i.x,i.y,i.width,i.height)}function aa(t,e){var r=[];return t.frontFace!==e.frontFace&&r.push(jo),(t.cull.enabled!==e.cull.enabled||t.cull.face!==e.cull.face)&&r.push(qo),t.lineWidth!==e.lineWidth&&r.push(Ho),(t.polygonOffset.enabled!==e.polygonOffset.enabled||t.polygonOffset.factor!==e.polygonOffset.factor||t.polygonOffset.units!==e.polygonOffset.units)&&r.push(Yo),(t.depthRange.near!==e.depthRange.near||t.depthRange.far!==e.depthRange.far)&&r.push(Go),(t.depthTest.enabled!==e.depthTest.enabled||t.depthTest.func!==e.depthTest.func)&&r.push(Qo),(t.colorMask.red!==e.colorMask.red||t.colorMask.green!==e.colorMask.green||t.colorMask.blue!==e.colorMask.blue||t.colorMask.alpha!==e.colorMask.alpha)&&r.push(Ko),t.depthMask!==e.depthMask&&r.push(Jo),t.stencilMask!==e.stencilMask&&r.push(Zo),(t.stencilTest.enabled!==e.stencilTest.enabled||t.stencilTest.frontFunction!==e.stencilTest.frontFunction||t.stencilTest.backFunction!==e.stencilTest.backFunction||t.stencilTest.reference!==e.stencilTest.reference||t.stencilTest.mask!==e.stencilTest.mask||t.stencilTest.frontOperation.fail!==e.stencilTest.frontOperation.fail||t.stencilTest.frontOperation.zFail!==e.stencilTest.frontOperation.zFail||t.stencilTest.backOperation.fail!==e.stencilTest.backOperation.fail||t.stencilTest.backOperation.zFail!==e.stencilTest.backOperation.zFail||t.stencilTest.backOperation.zPass!==e.stencilTest.backOperation.zPass)&&r.push(ra),(t.sampleCoverage.enabled!==e.sampleCoverage.enabled||t.sampleCoverage.value!==e.sampleCoverage.value||t.sampleCoverage.invert!==e.sampleCoverage.invert)&&r.push(na),r}Vo.apply=function(t,e,r){jo(t,e),qo(t,e),Ho(t,e),Yo(t,e),Go(t,e),Qo(t,e),Ko(t,e),Jo(t,e),Zo(t,e),ra(t,e),na(t,e),Wo(t,e,r),ea(t,e,r),oa(t,e,r)},Vo.partialApply=function(t,r,n,i,o,a,s,u){if(r!==n){var l=n._applyFunctions[r.id];e.t(l)||(l=aa(r,n),n._applyFunctions[r.id]=l);for(var c=l.length,p=0;p<c;++p)l[p](t,n)}((e.t(i.scissorTest)?i.scissorTest:r.scissorTest)!==(e.t(o.scissorTest)?o.scissorTest:n.scissorTest)||u)&&Wo(t,n,o);var f=e.t(i.blendingEnabled)?i.blendingEnabled:r.blending.enabled,h=e.t(o.blendingEnabled)?o.blendingEnabled:n.blending.enabled;(f!==h||h&&r.blending!==n.blending)&&ea(t,n,o),(r!==n||i!==o||i.context!==o.context||a!==s)&&oa(t,n,o)},Vo.getState=function(t){if(!e.t(t))throw new u.t("renderState is required.");return{frontFace:t.frontFace,cull:{enabled:t.cull.enabled,face:t.cull.face},lineWidth:t.lineWidth,polygonOffset:{enabled:t.polygonOffset.enabled,factor:t.polygonOffset.factor,units:t.polygonOffset.units},scissorTest:{enabled:t.scissorTest.enabled,rectangle:a.n.clone(t.scissorTest.rectangle)},depthRange:{near:t.depthRange.near,far:t.depthRange.far},depthTest:{enabled:t.depthTest.enabled,func:t.depthTest.func},colorMask:{red:t.colorMask.red,green:t.colorMask.green,blue:t.colorMask.blue,alpha:t.colorMask.alpha},depthMask:t.depthMask,stencilMask:t.stencilMask,blending:{enabled:t.blending.enabled,color:s.e.clone(t.blending.color),equationRgb:t.blending.equationRgb,equationAlpha:t.blending.equationAlpha,functionSourceRgb:t.blending.functionSourceRgb,functionSourceAlpha:t.blending.functionSourceAlpha,functionDestinationRgb:t.blending.functionDestinationRgb,functionDestinationAlpha:t.blending.functionDestinationAlpha},stencilTest:{enabled:t.stencilTest.enabled,frontFunction:t.stencilTest.frontFunction,backFunction:t.stencilTest.backFunction,reference:t.stencilTest.reference,mask:t.stencilTest.mask,frontOperation:{fail:t.stencilTest.frontOperation.fail,zFail:t.stencilTest.frontOperation.zFail,zPass:t.stencilTest.frontOperation.zPass},backOperation:{fail:t.stencilTest.backOperation.fail,zFail:t.stencilTest.backOperation.zFail,zPass:t.stencilTest.backOperation.zPass}},sampleCoverage:{enabled:t.sampleCoverage.enabled,value:t.sampleCoverage.value,invert:t.sampleCoverage.invert},viewport:e.t(t.viewport)?a.n.clone(t.viewport):void 0}};var sa,ua={ADD:i.t.FUNC_ADD,SUBTRACT:i.t.FUNC_SUBTRACT,REVERSE_SUBTRACT:i.t.FUNC_REVERSE_SUBTRACT,MIN:i.t.MIN,MAX:i.t.MAX},la=Object.freeze(ua),ca={ZERO:i.t.ZERO,ONE:i.t.ONE,SOURCE_COLOR:i.t.SRC_COLOR,ONE_MINUS_SOURCE_COLOR:i.t.ONE_MINUS_SRC_COLOR,DESTINATION_COLOR:i.t.DST_COLOR,ONE_MINUS_DESTINATION_COLOR:i.t.ONE_MINUS_DST_COLOR,SOURCE_ALPHA:i.t.SRC_ALPHA,ONE_MINUS_SOURCE_ALPHA:i.t.ONE_MINUS_SRC_ALPHA,DESTINATION_ALPHA:i.t.DST_ALPHA,ONE_MINUS_DESTINATION_ALPHA:i.t.ONE_MINUS_DST_ALPHA,CONSTANT_COLOR:i.t.CONSTANT_COLOR,ONE_MINUS_CONSTANT_COLOR:i.t.ONE_MINUS_CONSTANT_COLOR,CONSTANT_ALPHA:i.t.CONSTANT_ALPHA,ONE_MINUS_CONSTANT_ALPHA:i.t.ONE_MINUS_CONSTANT_ALPHA,SOURCE_ALPHA_SATURATE:i.t.SRC_ALPHA_SATURATE},pa=Object.freeze(ca),fa={DISABLED:Object.freeze({enabled:!1}),ALPHA_BLEND:Object.freeze({enabled:!0,equationRgb:la.ADD,equationAlpha:la.ADD,functionSourceRgb:pa.SOURCE_ALPHA,functionSourceAlpha:pa.ONE,functionDestinationRgb:pa.ONE_MINUS_SOURCE_ALPHA,functionDestinationAlpha:pa.ONE_MINUS_SOURCE_ALPHA}),PRE_MULTIPLIED_ALPHA_BLEND:Object.freeze({enabled:!0,equationRgb:la.ADD,equationAlpha:la.ADD,functionSourceRgb:pa.ONE,functionSourceAlpha:pa.ONE,functionDestinationRgb:pa.ONE_MINUS_SOURCE_ALPHA,functionDestinationAlpha:pa.ONE_MINUS_SOURCE_ALPHA}),ADDITIVE_BLEND:Object.freeze({enabled:!0,equationRgb:la.ADD,equationAlpha:la.ADD,functionSourceRgb:pa.SOURCE_ALPHA,functionSourceAlpha:pa.ONE,functionDestinationRgb:pa.ONE,functionDestinationAlpha:pa.ONE})},ha=Object.freeze(fa),ya=(sa=!0,function(t,e){var r=sa?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return sa=!1,r}),da=ya(void 0,(function(){return da.toString().search("(((.+)+)+)+$").toString().constructor(da).search("(((.+)+)+)+$")}));function ma(){}da(),ma.toVertexBuffer=function(t,e,r,n){var i=o.u.createVertexBuffer({context:t,typedArray:e.arrayBuffer,usage:n?o.A.DYNAMIC_DRAW:o.A.STATIC_DRAW});return i.vertexArrayDestroyable=!1,i.bytesPerElement=e.bytesPerElement,i.length=e.length,i.attributes=r,i.itemSize=e.bytesPerElement,i.dynamicDraw=n,!n&&delete e.arrayBuffer,i},ma.toIndexBuffer=function(t,e,r){if(0!==e.length){var i=o.u.createIndexBuffer({context:t,typedArray:e.arrayBuffer,usage:o.A.STATIC_DRAW,indexDatatype:n.IndexDatatype.UNSIGNED_SHORT});return i.vertexArrayDestroyable=!1,i.dynamicDraw=r,!i.dynamicDraw&&delete e.arrayBuffer,i}},ma.toRenderState=function(t,e,r){var n=ha.DISABLED;t.blendFunction[0]==i.t.ONE&&t.blendFunction[1]==i.t.ONE_MINUS_SRC_ALPHA&&(n=ha.ALPHA_BLEND);var o={frontFace:r.frontFace,cull:{enabled:r.enable,face:r.mode},depthRange:{near:e.range[0],far:e.range[1]},depthTest:{enabled:e.func!==i.t.ALWAYS,func:e.func},depthMask:e.mask,colorMask:{red:t.mask[0],green:t.mask[1],blue:t.mask[2],alpha:t.mask[3]},blending:n};return Vo.fromCache(o)},ma.toComponentDatatype=function(t){switch(t){case"Int8":return r.ComponentDatatype.BYTE;case"Uint8":return r.ComponentDatatype.UNSIGNED_BYTE;case"Int16":return r.ComponentDatatype.SHORT;case"Uint16":return r.ComponentDatatype.UNSIGNED_SHORT;case"Int32":return r.ComponentDatatype.INT;case"Uint32":return r.ComponentDatatype.UNSIGNED_INT;default:return r.ComponentDatatype.FLOAT}},ma.mbxAttributeToCesiumVertexArrtribute=function(t,e,n,i){var o=this.toComponentDatatype(t.type);r.ComponentDatatype.getSizeInBytes(o);var a=e.bytesPerElement,s=t.offset+a*(i||0);return{name:t.name,index:n,vertexBuffer:e,componentsPerAttribute:t.components,componentDatatype:o,offsetInBytes:s,strideInBytes:a,normalize:!1}};var ga,va=(ga=!0,function(t,e){var r=ga?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ga=!1,r}),xa=va(void 0,(function(){return xa.toString().search("(((.+)+)+)+$").toString().constructor(xa).search("(((.+)+)+)+$")}));xa();var ba=function(){this.ids=[],this.positions=[],this.indexed=!1};function _a(t,e,r,n){if(!(r>=n)){for(var i=t[r+n>>1],o=r-1,a=n+1;;){do{o++}while(t[o]<i);do{a--}while(t[a]>i);if(o>=a)break;wa(t,o,a),wa(e,3*o,3*a),wa(e,3*o+1,3*a+1),wa(e,3*o+2,3*a+2)}_a(t,e,r,a),_a(t,e,a+1,n)}}function wa(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}ba.prototype.add=function(t,e,r,n){this.ids.push(t),this.positions.push(e,r,n)},ba.prototype.getPositions=function(t){for(var e=0,r=this.ids.length-1;e<r;){var n=e+r>>1;this.ids[n]>=t?r=n:e=n+1}for(var i=[];this.ids[e]===t;){var o=this.positions[3*e],a=this.positions[3*e+1],s=this.positions[3*e+2];i.push({index:o,start:a,end:s}),e++}return i},ba.serialize=function(t,e){var r=new Float64Array(t.ids),n=new Uint32Array(t.positions);return _a(r,n,0,r.length-1),e&&e.push(r.buffer,n.buffer),{ids:r,positions:n}},ba.deserialize=function(t){var e=new ba;return e.ids=t.ids,e.positions=t.positions,e.indexed=!0,e},ri.register("FeaturePositionMap",ba);var Sa,Aa=(Sa=!0,function(t,e){var r=Sa?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Sa=!1,r}),Ea=Aa(void 0,(function(){return Ea.toString().search("(((.+)+)+)+$").toString().constructor(Ea).search("(((.+)+)+)+$")}));function Ta(t,e,r){return Math.min(r,Math.max(e,t))}function Ia(t,e){return 256*(t=Ta(Math.floor(t),0,255))+(e=Ta(Math.floor(e),0,255))}function Oa(t){return[Ia(255*t.r,255*t.g),Ia(255*t.b,255*t.a)]}Ea();var ka=function(t,e,r){this.value=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t})),this.type=r,this.maxValue=-1/0};ka.prototype.defines=function(){return this.names.map((function(t){return"HAS_UNIFORM_u_"+t}))},ka.prototype.setConstantPatternPositions=function(){},ka.prototype.populatePaintArray=function(){},ka.prototype.updatePaintArray=function(){},ka.prototype.upload=function(){},ka.prototype.destroy=function(){},ka.prototype.setUniforms=function(t,e,r,n){e.set(n.constantOr(this.value))},ka.prototype.getBinding=function(t,e){return"color"===this.type?new ko.UniformColor(t,e):new ko.Uniform1f(t,e)},ka.prototype.setUniformMap=function(t,e,r,n){var i=this;t[e]=function(){var t=n.get(r).constantOr(i.value);return"color"===i.type&&(t.red=t.r,t.green=t.g,t.blue=t.b,t.alpha=t.a),t}},ka.serialize=function(t){var e=t.value,r=t.names,n=t.type;return{value:ri.serialize(e),names:r,type:n}},ka.deserialize=function(t){var e=t.value,r=t.names,n=t.type;return new ka(ri.deserialize(e),r,n)};var Fa=function(t,e,r){this.value=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t})),this.type=r,this.maxValue=-1/0,this.patternPositions={patternTo:null,patternFrom:null}};Fa.prototype.defines=function(){return this.names.map((function(t){return"HAS_UNIFORM_u_"+t}))},Fa.prototype.populatePaintArray=function(){},Fa.prototype.updatePaintArray=function(){},Fa.prototype.upload=function(){},Fa.prototype.destroy=function(){},Fa.prototype.setConstantPatternPositions=function(t,e){this.patternPositions.patternTo=t.tlbr,this.patternPositions.patternFrom=e.tlbr},Fa.prototype.setUniforms=function(t,e,r,n,i){var o=this.patternPositions;"u_pattern_to"===i&&o.patternTo&&e.set(c.a(o.patternTo[0],o.patternTo[1],o.patternTo[2],o.patternTo[3])),"u_pattern_from"===i&&o.patternFrom&&e.set(c.a(o.patternFrom[0],o.patternFrom[1],o.patternFrom[2],o.patternFrom[3]))},Fa.prototype.getBinding=function(t,e){return new ko.Uniform4f(t,e)},Fa.prototype.setUniformMap=function(t,e,r,n){var i=this.patternPositions;t[e]=function(){return"u_pattern_to"===e&&i.patternTo?new c.a(i.patternTo[0],i.patternTo[1],i.patternTo[2],i.patternTo[3]):"u_pattern_from"===e&&i.patternFrom?new c.a(i.patternFrom[0],i.patternFrom[1],i.patternFrom[2],i.patternFrom[3]):void console.log("CrossFadedConstantBinder is not support")}};var Ra=function(t,e,r,n){this.expression=t,this.names=e,this.type=r,this.uniformNames=this.names.map((function(t){return"a_"+t})),this.maxValue=-1/0,this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Float32",components:"color"===r?2:1,offset:0}})),this.paintVertexArray=new n};Ra.prototype.defines=function(){return[]},Ra.prototype.setConstantPatternPositions=function(){},Ra.prototype.populatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,o=i.length;i.reserve(t);var a=this.expression.evaluate(new hi(0),e,{},[],n);if("color"===this.type)for(var s=Oa(a),u=o;u<t;u++)i.emplaceBack(s[0],s[1]);else{for(var l=o;l<t;l++)i.emplaceBack(a);this.maxValue=Math.max(this.maxValue,a)}},Ra.prototype.updatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,o=this.expression.evaluate({zoom:0},r,n);if("color"===this.type)for(var a=Oa(o),s=t;s<e;s++)i.emplace(s,a[0],a[1]);else{for(var u=t;u<e;u++)i.emplace(u,o);this.maxValue=Math.max(this.maxValue,o)}},Ra.prototype.upload=function(t){this.paintVertexArray&&this.paintVertexArray.arrayBuffer&&(this.paintVertexBuffer&&this.paintVertexBuffer.buffer?this.paintVertexBuffer.updateData(this.paintVertexArray):this.paintVertexBuffer=ma.toVertexBuffer(t,this.paintVertexArray,this.paintVertexAttributes))},Ra.prototype.destroy=function(){this.paintVertexBuffer&&this.paintVertexBuffer.destroy()},Ra.prototype.setUniforms=function(t,e){e.set(0)},Ra.prototype.getBinding=function(t,e){return new ko.Uniform1f(t,e)},Ra.prototype.setUniformMap=function(t,e,r,n){t[e]=function(){return 0}};var Ma=function(t,e,r,n,i,o){this.expression=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t+"_t"})),this.type=r,this.useIntegerZoom=n,this.zoom=i,this.maxValue=-1/0;var a=o;this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Float32",components:"color"===r?4:2,offset:0}})),this.paintVertexArray=new a};Ma.prototype.defines=function(){return[]},Ma.prototype.setConstantPatternPositions=function(){},Ma.prototype.populatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,o=i.length;i.reserve(t);var a=this.expression.evaluate(new hi(this.zoom),e,{},[],n),s=this.expression.evaluate(new hi(this.zoom+1),e,{},[],n);if("color"===this.type)for(var u=Oa(a),l=Oa(s),c=o;c<t;c++)i.emplaceBack(u[0],u[1],l[0],l[1]);else{for(var p=o;p<t;p++)i.emplaceBack(a,s);this.maxValue=Math.max(this.maxValue,a,s)}},Ma.prototype.updatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,o=this.expression.evaluate({zoom:this.zoom},r,n),a=this.expression.evaluate({zoom:this.zoom+1},r,n);if("color"===this.type)for(var s=Oa(o),u=Oa(a),l=t;l<e;l++)i.emplace(l,s[0],s[1],u[0],u[1]);else{for(var c=t;c<e;c++)i.emplace(c,o,a);this.maxValue=Math.max(this.maxValue,o,a)}},Ma.prototype.upload=function(t){this.paintVertexArray&&this.paintVertexArray.arrayBuffer&&(this.paintVertexBuffer&&this.paintVertexBuffer.buffer?this.paintVertexBuffer.updateData(this.paintVertexArray):this.paintVertexBuffer=ma.toVertexBuffer(t,this.paintVertexArray,this.paintVertexAttributes))},Ma.prototype.destroy=function(){this.paintVertexBuffer&&this.paintVertexBuffer.destroy()},Ma.prototype.interpolationFactor=function(t){return this.useIntegerZoom&&(t=Math.floor(t)),Ta(this.expression.interpolationFactor(t,this.zoom,this.zoom+1),0,1)},Ma.prototype.setUniforms=function(t,e,r){e.set(this.interpolationFactor(r.zoom))},Ma.prototype.getBinding=function(t,e){return new ko.Uniform1f(t,e)},Ma.prototype.setUniformMap=function(t,e,r,n){var i=this;t[e]=function(){return i.interpolationFactor(0)}};var Ca=function(t,e,r,n,i,o,a){this.expression=t,this.names=e,this.type=r,this.uniformNames=this.names.map((function(t){return"u_"+t+"_t"})),this.useIntegerZoom=n,this.zoom=i,this.maxValue=-1/0,this.layerId=a,this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Float32",components:4,offset:0}})),this.zoomInPaintVertexArray=new o,this.zoomOutPaintVertexArray=new o};Ca.prototype.defines=function(){return[]},Ca.prototype.setConstantPatternPositions=function(){},Ca.prototype.populatePaintArray=function(t,e,r){var n=this.zoomInPaintVertexArray,i=this.zoomOutPaintVertexArray,o=this.layerId,a=n.length;if(n.reserve(t),i.reserve(t),r&&e.patterns&&e.patterns[o]){var s=e.patterns[o],u=s.min,l=s.mid,c=s.max,p=r[u],f=r[l],h=r[c];if(!p||!f||!h)return;for(var y=a;y<t;y++)n.emplaceBack(f.tl[0],f.tl[1],f.br[0],f.br[1],p.tl[0],p.tl[1],p.br[0],p.br[1]),i.emplaceBack(f.tl[0],f.tl[1],f.br[0],f.br[1],h.tl[0],h.tl[1],h.br[0],h.br[1])}},Ca.prototype.updatePaintArray=function(t,e,r,n,i){var o=this.zoomInPaintVertexArray,a=this.zoomOutPaintVertexArray,s=this.layerId;if(i&&r.patterns&&r.patterns[s]){var u=r.patterns[s],l=u.min,c=u.mid,p=u.max,f=i[l],h=i[c],y=i[p];if(!f||!h||!y)return;for(var d=t;d<e;d++)o.emplace(d,h.tl[0],h.tl[1],h.br[0],h.br[1],f.tl[0],f.tl[1],f.br[0],f.br[1]),a.emplace(d,h.tl[0],h.tl[1],h.br[0],h.br[1],y.tl[0],y.tl[1],y.br[0],y.br[1])}},Ca.prototype.upload=function(t){this.zoomInPaintVertexArray&&this.zoomInPaintVertexArray.arrayBuffer&&this.zoomOutPaintVertexArray&&this.zoomOutPaintVertexArray.arrayBuffer&&(this.zoomInPaintVertexBuffer=ma.toVertexBuffer(t,this.zoomInPaintVertexArray,this.paintVertexAttributes),this.zoomOutPaintVertexBuffer=ma.toVertexBuffer(t,this.zoomOutPaintVertexArray,this.paintVertexAttributes))},Ca.prototype.destroy=function(){this.zoomOutPaintVertexBuffer&&this.zoomOutPaintVertexBuffer.destroy(),this.zoomInPaintVertexBuffer&&this.zoomInPaintVertexBuffer.destroy()},Ca.prototype.setUniforms=function(t,e){e.set(0)},Ca.prototype.getBinding=function(t,e){return new Uniform1f(t,e)},Ca.prototype.setUniformMap=function(t,e,r,n){t[e]=function(){return 0}};class Pa{constructor(){this.binders={},this.cacheKey="",this._buffers=[]}static createDynamic(t,e,r){const n=new Pa,i=[];for(const o in t.paint._values){if(!r(o))continue;const a=t.paint.get(o);if(!(a instanceof wo&&To(a.property.specification)))continue;const s=Ba(o,t.type),u=a.property.specification.type,l=a.property.useIntegerZoom;if("cross-faded"===a.property.specification["property-type"]||"cross-faded-data-driven"===a.property.specification["property-type"])if("constant"===a.value.kind)n.binders[o]=new Fa(a.value.value,s,u),i.push("/u_"+o);else{const r=Ua(o,u,"source");n.binders[o]=new Ca(a.value,s,u,l,e,r,t.id),i.push("/a_"+o)}else if("constant"===a.value.kind)n.binders[o]=new ka(a.value.value,s,u),i.push("/u_"+o);else if("source"===a.value.kind){const t=Ua(o,u,"source");n.binders[o]=new Ra(a.value,s,u,t),i.push("/a_"+o)}else{const t=Ua(o,u,"composite");n.binders[o]=new Ma(a.value,s,u,l,e,t),i.push("/z_"+o)}}return n.cacheKey=i.sort().join(""),n}populatePaintArrays(t,e,r,n,i){for(const r in this.binders){this.binders[r].populatePaintArray(t,e,n,i)}}setConstantPatternPositions(t,e){for(const r in this.binders){this.binders[r].setConstantPatternPositions(t,e)}}updatePaintArrays(t,e,r,n,i){let o=!1;for(const a in t){const s=e.getPositions(+a);for(const e of s){const s=r.feature(e.index);for(const r in this.binders){const u=this.binders[r];if(!(u instanceof ka||u instanceof Fa)&&!0===u.expression.isStateDependent){const l=n.paint.get(r);u.expression=l.value,u.updatePaintArray(e.start,e.end,s,t[a],i),o=!0}}}}return o}defines(){var t=[];for(var e in this.binders)t.push.apply(t,this.binders[e].defines());return t}getPaintVertexBuffers(){return this._buffers}getUniforms(t,e){const r=[];for(const n in this.binders){const i=this.binders[n];for(const o of i.uniformNames)if(e[o]){const a=i.getBinding(t,e[o]);r.push({name:o,property:n,binding:a})}}return r}setUniforms(t,e,r,n){for(const{name:i,property:o,binding:a}of e)this.binders[o].setUniforms(t,a,n,r.get(o),i)}updatePatternPaintBuffers(t){const e=[];for(const r in this.binders){const n=this.binders[r];if(n instanceof Ca){const r=2===t.fromScale?n.zoomInPaintVertexBuffer:n.zoomOutPaintVertexBuffer;r&&e.push(r)}else(n instanceof Ra||n instanceof Ma)&&n.paintVertexBuffer&&e.push(n.paintVertexBuffer)}this._buffers=e}upload(t){for(const e in this.binders)this.binders[e].upload(t);const e=[];for(const t in this.binders){const r=this.binders[t];(r instanceof Ra||r instanceof Ma)&&r.paintVertexBuffer&&e.push(r.paintVertexBuffer)}this._buffers=e}getAttributeLocation(){for(var t=0,e={},r=0;r<this.layoutAttributes.length;r++){e[this.layoutAttributes[r].name]=r,t++}for(var n=this.getPaintVertexBuffers(),i=0;n&&i<n.length;i++)for(var o=n[i],a=0;a<o.attributes.length;a++){e[o.attributes[a].name]=t++}return e}getUniformMaps(t,e,r){for(var n in this.binders)for(var i=this.binders[n],o=0,a=i.uniformNames;o<a.length;o+=1){var s=a[o];t[s]&&i.setUniformMap(r,s,n,e)}for(var u in t)!r[u]&&u.indexOf("_t")>-1&&(r[u]=function(){return 0});return r}destroy(){for(const t in this.binders)this.binders[t].destroy()}}class za{constructor(t,e,r,n){void 0===n&&(n=function(){return!0}),this.programConfigurations={};for(const i of e)this.programConfigurations[i.id]=Pa.createDynamic(i,r,n),this.programConfigurations[i.id].layoutAttributes=t;this.needsUpload=!1,this._featureMap=new ba,this._bufferOffset=0}populatePaintArrays(t,e,r,n,i){for(const o in this.programConfigurations)this.programConfigurations[o].populatePaintArrays(t,e,r,n,i);void 0!==e.id&&this._featureMap.add(+e.id,r,this._bufferOffset,t),this._bufferOffset=t,this.needsUpload=!0}updatePaintArrays(t,e,r,n){for(const i of r)this.needsUpload=this.programConfigurations[i.id].updatePaintArrays(t,this._featureMap,e,i,n)||this.needsUpload}get(t){return this.programConfigurations[t]}upload(t){if(this.needsUpload){for(const e in this.programConfigurations)this.programConfigurations[e].upload(t);this.needsUpload=!1}}destroy(){for(const t in this.programConfigurations)this.programConfigurations[t].destroy()}}function Ba(t,e){return{"text-opacity":["opacity"],"icon-opacity":["opacity"],"text-color":["fill_color"],"icon-color":["fill_color"],"text-halo-color":["halo_color"],"icon-halo-color":["halo_color"],"text-halo-blur":["halo_blur"],"icon-halo-blur":["halo_blur"],"text-halo-width":["halo_width"],"text-show-background":["show-background"],"icon-halo-width":["halo_width"],"line-gap-width":["gapwidth"],"line-pattern":["pattern_to","pattern_from"],"fill-pattern":["pattern_to","pattern_from"],"fill-extrusion-pattern":["pattern_to","pattern_from"]}[t]||[t.replace(e+"-","").replace(/-/g,"_")]}function Da(t){return{"line-pattern":{source:io,composite:io},"fill-pattern":{source:io,composite:io},"fill-extrusion-pattern":{source:io,composite:io}}[t]}function Ua(t,e,r){var n={color:{source:eo,composite:ro},number:{source:Gi,composite:eo}},i=Da(t);return i&&i[r]||n[e][r]}ri.register("ConstantBinder",ka),ri.register("CrossFadedConstantBinder",Fa),ri.register("SourceExpressionBinder",Ra),ri.register("CrossFadedCompositeBinder",Ca),ri.register("CompositeExpressionBinder",Ma),ri.register("ProgramConfiguration",Pa,{omit:["_buffers"]}),ri.register("ProgramConfigurationSet",za);var Na,Va=(Na=!0,function(t,e){var r=Na?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Na=!1,r}),La=Va(void 0,(function(){return La.toString().search("(((.+)+)+)+$").toString().constructor(La).search("(((.+)+)+)+$")}));function $a(t,e,r,n,i){for(var o=i.patternDependencies,a=0,s=e;a<s.length;a+=1){var u=s[a],l=u.paint.get(t+"-pattern").value;if("constant"!==l.kind){var c=l.evaluate({zoom:n-1},r,{},i.availableImages),p=l.evaluate({zoom:n},r,{},i.availableImages),f=l.evaluate({zoom:n+1},r,{},i.availableImages);c=c&&c.name?c.name:c,p=p&&p.name?p.name:p,f=f&&f.name?f.name:f,o[c]=!0,o[p]=!0,o[f]=!0,r.patterns[u.id]={min:c,mid:p,max:f}}}return r}La();var Xa,ja=(Xa=!0,function(t,e){var r=Xa?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Xa=!1,r}),qa=ja(void 0,(function(){return qa.toString().search("(((.+)+)+)+$").toString().constructor(qa).search("(((.+)+)+)+$")}));qa();var Ha=["Unknown","Point","LineString","Polygon"],Ya=J([{name:"a_pos_normal",components:2,type:"Int16"},{name:"a_data",components:4,type:"Uint8"}],4),Wa=Ya.members,Ga=63,Qa=Math.cos(Math.PI/180*37.5),Ka=15,Ja=20,Za=15,ts=.5,es=Math.pow(2,Za-1)/ts,rs=function(t){this.zoom=t.zoom,this.overscaling=1,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.hasPattern=!1,this.patternFeatures=[],this.layoutVertexArray=new Ni,this.indexArray=new Ji,this.programConfigurations=new za(Wa,t.layers,t.zoom),this.segments=new Ei,this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};function ns(t,e,r,n,i){is(t,e,r||0,n||t.length-1,i||as)}function is(t,e,r,n,i){for(;n>r;){if(n-r>600){var o=n-r+1,a=e-r+1,s=Math.log(o),u=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*u*(o-u)/o)*(a-o/2<0?-1:1);is(t,e,Math.max(r,Math.floor(e-a*u/o+l)),Math.min(n,Math.floor(e+(o-a)*u/o+l)),i)}var c=t[e],p=r,f=n;for(os(t,r,e),i(t[n],c)>0&&os(t,r,n);p<f;){for(os(t,p,f),p++,f--;i(t[p],c)<0;)p++;for(;i(t[f],c)>0;)f--}0===i(t[r],c)?os(t,r,f):os(t,++f,n),f<=e&&(r=f+1),e<=f&&(n=f-1)}}function os(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function as(t,e){return t<e?-1:t>e?1:0}rs.prototype.populate=function(t,e){this.hasPattern=!1;for(var r=this.layers[0].layout.get("line-sort-key"),n=[],i=0,o=t;i<o.length;i+=1){var a=o[i],s=a.feature,u=a.index,l=a.sourceLayerIndex;if(this.layers[0]._featureFilter(new hi(0),s)){var c=_i(s),p=r?r.evaluate(s,{}):void 0,f={id:s.id,properties:s.properties,type:s.type,sourceLayerIndex:l,index:u,geometry:c,patterns:{},sortKey:p};n.push(f)}}r&&n.sort((function(t,e){return t.sortKey-e.sortKey}));for(var h=0,y=n;h<y.length;h+=1){var d=y[h],m=d,g=m.geometry,v=m.index,x=m.sourceLayerIndex;if(this.hasPattern){var b=$a("line",this.layers,d,this.zoom,e);this.patternFeatures.push(b)}else this.addFeature(d,g,v,{});var _=t[v].feature;e.featureIndex.insert(_,g,v,x,this.index)}},rs.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},rs.prototype.addFeatures=function(t,e){for(var r=0,n=this.patternFeatures;r<n.length;r+=1){var i=n[r];this.addFeature(i,i.geometry,i.index,e)}},rs.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},rs.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},rs.prototype.upload=function(t){if(!this.uploaded){if(null==this.layoutVertexArray)return;var e=Wa;this.layoutVertexArray.length>0&&(this.layoutCesiumVertexBuffer=ma.toVertexBuffer(t,this.layoutVertexArray,e),this.cesiumIndexBuffer=ma.toIndexBuffer(t,this.indexArray))}this.programConfigurations.upload(t),this.uploaded=!0},rs.prototype.destroy=function(){this.layoutCesiumVertexBuffer&&(this.layoutCesiumVertexBuffer&&(this.layoutCesiumVertexBuffer.destroy(),this.cesiumIndexBuffer.destroy()),this.programConfigurations.destroy(),this.segments.destroy())},rs.prototype.clear=function(){e.t(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.t(this.indexArray)&&(this.indexArray=null)},rs.prototype.addFeature=function(t,e,r,n){for(var i=this.layers[0].layout,o=i.get("line-join").evaluate(t,{}),a=i.get("line-cap"),s=i.get("line-miter-limit"),u=i.get("line-round-limit"),l=0,c=e;l<c.length;l+=1){var p=c[l];this.addLine(p,t,o,a,s,u,r,n)}},rs.prototype.addLine=function(t,e,r,n,i,o,a,s){if(this.distance=0,this.scaledDistance=0,this.totalDistance=0,e.properties&&e.properties.hasOwnProperty("mapbox_clip_start")&&e.properties.hasOwnProperty("mapbox_clip_end")){this.clipStart=+e.properties.mapbox_clip_start,this.clipEnd=+e.properties.mapbox_clip_end;for(var u=0;u<t.length-1;u++)this.totalDistance+=t[u].dist(t[u+1])}for(var l="Polygon"===Ha[e.type],c=t.length;c>=2&&t[c-1].equals(t[c-2]);)c--;for(var p=0;p<c-1&&t[p].equals(t[p+1]);)p++;if(!(c<(l?3:2))){"bevel"===r&&(i=1.05);var f,h=this.overscaling<=16?Ka*gi/(512*this.overscaling):0,y=this.segments.prepareSegment(10*c,this.layoutVertexArray,this.indexArray),d=void 0,m=void 0,g=void 0,v=void 0;this.e1=this.e2=-1,l&&(f=t[c-2],v=t[p].sub(f)._unit()._perp());for(var x=p;x<c;x++)if(!(m=l&&x===c-1?t[p+1]:t[x+1])||!t[x].equals(m)){v&&(g=v),f&&(d=f),f=t[x],v=m?m.sub(f)._unit()._perp():g;var b=(g=g||v).add(v);(0!==b.x||0!==b.y)&&b._unit();var _=g.x*v.x+g.y*v.y,w=b.x*v.x+b.y*v.y,S=0!==w?1/w:1/0,A=2*Math.sqrt(2-2*w),E=w<Qa&&d&&m,T=g.x*v.y-g.y*v.x>0;if(E&&x>p){var I=f.dist(d);if(I>2*h){var O=f.sub(f.sub(d)._mult(h/I)._round());this.updateDistance(d,O),this.addCurrentVertex(O,g,0,0,y),d=O}}var k=d&&m,F=k?r:l?"butt":n;if(k&&"round"===F&&(S<o?F="miter":S<=2&&(F="fakeround")),"miter"===F&&S>i&&(F="bevel"),"bevel"===F&&(S>2&&(F="flipbevel"),S<i&&(F="miter")),d&&this.updateDistance(d,f),"miter"===F)b._mult(S),this.addCurrentVertex(f,b,0,0,y);else if("flipbevel"===F){if(S>100)b=v.mult(-1);else{var R=S*g.add(v).mag()/g.sub(v).mag();b._perp()._mult(R*(T?-1:1))}this.addCurrentVertex(f,b,0,0,y),this.addCurrentVertex(f,b.mult(-1),0,0,y)}else if("bevel"===F||"fakeround"===F){var M=-Math.sqrt(S*S-1),C=T?M:0,P=T?0:M;if(d&&this.addCurrentVertex(f,g,C,P,y),"fakeround"===F)for(var z=Math.round(180*A/Math.PI/Ja),B=1;B<z;B++){var D=B/z;if(.5!==D){var U=D-.5;D+=D*U*(D-1)*((1.0904+_*(_*(3.55645-1.43519*_)-3.2452))*U*U+(.848013+_*(.215638*_-1.06021)))}var N=v.sub(g)._mult(D)._add(g)._unit()._mult(T?-1:1);this.addHalfVertex(f,N.x,N.y,!1,T,0,y)}m&&this.addCurrentVertex(f,v,-C,-P,y)}else if("butt"===F)this.addCurrentVertex(f,b,0,0,y);else if("square"===F){var V=d?1:-1;this.addCurrentVertex(f,b,V,V,y)}else"round"===F&&(d&&(this.addCurrentVertex(f,g,0,0,y),this.addCurrentVertex(f,g,1,1,y,!0)),m&&(this.addCurrentVertex(f,v,-1,-1,y,!0),this.addCurrentVertex(f,v,0,0,y)));if(E&&x<c-1){var L=f.dist(m);if(L>2*h){var $=f.add(m.sub(f)._mult(h/L)._round());this.updateDistance(f,$),this.addCurrentVertex($,v,0,0,y),f=$}}}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,e,a,s)}},rs.prototype.addCurrentVertex=function(t,e,r,n,i,o){void 0===o&&(o=!1);var a=e.x+e.y*r,s=e.y-e.x*r,u=-e.x+e.y*n,l=-e.y-e.x*n;this.addHalfVertex(t,a,s,o,!1,r,i),this.addHalfVertex(t,u,l,o,!0,-n,i),this.distance>es/2&&0===this.totalDistance&&(this.distance=0,this.addCurrentVertex(t,e,r,n,i,o))},rs.prototype.addHalfVertex=function(t,e,r,n,i,o,a){var s=t.x,u=t.y,l=this.scaledDistance*ts;this.layoutVertexArray.emplaceBack((s<<1)+(n?1:0),(u<<1)+(i?1:0),Math.round(Ga*e)+128,Math.round(Ga*r)+128,1+(0===o?0:o<0?-1:1)|(63&l)<<2,l>>6);var c=a.vertexLength++;this.e1>=0&&this.e2>=0&&(this.indexArray.emplaceBack(this.e1,this.e2,c),a.primitiveLength++),i?this.e2=c:this.e1=c},rs.prototype.updateDistance=function(t,e){this.distance+=t.dist(e),this.scaledDistance=this.totalDistance>0?(this.clipStart+(this.clipEnd-this.clipStart)*this.distance/this.totalDistance)*(es-1):this.distance},ri.register("LineBucket",rs,{omit:["layers","patternFeatures"]});var ss,us=(ss=!0,function(t,e){var r=ss?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ss=!1,r}),ls=us(void 0,(function(){return ls.toString().search("(((.+)+)+)+$").toString().constructor(ls).search("(((.+)+)+)+$")}));function cs(t){for(var e=0,r=0,n=t.length,i=n-1,o=void 0,a=void 0;r<n;i=r++)o=t[r],e+=((a=t[i]).x-o.x)*(o.y+a.y);return e}function ps(t,e){var r=t.length;if(r<=1)return[t];for(var n,i,o=[],a=0;a<r;a++){var s=cs(t[a]);0!==s&&(t[a].area=Math.abs(s),void 0===i&&(i=s<0),i===s<0?(n&&o.push(n),n=[t[a]]):n.push(t[a]))}if(n&&o.push(n),e>1)for(var u=0;u<o.length;u++)o[u].length<=e||(ns(o[u],e,1,o[u].length-1,fs),o[u]=o[u].slice(0,e));return o}function fs(t,e){return e.area-t.area}ls();var hs,ys=(hs=!0,function(t,e){var r=hs?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return hs=!1,r}),ds=ys(void 0,(function(){return ds.toString().search("(((.+)+)+)+$").toString().constructor(ds).search("(((.+)+)+)+$")}));function ms(t,e,r){for(var n=r.patternDependencies,i=!1,o=0,a=e;o<a.length;o+=1){var s=a[o].paint.get(t+"-pattern");!s.isConstant()&&(i=!0);var u=s.constantOr(null);u&&(i=!0,n[u.to]=!0,n[u.from]=!0)}return i}if(ds(),typeof WebAssembly<"u"){let t=function(t){return vs.locateFile?vs.locateFile(t,Ts):Ts+t},e=function(t){e.shown||(e.shown={}),e.shown[t]||(e.shown[t]=1)},r=function(t,e,r){switch("*"===(e=e||"i8").charAt(e.length-1)&&(e="i32"),e){case"i1":case"i8":return Ds[t>>0];case"i16":return Ns[t>>1];case"i32":case"i64":return Vs[t>>2];case"float":return $s[t>>2];case"double":return Xs[t>>3];default:ft("invalid type for getValue: "+e)}return null},n=function(t,e){t||ft("Assertion failed: "+e)},i=function(t){var e=vs["_"+t];return n(e,"Cannot call unknown function "+t+", make sure it is exported"),e},o=function(t,e,r,o,a){var s={string:function(t){var e=0;if(null!=t&&0!==t){var r=1+(t.length<<2);e=zu(r),c(t,e,r)}return e},array:function(t){var e=zu(t.length);return p(t,e),e}};var l=i(t),f=[],h=0;if(n("array"!==e,'Return type should not be "array".'),o)for(var y=0;y<o.length;y++){var d=s[r[y]];d?(0===h&&(h=Du()),f[y]=d(o[y])):f[y]=o[y]}var m=l.apply(null,f);return m=function(t){return"string"===e?u(t):"boolean"===e?Boolean(t):t}(m),0!==h&&Bu(h),m},a=function(t,e,r,n){return function(){return o(t,e,r,arguments)}},s=function(t,r,n){for(var i=r+n,o=r;t[o]&&!(o>=i);)++o;if(o-r>16&&t.subarray&&zs)return zs.decode(t.subarray(r,o));for(var a="";r<o;){var s=t[r++];if(128&s){var u=63&t[r++];if(192!=(224&s)){var l=63&t[r++];if(224==(240&s)?s=(15&s)<<12|u<<6|l:(240!=(248&s)&&e("Invalid UTF-8 leading byte 0x"+s.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),s=(7&s)<<18|u<<12|l<<6|63&t[r++]),s<65536)a+=String.fromCharCode(s);else{var c=s-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&s)<<6|u)}else a+=String.fromCharCode(s)}return a},u=function(t,e){return t?s(Us,t,e):""},l=function(t,r,n,i){if(!(i>0))return 0;for(var o=n,a=n+i-1,s=0;s<t.length;++s){var u=t.charCodeAt(s);if(u>=55296&&u<=57343)u=65536+((1023&u)<<10)|1023&t.charCodeAt(++s);if(u<=127){if(n>=a)break;r[n++]=u}else if(u<=2047){if(n+1>=a)break;r[n++]=192|u>>6,r[n++]=128|63&u}else if(u<=65535){if(n+2>=a)break;r[n++]=224|u>>12,r[n++]=128|u>>6&63,r[n++]=128|63&u}else{if(n+3>=a)break;u>=2097152&&e("Invalid Unicode code point 0x"+u.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),r[n++]=240|u>>18,r[n++]=128|u>>12&63,r[n++]=128|u>>6&63,r[n++]=128|63&u}}return r[n]=0,n-o},c=function(t,e,r){return n("number"==typeof r,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),l(t,Us,e,r)},p=function(t,e){n(t.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),Ds.set(t,e)},f=function(t){return t.replace(/__Z[\w\d_]+/g,(function(t){return t===t?t:t+" ["+t+"]"}))},h=function(){var t=new Error;if(!t.stack){try{throw new Error(0)}catch(e){t=e}if(!t.stack)return"(no stack trace available)"}return t.stack.toString()},y=function(){var t=h();return vs.extraStackTrace&&(t+="\n"+vs.extraStackTrace()),f(t)},d=function(t,e){return t%e>0&&(t+=e-t%e),t},m=function(){vs.HEAP8=Ds=new Int8Array(Bs),vs.HEAP16=Ns=new Int16Array(Bs),vs.HEAP32=Vs=new Int32Array(Bs),vs.HEAPU8=Us=new Uint8Array(Bs),vs.HEAPU16=new Uint16Array(Bs),vs.HEAPU32=Ls=new Uint32Array(Bs),vs.HEAPF32=$s=new Float32Array(Bs),vs.HEAPF64=Xs=new Float64Array(Bs)},g=function(){n(0==(3&Hs)),Ls[(Hs>>2)-1]=34821223,Ls[(Hs>>2)-2]=2310721022},v=function(){(34821223!=Ls[(Hs>>2)-1]||2310721022!=Ls[(Hs>>2)-2])&&ft("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x02135467, but received 0x"+Ls[(Hs>>2)-2].toString(16)+" "+Ls[(Hs>>2)-1].toString(16)),1668509029!==Vs[0]&&ft("Runtime error: The application has corrupted its heap memory area (address zero)!")},x=function(t){ft("Stack overflow! Attempted to allocate "+t+" bytes on the stack, but stack has only "+(Hs-Du()+t)+" bytes available!")},b=function(t){for(;t.length>0;){var e=t.shift();if("function"!=typeof e){var r=e.func;"number"==typeof r?void 0===e.arg?vs.dynCall_v(r):vs.dynCall_vi(r,e.arg):r(void 0===e.arg?null:e.arg)}else e()}},_=function(){if(vs.preRun)for("function"==typeof vs.preRun&&(vs.preRun=[vs.preRun]);vs.preRun.length;)E(vs.preRun.shift());b(Ks)},w=function(){v(),!eu&&(eu=!0,b(Js))},S=function(){v(),b(Zs)},A=function(){if(v(),vs.postRun)for("function"==typeof vs.postRun&&(vs.postRun=[vs.postRun]);vs.postRun.length;)T(vs.postRun.shift());b(tu)},E=function(t){Ks.unshift(t)},T=function(t){tu.unshift(t)},I=function(t){nu++,vs.monitorRunDependencies&&vs.monitorRunDependencies(nu),t&&(n(!au[t]),au[t]=1,null===iu&&typeof setInterval<"u"&&(iu=setInterval((function(){if(Ps)return clearInterval(iu),void(iu=null)}),1e4)))},O=function(t){if(nu--,vs.monitorRunDependencies&&vs.monitorRunDependencies(nu),t&&(n(au[t]),delete au[t]),0==nu&&(null!==iu&&(clearInterval(iu),iu=null),ou)){var e=ou;ou=null,e()}},k=function(t){return String.prototype.startsWith?t.startsWith(lu):0===t.indexOf(lu)},F=function(){try{if(vs.wasmBinary)return new Uint8Array(vs.wasmBinary);if(vs.readBinary)return vs.readBinary(uu);throw"both async and sync fetching of the wasm failed"}catch(t){ft(t)}},R=function(){return vs.wasmBinary||!bs&&!_s||"function"!=typeof fetch?new Promise((function(t,e){t(F())})):fetch(uu,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+uu+"'";return t.arrayBuffer()})).catch((function(){return F()}))},M=function(t){var e={env:t,global:{NaN:NaN,Infinity:1/0},"global.Math":Math,asm2wasm:ks};function r(t,e){var r=t.exports;vs.asm=r,O("wasm-instantiate")}I("wasm-instantiate");var i=vs;function o(t){n(vs===i,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),i=null,r(t.instance)}function a(t){return R().then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(t){}))}if(vs.instantiateWasm)try{return vs.instantiateWasm(e,r)}catch{return!1}return function(){if(vs.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||k(uu)||"function"!=typeof fetch)return a(o);fetch(uu,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(o,(function(t){a(o)}))}))}(),{}},C=function(t){return Pu(t)},P=function(t){t&&fu[t].refcount++},z=function(t){if(!t||fu[t])return t;for(var e in fu)for(var r=+e,n=fu[r].adjusted,i=n.length,o=0;o<i;o++)if(n[o]===t)return r;return t},B=function(t){var e=fu[t];return e&&!e.caught&&(e.caught=!0,Cu.uncaught_exception--),e&&(e.rethrown=!1),hu.push(t),P(z(t)),t},D=function(t,e,r){throw fu[t]={ptr:t,adjusted:[t],type:e,destructor:r,refcount:0,caught:!1,rethrown:!1},"uncaught_exception"in Cu?Cu.uncaught_exception++:Cu.uncaught_exception=1,t+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."},U=function(){return!!Cu.uncaught_exception},N=function(){},V=function(){},L=function(t,e){yu.varargs=e;try{yu.getStreamFromFD(),yu.get(),yu.get(),yu.get(),yu.get();return ft("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(t){return(typeof su>"u"||!(t instanceof su.ErrnoError))&&ft(t),-t.errno}},$=function(){var t=vs._fflush;t&&t(0);var e=yu.buffers;e[1].length&&yu.printChar(1,10),e[2].length&&yu.printChar(2,10)},X=function(t,e){yu.varargs=e;try{for(var r=yu.get(),n=yu.get(),i=yu.get(),o=0,a=0;a<i;a++){for(var s=Vs[n+8*a>>2],u=Vs[n+(8*a+4)>>2],l=0;l<u;l++)yu.printChar(r,Us[s+l]);o+=u}return o}catch(t){return(typeof su>"u"||!(t instanceof su.ErrnoError))&&ft(t),-t.errno}},j=function(t,e){yu.varargs=e;try{return 0}catch(t){return(typeof su>"u"||!(t instanceof su.ErrnoError))&&ft(t),-t.errno}},q=function(t,e){yu.varargs=e;try{yu.getStreamFromFD();return ft("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(t){return(typeof su>"u"||!(t instanceof su.ErrnoError))&&ft(t),-t.errno}},H=function(){},Y=function(){vs.abort()},W=function(){return Ds.length},G=function(t,e,r){Us.set(Us.subarray(e,e+r),t)},Q=function(t){if(!vs.___errno_location)return t;Vs[vs.___errno_location()>>2]=t},K=function(t){ft("Cannot enlarge memory arrays to size "+t+" bytes (OOM). Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+Ds.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},J=function(t){t=d(t,65536);var e=Bs.byteLength;try{return-1!==Cs.grow((t-e)/65536)&&(Bs=Cs.buffer,!0)}catch(r){return console.error("emscripten_realloc_buffer: Attempted to grow from "+e+" bytes to "+t+" bytes, but got error: "+r),!1}},Z=function(t){var r=W();n(t>r);var i=65536,o=2147418112;if(t>o)return!1;for(var a=Math.max(r,16777216);a<t;)(a=a<=536870912?d(2*a,i):Math.min(d((3*a+2147483648)/4,i),o))===r&&e("Cannot ask for more memory since we reached the practical limit in browsers (which is just below 2GB), so the request would have failed. Requesting only "+Ds.length);return!!J(a)&&(m(),!0)},tt=function(t){Os("Invalid function pointer called with signature 'ii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},et=function(t){Os("Invalid function pointer called with signature 'iidiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},rt=function(t){Os("Invalid function pointer called with signature 'iiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},nt=function(t){Os("Invalid function pointer called with signature 'jiji'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},it=function(t){Os("Invalid function pointer called with signature 'v'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},ot=function(t){Os("Invalid function pointer called with signature 'vi'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},at=function(t){Os("Invalid function pointer called with signature 'vii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},st=function(t){Os("Invalid function pointer called with signature 'viiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},ut=function(t){Os("Invalid function pointer called with signature 'viiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},lt=function(t){Os("Invalid function pointer called with signature 'viiiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),Os("Build with ASSERTIONS=2 for more info."),ft(t)},ct=function(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t},pt=function(t){function e(){vs.calledRun||(vs.calledRun=!0,!Ps&&(w(),S(),vs.onRuntimeInitialized&&vs.onRuntimeInitialized(),n(!vs._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),A()))}t=t||vs.arguments,nu>0||(g(),_(),nu>0)||vs.calledRun||(vs.setStatus?(vs.setStatus("Running..."),setTimeout((function(){setTimeout((function(){vs.setStatus("")}),1),e()}),1)):e(),v())},ft=function(t){vs.onAbort&&vs.onAbort(t),Ps=!0;var e="abort("+(t=void 0!==t?'"'+t+'"':"")+") at "+y();throw Uu&&Uu.forEach((function(r){e=r(e,t)})),e};var gs,vs=typeof vs<"u"?vs:{},xs={};for(gs in vs)vs.hasOwnProperty(gs)&&(xs[gs]=vs[gs]);vs.arguments=[],vs.thisProgram="./this.program",vs.quit=function(t,e){throw e},vs.preRun=[],vs.postRun=[];var bs=!1,_s=!1,ws=!1,Ss=!1;if(bs="object"==typeof window,_s="function"==typeof importScripts,ws="object"==typeof process&&"function"==typeof require&&!bs&&!_s,Ss=!bs&&!ws&&!_s,vs.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var As,Es,Ts="";if(ws)Ts=__dirname+"/",vs.read=function(t,e){var r;return As||(As=require("fs")),Es||(Es=require("path")),t=Es.normalize(t),r=As.readFileSync(t),e?r:r.toString()},vs.readBinary=function(t){var e=vs.read(t,!0);return e.buffer||(e=new Uint8Array(e)),n(e.buffer),e},process.argv.length>1&&(vs.thisProgram=process.argv[1].replace(/\\/g,"/")),vs.arguments=process.argv.slice(2),typeof module<"u"&&(module.exports=vs),process.on("uncaughtException",(function(t){if(!(t instanceof ct))throw t})),process.on("unhandledRejection",ft),vs.quit=function(t){process.exit(t)},vs.inspect=function(){return"[Emscripten Module object]"};else if(Ss)typeof read<"u"&&(vs.read=function(t){return read(t)}),vs.readBinary=function(t){var e;return"function"==typeof readbuffer?new Uint8Array(readbuffer(t)):(e=read(t,"binary"),n("object"==typeof e),e)},typeof scriptArgs<"u"?vs.arguments=scriptArgs:typeof arguments<"u"&&(vs.arguments=arguments),"function"==typeof quit&&(vs.quit=function(t){quit(t)});else{if(!bs&&!_s)throw new Error("environment detection error");_s?Ts=self.location.href:document.currentScript&&(Ts=document.currentScript.src),Ts=0!==Ts.indexOf("blob:")?Ts.substr(0,Ts.lastIndexOf("/")+1):"",vs.read=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},_s&&(vs.readBinary=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),vs.readAsync=function(t,e,r){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?e(n.response):r()},n.onerror=r,n.send(null)},vs.setWindowTitle=function(t){document.title=t}}var Is=vs.print||(typeof console<"u"?console.log.bind(console):typeof print<"u"?print:null),Os=vs.printErr||(typeof printErr<"u"?printErr:typeof console<"u"&&console.warn.bind(console)||Is);for(gs in xs)xs.hasOwnProperty(gs)&&(vs[gs]=xs[gs]);xs=void 0,n(typeof vs.memoryInitializerPrefixURL>"u","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),n(typeof vs.pthreadMainPrefixURL>"u","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),n(typeof vs.cdInitializerPrefixURL>"u","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),n(typeof vs.filePackagePrefixURL>"u","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),Du=Bu=zu=function(){ft("cannot use the stack before compiled code is ready to run, and has provided stack access")};var ks={"f64-rem":function(t,e){return t%e},debugger:function(){}};new Array(0);var Fs=0,Rs=function(t){Fs=t},Ms=function(){return Fs};"object"!=typeof WebAssembly&&ft("No WebAssembly support found. Build with -s WASM=0 to target JavaScript instead.");var Cs,Ps=!1,zs=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;typeof TextDecoder<"u"&&new TextDecoder("utf-16le");var Bs,Ds,Us,Ns,Vs,Ls,$s,Xs,js=65536,qs=5872,Hs=5248752,Ys=5248752,Ws=5840;n(qs%16==0,"stack must start aligned"),n(Ys%16==0,"heap must start aligned");var Gs=5242880;vs.TOTAL_STACK&&n(Gs===vs.TOTAL_STACK,"the stack size can no longer be determined at runtime");var Qs=vs.TOTAL_MEMORY||16777216;if(Qs<Gs&&Os("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+Qs+"! (TOTAL_STACK="+Gs+")"),n(typeof Int32Array<"u"&&typeof Float64Array<"u"&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),vs.buffer?(Bs=vs.buffer,n(Bs.byteLength===Qs,"provided buffer should be "+Qs+" bytes, but it is "+Bs.byteLength)):("object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(n(Qs%js==0),Cs=new WebAssembly.Memory({initial:Qs/js}),Bs=Cs.buffer):Bs=new ArrayBuffer(Qs),n(Bs.byteLength===Qs)),m(),Vs[Ws>>2]=Ys,Vs[0]=1668509029,Ns[1]=25459,115!==Us[2]||99!==Us[3])throw"Runtime error: expected the system to be little-endian!";var Ks=[],Js=[],Zs=[],tu=[],eu=!1,ru=!1;n(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),n(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),n(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),n(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var nu=0,iu=null,ou=null,au={};vs.preloadedImages={},vs.preloadedAudios={};var su={error:function(){ft("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){su.error()},createDataFile:function(){su.error()},createPreloadedFile:function(){su.error()},createLazyFile:function(){su.error()},open:function(){su.error()},mkdev:function(){su.error()},registerDevice:function(){su.error()},analyzePath:function(){su.error()},loadFilesFromDB:function(){su.error()},ErrnoError:function(){su.error()}};vs.FS_createDataFile=su.createDataFile,vs.FS_createPreloadedFile=su.createPreloadedFile;var uu,lu="data:application/octet-stream;base64,",cu=typeof window>"u"?self:window;uu=cu.location.href.endsWith(".openrealspace")?"../../static/Build/Cesium/ThirdParty/earcut.wasm":"ThirdParty/earcut.wasm",k(uu)||(uu=t(uu)),vs.asm=function(t,e,r){e.memory=Cs,e.table=new WebAssembly.Table({initial:260,maximum:260,element:"anyfunc"}),e.__memory_base=1024,e.__table_base=0;var i=M(e);return n(i,"binaryen setup failed (no wasm support?)"),i};var pu=5856;n(pu%8==0);var fu={},hu=[],yu={buffers:[null,[],[]],printChar:function(t,e){var r=yu.buffers[t];n(r),0===e||10===e?((1===t?Is:Os)(s(r,0)),r.length=0):r.push(e)},varargs:0,get:function(t){return yu.varargs+=4,Vs[yu.varargs-4>>2]},getStr:function(){return u(yu.get())},get64:function(){var t=yu.get(),e=yu.get();return n(t>=0?0===e:-1===e),t},getZero:function(){n(0===yu.get())}},du={},mu={abort:ft,setTempRet0:Rs,getTempRet0:Ms,abortStackOverflow:x,nullFunc_ii:tt,nullFunc_iidiiii:et,nullFunc_iiii:rt,nullFunc_jiji:nt,nullFunc_v:it,nullFunc_vi:ot,nullFunc_vii:at,nullFunc_viiii:st,nullFunc_viiiii:ut,nullFunc_viiiiii:lt,___cxa_allocate_exception:C,___cxa_begin_catch:B,___cxa_throw:D,___cxa_uncaught_exception:U,___exception_addRef:P,___exception_deAdjust:z,___gxx_personality_v0:N,___lock:V,___setErrNo:Q,___syscall140:L,___syscall146:X,___syscall54:j,___syscall6:q,___unlock:H,_abort:Y,_emscripten_get_heap_size:W,_emscripten_memcpy_big:G,_emscripten_resize_heap:Z,abortOnCannotGrowMemory:K,emscripten_realloc_buffer:J,flush_NO_FILESYSTEM:$,tempDoublePtr:pu,DYNAMICTOP_PTR:Ws},gu=vs.asm(du,mu,Bs),vu=gu.__ZSt18uncaught_exceptionv;gu.__ZSt18uncaught_exceptionv=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vu.apply(null,arguments)};var xu=gu.___cxa_can_catch;gu.___cxa_can_catch=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),xu.apply(null,arguments)};var bu=gu.___cxa_is_pointer_type;gu.___cxa_is_pointer_type=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),bu.apply(null,arguments)};var _u=gu.___errno_location;gu.___errno_location=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_u.apply(null,arguments)};var wu=gu._earcut;gu._earcut=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),wu.apply(null,arguments)};var Su=gu._fflush;gu._fflush=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Su.apply(null,arguments)};var Au=gu._free;gu._free=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Au.apply(null,arguments)};var Eu=gu._llvm_maxnum_f64;gu._llvm_maxnum_f64=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Eu.apply(null,arguments)};var Tu=gu._llvm_minnum_f64;gu._llvm_minnum_f64=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Tu.apply(null,arguments)};var Iu=gu._malloc;gu._malloc=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Iu.apply(null,arguments)};var Ou=gu._sbrk;gu._sbrk=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ou.apply(null,arguments)};var ku=gu.establishStackSpace;gu.establishStackSpace=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ku.apply(null,arguments)};var Fu=gu.stackAlloc;gu.stackAlloc=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Fu.apply(null,arguments)};var Ru=gu.stackRestore;gu.stackRestore=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ru.apply(null,arguments)};var Mu=gu.stackSave;gu.stackSave=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Mu.apply(null,arguments)},vs.asm=gu;var Cu=vs.__ZSt18uncaught_exceptionv=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.__ZSt18uncaught_exceptionv.apply(null,arguments)};vs.___cxa_can_catch=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.___cxa_can_catch.apply(null,arguments)},vs.___cxa_is_pointer_type=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.___cxa_is_pointer_type.apply(null,arguments)},vs.___errno_location=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.___errno_location.apply(null,arguments)},vs._earcut=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._earcut.apply(null,arguments)},vs._emscripten_replace_memory=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._emscripten_replace_memory.apply(null,arguments)},vs._fflush=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._fflush.apply(null,arguments)},vs._free=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._free.apply(null,arguments)},vs._llvm_maxnum_f64=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._llvm_maxnum_f64.apply(null,arguments)},vs._llvm_minnum_f64=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._llvm_minnum_f64.apply(null,arguments)};var Pu=vs._malloc=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._malloc.apply(null,arguments)};vs._memcpy=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._memcpy.apply(null,arguments)},vs._memset=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._memset.apply(null,arguments)},vs._sbrk=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm._sbrk.apply(null,arguments)},vs.establishStackSpace=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.establishStackSpace.apply(null,arguments)};var zu=vs.stackAlloc=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.stackAlloc.apply(null,arguments)},Bu=vs.stackRestore=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.stackRestore.apply(null,arguments)},Du=vs.stackSave=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.stackSave.apply(null,arguments)};vs.dynCall_ii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_ii.apply(null,arguments)},vs.dynCall_iidiiii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_iidiiii.apply(null,arguments)},vs.dynCall_iiii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_iiii.apply(null,arguments)},vs.dynCall_jiji=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_jiji.apply(null,arguments)},vs.dynCall_v=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_v.apply(null,arguments)},vs.dynCall_vi=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_vi.apply(null,arguments)},vs.dynCall_vii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_vii.apply(null,arguments)},vs.dynCall_viiii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_viiii.apply(null,arguments)},vs.dynCall_viiiii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_viiiii.apply(null,arguments)},vs.dynCall_viiiiii=function(){return n(eu,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),n(!ru,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vs.asm.dynCall_viiiiii.apply(null,arguments)},vs.asm=gu,vs.intArrayFromString||(vs.intArrayFromString=function(){ft("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.intArrayToString||(vs.intArrayToString=function(){ft("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.ccall=o,vs.cwrap=a,vs.setValue||(vs.setValue=function(){ft("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getValue=r,vs.allocate||(vs.allocate=function(){ft("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getMemory||(vs.getMemory=function(){ft("'getMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.AsciiToString||(vs.AsciiToString=function(){ft("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stringToAscii||(vs.stringToAscii=function(){ft("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.UTF8ArrayToString||(vs.UTF8ArrayToString=function(){ft("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.UTF8ToString||(vs.UTF8ToString=function(){ft("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stringToUTF8Array||(vs.stringToUTF8Array=function(){ft("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stringToUTF8||(vs.stringToUTF8=function(){ft("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.lengthBytesUTF8||(vs.lengthBytesUTF8=function(){ft("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.UTF16ToString||(vs.UTF16ToString=function(){ft("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stringToUTF16||(vs.stringToUTF16=function(){ft("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.lengthBytesUTF16||(vs.lengthBytesUTF16=function(){ft("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.UTF32ToString||(vs.UTF32ToString=function(){ft("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stringToUTF32||(vs.stringToUTF32=function(){ft("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.lengthBytesUTF32||(vs.lengthBytesUTF32=function(){ft("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.allocateUTF8||(vs.allocateUTF8=function(){ft("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stackTrace||(vs.stackTrace=function(){ft("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addOnPreRun||(vs.addOnPreRun=function(){ft("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addOnInit||(vs.addOnInit=function(){ft("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addOnPreMain||(vs.addOnPreMain=function(){ft("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addOnExit||(vs.addOnExit=function(){ft("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addOnPostRun||(vs.addOnPostRun=function(){ft("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.writeStringToMemory||(vs.writeStringToMemory=function(){ft("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.writeArrayToMemory||(vs.writeArrayToMemory=function(){ft("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.writeAsciiToMemory||(vs.writeAsciiToMemory=function(){ft("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addRunDependency||(vs.addRunDependency=function(){ft("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.removeRunDependency||(vs.removeRunDependency=function(){ft("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.ENV||(vs.ENV=function(){ft("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.FS||(vs.FS=function(){ft("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.FS_createFolder||(vs.FS_createFolder=function(){ft("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createPath||(vs.FS_createPath=function(){ft("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createDataFile||(vs.FS_createDataFile=function(){ft("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createPreloadedFile||(vs.FS_createPreloadedFile=function(){ft("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createLazyFile||(vs.FS_createLazyFile=function(){ft("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createLink||(vs.FS_createLink=function(){ft("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_createDevice||(vs.FS_createDevice=function(){ft("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.FS_unlink||(vs.FS_unlink=function(){ft("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),vs.GL||(vs.GL=function(){ft("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.dynamicAlloc||(vs.dynamicAlloc=function(){ft("'dynamicAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.warnOnce||(vs.warnOnce=function(){ft("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.loadDynamicLibrary||(vs.loadDynamicLibrary=function(){ft("'loadDynamicLibrary' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.loadWebAssemblyModule||(vs.loadWebAssemblyModule=function(){ft("'loadWebAssemblyModule' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getLEB||(vs.getLEB=function(){ft("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getFunctionTables||(vs.getFunctionTables=function(){ft("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.alignFunctionTables||(vs.alignFunctionTables=function(){ft("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.registerFunctions||(vs.registerFunctions=function(){ft("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.addFunction||(vs.addFunction=function(){ft("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.removeFunction||(vs.removeFunction=function(){ft("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getFuncWrapper||(vs.getFuncWrapper=function(){ft("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.prettyPrint||(vs.prettyPrint=function(){ft("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.makeBigInt||(vs.makeBigInt=function(){ft("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.dynCall||(vs.dynCall=function(){ft("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getCompilerSetting||(vs.getCompilerSetting=function(){ft("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stackSave||(vs.stackSave=function(){ft("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stackRestore||(vs.stackRestore=function(){ft("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.stackAlloc||(vs.stackAlloc=function(){ft("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.establishStackSpace||(vs.establishStackSpace=function(){ft("'establishStackSpace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.print||(vs.print=function(){ft("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.printErr||(vs.printErr=function(){ft("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.getTempRet0||(vs.getTempRet0=function(){ft("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.setTempRet0||(vs.setTempRet0=function(){ft("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.Pointer_stringify||(vs.Pointer_stringify=function(){ft("'Pointer_stringify' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),vs.ALLOC_NORMAL||Object.defineProperty(vs,"ALLOC_NORMAL",{get:function(){ft("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),vs.ALLOC_STACK||Object.defineProperty(vs,"ALLOC_STACK",{get:function(){ft("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),vs.ALLOC_DYNAMIC||Object.defineProperty(vs,"ALLOC_DYNAMIC",{get:function(){ft("'ALLOC_DYNAMIC' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),vs.ALLOC_NONE||Object.defineProperty(vs,"ALLOC_NONE",{get:function(){ft("'ALLOC_NONE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),ct.prototype=new Error,ct.prototype.constructor=ct,ou=function t(){vs.calledRun||pt(),vs.calledRun||(ou=t)},vs.run=pt;var Uu=[];if(vs.abort=ft,vs.preInit)for("function"==typeof vs.preInit&&(vs.preInit=[vs.preInit]);vs.preInit.length>0;)vs.preInit.pop()();vs.noExitRuntime=!0,pt()}else vs=null;var Nu,Vu=vs,Lu=(Nu=!0,function(t,e){var r=Nu?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Nu=!1,r}),$u=Lu(void 0,(function(){return $u.toString().search("(((.+)+)+)+$").toString().constructor($u).search("(((.+)+)+)+$")}));$u();var Xu=!1;if(e.t(Vu)){Vu.onRuntimeInitialized=function(){Xu=!0};var ju=Vu.cwrap("earcut","number",["number","number","number","number","number","number"])}var qu=J([{name:"a_pos",components:2,type:"Int16"}],4),Hu=qu.members,Yu=500,Wu=function(t){this.zoom=t.zoom,this.overscaling=1,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.hasPattern=!1,this.patternFeatures=[],this.layoutVertexArray=new Bi,this.indexArray=new Ji,this.indexArray2=new Zi,this.programConfigurations=new za(Hu,t.layers,t.zoom),this.segments=new Ei,this.segments2=new Ei,this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};Wu.prototype.populate=function(t,e){this.hasPattern=ms("fill",this.layers,e);for(var r=[],n=0,i=t;n<i.length;n+=1){var o=i[n],a=o.feature,s=o.index,u=o.sourceLayerIndex;if(this.layers[0]._featureFilter(new hi(0),a)){var l=_i(a),c={id:a.id,properties:a.properties,type:a.type,sourceLayerIndex:u,index:s,geometry:l,patterns:{},sortKey:undefined};r.push(c)}}for(var p=0,f=r;p<f.length;p+=1){var h=f[p],y=h,d=y.geometry,m=y.index,g=y.sourceLayerIndex;if(this.hasPattern){var v=$a("fill",this.layers,h,this.zoom,e);this.patternFeatures.push(v)}else this.addFeature(h,d,m,{},e.indexData);var x=t[m].feature;e.featureIndex.insert(x,d,m,g,this.index)}},Wu.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},Wu.prototype.addFeatures=function(t,e){for(var r=0,n=this.patternFeatures;r<n.length;r+=1){var i=n[r];this.addFeature(i,i.geometry,i.index,e)}},Wu.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},Wu.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},Wu.prototype.upload=function(t){if(!this.uploaded){if(null==this.layoutVertexArray)return;var e=Hu;this.layoutVertexArray.length>0&&(this.layoutCesiumVertexBuffer=ma.toVertexBuffer(t,this.layoutVertexArray,e),this.cesiumIndexBuffer=ma.toIndexBuffer(t,this.indexArray),this.cesiumIndexBuffer2=ma.toIndexBuffer(t,this.indexArray2))}this.programConfigurations.upload(t),this.uploaded=!0},Wu.prototype.destroy=function(){this.layoutCesiumVertexBuffer&&(this.layoutCesiumVertexBuffer.destroy(),this.cesiumIndexBuffer?.destroy(),this.cesiumIndexBuffer2?.destroy(),this.programConfigurations.destroy(),this.segments.destroy(),this.segments2.destroy())},Wu.prototype.clear=function(){e.t(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.t(this.indexArray)&&(this.indexArray=null),e.t(this.indexArray2)&&(this.indexArray2=null)},Wu.prototype.addFeature=function(t,r,n,i,o){for(var a=0,s=ps(r,Yu);a<s.length;a+=1){for(var u=s[a],c=0,p=0,f=u;p<f.length;p+=1){c+=f[p].length}for(var h,y=this.segments.prepareSegment(c,this.layoutVertexArray,this.indexArray),d=y.vertexLength,m=[],g=[],v=0,x=u;v<x.length;v+=1){var b=x[v];if(0!==b.length){b!==u[0]&&g.push(m.length/2);var _=this.segments2.prepareSegment(b.length,this.layoutVertexArray,this.indexArray2),w=_.vertexLength;this.layoutVertexArray.emplaceBack(b[0].x,b[0].y),this.indexArray2.emplaceBack(w+b.length-1,w),m.push(b[0].x),m.push(b[0].y);for(var S=1;S<b.length;S++)this.layoutVertexArray.emplaceBack(b[S].x,b[S].y),this.indexArray2.emplaceBack(w+S-1,w+S),m.push(b[S].x),m.push(b[S].y);_.vertexLength+=b.length,_.primitiveLength+=b.length}}if(e.t(o)&&e.t(o[t.id]))h=o[t.id];else if(!0===Xu){var A=new Int32Array(m),E=A.length,T=Vu._malloc(Int32Array.BYTES_PER_ELEMENT*E);Vu.HEAP32.set(A,T/Int32Array.BYTES_PER_ELEMENT);var I=new Int32Array(g),O=I.length,k=Vu._malloc(Int32Array.BYTES_PER_ELEMENT*O);Vu.HEAP32.set(I,k/Int32Array.BYTES_PER_ELEMENT);var F=new Int32Array(10*E),R=Vu._malloc(Int32Array.BYTES_PER_ELEMENT*E*10);Vu.HEAP32.set(F,R/Int32Array.BYTES_PER_ELEMENT);var M=ju(T,E,k,O,2,R),C=new Int32Array(Vu.HEAP32.buffer,R,M);h=new Int32Array(C),Vu._free(T),Vu._free(k),Vu._free(R)}else h=l.m(m,g);for(var P=0;P<h.length;P+=3)this.indexArray.emplaceBack(d+h[P],d+h[P+1],d+h[P+2]);y.vertexLength+=c,y.primitiveLength+=h.length/3}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,t,n,i)},ri.register("FillBucket",Wu,{omit:["layers","patternFeatures"]});var Gu,Qu=(Gu=!0,function(t,e){var r=Gu?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Gu=!1,r}),Ku=Qu(void 0,(function(){return Ku.toString().search("(((.+)+)+)+$").toString().constructor(Ku).search("(((.+)+)+)+$")}));function Ju(t,e,r,n,i){this.properties={},this.extent=r,this.type=0,this._pbf=t,this._geometry=-1,this._keys=n,this._values=i,t.readFields(Zu,this,e)}function Zu(t,e,r){1==t?e.id=r.readVarint():2==t?tl(r,e):3==t?e.type=r.readVarint():4==t&&(e._geometry=r.pos)}function tl(t,e){for(var r=t.readVarint()+t.pos;t.pos<r;){var n=e._keys[t.readVarint()],i=e._values[t.readVarint()];e.properties[n]=i}}function el(t){var e=t.length;if(e<=1)return[t];for(var r,n,i=[],o=0;o<e;o++){var a=rl(t[o]);0!==a&&(void 0===n&&(n=a<0),n===a<0?(r&&i.push(r),r=[t[o]]):r.push(t[o]))}return r&&i.push(r),i}function rl(t){for(var e,r,n=0,i=0,o=t.length,a=o-1;i<o;a=i++)e=t[i],n+=((r=t[a]).x-e.x)*(e.y+r.y);return n}Ku(),Ju.types=["Unknown","Point","LineString","Polygon"],Ju.prototype.loadGeometry=function(){var t=this._pbf;t.pos=this._geometry;for(var e,r=t.readVarint()+t.pos,n=1,i=0,o=0,a=0,s=[];t.pos<r;){if(i<=0){var u=t.readVarint();n=7&u,i=u>>3}i--,1===n||2===n?(o+=t.readSVarint(),a+=t.readSVarint(),1===n&&(e&&s.push(e),e=[]),e.push(new zi(o,a))):7===n?e&&e.push(e[0].clone()):console.log("VectorTileFeature loadGeometry unknown command "+n)}return e&&s.push(e),s},Ju.prototype.bbox=function(){var t=this._pbf;t.pos=this._geometry;for(var e=t.readVarint()+t.pos,r=1,n=0,i=0,o=0,a=1/0,s=-1/0,u=1/0,l=-1/0;t.pos<e;){if(n<=0){var c=t.readVarint();r=7&c,n=c>>3}if(n--,1===r||2===r)(i+=t.readSVarint())<a&&(a=i),i>s&&(s=i),(o+=t.readSVarint())<u&&(u=o),o>l&&(l=o);else if(7!==r)throw new Error("unknown command "+r)}return[a,u,s,l]},Ju.prototype.toGeoJSON=function(t,e,r){var n,i,o=this.extent*Math.pow(2,r),a=this.extent*t,s=this.extent*e,u=this.loadGeometry(),l=Ju.types[this.type];function c(t){for(var e=0;e<t.length;e++){var r=t[e],n=180-360*(r.y+s)/o;t[e]=[360*(r.x+a)/o-180,360/Math.PI*Math.atan(Math.exp(n*Math.PI/180))-90]}}switch(this.type){case 1:var p=[];for(n=0;n<u.length;n++)p[n]=u[n][0];c(u=p);break;case 2:for(n=0;n<u.length;n++)c(u[n]);break;case 3:for(u=el(u),n=0;n<u.length;n++)for(i=0;i<u[n].length;i++)c(u[n][i])}1===u.length?u=u[0]:l="Multi"+l;var f={type:"Feature",geometry:{type:l,coordinates:u},properties:this.properties};return"id"in this&&(f.id=this.id),f};var nl,il=(nl=!0,function(t,e){var r=nl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return nl=!1,r}),ol=il(void 0,(function(){return ol.toString().search("(((.+)+)+)+$").toString().constructor(ol).search("(((.+)+)+)+$")}));function al(t,e){this.version=1,this.name=null,this.extent=4096,this.length=0,this._pbf=t,this._keys=[],this._values=[],this._features=[],t.readFields(sl,this,e),this.length=this._features.length}function sl(t,e,r){15===t?e.version=r.readVarint():1===t?e.name=r.readString():5===t?e.extent=r.readVarint():2===t?e._features.push(r.pos):3===t?e._keys.push(r.readString()):4===t&&e._values.push(ul(r))}function ul(t){for(var e=null,r=t.readVarint()+t.pos;t.pos<r;){var n=t.readVarint()>>3;e=1===n?t.readString():2===n?t.readFloat():3===n?t.readDouble():4===n?t.readVarint64():5===n?t.readVarint():6===n?t.readSVarint():7===n?t.readBoolean():null}return e}ol(),al.prototype.feature=function(t){if(t<0||t>=this._features.length)throw new Error("feature index out of bounds");this._pbf.pos=this._features[t];var e=this._pbf.readVarint()+this._pbf.pos;return new Ju(this._pbf,e,this.extent,this._keys,this._values)};var ll,cl=(ll=!0,function(t,e){var r=ll?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ll=!1,r}),pl=cl(void 0,(function(){return pl.toString().search("(((.+)+)+)+$").toString().constructor(pl).search("(((.+)+)+)+$")}));function fl(t,e){this.layers=t.readFields(hl,{},e)}function hl(t,e,r){if(3===t){var n=new al(r,r.readVarint()+r.pos);n.length&&(e[n.name]=n)}}pl();var yl,dl=(yl=!0,function(t,e){var r=yl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return yl=!1,r}),ml=dl(void 0,(function(){return ml.toString().search("(((.+)+)+)+$").toString().constructor(ml).search("(((.+)+)+)+$")}));ml();var gl=3;function vl(t,e,r){var n=this.cells=[];if(t instanceof ArrayBuffer){this.arrayBuffer=t;var i=new Int32Array(this.arrayBuffer);t=i[0],e=i[1],r=i[2],this.d=e+2*r;for(var o=0;o<this.d*this.d;o++){var a=i[gl+o],s=i[gl+o+1];n.push(a===s?null:i.subarray(a,s))}var u=i[gl+n.length],l=i[gl+n.length+1];this.keys=i.subarray(u,l),this.bboxes=i.subarray(l),this.insert=this._insertReadonly}else{this.d=e+2*r;for(var c=0;c<this.d*this.d;c++)n.push([]);this.keys=[],this.bboxes=[]}this.n=e,this.extent=t,this.padding=r,this.scale=e/t,this.uid=0;var p=r/e*t;this.min=-p,this.max=t+p}vl.prototype.insert=function(t,e,r,n,i){this._forEachCell(e,r,n,i,this._insertCell,this.uid++),this.keys.push(t),this.bboxes.push(e),this.bboxes.push(r),this.bboxes.push(n),this.bboxes.push(i)},vl.prototype._insertReadonly=function(){throw"Cannot insert into a GridIndex created from an ArrayBuffer."},vl.prototype._insertCell=function(t,e,r,n,i,o){this.cells[i].push(o)},vl.prototype.query=function(t,e,r,n,i){var o=this.min,a=this.max;if(t<=o&&e<=o&&a<=r&&a<=n&&!i)return Array.prototype.slice.call(this.keys);var s=[];return this._forEachCell(t,e,r,n,this._queryCell,s,{},i),s},vl.prototype._queryCell=function(t,e,r,n,i,o,a,s){var u=this.cells[i];if(null!==u)for(var l=this.keys,c=this.bboxes,p=0;p<u.length;p++){var f=u[p];if(void 0===a[f]){var h=4*f;(s?s(c[h+0],c[h+1],c[h+2],c[h+3]):t<=c[h+2]&&e<=c[h+3]&&r>=c[h+0]&&n>=c[h+1])?(a[f]=!0,o.push(l[f])):a[f]=!1}}},vl.prototype._forEachCell=function(t,e,r,n,i,o,a,s){for(var u=this._convertToCellCoord(t),l=this._convertToCellCoord(e),c=this._convertToCellCoord(r),p=this._convertToCellCoord(n),f=u;f<=c;f++)for(var h=l;h<=p;h++){var y=this.d*h+f;if((!s||s(this._convertFromCellCoord(f),this._convertFromCellCoord(h),this._convertFromCellCoord(f+1),this._convertFromCellCoord(h+1)))&&i.call(this,t,e,r,n,y,o,a,s))return}},vl.prototype._convertFromCellCoord=function(t){return(t-this.padding)/this.scale},vl.prototype._convertToCellCoord=function(t){return Math.max(0,Math.min(this.d-1,Math.floor(t*this.scale)+this.padding))},vl.prototype.toArrayBuffer=function(){if(this.arrayBuffer)return this.arrayBuffer;for(var t=this.cells,e=gl+this.cells.length+1+1,r=0,n=0;n<this.cells.length;n++)r+=this.cells[n].length;var i=new Int32Array(e+r+this.keys.length+this.bboxes.length);i[0]=this.extent,i[1]=this.n,i[2]=this.padding;for(var o=e,a=0;a<t.length;a++){var s=t[a];i[gl+a]=o,i.set(s,o),o+=s.length}return i[gl+t.length]=o,i.set(this.keys,o),o+=this.keys.length,i[gl+t.length+1]=o,i.set(this.bboxes,o),o+=this.bboxes.length,i.buffer},ri.register("GridIndex",vl,{omit:["layers","patternFeatures"]});const xl=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),bl=xl(void 0,(function(){return bl.toString().search("(((.+)+)+)+$").toString().constructor(bl).search("(((.+)+)+)+$")}));function _l(t,e,r,n){const i=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),o=i(this,(function(){return o.toString().search("(((.+)+)+)+$").toString().constructor(o).search("(((.+)+)+)+$")}));o()}function wl(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];for(var n=0,i=e;n<i.length;n+=1){var o=i[n];for(var a in o)t[a]=o[a]}return t}function Sl(t,e){return-1!==t.indexOf(e,t.length-e.length)}function Al(t,e,r){const n={};for(const i in t)n[i]=e.call(r||this,t[i],i,t);return n}function El(t,e,r){const n={};for(const i in t)e.call(r||this,t[i],i,t)&&(n[i]=t[i]);return n}function Tl(t){return Array.isArray(t)?t.map(Tl):"object"==typeof t&&t?Al(t,Tl):t}function Il(t,e){for(let r=0;r<t.length;r++)if(e.indexOf(t[r])>=0)return!0;return!1}function Ol(t,e,r){return(r.y-t.y)*(e.x-t.x)>(e.y-t.y)*(r.x-t.x)}function kl(){return"undefined"!=typeof WorkerGlobalScope&&"undefined"!=typeof self&&self instanceof WorkerGlobalScope}bl(),_l();var Fl,Rl=(Fl=!0,function(t,e){var r=Fl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Fl=!1,r}),Ml=Rl(void 0,(function(){return Ml.toString().search("(((.+)+)+)+$").toString().constructor(Ml).search("(((.+)+)+)+$")}));Ml();var Cl=function(t,e){void 0===e&&(e=[]),this.parent=t,this.bindings={};for(var r=0,n=e;r<n.length;r+=1){var i=n[r],o=i[0],a=i[1];this.bindings[o]=a}};Cl.prototype.concat=function(t){return new Cl(this,t)},Cl.prototype.get=function(t){if(this.bindings[t])return this.bindings[t];if(this.parent)return this.parent.get(t);throw new Error(t+" not found in scope.")},Cl.prototype.has=function(t){return!!this.bindings[t]||!!this.parent&&this.parent.has(t)};var Pl,zl=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e,r){t.call(this,r),this.message=r,this.key=e}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i}(Error),Bl=(Pl=!0,function(t,e){var r=Pl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Pl=!1,r}),Dl=Bl(void 0,(function(){return Dl.toString().search("(((.+)+)+)+$").toString().constructor(Dl).search("(((.+)+)+)+$")}));Dl();var Ul=["Unknown","Point","LineString","Polygon"],Nl=function(){this.globals=null,this.feature=null,this.featureState=null,this.formattedSection=null,this._parseColorCache={},this.availableImages=null};Nl.prototype.id=function(){return this.feature&&"id"in this.feature?this.feature.id:null},Nl.prototype.geometryType=function(){return this.feature?"number"==typeof this.feature.type?Ul[this.feature.type]:this.feature.type:null},Nl.prototype.properties=function(){return this.feature&&this.feature.properties||{}},Nl.prototype.parseColor=function(t){var e=this._parseColorCache[t];return!e&&(e=this._parseColorCache[t]=Mt.parse(t)),e},ri.register("EvaluationContext",Nl);var Vl,Ll=(Vl=!0,function(t,e){var r=Vl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Vl=!1,r}),$l=Ll(void 0,(function(){return $l.toString().search("(((.+)+)+)+$").toString().constructor($l).search("(((.+)+)+)+$")}));function Xl(){}$l(),Xl.isFeatureConstant=function(t){if(t instanceof Yl.CompoundExpression){if("get"===t.name&&1===t.args.length)return!1;if("feature-state"===t.name)return!1;if("has"===t.name&&1===t.args.length)return!1;if("properties"===t.name||"geometry-type"===t.name||"id"===t.name)return!1;if(/^filter-/.test(t.name))return!1}var e=!0;return t.eachChild((function(t){e&&!Xl.isFeatureConstant(t)&&(e=!1)})),e},Xl.isStateConstant=function(t){if(t instanceof Yl.CompoundExpression&&"feature-state"===t.name)return!1;var e=!0;return t.eachChild((function(t){e&&!Xl.isStateConstant(t)&&(e=!1)})),e},Xl.isGlobalPropertyConstant=function(t,e){if(t instanceof Yl.CompoundExpression&&e.indexOf(t.name)>=0)return!1;var r=!0;return t.eachChild((function(t){r&&!Xl.isGlobalPropertyConstant(t,e)&&(r=!1)})),r};var jl,ql=(jl=!0,function(t,e){var r=jl?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return jl=!1,r}),Hl=ql(void 0,(function(){return Hl.toString().search("(((.+)+)+)+$").toString().constructor(Hl).search("(((.+)+)+)+$")}));Hl();var Yl=function(t,e,r,n,i){void 0===e&&(e=[]),void 0===n&&(n=new Cl),void 0===i&&(i=[]),this.registry=t,this.path=e,this.key=e.map((function(t){return"["+t+"]"})).join(""),this.scope=n,this.errors=i,this.expectedType=r};function Wl(t,e){const r=e[t];return void 0===r?null:r}Yl.prototype.parse=function(t,e,r,n,i){return void 0===i&&(i={}),e?this.concat(e,r,n)._parse(t,i):this._parse(t,i)},Yl.prototype._parse=function(t,e){function r(t,e,r){return"assert"===r?new Ce(e,[t]):"coerce"===r?new ar(e,[t]):t}if((null===t||"string"==typeof t||"boolean"==typeof t||"number"==typeof t)&&(t=["literal",t]),Array.isArray(t)){if(0===t.length)return this.error('Expected an array with at least one element. If you wanted a literal array, use ["literal", []].');var n=t[0];if("string"!=typeof n)return this.error("Expression name must be a string, but found "+typeof n+' instead. If you wanted a literal array, use ["literal", [...]].',0),null;var i=this.registry[n];if(i){var o=i.parse(t,this);if(!o)return null;if(this.expectedType){var a=this.expectedType,s=o.type;if("string"!==a.kind&&"number"!==a.kind&&"boolean"!==a.kind&&"object"!==a.kind&&"array"!==a.kind||"value"!==s.kind)if("color"!==a.kind&&"formatted"!==a.kind&&"resolvedImage"!==a.kind||"value"!==s.kind&&"string"!==s.kind){if(this.checkSubtype(a,s))return null}else o=r(o,a,e.typeAnnotation||"coerce");else o=r(o,a,e.typeAnnotation||"assert")}return!(o instanceof An)&&o.type.kind,o}return this.error('Unknown expression "'+n+'". If you wanted a literal array, use ["literal", [...]].',0)}return void 0===t?this.error("'undefined' value invalid. Use null instead."):"object"==typeof t?this.error('Bare objects invalid. Use ["literal", {...}] instead.'):this.error("Expected an array, but found "+typeof t+" instead.")},Yl.prototype.concat=function(t,e,r){var n="number"==typeof t?this.path.concat(t):this.path,i=r?this.scope.concat(r):this.scope;return new Yl(this.registry,n,e||null,i,this.errors)},Yl.prototype.error=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];var n=""+this.key+e.map((function(t){return"["+t+"]"})).join("");this.errors.push(new zl(n,t))},Yl.prototype.checkSubtype=function(t,e){var r=qt(t,e);return r&&this.error(r),r};var Gl=function(t,e,r,n){this.name=t,this.type=e,this._evaluate=r,this.args=n};function Ql(t){return Array.isArray(t)?"("+t.map(toString).join(", ")+")":"("+toString(t.type)+"...)"}Gl.prototype.evaluate=function(t,e){return this._evaluate(t,this.args,e)},Gl.prototype.eachChild=function(t){this.args.forEach(t)},Gl.prototype.possibleOutputs=function(){return[void 0]},Gl.prototype.serialize=function(){return[this.name].concat(this.args.map((function(t){return t.serialize()})))},Gl.parse=function(t,e){var r,n=t[0],i=Gl.definitions[n];if(!i)return e.error('Unknown expression "'+n+'". If you wanted a literal array, use ["literal", [...]].',0);for(var o=Array.isArray(i)?i[0]:i.type,a=Array.isArray(i)?[[i[1],i[2]]]:i.overloads,s=a.filter((function(e){var r=e[0];return!Array.isArray(r)||r.length===t.length-1})),u=null,l=0,c=s;l<c.length;l+=1){var p=c[l],f=p[0],h=p[1];u=new Yl(e.registry,e.path,null,e.scope);for(var y=[],d=!1,m=1;m<t.length;m++){var g=t[m],v=Array.isArray(f)?f[m-1]:f.type,x=u.parse(g,1+y.length,v);if(!x){d=!0;break}y.push(x)}if(!d)if(Array.isArray(f)&&f.length!==y.length)u.error("Expected "+f.length+" arguments, but found "+y.length+" instead.");else{for(var b=0;b<y.length;b++){var _=Array.isArray(f)?f[b]:f.type,w=y[b];u.concat(b+1).checkSubtype(_,w.type)}if(0===u.errors.length)return new Gl(n,o,h,y)}}if(1===s.length)(r=e.errors).push.apply(r,u.errors);else{for(var S=(s.length?s:a).map((function(t){return Ql(t[0])})).join(" | "),A=[],E=1;E<t.length;E++){var T=e.parse(t[E],1+A.length);if(!T)return null;A.push(toString(T.type))}e.error("Expected arguments of type "+S+", but found ("+A.join(", ")+") instead.")}return null},Gl.register=function(t,e){for(var r in Gl.definitions=e,e)t[r]=Gl},ri.register("CompoundExpression",Gl);var Kl={kind:"number"},Jl={kind:"string"},Zl={kind:"boolean"},tc={kind:"color"},ec={kind:"object"},rc={kind:"value"},nc={kind:"error"},ic={kind:"collator"};function oc(t,e){return{kind:"array",itemType:t,N:e}}function ac(t){return{type:t}}function sc(t,e){var r=e[0],n=e[1],i=e[2],o=e[3];r=r.evaluate(t),n=n.evaluate(t),i=i.evaluate(t);var a=o?o.evaluate(t):1,s=Values.validateRGBA(r,n,i,a);if(s)throw new RuntimeError(s);return new Color(r/255*a,n/255*a,i/255*a,a)}Gl.register(Zn,{error:[nc,[Jl],function(t,e){var r=e[0];throw new RuntimeError(r.evaluate(t))}],typeof:[Jl,[rc],function(t,e){var r=e[0];return toString(Values.typeOf(r.evaluate(t)))}],"to-rgba":[oc(Kl,4),[tc],function(t,e){return e[0].evaluate(t).toArray()}],rgb:[tc,[Kl,Kl,Kl],sc],rgba:[tc,[Kl,Kl,Kl,Kl],sc],has:{type:Zl,overloads:[[[Jl],function(t,e){var r=e[0];return has(r.evaluate(t),t.properties())}],[[Jl,ec],function(t,e){var r=e[0],n=e[1];return has(r.evaluate(t),n.evaluate(t))}]]},get:{type:rc,overloads:[[[Jl],function(t,e){return Wl(e[0].evaluate(t),t.properties())}],[[Jl,ec],function(t,e){var r=e[0],n=e[1];return Wl(r.evaluate(t),n.evaluate(t))}]]},"feature-state":[rc,[Jl],function(t,e){return Wl(e[0].evaluate(t),t.featureState||{})}],properties:[ec,[],function(t){return t.properties()}],"geometry-type":[Jl,[],function(t){return t.geometryType()}],id:[rc,[],function(t){return t.id()}],zoom:[Kl,[],function(t){return t.globals.zoom}],"heatmap-density":[Kl,[],function(t){return t.globals.heatmapDensity||0}],"line-progress":[Kl,[],function(t){return t.globals.lineProgress||0}],accumulated:[rc,[],function(t){return void 0===t.globals.accumulated?null:t.globals.accumulated}],"+":[Kl,ac(Kl),function(t,e){for(var r=0,n=0,i=e;n<i.length;n+=1){r+=i[n].evaluate(t)}return r}],"*":[Kl,ac(Kl),function(t,e){for(var r=1,n=0,i=e;n<i.length;n+=1){r*=i[n].evaluate(t)}return r}],"-":{type:Kl,overloads:[[[Kl,Kl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)-n.evaluate(t)}],[[Kl],function(t,e){return-e[0].evaluate(t)}]]},"/":[Kl,[Kl,Kl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)/n.evaluate(t)}],"%":[Kl,[Kl,Kl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)%n.evaluate(t)}],ln2:[Kl,[],function(){return Math.LN2}],pi:[Kl,[],function(){return Math.PI}],e:[Kl,[],function(){return Math.E}],"^":[Kl,[Kl,Kl],function(t,e){var r=e[0],n=e[1];return Math.pow(r.evaluate(t),n.evaluate(t))}],sqrt:[Kl,[Kl],function(t,e){var r=e[0];return Math.sqrt(r.evaluate(t))}],log10:[Kl,[Kl],function(t,e){var r=e[0];return Math.log(r.evaluate(t))/Math.LN10}],ln:[Kl,[Kl],function(t,e){var r=e[0];return Math.log(r.evaluate(t))}],log2:[Kl,[Kl],function(t,e){var r=e[0];return Math.log(r.evaluate(t))/Math.LN2}],sin:[Kl,[Kl],function(t,e){var r=e[0];return Math.sin(r.evaluate(t))}],cos:[Kl,[Kl],function(t,e){var r=e[0];return Math.cos(r.evaluate(t))}],tan:[Kl,[Kl],function(t,e){var r=e[0];return Math.tan(r.evaluate(t))}],asin:[Kl,[Kl],function(t,e){var r=e[0];return Math.asin(r.evaluate(t))}],acos:[Kl,[Kl],function(t,e){var r=e[0];return Math.acos(r.evaluate(t))}],atan:[Kl,[Kl],function(t,e){var r=e[0];return Math.atan(r.evaluate(t))}],min:[Kl,ac(Kl),function(t,e){return Math.min.apply(Math,e.map((function(e){return e.evaluate(t)})))}],max:[Kl,ac(Kl),function(t,e){return Math.max.apply(Math,e.map((function(e){return e.evaluate(t)})))}],abs:[Kl,[Kl],function(t,e){var r=e[0];return Math.abs(r.evaluate(t))}],round:[Kl,[Kl],function(t,e){var r=e[0].evaluate(t);return r<0?-Math.round(-r):Math.round(r)}],floor:[Kl,[Kl],function(t,e){var r=e[0];return Math.floor(r.evaluate(t))}],ceil:[Kl,[Kl],function(t,e){var r=e[0];return Math.ceil(r.evaluate(t))}],"filter-==":[Zl,[Jl,rc],function(t,e,r){var n=e[0],i=e[1];if(r){var o,a,s=n.value,u=i.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),c=t.properties()[l[1]],p=l[2],f=t.properties()[l[3]];switch(p){case"+":o=c+f;break;case"-":o=c-f}return o===(a=i.value)}if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),y=h[1],d=(c=t.properties()[h[2]],h[3]);if(o="left"==y?c.substring(0,d):c.substring(c.length-d),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),g=m[1],v=(f=t.properties()[m[2]],m[3]);a="left"==g?f.substring(0,v):f.substring(f.length-v)}else a=i.value;return o===a}return t.properties()[n.value]===t.properties()[i.value]}return t.properties()[n.value]===i.value}],"filter-id-==":[Zl,[rc],function(t,e){var r=e[0];return t.id()===r.value}],"filter-like":[Zl,[Jl,Jl],function(t,e){var r=e[0].value,n=e[1].value,i=t.properties();return r in i&&(/^%.*[^%]$/.test(n)?(n=n.replace("%",""),i[r].endsWith(n)):/^(?!%).+%$/.test(n)?(n=n.replace("%",""),i[r].startsWith(n)):(n=n.replace(/%/g,""),i[r].indexOf(n)>-1))}],"filter-type-==":[Zl,[Jl],function(t,e){var r=e[0];return t.geometryType()===r.value}],"filter-<":[Zl,[Jl,rc],function(t,e,r){var n,i,o=e[0],a=e[1],s=o.value,u=a.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),c=t.properties()[l[1]],p=l[2],f=t.properties()[l[3]];switch(p){case"+":n=c+f;break;case"-":n=c-f}i=a.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),y=h[1],d=(c=t.properties()[h[2]],h[3]);if(n="left"==y?c.substring(0,d):c.substring(c.length-d),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),g=m[1],v=(f=t.properties()[m[2]],m[3]);i="left"==g?f.substring(0,v):f.substring(f.length-v)}else i=a.value}else n=t.properties()[o.value],i=a.value,r&&(i=t.properties()[i]);return"number"==typeof i&&!isNaN(Number(n))&&(n=Number(n)),typeof n==typeof i&&n<i}],"filter-id-<":[Zl,[rc],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n<i}],"filter->":[Zl,[Jl,rc],function(t,e,r){var n,i,o=e[0],a=e[1],s=o.value,u=a.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),c=t.properties()[l[1]],p=l[2],f=t.properties()[l[3]];switch(p){case"+":n=c+f;break;case"-":n=c-f}i=a.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),y=h[1],d=(c=t.properties()[h[2]],h[3]);if(n="left"==y?c.substring(0,d):c.substring(c.length-d),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),g=m[1],v=(f=t.properties()[m[2]],m[3]);i="left"==g?f.substring(0,v):f.substring(f.length-v)}else i=a.value}else n=t.properties()[o.value],i=a.value,r&&(i=t.properties()[i]);return"number"==typeof i&&!isNaN(Number(n))&&(n=Number(n)),typeof n==typeof i&&n>i}],"filter-id->":[Zl,[rc],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n>i}],"filter-<=":[Zl,[Jl,rc],function(t,e,r){var n,i,o=e[0],a=e[1],s=o.value,u=a.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),c=t.properties()[l[1]],p=l[2],f=t.properties()[l[3]];switch(p){case"+":n=c+f;break;case"-":n=c-f}i=a.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),y=h[1],d=(c=t.properties()[h[2]],h[3]);if(n="left"==y?c.substring(0,d):c.substring(c.length-d),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),g=m[1],v=(f=t.properties()[m[2]],m[3]);i="left"==g?f.substring(0,v):f.substring(f.length-v)}else i=a.value}else n=t.properties()[o.value],i=a.value,r&&(i=t.properties()[i]);return"number"==typeof i&&!isNaN(Number(n))&&(n=Number(n)),typeof n==typeof i&&n<=i}],"filter-id-<=":[Zl,[rc],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n<=i}],"filter->=":[Zl,[Jl,rc],function(t,e,r){var n,i,o=e[0],a=e[1],s=o.value,u=a.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),c=t.properties()[l[1]],p=l[2],f=t.properties()[l[3]];switch(p){case"+":n=c+f;break;case"-":n=c-f}i=a.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),y=h[1],d=(c=t.properties()[h[2]],h[3]);if(n="left"==y?c.substring(0,d):c.substring(c.length-d),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),g=m[1],v=(f=t.properties()[m[2]],m[3]);i="left"==g?f.substring(0,v):f.substring(f.length-v)}else i=a.value}else n=t.properties()[o.value],i=a.value,r&&(i=t.properties()[i]);return"number"==typeof i&&!isNaN(Number(n))&&(n=Number(n)),typeof n==typeof i&&n>=i}],"filter-id->=":[Zl,[rc],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n>=i}],"filter-has":[Zl,[rc],function(t,e){return e[0].value in t.properties()}],"filter-has-id":[Zl,[],function(t){return null!==t.id()}],"filter-type-in":[Zl,[oc(Jl)],function(t,e){return e[0].value.indexOf(t.geometryType())>=0}],"filter-id-in":[Zl,[oc(rc)],function(t,e){return e[0].value.indexOf(t.id())>=0}],"filter-in-small":[Zl,[Jl,oc(rc)],function(t,e){var r=e[0];return e[1].value.indexOf(t.properties()[r.value])>=0}],"filter-in-large":[Zl,[Jl,oc(rc)],function(t,e){var r=e[0],n=e[1];return binarySearch(t.properties()[r.value],n.value,0,n.value.length-1)}],all:{type:Zl,overloads:[[[Zl,Zl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)&&n.evaluate(t)}],[ac(Zl),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(!n[r].evaluate(t))return!1}return!0}]]},crossFields:{type:Zl,overloads:[[[Zl,Zl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t,!0)&&n.evaluate(t,!0)}],[ac(Zl),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(!n[r].evaluate(t,!0))return!1}return!0}]]},any:{type:Zl,overloads:[[[Zl,Zl],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)||n.evaluate(t)}],[ac(Zl),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(n[r].evaluate(t))return!0}return!1}]]},"!":[Zl,[Zl],function(t,e,r){return!e[0].evaluate(t,r)}],"is-supported-script":[Zl,[Jl],function(t,e){var r=e[0],n=t.globals&&t.globals.isSupportedScript;return!n||n(r.evaluate(t))}],upcase:[Jl,[Jl],function(t,e){return e[0].evaluate(t).toUpperCase()}],downcase:[Jl,[Jl],function(t,e){return e[0].evaluate(t).toLowerCase()}],concat:[Jl,ac(rc),function(t,e){return e.map((function(e){return Values.toString$1(e.evaluate(t))})).join("")}],"resolved-locale":[Jl,[ic],function(t,e){return e[0].evaluate(t).resolvedLocale()}]}),Yl.CompoundExpression=Gl;var uc,lc=(uc=!0,function(t,e){var r=uc?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return uc=!1,r}),cc=lc(void 0,(function(){return cc.toString().search("(((.+)+)+)+$").toString().constructor(cc).search("(((.+)+)+)+$")}));cc();var pc=function(t,e){this.expression=t,this._warningHistory={},this._evaluator=new Nl,this._defaultValue=e?hc(e):null,this._enumValues=e&&"enum"===e.type?e.values:null};function fc(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}function hc(t){return"color"===t.type&&fc(t.default)?new Mt(0,0,0,0):"color"===t.type?Mt.parse(t.default)||null:void 0===t.default?null:t.default}pc.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i){return this._evaluator.globals=t,this._evaluator.feature=e,this._evaluator.featureState=r,this._evaluator.availableImages=n||null,this._evaluator.formattedSection=i,this.expression.evaluate(this._evaluator)},pc.prototype.evaluate=function(t,e,r,n,i){this._evaluator.globals=t,this._evaluator.feature=e||null,this._evaluator.featureState=r||null,this._evaluator.availableImages=n||null,this._evaluator.formattedSection=i||null;try{var o=this.expression.evaluate(this._evaluator);if(null==o||"number"==typeof o&&o!=o)return this._defaultValue;if(this._enumValues&&!(o in this._enumValues))throw new RuntimeError("Expected value to be one of "+Object.keys(this._enumValues).map((function(t){return JSON.stringify(t)})).join(", ")+", but found "+JSON.stringify(o)+" instead.");return o}catch(t){return!this._warningHistory[t.message]&&(this._warningHistory[t.message]=!0,"undefined"!=typeof console&&console.warn(t.message)),this._defaultValue}},ri.register("StyleExpression",pc);var yc,dc=(yc=!0,function(t,e){var r=yc?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return yc=!1,r}),mc=dc(void 0,(function(){return mc.toString().search("(((.+)+)+)+$").toString().constructor(mc).search("(((.+)+)+)+$")}));function gc(){}function vc(t){return{result:"success",value:t}}function xc(t){return{result:"error",value:t}}mc(),gc.isExpression=function(t){return Array.isArray(t)&&t.length>0&&"string"==typeof t[0]&&t[0]in Zn};var bc={kind:"number"},_c={kind:"string"},wc={kind:"boolean"},Sc={kind:"color"},Ac={kind:"value"},Ec={kind:"formatted"},Tc={kind:"resolvedImage"};function Ic(t,e){return{kind:"array",itemType:t,N:e}}function Oc(t){var e=null;if(t instanceof dn)e=Oc(t.result);else if(t instanceof Qe){for(var r of t.args)if(e=Oc(r))break}else(t instanceof jn||t instanceof Jr)&&t.input instanceof Yl.CompoundExpression&&"zoom"===t.input.name&&(e=t);return e instanceof zl||t.eachChild((t=>{var r=Oc(t);r instanceof zl?e=r:!e&&r?e=new zl("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.'):e&&r&&e!==r&&(e=new zl("",'Only one zoom-based "step" or "interpolate" subexpression may be used in an expression.'))})),e}function kc(t){var e={color:Sc,string:_c,number:bc,enum:_c,boolean:wc,formatted:Ec,resolvedImage:Tc};return"array"===t.type?Ic(e[t.value]||Ac,t.length):e[t.type]}function Fc(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}function Rc(t){return t}function Mc(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];for(var n=0,i=e;n<i.length;n+=1){var o=i[n];for(var a in o)t[a]=o[a]}return t}function Cc(t,e,r){var n=void 0!==t.base?t.base:1;if("number"!==Xc(r))return Vc(t.default,e.default);var i=t.stops.length;if(1===i)return t.stops[0][1];if(r<=t.stops[0][0])return t.stops[0][1];if(r>=t.stops[i-1][0])return t.stops[i-1][1];var o=Nn(t.stops.map((t=>t[0])),r),a=Lc(r,n,t.stops[o][0],t.stops[o+1][0]),s=t.stops[o][1],u=t.stops[o+1][1],l=interpolate[e.type]||Rc;if(t.colorSpace&&"rgb"!==t.colorSpace){var c=colorSpaces[t.colorSpace];l=(t,e)=>c.reverse(c.interpolate(c.forward(t),c.forward(e),a))}return"function"==typeof s.evaluate?{evaluate(...t){var e=s.evaluate.apply(void 0,t),r=u.evaluate.apply(void 0,t);if(void 0!==e&&void 0!==r)return l(e,r,a)}}:l(s,u,a)}function Pc(t,e,r){return"color"===e.type?r=Mt.parse(r):"formatted"===e.type?r=Formatted.fromString(r.toString()):"resolvedImage"===e.type?r=ResolvedImage.fromString(r.toString()):Xc(r)!==e.type&&("enum"!==e.type||!e.values[r])&&(r=void 0),Vc(r,t.default,e.default)}function zc(t,e){var r=new Yl(Zn,[],e?kc(e):void 0),n=r.parse(t,void 0,void 0,void 0,e&&"string"===e.type?{typeAnnotation:"coerce"}:void 0);return n?vc(new pc(n,e)):xc(r.errors)}function Bc(t,e){this.kind=t,this._styleExpression=e,this.isStateDependent="constant"!==t&&!Xl.isStateConstant(e.expression)}function Dc(t,e,r,n){this.kind=t,this.zoomStops=r,this._styleExpression=e,this.isStateDependent="camera"!==t&&!Xl.isStateConstant(e.expression),this.interpolationType=n}function Uc(t,e){if("error"===(t=zc(t,e)).result)return t;var r=t.value.expression,n=Xl.isFeatureConstant(r);if(!n&&!To(e))return xc([new zl("","data expressions not supported")]);var i=Xl.isGlobalPropertyConstant(r,["zoom"]);if(!i&&!Io(e))return xc([new zl("","zoom expressions not supported")]);var o=Oc(r);if(!o&&!i)return xc([new zl("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.')]);if(o instanceof zl)return xc([o]);if(o instanceof Jr&&!Oo(e))return xc([new zl("",'"interpolate" expressions cannot be used with this property')]);if(!o)return vc(new Bc(n?"constant":"source",t.value));var a=o instanceof Jr?o.interpolation:void 0;return vc(new Dc(n?"camera":"composite",t.value,o.labels,a))}function Nc(t,e){var r,n,i,o="color"===e.type,a=t.stops&&"object"==typeof t.stops[0][0],s=a||void 0!==t.property,u=a||!s,l=t.type||(Oo(e)?"exponential":"interval");if(o&&((t=Mc({},t)).stops&&(t.stops=t.stops.map((function(t){return[t[0],Mt.parse(t[1])]}))),t.default?t.default=Mt.parse(t.default):t.default=Mt.parse(e.default)),t.colorSpace&&"rgb"!==t.colorSpace&&!colorSpaces[t.colorSpace])throw new Error("Unknown color space: "+t.colorSpace);if("exponential"===l)r=Cc;else if("interval"===l)r=evaluateIntervalFunction;else if("categorical"===l){r=evaluateCategoricalFunction,n=Object.create(null);for(var c=0,p=t.stops;c<p.length;c+=1){var f=p[c];n[f[0]]=f[1]}i=typeof t.stops[0][0]}else{if("identity"!==l)throw new Error('Unknown function type "'+l+'"');r=Pc}if(a){for(var h={},y=[],d=0;d<t.stops.length;d++){var m=t.stops[d],g=m[0].zoom;void 0===h[g]&&(h[g]={zoom:g,type:t.type,property:t.property,default:t.default,stops:[]},y.push(g)),h[g].stops.push([m[0].value,m[1]])}for(var v=[],x=0,b=y;x<b.length;x+=1){var _=b[x];v.push([h[_].zoom,Nc(h[_],e)])}var w={name:"linear"};return{kind:"composite",interpolationType:w,interpolationFactor:Jr.interpolationFactor.bind(void 0,w),zoomStops:v.map((function(t){return t[0]})),evaluate:function(r,n){var i=r.zoom;return Cc({stops:v,base:t.base},e,i).evaluate(i,n)}}}if(u){var S="exponential"===l?{name:"exponential",base:void 0!==t.base?t.base:1}:null;return{kind:"camera",interpolationType:S,interpolationFactor:Jr.interpolationFactor.bind(void 0,S),zoomStops:t.stops.map((function(t){return t[0]})),evaluate:function(o){var a=o.zoom;return r(t,e,a,n,i)}}}return{kind:"source",evaluate:function(o,a){var s=a&&a.properties?a.properties[t.property]:void 0;return void 0===s?Vc(t.default,e.default):r(t,e,s,n,i)}}}function Vc(t,e,r){return void 0!==t?t:void 0!==e?e:void 0!==r?r:void 0}function Lc(t,e,r,n){var i=n-r,o=t-r;return 0===i?0:1===e?o/i:(Math.pow(e,o)-1)/(Math.pow(e,i)-1)}gc.createExpression=function(t,e){var r=new Yl(Zn,[],e?kc(e):void 0),n=r.parse(t,void 0,void 0,void 0,e&&"string"===e.type?{typeAnnotation:"coerce"}:void 0);return n?vc(new pc(n,e)):xc(r.errors)},Bc.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i,o){return this._styleExpression.evaluateWithoutErrorHandling(t,e,r,n,i,o)},Bc.prototype.evaluate=function(t,e,r,n,i,o){return this._styleExpression.evaluate(t,e,r,n,i,o)},ri.register("ZoomConstantExpression",Bc),Dc.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i,o){return this._styleExpression.evaluateWithoutErrorHandling(t,e,r,n,i,o)},Dc.prototype.evaluate=function(t,e,r,n,i,o){return this._styleExpression.evaluate(t,e,r,n,i,o)},Dc.prototype.interpolationFactor=function(t,e,r){return this.interpolationType?Jr.interpolationFactor(this.interpolationType,t,e,r):0},ri.register("ZoomDependentExpression",Dc);var $c=function(t,e){this._parameters=t,this._specification=e,Mc(this,Nc(this._parameters,this._specification))};function Xc(t){return t instanceof Number?"number":t instanceof String?"string":t instanceof Boolean?"boolean":Array.isArray(t)?"array":null===t?"null":typeof t}$c.deserialize=function(t){return new $c(t._parameters,t._specification)},$c.serialize=function(t){return{_parameters:t._parameters,_specification:t._specification}},ri.register("StylePropertyFunction",$c),gc.normalizePropertyExpression=function(t,e){if(Fc(t))return new $c(t,e);if(gc.isExpression(t)){var r=Uc(t,e);if("error"===r.result)throw new Error(r.value.map((function(t){return t.key+": "+t.message})).join(", "));return r.value}var n=t;return"string"==typeof t&&"color"===e.type&&(n=Mt.parse(t)),{kind:"constant",evaluate:function(){return n}}};var jc,qc=(jc=!0,function(t,e){var r=jc?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return jc=!1,r}),Hc=qc(void 0,(function(){return Hc.toString().search("(((.+)+)+)+$").toString().constructor(Hc).search("(((.+)+)+)+$")}));function Yc(){}Hc(),Yc.isExpressionFilter=function(t){if(!0===t||!1===t)return!0;if(!Array.isArray(t)||0===t.length)return!1;switch(t[0]){case"has":return t.length>=2&&"$id"!==t[1]&&"$type"!==t[1];case"in":return t.length>=3&&Array.isArray(t[2]);case"!in":case"!has":case"none":case"crossFields":return!1;case"==":case"!=":case">":case">=":case"<":case"<=":case"like":case"!like":return 3!==t.length||Array.isArray(t[1])||Array.isArray(t[2]);case"any":case"all":for(var e=0,r=t.slice(1);e<r.length;e+=1){var n=r[e];if(!Yc.isExpressionFilter(n)&&"boolean"!=typeof n)return!1}return!0;default:return!0}};var Wc={type:"boolean",default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}};function Gc(t,e){return t<e?-1:t>e?1:0}function Qc(t){if(!t)return!0;var e=t[0];return t.length<=1?"any"!==e:"=="===e?Kc(t[1],t[2],"=="):"!="===e?ep(Kc(t[1],t[2],"==")):"<"===e||">"===e||"<="===e||">="===e?Kc(t[1],t[2],e):"any"===e?Jc(t.slice(1)):"all"===e?["all"].concat(t.slice(1).map(Qc)):"crossFields"===e?["crossFields"].concat(t.slice(1).map(Qc)):"none"===e?["all"].concat(t.slice(1).map(Qc).map(ep)):"in"===e?Zc(t[1],t.slice(2)):"!in"===e?ep(Zc(t[1],t.slice(2))):"has"===e?tp(t[1]):"!has"===e?ep(tp(t[1])):"like"===e?Kc(t[1],t[2],"like"):"!like"!==e||ep(Kc(t[1],t[2],"like"))}function Kc(t,e,r){switch(t){case"$type":return["filter-type-"+r,e];case"$id":return["filter-id-"+r,e];default:return["filter-"+r,t,e]}}function Jc(t){return["any"].concat(t.map(Qc))}function Zc(t,e){if(0===e.length)return!1;switch(t){case"$type":return["filter-type-in",["literal",e]];case"$id":return["filter-id-in",["literal",e]];default:return e.length>200&&!e.some((function(t){return typeof t!=typeof e[0]}))?["filter-in-large",t,["literal",e.sort(Gc)]]:["filter-in-small",t,["literal",e]]}}function tp(t){switch(t){case"$type":return!0;case"$id":return["filter-has-id"];default:return["filter-has",t]}}function ep(t){return["!",t]}Yc.createFilter=function(t){if(null==t)return function(){return!0};!Yc.isExpressionFilter(t)&&(t=Qc(t));var e=gc.createExpression(t,Wc);if("error"===e.result)throw new Error(e.value.map((function(t){return t.key+": "+t.message})).join(", "));return function(t,r){return e.value.evaluate(t,r)}};var rp,np=(rp=!0,function(t,e){var r=rp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return rp=!1,r}),ip=np(void 0,(function(){return ip.toString().search("(((.+)+)+)+$").toString().constructor(ip).search("(((.+)+)+)+$")}));ip();var op=function(t,e,r){this.x=t.x,this.y=t.y,this.z=t.z,this.grid=e||new vl(gi,16,0),this.featureIndexArray=r||new vo};function ap(t){for(var e=1/0,r=1/0,n=-1/0,i=-1/0,o=0,a=t;o<a.length;o+=1){var s=a[o];e=Math.min(e,s.x),r=Math.min(r,s.y),n=Math.max(n,s.x),i=Math.max(i,s.y)}return{minX:e,minY:r,maxX:n,maxY:i}}function sp(t,e){return e-t}function up(t,r,n){if(e.t(n)&&n.realtime&&e.t(n.zoom)){var i=n.zoom-t,o=gi/(r*Math.pow(2,i));return o*=r/512}return gi/r}op.prototype.insert=function(t,e,r,n,i,o,a){var s=this.featureIndexArray.length;this.featureIndexArray.emplaceBack(r,n,i);var u=this.grid;a=a||0;for(var l=0;l<e.length;l++){for(var c=e[l],p=[1/0,1/0,-1/0,-1/0],f=0;f<c.length;f++){var h=c[f];p[0]=Math.min(p[0],h.x),p[1]=Math.min(p[1],h.y),p[2]=Math.max(p[2],h.x),p[3]=Math.max(p[3],h.y)}p[0]<gi&&p[1]<gi&&p[2]>=0&&p[3]>=0&&u.insert(s,p[0]-a,p[1]-a,p[2]+a,p[3]+a)}},op.prototype.loadVTLayers=function(){return!this.vtLayers&&(this.vtLayers=new fl(new b(this.rawTileData)).layers,this.sourceLayerCoder=new Y(this.vtLayers?Object.keys(this.vtLayers).sort():["_geojsonTileLayer"])),this.vtLayers},op.prototype.query=function(t,r,n){var i=this;this.loadVTLayers();var o=t.params||{},a=up(this.z,t.tileSize,o),s=Yc.createFilter(o.filter),u=t.queryGeometry,l=5,c=ap(u),p=[];e.t(o.selectTolerance)&&(l+=a*o.selectTolerance),(p=this.grid.query(c.minX-l,c.minY-l,c.maxX+l,c.maxY+l)).sort(sp);for(var f,h={},y=function(e){var n=p[e];if(n!==f){f=n;var l=i.featureIndexArray.get(n),c=null;i.loadMatchingFeature(h,l.bucketIndex,l.sourceLayerIndex,l.featureIndex,s,o.layers,r,(function(e,r){!c&&(c=_i(e));return r.queryIntersectsFeature(u,e,{},c,i.z,t.transform,a,t.pixelPosMatrix,t.adjustScale)}))}},d=0;d<p.length;d++)y(d);return h},op.prototype.loadMatchingFeature=function(t,r,n,i,o,a,s,u){if(e.t(r)&&e.t(n)&&e.t(i)){var l=this.bucketLayerIDs[r];if(!a||Il(a,l)){var c=this.sourceLayerCoder.decode(n),p=this.vtLayers[c].feature(i);if(o(new hi(this.z),p))for(var f=0;f<l.length;f++){var h=l[f];if(!(a&&a.indexOf(h)<0)){var y=s[h];if(y){var d=!u||u(p,y);if(d){p.layer=y.serialize();var m=t[h];void 0===m&&(m=t[h]=[]),m.push({featureIndex:i,feature:p,intersectionZ:d})}}}}}}},op.prototype.lookupSymbolFeatures=function(t,e,r,n,i,o){var a={};this.loadVTLayers();for(var s=createFilter(n),u=0,l=t;u<l.length;u+=1){var c=l[u];this.loadMatchingFeature(a,e,r,c,s,i,o)}return a},op.prototype.hasLayer=function(t){for(var e=0,r=this.bucketLayerIDs;e<r.length;e+=1)for(var n=0,i=r[e];n<i.length;n+=1){if(t===i[n])return!0}return!1},ri.register("FeatureIndex",op,{omit:["rawTileData","sourceLayerCoder","vtLayers"]});var lp,cp=(lp=!0,function(t,e){var r=lp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return lp=!1,r}),pp=cp(void 0,(function(){return pp.toString().search("(((.+)+)+)+$").toString().constructor(pp).search("(((.+)+)+)+$")}));pp();const fp=["type","source","source-layer","minzoom","maxzoom","filter","layout"];function hp(t){var e=typeof t;if("number"===e||"boolean"===e||"string"===e||null==t)return JSON.stringify(t);if(Array.isArray(t)){for(var r="[",n=0,i=t;n<i.length;n+=1){r+=hp(i[n])+","}return r+"]"}for(var o=Object.keys(t).sort(),a="{",s=0;s<o.length;s++)a+=JSON.stringify(o[s])+":"+hp(t[o[s]])+",";return a+"}"}function yp(t){for(var e="",r=0,n=fp;r<n.length;r+=1){e+="/"+hp(t[n[r]])}return e}function dp(t,e){for(var r={},n=0;n<t.length;n++){var i=e&&e[t[n].id]||yp(t[n]);e&&(e[t[n].id]=i);var o=r[i];!o&&(o=r[i]=[]),o.push(t[n])}var a=[];for(var s in r)a.push(r[s]);return a}var mp,gp=(mp=!0,function(t,e){var r=mp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return mp=!1,r}),vp=gp(void 0,(function(){return vp.toString().search("(((.+)+)+)+$").toString().constructor(vp).search("(((.+)+)+)+$")}));vp();var xp=J([{name:"a_pos",components:2,type:"Int16"}],4),bp=xp.members,_p=J([{name:"a_pos",components:2,type:"Float32"}],4),wp=_p.members;function Sp(t,e,r,n,i){t.emplaceBack(2*e+(n+1)/2,2*r+(i+1)/2)}var Ap=function(t){this.zoom=t.zoom,this.overscaling=1,this.layers=t.layers,this._sourceLayerIds={};var e=this;this.layerIds=this.layers.map((function(t,r){return e._sourceLayerIds[t.sourceLayer]=r,t.sourceLayer})),this.index=t.index,this.hasPattern=!1,this.layoutVertexArray=new Bi,this.indexArray=new Ji,this.segments=new Ei,this.programConfigurations=new za(members,t.layers,t.zoom),this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};Ap.prototype.populate=function(t,e){var r=this.layers[0],n=[],i=null;"circle"===r.type&&(i=r.layout.get("circle-sort-key"));for(var o=0,a=t;o<a.length;o+=1){var s=a[o],u=s.feature,l=s.index,c=s.sourceLayerIndex,p=s.sourceLayerId,f=this._sourceLayerIds[p],h=this.layers[f];if(h){var y=gi/512,d=h.paint.get("circle-radius").value.value*y;if(this.layers[0]._featureFilter(new hi(0),u)){var m=_i(u),g=i?i.evaluate(u,{}):void 0,v={id:u.id,properties:u.properties,type:u.type,sourceLayerIndex:c,index:l,geometry:m,patterns:{},sortKey:g,circleRadius:d};n.push(v)}}}i&&n.sort((function(t,e){return t.sortKey-e.sortKey}));for(var x=0,b=n;x<b.length;x+=1){var _=b[x],w=_,S=w.geometry,A=w.index,E=w.sourceLayerIndex,T=t[A].feature;this.addFeature(_,S,A),e.featureIndex.insert(T,S,A,E,this.index,void 0,w.circleRadius)}},Ap.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},Ap.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},Ap.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},Ap.prototype.upload=function(t){if(!this.uploaded){if(null==this.layoutVertexArray)return;var e=t.webgpu?wp:bp;this.layoutVertexArray.length>0&&(this.layoutCesiumVertexBuffer=ma.toVertexBuffer(t,this.layoutVertexArray,e),this.cesiumIndexBuffer=ma.toIndexBuffer(t,this.indexArray))}this.programConfigurations.upload(t),this.uploaded=!0},Ap.prototype.destroy=function(){this.layoutCesiumVertexBuffer&&(this.layoutCesiumVertexBuffer.destroy(),this.cesiumIndexBuffer.destroy(),this.programConfigurations.destroy(),this.segments.destroy())},Ap.prototype.clear=function(){e.t(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.t(this.indexArray)&&(this.indexArray=null)},Ap.prototype.addFeature=function(t,e,r){for(var n=0,i=e;n<i.length;n+=1)for(var o=0,a=i[n];o<a.length;o+=1){var s=a[o],u=s.x,l=s.y;if(!(u<0||u>=gi||l<0||l>=gi)){var c=this.segments.prepareSegment(4,this.layoutVertexArray,this.indexArray,t.sortKey),p=c.vertexLength;Sp(this.layoutVertexArray,u,l,-1,-1),Sp(this.layoutVertexArray,u,l,1,-1),Sp(this.layoutVertexArray,u,l,1,1),Sp(this.layoutVertexArray,u,l,-1,1),this.indexArray.emplaceBack(p,p+1,p+2),this.indexArray.emplaceBack(p,p+3,p+2),c.vertexLength+=4,c.primitiveLength+=2}}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,t,r,{})},ri.register("CircleBucket",Ap,{omit:["layers"]});var Ep,Tp=(Ep=!0,function(t,e){var r=Ep?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ep=!1,r}),Ip=Tp(void 0,(function(){return Ip.toString().search("(((.+)+)+)+$").toString().constructor(Ip).search("(((.+)+)+)+$")}));Ip();var Op=function(t){this.specification=t};Op.prototype.possiblyEvaluate=function(t,e){return t.expression.evaluate(e)},Op.prototype.interpolate=function(t,e,r){var n=interpolate[this.specification.type];return n?n(t,e,r):t},ri.register("DataConstantProperty",Op);var kp,Fp=(kp=!0,function(t,e){var r=kp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return kp=!1,r}),Rp=Fp(void 0,(function(){return Rp.toString().search("(((.+)+)+)+$").toString().constructor(Rp).search("(((.+)+)+)+$")}));Rp();var Mp=function(t,e){this.specification=t,this.overrides=e};Mp.prototype.possiblyEvaluate=function(t,e,r){return"constant"===t.expression.kind||"camera"===t.expression.kind?new wo(this,{kind:"constant",value:t.expression.evaluate(e,null,{},r)},e):new wo(this,t.expression,e)},Mp.prototype.interpolate=function(t,e,r){if("constant"!==t.value.kind||"constant"!==e.value.kind)return t;if(void 0===t.value.value||void 0===e.value.value)return new wo(this,{kind:"constant",value:void 0},t.parameters);var n=interpolate[this.specification.type];return n?new wo(this,{kind:"constant",value:n(t.value.value,e.value.value,r)},t.parameters):t},Mp.prototype.evaluate=function(t,e,r,n,i){return"constant"===t.kind?t.value:t.evaluate(e,r,n,i)},ri.register("DataDrivenProperty",Mp);var Cp,Pp=(Cp=!0,function(t,e){var r=Cp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Cp=!1,r}),zp=Pp(void 0,(function(){return zp.toString().search("(((.+)+)+)+$").toString().constructor(zp).search("(((.+)+)+)+$")}));zp();var Bp=function(t,e){this.property=t,this.value=e,this.expression=gc.normalizePropertyExpression(void 0===e?t.specification.default:e,t.specification)};Bp.prototype.isDataDriven=function(){return"source"===this.expression.kind||"composite"===this.expression.kind},Bp.prototype.possiblyEvaluate=function(t,e){return this.property.possiblyEvaluate(this,t,e)};var Dp,Up=(Dp=!0,function(t,e){var r=Dp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Dp=!1,r}),Np=Up(void 0,(function(){return Np.toString().search("(((.+)+)+)+$").toString().constructor(Np).search("(((.+)+)+)+$")}));function Vp(t){if(t<=0)return 0;if(t>=1)return 1;var e=t*t,r=e*t;return 4*(t<.5?r:3*(t-e)+r-.75)}Np();var Lp=function(t,e,r,n,i){this.property=t,this.value=e,this.begin=i+n.delay||0,this.end=this.begin+n.duration||0,t.specification.transition&&(n.delay||n.duration)&&(this.prior=r)};Lp.prototype.possiblyEvaluate=function(t,e){var r=t.now||0,n=this.value.possiblyEvaluate(t,e),i=this.prior;if(i){if(r>this.end)return this.prior=null,n;if(this.value.isDataDriven())return this.prior=null,n;if(r<this.begin)return i.possiblyEvaluate(t,e);var o=(r-this.begin)/(this.end-this.begin);return this.property.interpolate(i.possiblyEvaluate(t,e),n,Vp(o))}return n};var $p,Xp=($p=!0,function(t,e){var r=$p?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return $p=!1,r}),jp=Xp(void 0,(function(){return jp.toString().search("(((.+)+)+)+$").toString().constructor(jp).search("(((.+)+)+)+$")}));jp();var qp=function(t){this.property=t,this.value=new Bp(t,void 0)};qp.prototype.transitioned=function(t,e){return new Lp(this.property,this.value,e,wl({},t.transition,this.transition),t.now)},qp.prototype.untransitioned=function(){return new Lp(this.property,this.value,null,{},0)};var Hp,Yp=(Hp=!0,function(t,e){var r=Hp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Hp=!1,r}),Wp=Yp(void 0,(function(){return Wp.toString().search("(((.+)+)+)+$").toString().constructor(Wp).search("(((.+)+)+)+$")}));Wp();var Gp,Qp=function(t){for(var e in this.properties=t,this.defaultPropertyValues={},this.defaultTransitionablePropertyValues={},this.defaultTransitioningPropertyValues={},this.defaultPossiblyEvaluatedValues={},this.overridableProperties=[],t){var r=t[e];r.specification.overridable&&this.overridableProperties.push(e);var n=this.defaultPropertyValues[e]=new Bp(r,void 0),i=this.defaultTransitionablePropertyValues[e]=new qp(r);this.defaultTransitioningPropertyValues[e]=i.untransitioned(),this.defaultPossiblyEvaluatedValues[e]=n.possiblyEvaluate({})}},Kp=(Gp=!0,function(t,e){var r=Gp?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Gp=!1,r}),Jp=Kp(void 0,(function(){return Jp.toString().search("(((.+)+)+)+$").toString().constructor(Jp).search("(((.+)+)+)+$")}));function Zp(){}Jp(),Zp.getMaximumPaintValue=function(t,e,r){var n=e.paint.get(t).value;return"constant"===n.kind?n.value:r.programConfigurations.get(e.id).binders[t].maxValue},Zp.translateDistance=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},Zp.translate=function(t,e,r,n,i){if(!e||!e[0]&&!e[1])return t;var o=zi.convert(e)._mult(i);"viewport"===r&&o._rotate(-n);for(var a=[],s=0;s<t.length;s++){var u=t[s];a.push(u.sub(o))}return a};var tf,ef=(tf=!0,function(t,e){var r=tf?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return tf=!1,r}),rf=ef(void 0,(function(){return rf.toString().search("(((.+)+)+)+$").toString().constructor(rf).search("(((.+)+)+)+$")}));rf();var nf=function(t){this._properties=t,this._values=Object.create(t.defaultPossiblyEvaluatedValues)};nf.prototype.get=function(t){return this._values[t]};var of,af=(of=!0,function(t,e){var r=of?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return of=!1,r}),sf=af(void 0,(function(){return sf.toString().search("(((.+)+)+)+$").toString().constructor(sf).search("(((.+)+)+)+$")}));sf();var uf=function(t){this._properties=t,this._values=Object.create(t.defaultPropertyValues)};uf.prototype.getValue=function(t){return Tl(this._values[t].value)},uf.prototype.setValue=function(t,e){this._values[t]=new Bp(this._values[t].property,null===e?void 0:Tl(e))},uf.prototype.serialize=function(){for(var t={},e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e],i=this.getValue(n);void 0!==i&&(t[n]=i)}return t},uf.prototype.possiblyEvaluate=function(t,e){for(var r=new nf(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var o=i[n];r._values[o]=this._values[o].possiblyEvaluate(t,e)}return r};var lf,cf=(lf=!0,function(t,e){var r=lf?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return lf=!1,r}),pf=cf(void 0,(function(){return pf.toString().search("(((.+)+)+)+$").toString().constructor(pf).search("(((.+)+)+)+$")}));pf();var ff=function(t){this._properties=t,this._values=Object.create(t.defaultTransitioningPropertyValues)};ff.prototype.possiblyEvaluate=function(t,e){for(var r=new nf(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var o=i[n];r._values[o]=this._values[o].possiblyEvaluate(t,e)}return r},ff.prototype.hasTransition=function(){for(var t=0,e=Object.keys(this._values);t<e.length;t+=1){var r=e[t];if(this._values[r].prior)return!0}return!1};var hf,yf=(hf=!0,function(t,e){var r=hf?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return hf=!1,r}),df=yf(void 0,(function(){return df.toString().search("(((.+)+)+)+$").toString().constructor(df).search("(((.+)+)+)+$")}));df();var mf=function(t){this._properties=t,this._values=Object.create(t.defaultTransitionablePropertyValues)};mf.prototype.getValue=function(t){return Tl(this._values[t].value.value)},mf.prototype.setValue=function(t,e){!this._values.hasOwnProperty(t)&&(this._values[t]=new qp(this._values[t].property)),this._values[t].value=new Bp(this._values[t].property,null===e?void 0:Tl(e))},mf.prototype.getTransition=function(t){return Tl(this._values[t].transition)},mf.prototype.setTransition=function(t,e){!this._values.hasOwnProperty(t)&&(this._values[t]=new qp(this._values[t].property)),this._values[t].transition=Tl(e)||void 0},mf.prototype.serialize=function(){for(var t={},e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e],i=this.getValue(n);void 0!==i&&(t[n]=i);var o=this.getTransition(n);void 0!==o&&(t[n+"-transition"]=o)}return t},mf.prototype.transitioned=function(t,e){for(var r=new ff(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var o=i[n];r._values[o]=this._values[o].transitioned(t,e._values[o])}return r},mf.prototype.untransitioned=function(){for(var t=new ff(this._properties),e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e];t._values[n]=this._values[n].untransitioned()}return t};var gf,vf=(gf=!0,function(t,e){var r=gf?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return gf=!1,r}),xf=vf(void 0,(function(){return xf.toString().search("(((.+)+)+)+$").toString().constructor(xf).search("(((.+)+)+)+$")}));xf();var bf="-transition";function _f(t,e){if(this.id=t.id,this.type=t.type,"custom"!==t.type&&(t=t,this.metadata=t.metadata,this.minzoom=t.minzoom,this.maxzoom=t.maxzoom,"background"!==t.type&&(this.source=t.source,this.sourceLayer=t["source-layer"],this.filter=t.filter),e.layout&&(this._unevaluatedLayout=new uf(e.layout)),e.paint)){for(var r in this._transitionablePaint=new mf(e.paint),t.paint)this.setPaintProperty(r,t.paint[r],{validate:!1});for(var n in t.layout)this.setLayoutProperty(n,t.layout[n],{validate:!1});this._transitioningPaint=this._transitionablePaint.untransitioned()}}_f.prototype.getCrossfadeParameters=function(){return this._crossfadeParameters},_f.prototype.getLayoutProperty=function(t){return"visibility"===t?this.visibility:this._unevaluatedLayout.getValue(t)},_f.prototype.setLayoutProperty=function(t,e,r){if(null!=e)this.id;if("visibility"===t)return this.visibility=e,void(this.config&&this.config.layout&&(this.config.layout.visibility=e));this._unevaluatedLayout.setValue(t,e)},_f.prototype.getPaintProperty=function(t){return Sl(t,bf)?this._transitionablePaint.getTransition(t.slice(0,-bf.length)):this._transitionablePaint.getValue(t)},_f.prototype.setPaintProperty=function(t,e,r){if(null!=e)this.id;if(Sl(t,bf))return this._transitionablePaint.setTransition(t.slice(0,-bf.length),e||void 0),!1;var n=this._transitionablePaint._values[t],i="cross-faded-data-driven"===n.property.specification["property-type"],o=n.value.isDataDriven(),a=n.value;this._transitionablePaint.setValue(t,e),this._handleSpecialPaintPropertyUpdate(t);var s=this._transitionablePaint._values[t].value;return s.isDataDriven()||o||i||this._handleOverridablePaintPropertyUpdate(t,a,s)},_f.prototype._handleSpecialPaintPropertyUpdate=function(t){},_f.prototype._handleOverridablePaintPropertyUpdate=function(t,e,r){return!1},_f.prototype.isHidden=function(t){return!!(this.minzoom&&t<this.minzoom)||(!!(this.maxzoom&&t>=this.maxzoom)||"none"===this.visibility)},_f.prototype.updateTransitions=function(t){this._transitioningPaint=this._transitionablePaint.transitioned(t,this._transitioningPaint)},_f.prototype.hasTransition=function(){return this._transitioningPaint.hasTransition()},_f.prototype.recalculate=function(t,e){t.getCrossfadeParameters&&(this._crossfadeParameters=t.getCrossfadeParameters()),this._unevaluatedLayout&&(this.layout=this._unevaluatedLayout.possiblyEvaluate(t,e)),this.paint=this._transitioningPaint.possiblyEvaluate(t,e)},_f.prototype.serialize=function(){var t={id:this.id,type:this.type,source:this.source,"source-layer":this.sourceLayer,metadata:this.metadata,minzoom:this.minzoom,maxzoom:this.maxzoom,filter:this.filter,layout:this._unevaluatedLayout&&this._unevaluatedLayout.serialize(),paint:this._transitionablePaint&&this._transitionablePaint.serialize()};return this.visibility&&(t.layout=t.layout||{},t.layout.visibility=this.visibility),El(t,(function(t,e){return!(void 0===t||"layout"===e&&!Object.keys(t).length||"paint"===e&&!Object.keys(t).length)}))},_f.prototype._validate=function(t,e,r,n,i){return!0},_f.prototype.is3D=function(){return!1},_f.prototype.isTileClipped=function(){return!1},_f.prototype.hasOffscreenPass=function(){return!1},_f.prototype.resize=function(){},_f.prototype.isStateDependent=function(){return!0};var wf,Sf=(wf=!0,function(t,e){var r=wf?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return wf=!1,r}),Af=Sf(void 0,(function(){return Af.toString().search("(((.+)+)+)+$").toString().constructor(Af).search("(((.+)+)+)+$")}));Af();var Ef,Tf=8,If={version:{required:!0,type:"enum",values:[8]},name:{type:"string"},metadata:{type:"*"},center:{type:"array",value:"number"},zoom:{type:"number"},bearing:{type:"number",default:0,period:360,units:"degrees"},pitch:{type:"number",default:0,units:"degrees"},light:{type:"light"},sources:{required:!0,type:"sources"},sprite:{type:"string"},glyphs:{type:"string"},transition:{type:"transition"},layers:{required:!0,type:"array",value:"layer"}},Of={"*":{type:"source"}},kf=["source_vector","source_raster","source_raster_dem","source_geojson","source_video","source_image"],Ff={type:{required:!0,type:"enum",values:{vector:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},attribution:{type:"string"},"*":{type:"*"}},Rf={type:{required:!0,type:"enum",values:{raster:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},attribution:{type:"string"},"*":{type:"*"}},Mf={type:{required:!0,type:"enum",values:{"raster-dem":{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},attribution:{type:"string"},encoding:{type:"enum",values:{terrarium:{},mapbox:{}},default:"mapbox"},"*":{type:"*"}},Cf={type:{required:!0,type:"enum",values:{geojson:{}}},data:{type:"*"},maxzoom:{type:"number",default:18},attribution:{type:"string"},buffer:{type:"number",default:128,maximum:512,minimum:0},tolerance:{type:"number",default:.375},cluster:{type:"boolean",default:!1},clusterRadius:{type:"number",default:50,minimum:0},clusterMaxZoom:{type:"number"},clusterProperties:{type:"*"},lineMetrics:{type:"boolean",default:!1},generateId:{type:"boolean",default:!1}},Pf={type:{required:!0,type:"enum",values:{video:{}}},urls:{required:!0,type:"array",value:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},zf={type:{required:!0,type:"enum",values:{image:{}}},url:{required:!0,type:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},Bf={id:{type:"string",required:!0},type:{type:"enum",values:{fill:{},line:{},symbol:{},circle:{},heatmap:{},"fill-extrusion":{},raster:{},hillshade:{},background:{}},required:!0},metadata:{type:"*"},source:{type:"string"},"source-layer":{type:"string"},minzoom:{type:"number",minimum:0,maximum:24},maxzoom:{type:"number",minimum:0,maximum:24},filter:{type:"filter"},layout:{type:"layout"},paint:{type:"paint"}},Df=["layout_fill","layout_line","layout_circle","layout_heatmap","layout_fill-extrusion","layout_symbol","layout_raster","layout_hillshade","layout_background"],Uf={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Nf={"fill-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Vf={"circle-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Lf={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},$f={"line-cap":{type:"enum",values:{butt:{},round:{},square:{}},default:"butt",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-join":{type:"enum",values:{bevel:{},round:{},miter:{}},default:"miter",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-miter-limit":{type:"number",default:2,requires:[{"line-join":"miter"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-round-limit":{type:"number",default:1.05,requires:[{"line-join":"round"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Xf={"symbol-placement":{type:"enum",values:{point:{},line:{},"line-center":{}},default:"point",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-spacing":{type:"number",default:250,minimum:1,units:"pixels",requires:[{"symbol-placement":"line"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"symbol-avoid-edges":{type:"boolean",default:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"symbol-z-order":{type:"enum",values:{auto:{},"viewport-y":{},source:{}},default:"auto",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-allow-overlap":{type:"boolean",default:!1,requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-ignore-placement":{type:"boolean",default:!1,requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-optional":{type:"boolean",default:!1,requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-rotation-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-size":{type:"number",default:1,minimum:0,units:"factor of the original icon size",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-text-fit":{type:"enum",values:{none:{},width:{},height:{},both:{}},default:"none",requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-text-fit-padding":{type:"array",value:"number",length:4,default:[0,0,0,0],units:"pixels",requires:["icon-image","text-field",{"icon-text-fit":["both","width","height"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-image":{type:"resolvedImage",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-padding":{type:"number",default:2,minimum:0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-keep-upright":{type:"boolean",default:!1,requires:["icon-image",{"icon-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-offset":{type:"array",value:"number",length:2,default:[0,0],requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotation-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-field":{type:"formatted",default:"",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-font":{type:"array",value:"string",default:["Open Sans Regular","Arial Unicode MS Regular"],requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-size":{type:"number",default:16,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-width":{type:"number",default:10,minimum:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-line-height":{type:"number",default:1.2,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-letter-spacing":{type:"number",default:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-justify":{type:"enum",values:{auto:{},left:{},center:{},right:{}},default:"center",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-radial-offset":{type:"number",units:"ems",default:0,requires:["text-field"],"property-type":"data-driven",expression:{interpolated:!0,parameters:["zoom","feature"]}},"text-variable-anchor":{type:"array",value:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["text-field",{"!":"text-variable-anchor"}],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-angle":{type:"number",default:45,units:"degrees",requires:["text-field",{"symbol-placement":["line","line-center"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-writing-mode":{type:"array",value:"enum",values:{horizontal:{},vertical:{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-padding":{type:"number",default:2,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-keep-upright":{type:"boolean",default:!0,requires:["text-field",{"text-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-transform":{type:"enum",values:{none:{},uppercase:{},lowercase:{}},default:"none",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-offset":{type:"array",value:"number",units:"ems",length:2,default:[0,0],requires:["text-field",{"!":"text-radial-offset"}],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-allow-overlap":{type:"boolean",default:!1,requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-ignore-placement":{type:"boolean",default:!1,requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-optional":{type:"boolean",default:!1,requires:["text-field","icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},jf={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},qf={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Hf={type:"array",value:"*"},Yf={type:"enum",values:{"==":{},"!=":{},">":{},">=":{},"<":{},"<=":{},in:{},"!in":{},all:{},any:{},none:{},has:{},"!has":{}}},Wf={type:"enum",values:{Point:{},LineString:{},Polygon:{}}},Gf={type:"array",minimum:0,maximum:24,value:["number","color"],length:2},Qf={type:"array",value:"*",minimum:1},Kf={type:"enum",values:{let:{group:"Variable binding"},var:{group:"Variable binding"},literal:{group:"Types"},array:{group:"Types"},at:{group:"Lookup"},in:{group:"Lookup"},case:{group:"Decision"},match:{group:"Decision"},coalesce:{group:"Decision"},step:{group:"Ramps, scales, curves"},interpolate:{group:"Ramps, scales, curves"},"interpolate-hcl":{group:"Ramps, scales, curves"},"interpolate-lab":{group:"Ramps, scales, curves"},ln2:{group:"Math"},pi:{group:"Math"},e:{group:"Math"},typeof:{group:"Types"},string:{group:"Types"},number:{group:"Types"},boolean:{group:"Types"},object:{group:"Types"},collator:{group:"Types"},format:{group:"Types"},image:{group:"Types"},"number-format":{group:"Types"},"to-string":{group:"Types"},"to-number":{group:"Types"},"to-boolean":{group:"Types"},"to-rgba":{group:"Color"},"to-color":{group:"Types"},rgb:{group:"Color"},rgba:{group:"Color"},get:{group:"Lookup"},has:{group:"Lookup"},length:{group:"Lookup"},properties:{group:"Feature data"},"feature-state":{group:"Feature data"},"geometry-type":{group:"Feature data"},id:{group:"Feature data"},zoom:{group:"Zoom"},"heatmap-density":{group:"Heatmap"},"line-progress":{group:"Feature data"},accumulated:{group:"Feature data"},"+":{group:"Math"},"*":{group:"Math"},"-":{group:"Math"},"/":{group:"Math"},"%":{group:"Math"},"^":{group:"Math"},sqrt:{group:"Math"},log10:{group:"Math"},ln:{group:"Math"},log2:{group:"Math"},sin:{group:"Math"},cos:{group:"Math"},tan:{group:"Math"},asin:{group:"Math"},acos:{group:"Math"},atan:{group:"Math"},min:{group:"Math"},max:{group:"Math"},round:{group:"Math"},abs:{group:"Math"},ceil:{group:"Math"},floor:{group:"Math"},"==":{group:"Decision"},"!=":{group:"Decision"},">":{group:"Decision"},"<":{group:"Decision"},">=":{group:"Decision"},"<=":{group:"Decision"},all:{group:"Decision"},any:{group:"Decision"},"!":{group:"Decision"},"is-supported-script":{group:"String"},upcase:{group:"String"},downcase:{group:"String"},concat:{group:"String"},"resolved-locale":{group:"String"}}},Jf={anchor:{type:"enum",default:"viewport",values:{map:{},viewport:{}},"property-type":"data-constant",transition:!1,expression:{interpolated:!1,parameters:["zoom"]}},position:{type:"array",default:[1.15,210,30],length:3,value:"number","property-type":"data-constant",transition:!0,expression:{interpolated:!0,parameters:["zoom"]}},color:{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},intensity:{type:"number","property-type":"data-constant",default:.5,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0}},Zf=["paint_fill","paint_line","paint_circle","paint_heatmap","paint_fill-extrusion","paint_symbol","paint_raster","paint_hillshade","paint_background"],th={"fill-antialias":{type:"boolean",default:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-outline-color":{type:"color",transition:!0,requires:[{"!":"fill-pattern"},{"fill-antialias":!0}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"}},eh={"line-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"line-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["line-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-width":{type:"number",default:1,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-gap-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-offset":{type:"number",default:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-dasharray":{type:"array",value:"number",minimum:0,transition:!0,units:"line widths",requires:[{"!":"line-pattern"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"line-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"line-gradient":{type:"color",transition:!1,requires:[{"!":"line-dasharray"},{"!":"line-pattern"},{source:"geojson",has:{lineMetrics:!0}}],expression:{interpolated:!0,parameters:["line-progress"]},"property-type":"color-ramp"}},rh={"circle-radius":{type:"number",default:5,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-blur":{type:"number",default:0,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"circle-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["circle-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-scale":{type:"enum",values:{map:{},viewport:{}},default:"map",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-alignment":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-stroke-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"}},nh={"heatmap-radius":{type:"number",default:30,minimum:1,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-weight":{type:"number",default:1,minimum:0,transition:!1,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-intensity":{type:"number",default:1,minimum:0,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"heatmap-color":{type:"color",default:["interpolate",["linear"],["heatmap-density"],0,"rgba(0, 0, 255, 0)",.1,"royalblue",.3,"cyan",.5,"lime",.7,"yellow",1,"red"],transition:!1,expression:{interpolated:!0,parameters:["heatmap-density"]},"property-type":"color-ramp"},"heatmap-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},ih={"icon-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-color":{type:"color",default:"#000000",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["icon-image","icon-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-color":{type:"color",default:"#000000",transition:!0,overridable:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-show-background":{type:"boolean",default:!1,transition:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["text-field","text-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},oh={"raster-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-hue-rotate":{type:"number",default:0,period:360,transition:!0,units:"degrees",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-min":{type:"number",default:0,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-max":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-saturation":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-contrast":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-resampling":{type:"enum",values:{linear:{},nearest:{}},default:"linear",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"raster-fade-duration":{type:"number",default:300,minimum:0,transition:!1,units:"milliseconds",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},ah={"hillshade-illumination-direction":{type:"number",default:335,minimum:0,maximum:359,transition:!1,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-illumination-anchor":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-exaggeration":{type:"number",default:.5,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-shadow-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-highlight-color":{type:"color",default:"#FFFFFF",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-accent-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},sh={"background-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"background-pattern"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"background-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"background-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},uh={duration:{type:"number",default:300,minimum:0,units:"milliseconds"},delay:{type:"number",default:0,minimum:0,units:"milliseconds"}},lh={$version:Tf,$root:If,sources:Of,source:kf,source_vector:Ff,source_raster:Rf,source_raster_dem:Mf,source_geojson:Cf,source_video:Pf,source_image:zf,layer:Bf,layout:Df,layout_background:Uf,layout_fill:Nf,layout_circle:Vf,layout_heatmap:Lf,"layout_fill-extrusion":{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_line:$f,layout_symbol:Xf,layout_raster:jf,layout_hillshade:qf,filter:Hf,filter_operator:Yf,geometry_type:Wf,function:{expression:{type:"expression"},stops:{type:"array",value:"function_stop"},base:{type:"number",default:1,minimum:0},property:{type:"string",default:"$zoom"},type:{type:"enum",values:{identity:{},exponential:{},interval:{},categorical:{}},default:"exponential"},colorSpace:{type:"enum",values:{rgb:{},lab:{},hcl:{}},default:"rgb"},default:{type:"*",required:!1}},function_stop:Gf,expression:Qf,expression_name:Kf,light:Jf,paint:Zf,paint_fill:th,"paint_fill-extrusion":{"fill-extrusion-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-extrusion-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-extrusion-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"fill-extrusion-height":{type:"number",default:0,minimum:0,units:"meters",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-base":{type:"number",default:0,minimum:0,units:"meters",transition:!0,requires:["fill-extrusion-height"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-vertical-gradient":{type:"boolean",default:!0,transition:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},paint_line:eh,paint_circle:rh,paint_heatmap:nh,paint_symbol:ih,paint_raster:oh,paint_hillshade:ah,paint_background:sh,transition:uh,"property-type":{"data-driven":{type:"property-type"},"cross-faded":{type:"property-type"},"cross-faded-data-driven":{type:"property-type"},"color-ramp":{type:"property-type"},"data-constant":{type:"property-type"},constant:{type:"property-type"}}},ch=(Ef=!0,function(t,e){var r=Ef?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ef=!1,r}),ph=ch(void 0,(function(){return ph.toString().search("(((.+)+)+)+$").toString().constructor(ph).search("(((.+)+)+)+$")}));function fh(){}function hh(t,e,r){if(t.length>1){if(yh(t,e))return!0;for(var n=0;n<e.length;n++)if(mh(e[n],t,r))return!0}for(var i=0;i<t.length;i++)if(mh(t[i],e,r))return!0;return!1}function yh(t,e){if(0===t.length||0===e.length)return!1;for(var r=0;r<t.length-1;r++)for(var n=t[r],i=t[r+1],o=0;o<e.length-1;o++){if(dh(n,i,e[o],e[o+1]))return!0}return!1}function dh(t,e,r,n){return Ol(t,r,n)!==Ol(e,r,n)&&Ol(t,e,r)!==Ol(t,e,n)}function mh(t,e,r){var n=r*r;if(1===e.length)return t.distSqr(e[0])<n;for(var i=1;i<e.length;i++){var o=e[i-1],a=e[i];if(fh.distToSegmentSquared(t,o,a)<n)return!0}return!1}function gh(t,e){for(var r,n,i,o=!1,a=0;a<t.length;a++)for(var s=0,u=(r=t[a]).length-1;s<r.length;u=s++)n=r[s],i=r[u],n.y>e.y!=i.y>e.y&&e.x<(i.x-n.x)*(e.y-n.y)/(i.y-n.y)+n.x&&(o=!o);return o}function vh(t,e){for(var r=!1,n=0,i=t.length-1;n<t.length;i=n++){var o=t[n],a=t[i];o.y>e.y!=a.y>e.y&&e.x<(a.x-o.x)*(e.y-o.y)/(a.y-o.y)+o.x&&(r=!r)}return r}ph(),fh.polygonIntersectsPolygon=function(t,e){for(var r=0;r<t.length;r++)if(vh(e,t[r]))return!0;for(var n=0;n<e.length;n++)if(vh(t,e[n]))return!0;return!!yh(t,e)},fh.polygonIntersectsBufferedPoint=function(t,e,r){return!!vh(t,e)||!!mh(e,t,r)},fh.polygonIntersectsMultiPolygon=function(t,e){if(1===t.length)return gh(e,t[0]);for(var r=0;r<e.length;r++)for(var n=e[r],i=0;i<n.length;i++)if(vh(t,n[i]))return!0;for(var o=0;o<t.length;o++)if(gh(e,t[o]))return!0;for(var a=0;a<e.length;a++)if(yh(t,e[a]))return!0;return!1},fh.polygonIntersectsBufferedMultiLine=function(t,e,r){for(var n=0;n<e.length;n++){var i=e[n];if(t.length>=3)for(var o=0;o<i.length;o++)if(vh(t,i[o]))return!0;if(hh(t,i,r))return!0}return!1},fh.distToSegmentSquared=function(t,e,r){var n=e.distSqr(r);if(0===n)return t.distSqr(e);var i=((t.x-e.x)*(r.x-e.x)+(t.y-e.y)*(r.y-e.y))/n;return i<0?t.distSqr(e):i>1?t.distSqr(r):t.distSqr(r.sub(e)._mult(i)._add(e))};var xh=new Qp({"circle-sort-key":new Mp(lh.layout_circle["circle-sort-key"])}),bh=new Qp({"circle-radius":new Mp(lh.paint_circle["circle-radius"]),"circle-color":new Mp(lh.paint_circle["circle-color"]),"circle-blur":new Mp(lh.paint_circle["circle-blur"]),"circle-opacity":new Mp(lh.paint_circle["circle-opacity"]),"circle-translate":new Op(lh.paint_circle["circle-translate"]),"circle-translate-anchor":new Op(lh.paint_circle["circle-translate-anchor"]),"circle-pitch-scale":new Op(lh.paint_circle["circle-pitch-scale"]),"circle-pitch-alignment":new Op(lh.paint_circle["circle-pitch-alignment"]),"circle-stroke-width":new Mp(lh.paint_circle["circle-stroke-width"]),"circle-stroke-color":new Mp(lh.paint_circle["circle-stroke-color"]),"circle-stroke-opacity":new Mp(lh.paint_circle["circle-stroke-opacity"])}),_h={paint:bh,layout:xh},wh=function(t){var r,n=(r=!0,function(t,e){var n=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,n}),i=n(this,(function(){return i.toString().search("(((.+)+)+)+$").toString().constructor(i).search("(((.+)+)+)+$")}));function o(e){t.call(this,e,_h)}return i(),t&&(o.__proto__=t),o.prototype=Object.create(t&&t.prototype),o.prototype.constructor=o,o.prototype.createBucket=function(t){return new Ap(t)},o.prototype.queryRadius=function(t){var e=t;return Zp.getMaximumPaintValue("circle-radius",this,e)+Zp.getMaximumPaintValue("circle-stroke-width",this,e)+Zp.translateDistance(this.paint.get("circle-translate"))},o.prototype.queryIntersectsFeature=function(t,r,n,i,o,a,s,u,l){u=Eh();for(var c=Zp.translate(t,this.paint.get("circle-translate"),this.paint.get("circle-translate-anchor"),0,s),p=this.paint.get("circle-radius")?.evaluate(r,n),f=this.paint.get("circle-stroke-width")?.evaluate(r,n),h=p+f,y="map"===this.paint.get("circle-pitch-alignment"),d=y?c:Ah(c,u),m=y?h*s:h,g=0,v=i;g<v.length;g+=1)for(var x=0,b=v[g];x<b.length;x+=1){var _=b[x],w=y?_:Sh(_,u),S=m;if(Th([],[_.x,_.y,0,1],u),"viewport"===this.paint.get("circle-pitch-scale")&&"map"===this.paint.get("circle-pitch-alignment")||"map"===this.paint.get("circle-pitch-scale")&&this.paint.get("circle-pitch-alignment"),l=e.t(l)?l:10,fh.polygonIntersectsBufferedPoint(d,w,S*l))return!0}return!1},o}(_f);function Sh(t,e){var r=Th([],[t.x,t.y,0,1],e);return new zi(r[0],r[1])}function Ah(t,e){return t.map((function(t){return Sh(t,e)}))}function Eh(){var t=new Float32Array(16);return t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function Th(t,e,r){var n=e[0],i=e[1],o=e[2],a=r[3]*n+r[7]*i+r[11]*o+r[15];return a=a||1,t[0]=(r[0]*n+r[4]*i+r[8]*o+r[12])/a,t[1]=(r[1]*n+r[5]*i+r[9]*o+r[13])/a,t[2]=(r[2]*n+r[6]*i+r[10]*o+r[14])/a,t}var Ih=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(){t.apply(this,arguments)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.possiblyEvaluate=function(t,e,r){if(void 0===t.value)return new wo(this,{kind:"constant",value:void 0},e);if("constant"===t.expression.kind){var n=t.expression.evaluate(e,null,{},r),i="resolvedImage"===t.property.specification.type&&"string"!=typeof n?n.name:n,o=this._calculate(i,i,i,e);return new wo(this,{kind:"constant",value:o},e)}if("camera"===t.expression.kind){var a=this._calculate(t.expression.evaluate({zoom:e.zoom-1}),t.expression.evaluate({zoom:e.zoom}),t.expression.evaluate({zoom:e.zoom+1}),e);return new wo(this,{kind:"constant",value:a},e)}return new wo(this,t.expression,e)},i.prototype.evaluate=function(t,e,r,n,i){if("source"===t.kind){var o=t.evaluate(e,r,n,i);return this._calculate(o,o,o,e)}return"composite"===t.kind?this._calculate(t.evaluate({zoom:Math.floor(e.zoom)-1},r,n),t.evaluate({zoom:Math.floor(e.zoom)},r,n),t.evaluate({zoom:Math.floor(e.zoom)+1},r,n),e):t.value},i.prototype._calculate=function(t,e,r,n){return n.zoom>n.zoomHistory.lastIntegerZoom?{from:t,to:e}:{from:r,to:e}},i.prototype.interpolate=function(t){return t},i}(Mp);ri.register("DataDrivenProperty",Mp);var Oh,kh=new Qp({"fill-sort-key":new Mp(lh.layout_fill["fill-sort-key"])}),Fh=new Qp({"fill-antialias":new Op(lh.paint_fill["fill-antialias"]),"fill-opacity":new Mp(lh.paint_fill["fill-opacity"]),"fill-color":new Mp(lh.paint_fill["fill-color"]),"fill-outline-color":new Mp(lh.paint_fill["fill-outline-color"]),"fill-translate":new Op(lh.paint_fill["fill-translate"]),"fill-translate-anchor":new Op(lh.paint_fill["fill-translate-anchor"]),"fill-pattern":new Ih(lh.paint_fill["fill-pattern"])}),Rh={paint:Fh,layout:kh},Mh=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e){t.call(this,e,Rh)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.recalculate=function(e,r){t.prototype.recalculate.call(this,e,r);var n=this.paint._values["fill-outline-color"];"constant"===n.value.kind&&void 0===n.value.value&&(this.paint._values["fill-outline-color"]=this.paint._values["fill-color"])},i.prototype.createBucket=function(t){return new Wu(t)},i.prototype.queryRadius=function(){return Zp.translateDistance(this.paint.get("fill-translate"))},i.prototype.queryIntersectsFeature=function(t,e,r,n,i,o,a){var s=Zp.translate(t,this.paint.get("fill-translate"),this.paint.get("fill-translate-anchor"),0,a);return fh.polygonIntersectsMultiPolygon(s,n)},i.prototype.isTileClipped=function(){return!0},i}(_f),Ch=(Oh=!0,function(t,e){var r=Oh?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Oh=!1,r}),Ph=Ch(void 0,(function(){return Ph.toString().search("(((.+)+)+)+$").toString().constructor(Ph).search("(((.+)+)+)+$")}));Ph();var zh=function(t){this.specification=t};zh.prototype.possiblyEvaluate=function(t,e,r){if(void 0!==t.value){if("constant"===t.expression.kind){var n=t.expression.evaluate(e,null,{},r);return this._calculate(n,n,n,e)}return this._calculate(t.expression.evaluate(new hi(Math.floor(e.zoom-1),e)),t.expression.evaluate(new hi(Math.floor(e.zoom),e)),t.expression.evaluate(new hi(Math.floor(e.zoom+1),e)),e)}},zh.prototype._calculate=function(t,e,r,n){return n.zoom>n.zoomHistory.lastIntegerZoom?{from:t,to:e}:{from:r,to:e}},zh.prototype.interpolate=function(t){return t},ri.register("CrossFadedProperty",zh);var Bh,Dh=(Bh=!0,function(t,e){var r=Bh?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Bh=!1,r}),Uh=Dh(void 0,(function(){return Uh.toString().search("(((.+)+)+)+$").toString().constructor(Uh).search("(((.+)+)+)+$")}));Uh();var Nh=function(t){this.specification=t};Nh.prototype.possiblyEvaluate=function(t,e,r){return!!t.expression.evaluate(e,null,{},r)},Nh.prototype.interpolate=function(){return!1},ri.register("ColorRampProperty",Nh);var Vh=new Qp({"line-cap":new Op(lh.layout_line["line-cap"]),"line-join":new Mp(lh.layout_line["line-join"]),"line-miter-limit":new Op(lh.layout_line["line-miter-limit"]),"line-round-limit":new Op(lh.layout_line["line-round-limit"]),"line-sort-key":new Mp(lh.layout_line["line-sort-key"])}),Lh=new Qp({"line-opacity":new Mp(lh.paint_line["line-opacity"]),"line-color":new Mp(lh.paint_line["line-color"]),"line-translate":new Op(lh.paint_line["line-translate"]),"line-translate-anchor":new Op(lh.paint_line["line-translate-anchor"]),"line-width":new Mp(lh.paint_line["line-width"]),"line-gap-width":new Mp(lh.paint_line["line-gap-width"]),"line-offset":new Mp(lh.paint_line["line-offset"]),"line-blur":new Mp(lh.paint_line["line-blur"]),"line-dasharray":new zh(lh.paint_line["line-dasharray"]),"line-pattern":new Ih(lh.paint_line["line-pattern"]),"line-gradient":new Nh(lh.paint_line["line-gradient"])}),$h={paint:Lh,layout:Vh},Xh=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(){t.apply(this,arguments)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.possiblyEvaluate=function(e,r){return r=new hi(Math.floor(r.zoom),{now:r.now,fadeDuration:r.fadeDuration,zoomHistory:r.zoomHistory,transition:r.transition}),t.prototype.possiblyEvaluate.call(this,e,r)},i.prototype.evaluate=function(e,r,n,i){return r=extend({},r,{zoom:Math.floor(r.zoom)}),t.prototype.evaluate.call(this,e,r,n,i)},i}(Mp),jh=new Xh($h.paint.properties["line-width"].specification);jh.useIntegerZoom=!0;var qh=function(t){function e(e){t.call(this,e,$h)}function r(t,e){return e>0?e+2*t:t}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._handleSpecialPaintPropertyUpdate=function(t){"line-gradient"===t&&this._updateGradient()},e.prototype._updateGradient=function(){var t=this._transitionablePaint._values["line-gradient"].value.expression;this.gradient=renderColorRamp(t,"lineProgress"),this.gradientTexture=null},e.prototype.recalculate=function(e,r){t.prototype.recalculate.call(this,e,r),this.paint._values["line-floorwidth"]=jh.possiblyEvaluate(this._transitioningPaint._values["line-width"].value,e)},e.prototype.createBucket=function(t){return new rs(t)},e.prototype.queryRadius=function(t){var e=t,n=r(Zp.getMaximumPaintValue("line-width",this,e),Zp.getMaximumPaintValue("line-gap-width",this,e)),i=Zp.getMaximumPaintValue("line-offset",this,e);return n/2+Math.abs(i)+Zp.translateDistance(this.paint.get("line-translate"))},e.prototype.queryIntersectsFeature=function(t,e,n,i,o,a,s){var u=Zp.translate(t,this.paint.get("line-translate"),this.paint.get("line-translate-anchor"),0,s),l=r(this.paint.get("line-width")?.evaluate(e,n),this.paint.get("line-gap-width")?.evaluate(e,n)),c=s/2*(l=Math.max(l,5)),p=this.paint.get("line-offset")?.evaluate(e,n);return p&&(i=function(t,e){for(var r=[],n=new zi(0,0),i=0;i<t.length;i++){for(var o=t[i],a=[],s=0;s<o.length;s++){var u=o[s-1],l=o[s],c=o[s+1],p=0===s?n:l.sub(u)._unit()._perp(),f=s===o.length-1?n:c.sub(l)._unit()._perp(),h=p._add(f)._unit(),y=h.x*f.x+h.y*f.y;h._mult(1/y),a.push(h._mult(e)._add(l))}r.push(a)}return r}(i,p*s)),fh.polygonIntersectsBufferedMultiLine(u,i,c)},e.prototype.isTileClipped=function(){return!0},e}(_f);const Hh=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Yh=Hh(void 0,(function(){return Yh.toString().search("(((.+)+)+)+$").toString().constructor(Yh).search("(((.+)+)+)+$")}));Yh();const Wh=J([{name:"a_pos_offset",components:4,type:"Int16"},{name:"a_data",components:4,type:"Uint16"},{name:"a_pixeloffset",components:4,type:"Int16"}],4),Gh=J([{name:"a_projected_pos",components:3,type:"Float32"}],4);J([{name:"a_fade_opacity",components:1,type:"Uint32"}],4),J([{name:"a_fade_opacity",components:1,type:"Float32"}],4);const Qh=J([{name:"a_placed",components:2,type:"Uint8"},{name:"a_shift",components:2,type:"Float32"}]);J([{type:"Int16",name:"anchorPointX"},{type:"Int16",name:"anchorPointY"},{type:"Int16",name:"x1"},{type:"Int16",name:"y1"},{type:"Int16",name:"x2"},{type:"Int16",name:"y2"},{type:"Uint32",name:"featureIndex"},{type:"Uint16",name:"sourceLayerIndex"},{type:"Uint16",name:"bucketIndex"},{type:"Int16",name:"radius"},{type:"Int16",name:"signedDistanceFromAnchor"}]);const Kh=J([{name:"a_pos",components:2,type:"Int16"},{name:"a_anchor_pos",components:2,type:"Int16"},{name:"a_extrude",components:2,type:"Int16"}],4),Jh=J([{name:"a_pos",components:2,type:"Int16"},{name:"a_anchor_pos",components:2,type:"Int16"},{name:"a_extrude",components:2,type:"Int16"}],4);J([{type:"Int16",name:"anchorX"},{type:"Int16",name:"anchorY"},{type:"Uint16",name:"glyphStartIndex"},{type:"Uint16",name:"numGlyphs"},{type:"Uint32",name:"vertexStartIndex"},{type:"Uint32",name:"lineStartIndex"},{type:"Uint32",name:"lineLength"},{type:"Uint16",name:"segment"},{type:"Uint16",name:"lowerSize"},{type:"Uint16",name:"upperSize"},{type:"Float32",name:"lineOffsetX"},{type:"Float32",name:"lineOffsetY"},{type:"Uint8",name:"writingMode"},{type:"Uint8",name:"placedOrientation"},{type:"Uint8",name:"hidden"},{type:"Uint32",name:"crossTileID"},{type:"Int16",name:"associatedIconIndex"}]),J([{type:"Int16",name:"anchorX"},{type:"Int16",name:"anchorY"},{type:"Int16",name:"rightJustifiedTextSymbolIndex"},{type:"Int16",name:"centerJustifiedTextSymbolIndex"},{type:"Int16",name:"leftJustifiedTextSymbolIndex"},{type:"Int16",name:"verticalPlacedTextSymbolIndex"},{type:"Int16",name:"placedIconSymbolIndex"},{type:"Int16",name:"verticalPlacedIconSymbolIndex"},{type:"Uint16",name:"key"},{type:"Uint16",name:"textBoxStartIndex"},{type:"Uint16",name:"textBoxEndIndex"},{type:"Uint16",name:"verticalTextBoxStartIndex"},{type:"Uint16",name:"verticalTextBoxEndIndex"},{type:"Uint16",name:"iconBoxStartIndex"},{type:"Uint16",name:"iconBoxEndIndex"},{type:"Uint16",name:"verticalIconBoxStartIndex"},{type:"Uint16",name:"verticalIconBoxEndIndex"},{type:"Uint16",name:"featureIndex"},{type:"Uint16",name:"numHorizontalGlyphVertices"},{type:"Uint16",name:"numVerticalGlyphVertices"},{type:"Uint16",name:"numIconVertices"},{type:"Uint16",name:"numVerticalIconVertices"},{type:"Uint32",name:"crossTileID"},{type:"Float32",name:"textBoxScale"},{type:"Float32",components:2,name:"textOffset"}]),J([{type:"Float32",name:"offsetX"}]),J([{type:"Int16",name:"x"},{type:"Int16",name:"y"},{type:"Int16",name:"tileUnitDistanceFromAnchor"}]);var Zh,ty=(Zh=!0,function(t,e){var r=Zh?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Zh=!1,r}),ey=ty(void 0,(function(){return ey.toString().search("(((.+)+)+)+$").toString().constructor(ey).search("(((.+)+)+)+$")}));ey();var ry={"Latin-1 Supplement":function(t){return t>=128&&t<=255},Arabic:function(t){return t>=1536&&t<=1791},"Arabic Supplement":function(t){return t>=1872&&t<=1919},"Arabic Extended-A":function(t){return t>=2208&&t<=2303},"Hangul Jamo":function(t){return t>=4352&&t<=4607},"Unified Canadian Aboriginal Syllabics":function(t){return t>=5120&&t<=5759},Khmer:function(t){return t>=6016&&t<=6143},"Unified Canadian Aboriginal Syllabics Extended":function(t){return t>=6320&&t<=6399},"General Punctuation":function(t){return t>=8192&&t<=8303},"Letterlike Symbols":function(t){return t>=8448&&t<=8527},"Number Forms":function(t){return t>=8528&&t<=8591},"Miscellaneous Technical":function(t){return t>=8960&&t<=9215},"Control Pictures":function(t){return t>=9216&&t<=9279},"Optical Character Recognition":function(t){return t>=9280&&t<=9311},"Enclosed Alphanumerics":function(t){return t>=9312&&t<=9471},"Geometric Shapes":function(t){return t>=9632&&t<=9727},"Miscellaneous Symbols":function(t){return t>=9728&&t<=9983},"Miscellaneous Symbols and Arrows":function(t){return t>=11008&&t<=11263},"CJK Radicals Supplement":function(t){return t>=11904&&t<=12031},"Kangxi Radicals":function(t){return t>=12032&&t<=12255},"Ideographic Description Characters":function(t){return t>=12272&&t<=12287},"CJK Symbols and Punctuation":function(t){return t>=12288&&t<=12351},Hiragana:function(t){return t>=12352&&t<=12447},Katakana:function(t){return t>=12448&&t<=12543},Bopomofo:function(t){return t>=12544&&t<=12591},"Hangul Compatibility Jamo":function(t){return t>=12592&&t<=12687},Kanbun:function(t){return t>=12688&&t<=12703},"Bopomofo Extended":function(t){return t>=12704&&t<=12735},"CJK Strokes":function(t){return t>=12736&&t<=12783},"Katakana Phonetic Extensions":function(t){return t>=12784&&t<=12799},"Enclosed CJK Letters and Months":function(t){return t>=12800&&t<=13055},"CJK Compatibility":function(t){return t>=13056&&t<=13311},"CJK Unified Ideographs Extension A":function(t){return t>=13312&&t<=19903},"Yijing Hexagram Symbols":function(t){return t>=19904&&t<=19967},"CJK Unified Ideographs":function(t){return t>=19968&&t<=40959},"Yi Syllables":function(t){return t>=40960&&t<=42127},"Yi Radicals":function(t){return t>=42128&&t<=42191},"Hangul Jamo Extended-A":function(t){return t>=43360&&t<=43391},"Hangul Syllables":function(t){return t>=44032&&t<=55215},"Hangul Jamo Extended-B":function(t){return t>=55216&&t<=55295},"Private Use Area":function(t){return t>=57344&&t<=63743},"CJK Compatibility Ideographs":function(t){return t>=63744&&t<=64255},"Arabic Presentation Forms-A":function(t){return t>=64336&&t<=65023},"Vertical Forms":function(t){return t>=65040&&t<=65055},"CJK Compatibility Forms":function(t){return t>=65072&&t<=65103},"Small Form Variants":function(t){return t>=65104&&t<=65135},"Arabic Presentation Forms-B":function(t){return t>=65136&&t<=65279},"Halfwidth and Fullwidth Forms":function(t){return t>=65280&&t<=65519}};const ny=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),iy=ny(void 0,(function(){return iy.toString().search("(((.+)+)+)+$").toString().constructor(iy).search("(((.+)+)+)+$")}));function oy(t){for(const e of t)if(ay(e.charCodeAt(0)))return!0;return!1}function ay(t){return 746===t||747===t||!(t<4352)&&(!!ry["Bopomofo Extended"](t)||(!!ry.Bopomofo(t)||(!(!ry["CJK Compatibility Forms"](t)||t>=65097&&t<=65103)||(!!ry["CJK Compatibility Ideographs"](t)||(!!ry["CJK Compatibility"](t)||(!!ry["CJK Radicals Supplement"](t)||(!!ry["CJK Strokes"](t)||(!(!ry["CJK Symbols and Punctuation"](t)||t>=12296&&t<=12305||t>=12308&&t<=12319||12336===t)||(!!ry["CJK Unified Ideographs Extension A"](t)||(!!ry["CJK Unified Ideographs"](t)||(!!ry["Enclosed CJK Letters and Months"](t)||(!!ry["Hangul Compatibility Jamo"](t)||(!!ry["Hangul Jamo Extended-A"](t)||(!!ry["Hangul Jamo Extended-B"](t)||(!!ry["Hangul Jamo"](t)||(!!ry["Hangul Syllables"](t)||(!!ry.Hiragana(t)||(!!ry["Ideographic Description Characters"](t)||(!!ry.Kanbun(t)||(!!ry["Kangxi Radicals"](t)||(!!ry["Katakana Phonetic Extensions"](t)||(!(!ry.Katakana(t)||12540===t)||(!(!ry["Halfwidth and Fullwidth Forms"](t)||65288===t||65289===t||65293===t||t>=65306&&t<=65310||65339===t||65341===t||65343===t||t>=65371&&t<=65503||65507===t||t>=65512&&t<=65519)||(!(!ry["Small Form Variants"](t)||t>=65112&&t<=65118||t>=65123&&t<=65126)||(!!ry["Unified Canadian Aboriginal Syllabics"](t)||(!!ry["Unified Canadian Aboriginal Syllabics Extended"](t)||(!!ry["Vertical Forms"](t)||(!!ry["Yijing Hexagram Symbols"](t)||(!!ry["Yi Syllables"](t)||!!ry["Yi Radicals"](t))))))))))))))))))))))))))))))}function sy(t){return t>=1424&&t<=2303||ry["Arabic Presentation Forms-A"](t)||ry["Arabic Presentation Forms-B"](t)}function uy(t){for(const e of t)if(sy(e.charCodeAt(0)))return!0;return!1}iy();const ly=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),cy=ly(void 0,(function(){return cy.toString().search("(((.+)+)+)+$").toString().constructor(cy).search("(((.+)+)+)+$")}));cy();const py={"!":"︕","#":"＃",$:"＄","%":"％","&":"＆","(":"︵",")":"︶","*":"＊","+":"＋",",":"︐","-":"︲",".":"・","/":"／",":":"︓",";":"︔","<":"︿","=":"＝",">":"﹀","?":"︖","@":"＠","[":"﹇","\\":"＼","]":"﹈","^":"＾",_:"︳","`":"｀","{":"︷","|":"―","}":"︸","~":"～","¢":"￠","£":"￡","¥":"￥","¦":"￤","¬":"￢","¯":"￣","–":"︲","—":"︱","‘":"﹃","’":"﹄","“":"﹁","”":"﹂","…":"︙","‧":"・","₩":"￦","、":"︑","。":"︒","〈":"︿","〉":"﹀","《":"︽","》":"︾","「":"﹁","」":"﹂","『":"﹃","』":"﹄","【":"︻","】":"︼","〔":"︹","〕":"︺","〖":"︗","〗":"︘","！":"︕","（":"︵","）":"︶","，":"︐","－":"︲","．":"・","：":"︓","；":"︔","＜":"︿","＞":"﹀","？":"︖","［":"﹇","］":"﹈","＿":"︳","｛":"︷","｜":"―","｝":"︸","｟":"︵","｠":"︶","｡":"︒","｢":"﹁","｣":"﹂"};var fy,hy=(fy=!0,function(t,e){var r=fy?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return fy=!1,r}),yy=hy(void 0,(function(){return yy.toString().search("(((.+)+)+)+$").toString().constructor(yy).search("(((.+)+)+)+$")}));yy();var dy,my=(dy=!0,function(t,e){var r=dy?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return dy=!1,r}),gy=my(void 0,(function(){return gy.toString().search("(((.+)+)+)+$").toString().constructor(gy).search("(((.+)+)+)+$")}));function vy(t){var e={},r={},n=[],i=0;function o(e){n.push(t[e]),i++}function a(t,e,i){var o=r[t];return delete r[t],r[e]=o,n[o].geometry[0].pop(),n[o].geometry[0]=n[o].geometry[0].concat(i[0]),o}function s(t,r,i){var o=e[r];return delete e[r],e[t]=o,n[o].geometry[0].shift(),n[o].geometry[0]=i[0].concat(n[o].geometry[0]),o}function u(t,e,r){var n=r?e[0][e[0].length-1]:e[0][0];return t+":"+n.x+":"+n.y}for(var l=0;l<t.length;l++){var c=t[l],p=c.geometry,f=c.text?c.text.toString():null;if(f){var h=u(f,p),y=u(f,p,!0);if(h in r&&y in e&&r[h]!==e[y]){var d=s(h,y,p),m=a(h,y,n[d].geometry);delete e[h],delete r[y],r[u(f,n[m].geometry,!0)]=m,n[d].geometry=null}else h in r?a(h,y,p):y in e?s(h,y,p):(o(l),e[h]=i-1,r[y]=i-1)}else o(l)}return n.filter((function(t){return t.geometry}))}gy();var xy=function(t,e){void 0===e&&(e={}),wl(this,e),this.type=t};!function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e,r){void 0===r&&(r={}),t.call(this,"error",wl({error:e},r))}n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i}(xy);const by=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),_y=by(void 0,(function(){return _y.toString().search("(((.+)+)+)+$").toString().constructor(_y).search("(((.+)+)+)+$")}));_y();const wy=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Sy=wy(void 0,(function(){return Sy.toString().search("(((.+)+)+)+$").toString().constructor(Sy).search("(((.+)+)+)+$")}));Sy();const Ay=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Ey=Ay(void 0,(function(){return Ey.toString().search("(((.+)+)+)+$").toString().constructor(Ey).search("(((.+)+)+)+$")}));Ey();const Ty={Unknown:"Unknown",Style:"Style",Source:"Source",Tile:"Tile",Glyphs:"Glyphs",SpriteImage:"SpriteImage",SpriteJSON:"SpriteJSON",Image:"Image"};"function"==typeof Object.freeze&&Object.freeze(Ty),kl();const Iy=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Oy=Iy(void 0,(function(){return Oy.toString().search("(((.+)+)+)+$").toString().constructor(Oy).search("(((.+)+)+)+$")}));Oy();const ky=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Fy=ky(void 0,(function(){return Fy.toString().search("(((.+)+)+)+$").toString().constructor(Fy).search("(((.+)+)+)+$")}));Fy();var Ry,My=(Ry=!0,function(t,e){var r=Ry?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ry=!1,r}),Cy=My(void 0,(function(){return Cy.toString().search("(((.+)+)+)+$").toString().constructor(Cy).search("(((.+)+)+)+$")}));Cy();var Py,zy=(Py=!0,function(t,e){var r=Py?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Py=!1,r}),By=zy(void 0,(function(){return By.toString().search("(((.+)+)+)+$").toString().constructor(By).search("(((.+)+)+)+$")}));By();var Dy=function(t,e){Uy(this,t,1,e)};function Uy(t,e,r,n){var i=e.width,o=e.height;if(n){if(n instanceof Uint8ClampedArray)n=new Uint8Array(n.buffer);else if(n.length!==i*o*r)throw new RangeError("mismatched image size")}else n=new Uint8Array(i*o*r);return t.width=i,t.height=o,t.data=n,t}function Ny(t,e,r){var n=e.width,i=e.height;if(n!==t.width||i!==t.height){var o=Uy({},{width:n,height:i},r);Vy(t,o,{x:0,y:0},{x:0,y:0},{width:Math.min(t.width,n),height:Math.min(t.height,i)},r),t.width=n,t.height=i,t.data=o.data}}function Vy(t,e,r,n,i,o){if(0===i.width||0===i.height)return e;if(i.width>t.width||i.height>t.height||r.x>t.width-i.width||r.y>t.height-i.height)throw new RangeError("out of range source coordinates for image copy");if(i.width>e.width||i.height>e.height||n.x>e.width-i.width||n.y>e.height-i.height)throw new RangeError("out of range destination coordinates for image copy");for(var a=t.data,s=e.data,u=0;u<i.height;u++)for(var l=((r.y+u)*t.width+r.x)*o,c=((n.y+u)*e.width+n.x)*o,p=0;p<i.width*o;p++)s[c+p]=a[l+p];return e}Dy.prototype.resize=function(t){Ny(this,t,1)},Dy.prototype.clone=function(){return new Dy({width:this.width,height:this.height},new Uint8Array(this.data))},Dy.copy=function(t,e,r,n,i){Vy(t,e,r,n,i,1)},ri.register("AlphaImage",Dy);const Ly=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),$y=Ly(void 0,(function(){return $y.toString().search("(((.+)+)+)+$").toString().constructor($y).search("(((.+)+)+)+$")}));$y();const Xy=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),jy=Xy(void 0,(function(){return jy.toString().search("(((.+)+)+)+$").toString().constructor(jy).search("(((.+)+)+)+$")}));jy();const qy={horizontal:1,vertical:2,horizontalOnly:3};var Hy,Yy=(Hy=!0,function(t,e){var r=Hy?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Hy=!1,r}),Wy=Yy(void 0,(function(){return Wy.toString().search("(((.+)+)+)+$").toString().constructor(Wy).search("(((.+)+)+)+$")}));Wy();const Gy=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),Qy=Gy(void 0,(function(){return Qy.toString().search("(((.+)+)+)+$").toString().constructor(Qy).search("(((.+)+)+)+$")}));Qy();const Ky=128;function Jy(t,e){const{expression:r}=e;if("constant"===r.kind){return{kind:"constant",layoutSize:r.evaluate(new hi(t+1))}}if("source"===r.kind)return{kind:"source"};{const{zoomStops:e,interpolationType:n}=r;let i=0;for(;i<e.length&&e[i]<=t;)i++;i=Math.max(0,i-1);let o=i;for(;o<e.length&&e[o]<t+1;)o++;o=Math.min(e.length-1,o);const a=e[i],s=e[o];if("composite"===r.kind)return{kind:"composite",minZoom:a,maxZoom:s,interpolationType:n};return{kind:"camera",minZoom:a,maxZoom:s,minSize:r.evaluate(new hi(a)),maxSize:r.evaluate(new hi(s)),interpolationType:n}}}var Zy=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e,r,n,i){t.call(this,e,r),this.angle=n,void 0!==i&&(this.segment=i)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.clone=function(){return new i(this.x,this.y,this.angle,this.segment)},i}(zi);ri.register("Anchor",Zy);var td,ed=(td=!0,function(t,e){var r=td?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return td=!1,r}),rd=ed(void 0,(function(){return rd.toString().search("(((.+)+)+)+$").toString().constructor(rd).search("(((.+)+)+)+$")}));rd();var nd,id=(nd=!0,function(t,e){var r=nd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return nd=!1,r}),od=id(void 0,(function(){return od.toString().search("(((.+)+)+)+$").toString().constructor(od).search("(((.+)+)+)+$")}));od();var ad,sd=(ad=!0,function(t,e){var r=ad?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ad=!1,r}),ud=sd(void 0,(function(){return ud.toString().search("(((.+)+)+)+$").toString().constructor(ud).search("(((.+)+)+)+$")}));ud();var ld,cd=(ld=!0,function(t,e){var r=ld?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return ld=!1,r}),pd=cd(void 0,(function(){return pd.toString().search("(((.+)+)+)+$").toString().constructor(pd).search("(((.+)+)+)+$")}));pd();var fd,hd=(fd=!0,function(t,e){var r=fd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return fd=!1,r}),yd=hd(void 0,(function(){return yd.toString().search("(((.+)+)+)+$").toString().constructor(yd).search("(((.+)+)+)+$")}));yd();var dd,md=(dd=!0,function(t,e){var r=dd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return dd=!1,r}),gd=md(void 0,(function(){return gd.toString().search("(((.+)+)+)+$").toString().constructor(gd).search("(((.+)+)+)+$")}));gd();var vd,xd=(vd=!0,function(t,e){var r=vd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return vd=!1,r}),bd=xd(void 0,(function(){return bd.toString().search("(((.+)+)+)+$").toString().constructor(bd).search("(((.+)+)+)+$")}));bd();const _d=function(){let t=!0;return function(e,r){const n=t?function(){if(r){const t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,n}}(),wd=_d(void 0,(function(){return wd.toString().search("(((.+)+)+)+$").toString().constructor(wd).search("(((.+)+)+)+$")}));wd();const Sd=255,Ad=Sd*Ky;var Ed,Td=(Ed=!0,function(t,e){var r=Ed?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ed=!1,r}),Id=Td(void 0,(function(){return Id.toString().search("(((.+)+)+)+$").toString().constructor(Id).search("(((.+)+)+)+$")}));function Od(t,e,r){var n=e.layout.get("text-transform").evaluate(r,{});return"uppercase"===n?t=t.toLocaleUpperCase():"lowercase"===n&&(t=t.toLocaleLowerCase()),t}function kd(t,e,r){return t.sections.forEach((function(t){t.text=Od(t.text,e,r)})),t}Id();var Fd,Rd=(Fd=!0,function(t,e){var r=Fd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Fd=!1,r}),Md=Rd(void 0,(function(){return Md.toString().search("(((.+)+)+)+$").toString().constructor(Md).search("(((.+)+)+)+$")}));Md();var Cd=["Unknown","Point","LineString","Polygon"],Pd=[{name:"a_fade_opacity",components:1,type:"Uint8",offset:0}];function zd(t,e,r,n,i,o,a,s,u,l,c,p,f){var h=s?Math.min(Ad,Math.round(s[0])):0,y=s?Math.min(Ad,Math.round(s[1])):0;t.emplaceBack(e,r,Math.round(32*n),Math.round(32*i),o,a,(h<<1)+(u?1:0),y,16*l,16*c,256*p,256*f)}function Bd(t,e,r){t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r)}function Dd(t){for(var e=0,r=t.sections;e<r.length;e+=1){if(uy(r[e].text))return!0}return!1}var Ud=function(t){this.layoutVertexArray=new Li,this.indexArray=new Ji,this.programConfigurations=t,this.segments=new Ei,this.dynamicLayoutVertexArray=new $i,this.opacityVertexArray=new Xi,this.placedSymbolArray=new lo};Ud.prototype.upload=function(t,e,r,n){r&&(this.layoutVertexArray.length>0&&(this.layoutVertexBuffer=ma.toVertexBuffer(t,this.layoutVertexArray,Wh.members)),this.indexArray.length>0&&(this.indexBuffer=ma.toIndexBuffer(t,this.indexArray,e)),this.dynamicLayoutVertexArray.length>0&&(this.dynamicLayoutVertexBuffer=ma.toVertexBuffer(t,this.dynamicLayoutVertexArray,Gh.members,!0)),this.opacityVertexArray.length>0&&(this.opacityVertexBuffer=ma.toVertexBuffer(t,this.opacityVertexArray,Pd,!0),this.opacityVertexBuffer.itemSize=1)),(r||n)&&this.programConfigurations.upload(t)},Ud.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.layoutVertexBuffer=null),this.indexBuffer&&(this.indexBuffer.destroy(),this.indexBuffer=null),this.dynamicLayoutVertexBuffer&&(this.dynamicLayoutVertexBuffer.destroy(),this.dynamicLayoutVertexBuffer=null),this.opacityVertexBuffer&&(this.opacityVertexBuffer.destroy(),this.opacityVertexBuffer=null),this.programConfigurations.destroy(),this.segments.destroy()},Ud.prototype.clear=function(){},ri.register("SymbolBuffers",Ud);var Nd=function(t,e,r){this.layoutVertexArray=new t,this.layoutAttributes=e,this.indexArray=new r,this.segments=new Ei,this.collisionVertexArray=new Hi};Nd.prototype.upload=function(t){this.layoutVertexArray.length>0&&(this.layoutVertexBuffer=ma.toVertexBuffer(t,this.layoutVertexArray,this.layoutAttributes)),this.indexArray.length>0&&(this.indexBuffer=ma.toIndexBuffer(t,this.indexArray)),this.collisionVertexArray.length>0&&(this.collisionVertexBuffer=ma.toVertexBuffer(t,this.collisionVertexArray,Qh.members,!0))},Nd.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.layoutVertexBuffer=null),this.collisionVertexBuffer&&(this.collisionVertexBuffer.destroy(),this.collisionVertexBuffer=null),this.indexBuffer&&(this.indexBuffer.destroy(),this.indexBuffer=null),this.segments.destroy()},ri.register("CollisionBuffers",Nd);var Vd=function(t){this.collisionBoxArray=t.collisionBoxArray,this.zoom=t.zoom,this.overscaling=1,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.pixelRatio=t.pixelRatio,this.sourceLayerIndex=t.sourceLayerIndex,this.hasPattern=!1,this.hasPaintOverrides=!1,this.hasRTLText=!1;var e=this.layers[0]._unevaluatedLayout._values;this.textSizeData=Jy(this.zoom,e["text-size"]),this.iconSizeData=Jy(this.zoom,e["icon-size"]);var r=this.layers[0].layout,n=r.get("symbol-sort-key"),i=r.get("symbol-z-order");this.sortFeaturesByKey="viewport-y"!==i&&void 0!==n.constantOr(1);var o="viewport-y"===i||"auto"===i&&!this.sortFeaturesByKey;this.sortFeaturesByY=o&&(r.get("text-allow-overlap")||r.get("icon-allow-overlap")||r.get("text-ignore-placement")||r.get("icon-ignore-placement")),"point"===r.get("symbol-placement")&&(this.writingModes=r.get("text-writing-mode").map((function(t){return qy[t]}))),this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id})),this.sourceID=t.sourceID,this.overscaling=1};Vd.prototype.createArrays=function(){const t=this.layers[0].layout;this.hasPaintOverrides=Jd.hasPaintOverrides(t);var e=Wh.members;this.text=new Ud(new za(e,this.layers,this.zoom,(t=>/^text/.test(t)))),this.icon=new Ud(new za(e,this.layers,this.zoom,(t=>/^icon/.test(t)))),this.textCollisionBox=new Nd(qi,Kh.members,Zi),this.iconCollisionBox=new Nd(qi,Kh.members,Zi),this.textCollisionCircle=new Nd(qi,Jh.members,Ji),this.iconCollisionCircle=new Nd(qi,Jh.members,Ji),this.glyphOffsetArray=new ho,this.lineVertexArray=new mo,this.symbolInstances=new po},Vd.prototype.calculateGlyphDependencies=function(t,e,r,n,i){for(let o=0;o<t.length;o++)if(e[t.charCodeAt(o)]=!0,(r||n)&&i){const r=py[t.charAt(o)];r&&(e[r.charCodeAt(0)]=!0)}},Vd.prototype.populate=function(t,e){var r=this.layers[0],n=r.layout,i=n.get("text-font"),o=n.get("text-field"),a=n.get("icon-image"),s=("constant"!==o.value.kind||o.value.value instanceof ne&&!o.value.value.isEmpty()||o.value.value.toString().length>0)&&("constant"!==i.value.kind||i.value.value.length>0),u=("constant"!==a.value.kind||!!a.value.value)&&Object.keys(a.parameters).length>0,l=n.get("symbol-sort-key");if(this.features=[],s||u){for(var c=e.iconDependencies,p=e.glyphDependencies,f=e.availableImages,h=new hi(this.zoom),y=0,d=t;y<d.length;y+=1){var m=d[y],g=m.feature,v=m.index,x=m.sourceLayerIndex;if(r._featureFilter(h,g)){var b=void 0;if(s){var _=r.getValueAndResolveTokens("text-field",g,f),w=ne.factory(_);Dd(w)&&(this.hasRTLText=!0),(!this.hasRTLText||"unavailable"===getRTLTextPluginStatus()||this.hasRTLText&&plugin.isParsed())&&(b=kd(w,r,g))}var S=void 0;if(u){var A=r.getValueAndResolveTokens("icon-image",g,f);S=A instanceof se?A:se.fromString(A)}if(b||S){var E=this.sortFeaturesByKey?l.evaluate(g,{}):void 0,T={text:b,icon:S,index:v,sourceLayerIndex:x,geometry:_i(g),properties:g.properties,type:Cd[g.type],sortKey:E};if(void 0!==g.id&&(T.id=g.id),this.features.push(T),S&&(c[S.name]=!0),b){var I=i.evaluate(g,{}).join(","),O="map"===n.get("text-rotation-alignment")&&"point"!==n.get("symbol-placement");this.allowVerticalPlacement=this.writingModes&&this.writingModes.indexOf(qy.vertical)>=0;for(var k=0,F=b.sections;k<F.length;k+=1){var R=F[k];if(R.image)c[R.image.name]=!0;else{var M=oy(b.toString()),C=R.fontStack||I,P=p[C]=p[C]||{};this.calculateGlyphDependencies(R.text,P,O,this.allowVerticalPlacement,M)}}}}}}"line"===n.get("symbol-placement")&&(this.features=vy(this.features)),this.sortFeaturesByKey&&this.features.sort((function(t,e){return t.sortKey-e.sortKey}))}},Vd.prototype.update=function(t,e,r){this.stateDependentLayers.length&&(this.text.programConfigurations.updatePaintArrays(t,e,this.layers,r),this.icon.programConfigurations.updatePaintArrays(t,e,this.layers,r))},Vd.prototype.isEmpty=function(){return 0===this.symbolInstances.length&&!this.hasRTLText},Vd.prototype.uploadPending=function(){return!this.uploaded||this.text.programConfigurations.needsUpload||this.icon.programConfigurations.needsUpload},Vd.prototype.upload=function(t){this.text&&(!this.uploaded&&(this.textCollisionBox.upload(t),this.iconCollisionBox.upload(t),this.textCollisionCircle.upload(t),this.iconCollisionCircle.upload(t)),this.text.upload(t,this.sortFeaturesByY,!this.uploaded,this.text.programConfigurations.needsUpload),this.icon.upload(t,this.sortFeaturesByY,!this.uploaded,this.icon.programConfigurations.needsUpload),this.uploaded=!0)},Vd.prototype.destroy=function(){this.text&&(this.text.destroy(),this.icon.destroy(),this.textCollisionBox.destroy(),this.iconCollisionBox.destroy(),this.textCollisionCircle.destroy(),this.iconCollisionCircle.destroy())},Vd.prototype.clear=function(){},Vd.prototype.addToLineVertexArray=function(t,e){var r=this.lineVertexArray.length;if(void 0!==t.segment){for(var n=t.dist(e[t.segment+1]),i=t.dist(e[t.segment]),o={},a=t.segment+1;a<e.length;a++)o[a]={x:e[a].x,y:e[a].y,tileUnitDistanceFromAnchor:n},a<e.length-1&&(n+=e[a+1].dist(e[a]));for(var s=t.segment||0;s>=0;s--)o[s]={x:e[s].x,y:e[s].y,tileUnitDistanceFromAnchor:i},s>0&&(i+=e[s-1].dist(e[s]));for(var u=0;u<e.length;u++){var l=o[u];this.lineVertexArray.emplaceBack(l.x,l.y,l.tileUnitDistanceFromAnchor)}}return{lineStartIndex:r,lineLength:this.lineVertexArray.length-r}},Vd.prototype.addSymbols=function(t,e,r,n,i,o,a,s,u,l,c){var p=this,f=t.indexArray,h=t.layoutVertexArray,y=t.dynamicLayoutVertexArray,d=t.segments.prepareSegment(4*e.length,t.layoutVertexArray,t.indexArray,o.sortKey),m=this.glyphOffsetArray.length,g=d.vertexLength,v=this.allowVerticalPlacement&&a===qy.vertical?Math.PI/2:0,x=function(t){var e=t.tl,n=t.tr,i=t.bl,o=t.br,a=t.tex,u=t.pixelOffsetTL,l=t.pixelOffsetBR,c=t.minFontScaleX,m=t.minFontScaleY,g=d.vertexLength,x=t.glyphOffset[1];zd(h,s.x,s.y,e.x,x+e.y,a.x,a.y+a.h,r,t.isSDF,u.x,u.y,c,m),zd(h,s.x,s.y,n.x,x+n.y,a.x+a.w,a.y+a.h,r,t.isSDF,l.x,u.y,c,m),zd(h,s.x,s.y,i.x,x+i.y,a.x,a.y,r,t.isSDF,u.x,l.y,c,m),zd(h,s.x,s.y,o.x,x+o.y,a.x+a.w,a.y,r,t.isSDF,l.x,l.y,c,m),Bd(y,s,v),f.emplaceBack(g,g+1,g+2),f.emplaceBack(g+1,g+2,g+3),d.vertexLength+=4,d.primitiveLength+=2,p.glyphOffsetArray.emplaceBack(t.glyphOffset[0])};if(o.text&&o.text.sections){var b=o.text.sections;if(this.hasPaintOverrides){for(var _,w=function(e,r){void 0!==_&&(_!==e||r)&&t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,o,o.index,{},b[_]),_=e},S=0,A=e;S<A.length;S+=1){var E=A[S];w(E.sectionIndex,!1),x(E)}w(_,!0)}else{for(var T=0,I=e;T<I.length;T+=1){x(I[T])}t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,o,o.index,{},b[0])}}else{for(var O=0,k=e;O<k.length;O+=1){x(k[O])}t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,o,o.index,{})}t.placedSymbolArray.emplaceBack(s.x,s.y,m,this.glyphOffsetArray.length-m,g,u,l,s.segment,r?r[0]:0,r?r[1]:0,n[0],n[1],a,0,!1,0,c)},Vd.prototype._addCollisionDebugVertex=function(t,e,r,n,i,o){return e.emplaceBack(0,0),t.emplaceBack(r.x,r.y,n,i,Math.round(o.x),Math.round(o.y))},Vd.prototype.addCollisionDebugVertices=function(t,e,r,n,i,o,a,s){var u=i.segments.prepareSegment(4,i.layoutVertexArray,i.indexArray),l=u.vertexLength,c=i.layoutVertexArray,p=i.collisionVertexArray,f=a.anchorX,h=a.anchorY;if(this._addCollisionDebugVertex(c,p,o,f,h,new pointGeometry(t,e)),this._addCollisionDebugVertex(c,p,o,f,h,new pointGeometry(r,e)),this._addCollisionDebugVertex(c,p,o,f,h,new pointGeometry(r,n)),this._addCollisionDebugVertex(c,p,o,f,h,new pointGeometry(t,n)),u.vertexLength+=4,s){var y=i.indexArray;y.emplaceBack(l,l+1,l+2),y.emplaceBack(l,l+2,l+3),u.primitiveLength+=2}else{var d=i.indexArray;d.emplaceBack(l,l+1),d.emplaceBack(l+1,l+2),d.emplaceBack(l+2,l+3),d.emplaceBack(l+3,l),u.primitiveLength+=4}},Vd.prototype.addDebugCollisionBoxes=function(t,e,r,n){for(var i=t;i<e;i++){var o=this.collisionBoxArray.get(i),a=o.x1,s=o.y1,u=o.x2,l=o.y2,c=o.radius>0;this.addCollisionDebugVertices(a,s,u,l,c?n?this.textCollisionCircle:this.iconCollisionCircle:n?this.textCollisionBox:this.iconCollisionBox,o.anchorPoint,r,c)}},Vd.prototype.generateCollisionDebugBuffers=function(){for(var t=0;t<this.symbolInstances.length;t++){var e=this.symbolInstances.get(t);this.addDebugCollisionBoxes(e.textBoxStartIndex,e.textBoxEndIndex,e,!0),this.addDebugCollisionBoxes(e.verticalTextBoxStartIndex,e.verticalTextBoxEndIndex,e,!0),this.addDebugCollisionBoxes(e.iconBoxStartIndex,e.iconBoxEndIndex,e,!1),this.addDebugCollisionBoxes(e.verticalIconBoxStartIndex,e.verticalIconBoxEndIndex,e,!1)}},Vd.prototype._deserializeCollisionBoxesForSymbol=function(t,e,r,n,i,o,a,s,u){for(var l={},c=e;c<r;c++){var p=t.get(c);if(0===p.radius){l.textBox={x1:p.x1,y1:p.y1,x2:p.x2,y2:p.y2,anchorPointX:p.anchorPointX,anchorPointY:p.anchorPointY},l.textFeatureIndex=p.featureIndex;break}!l.textCircles&&(l.textCircles=[],l.textFeatureIndex=p.featureIndex);l.textCircles.push(p.anchorPointX,p.anchorPointY,p.radius,p.signedDistanceFromAnchor,1)}for(var f=n;f<i;f++){var h=t.get(f);if(0===h.radius){l.verticalTextBox={x1:h.x1,y1:h.y1,x2:h.x2,y2:h.y2,anchorPointX:h.anchorPointX,anchorPointY:h.anchorPointY},l.verticalTextFeatureIndex=h.featureIndex;break}}for(var y=o;y<a;y++){var d=t.get(y);if(0===d.radius){l.iconBox={x1:d.x1,y1:d.y1,x2:d.x2,y2:d.y2,anchorPointX:d.anchorPointX,anchorPointY:d.anchorPointY},l.iconFeatureIndex=d.featureIndex;break}}for(var m=s;m<u;m++){var g=t.get(m);if(0===g.radius){l.verticalIconBox={x1:g.x1,y1:g.y1,x2:g.x2,y2:g.y2,anchorPointX:g.anchorPointX,anchorPointY:g.anchorPointY},l.verticalIconFeatureIndex=g.featureIndex;break}}return l},Vd.prototype.deserializeCollisionBoxes=function(t){this.collisionArrays=[];for(var e=0;e<this.symbolInstances.length;e++){var r=this.symbolInstances.get(e);this.collisionArrays.push(this._deserializeCollisionBoxesForSymbol(t,r.textBoxStartIndex,r.textBoxEndIndex,r.verticalTextBoxStartIndex,r.verticalTextBoxEndIndex,r.iconBoxStartIndex,r.iconBoxEndIndex,r.verticalIconBoxStartIndex,r.verticalIconBoxEndIndex))}},Vd.prototype.hasTextData=function(){return this.text.segments.get().length>0},Vd.prototype.hasIconData=function(){return this.icon.segments.get().length>0},Vd.prototype.hasTextCollisionBoxData=function(){return this.textCollisionBox.segments.get().length>0},Vd.prototype.hasIconCollisionBoxData=function(){return this.iconCollisionBox.segments.get().length>0},Vd.prototype.hasTextCollisionCircleData=function(){return this.textCollisionCircle.segments.get().length>0},Vd.prototype.hasIconCollisionCircleData=function(){return this.iconCollisionCircle.segments.get().length>0},Vd.prototype.addIndicesForPlacedSymbol=function(t,e){for(var r=t.placedSymbolArray.get(e),n=r.vertexStartIndex+4*r.numGlyphs,i=r.vertexStartIndex;i<n;i+=4)t.indexArray.emplaceBack(i,i+1,i+2),t.indexArray.emplaceBack(i+1,i+2,i+3)},Vd.prototype.getSortedSymbolIndexes=function(t){if(this.sortedAngle===t&&void 0!==this.symbolInstanceIndexes)return this.symbolInstanceIndexes;for(var e=Math.sin(t),r=Math.cos(t),n=[],i=[],o=[],a=0;a<this.symbolInstances.length;++a){o.push(a);var s=this.symbolInstances.get(a);n.push(0|Math.round(e*s.anchorX+r*s.anchorY)),i.push(s.featureIndex)}return o.sort((function(t,e){return n[t]-n[e]||i[e]-i[t]})),o},Vd.prototype.sortFeatures=function(t){var e=this;if(this.sortFeaturesByY&&this.sortedAngle!==t&&!(this.text.segments.get().length>1||this.icon.segments.get().length>1)){this.symbolInstanceIndexes=this.getSortedSymbolIndexes(t),this.sortedAngle=t,this.text.indexArray.clear(),this.icon.indexArray.clear(),this.featureSortOrder=[];for(var r=0,n=this.symbolInstanceIndexes;r<n.length;r+=1){var i=n[r],o=this.symbolInstances.get(i);this.featureSortOrder.push(o.featureIndex),[o.rightJustifiedTextSymbolIndex,o.centerJustifiedTextSymbolIndex,o.leftJustifiedTextSymbolIndex].forEach((function(t,r,n){t>=0&&n.indexOf(t)===r&&e.addIndicesForPlacedSymbol(e.text,t)})),o.verticalPlacedTextSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.text,o.verticalPlacedTextSymbolIndex),o.placedIconSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.icon,o.placedIconSymbolIndex),o.verticalPlacedIconSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.icon,o.verticalPlacedIconSymbolIndex)}this.text.indexBuffer&&this.text.indexBuffer.updateData(this.text.indexArray),this.icon.indexBuffer&&this.icon.indexBuffer.updateData(this.icon.indexArray)}},ri.register("SymbolBucket",Vd,{omit:["layers","collisionBoxArray","compareText"]}),Vd.MAX_GLYPHS=65535,Vd.addDynamicAttributes=Bd;var Ld,$d=(Ld=!0,function(t,e){var r=Ld?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return Ld=!1,r}),Xd=$d(void 0,(function(){return Xd.toString().search("(((.+)+)+)+$").toString().constructor(Xd).search("(((.+)+)+)+$")}));function jd(t,e){return e.replace(/{([^{}]+)}/g,((e,r)=>r in t?String(t[r]):""))}Xd();var qd,Hd={kind:"color"},Yd={kind:"formatted"},Wd=new Qp({"symbol-placement":new Op(lh.layout_symbol["symbol-placement"]),"symbol-spacing":new Op(lh.layout_symbol["symbol-spacing"]),"symbol-avoid-edges":new Op(lh.layout_symbol["symbol-avoid-edges"]),"symbol-sort-key":new Mp(lh.layout_symbol["symbol-sort-key"]),"symbol-z-order":new Op(lh.layout_symbol["symbol-z-order"]),"icon-allow-overlap":new Op(lh.layout_symbol["icon-allow-overlap"]),"icon-ignore-placement":new Op(lh.layout_symbol["icon-ignore-placement"]),"icon-optional":new Op(lh.layout_symbol["icon-optional"]),"icon-rotation-alignment":new Op(lh.layout_symbol["icon-rotation-alignment"]),"icon-size":new Mp(lh.layout_symbol["icon-size"]),"icon-text-fit":new Op(lh.layout_symbol["icon-text-fit"]),"icon-text-fit-padding":new Op(lh.layout_symbol["icon-text-fit-padding"]),"icon-image":new Mp(lh.layout_symbol["icon-image"]),"icon-rotate":new Mp(lh.layout_symbol["icon-rotate"]),"icon-padding":new Op(lh.layout_symbol["icon-padding"]),"icon-keep-upright":new Op(lh.layout_symbol["icon-keep-upright"]),"icon-offset":new Mp(lh.layout_symbol["icon-offset"]),"icon-anchor":new Mp(lh.layout_symbol["icon-anchor"]),"icon-pitch-alignment":new Op(lh.layout_symbol["icon-pitch-alignment"]),"text-pitch-alignment":new Op(lh.layout_symbol["text-pitch-alignment"]),"text-rotation-alignment":new Op(lh.layout_symbol["text-rotation-alignment"]),"text-field":new Mp(lh.layout_symbol["text-field"]),"text-font":new Mp(lh.layout_symbol["text-font"]),"text-size":new Mp(lh.layout_symbol["text-size"]),"text-max-width":new Mp(lh.layout_symbol["text-max-width"]),"text-line-height":new Op(lh.layout_symbol["text-line-height"]),"text-letter-spacing":new Mp(lh.layout_symbol["text-letter-spacing"]),"text-justify":new Mp(lh.layout_symbol["text-justify"]),"text-radial-offset":new Mp(lh.layout_symbol["text-radial-offset"]),"text-variable-anchor":new Op(lh.layout_symbol["text-variable-anchor"]),"text-anchor":new Mp(lh.layout_symbol["text-anchor"]),"text-max-angle":new Op(lh.layout_symbol["text-max-angle"]),"text-writing-mode":new Op(lh.layout_symbol["text-writing-mode"]),"text-rotate":new Mp(lh.layout_symbol["text-rotate"]),"text-padding":new Op(lh.layout_symbol["text-padding"]),"text-keep-upright":new Op(lh.layout_symbol["text-keep-upright"]),"text-transform":new Mp(lh.layout_symbol["text-transform"]),"text-offset":new Mp(lh.layout_symbol["text-offset"]),"text-allow-overlap":new Op(lh.layout_symbol["text-allow-overlap"]),"text-ignore-placement":new Op(lh.layout_symbol["text-ignore-placement"]),"text-optional":new Op(lh.layout_symbol["text-optional"])}),Gd=new Qp({"icon-opacity":new Mp(lh.paint_symbol["icon-opacity"]),"icon-color":new Mp(lh.paint_symbol["icon-color"]),"icon-halo-color":new Mp(lh.paint_symbol["icon-halo-color"]),"icon-halo-width":new Mp(lh.paint_symbol["icon-halo-width"]),"icon-halo-blur":new Mp(lh.paint_symbol["icon-halo-blur"]),"icon-translate":new Op(lh.paint_symbol["icon-translate"]),"icon-translate-anchor":new Op(lh.paint_symbol["icon-translate-anchor"]),"text-opacity":new Mp(lh.paint_symbol["text-opacity"]),"text-color":new Mp(lh.paint_symbol["text-color"],{runtimeType:Hd,getOverride:function(t){return t.textColor},hasOverride:function(t){return!!t.textColor}}),"text-halo-color":new Mp(lh.paint_symbol["text-halo-color"]),"text-halo-width":new Mp(lh.paint_symbol["text-halo-width"]),"text-halo-blur":new Mp(lh.paint_symbol["text-halo-blur"]),"text-show-background":new Mp(lh.paint_symbol["text-show-background"]),"text-translate":new Op(lh.paint_symbol["text-translate"]),"text-translate-anchor":new Op(lh.paint_symbol["text-translate-anchor"])}),Qd={paint:Gd,layout:Wd},Kd=function(t){var e,r=(e=!0,function(t,r){var n=e?function(){if(r){var e=r.apply(t,arguments);return r=null,e}}:function(){};return e=!1,n}),n=r(this,(function(){return n.toString().search("(((.+)+)+)+$").toString().constructor(n).search("(((.+)+)+)+$")}));function i(e){t.call(this,e,Qd)}return n(),t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.prototype.recalculate=function(e,r){if(t.prototype.recalculate.call(this,e,r),"auto"===this.layout.get("icon-rotation-alignment")&&("point"!==this.layout.get("symbol-placement")?this.layout._values["icon-rotation-alignment"]="map":this.layout._values["icon-rotation-alignment"]="viewport"),"auto"===this.layout.get("text-rotation-alignment")&&("point"!==this.layout.get("symbol-placement")?this.layout._values["text-rotation-alignment"]="map":this.layout._values["text-rotation-alignment"]="viewport"),"auto"===this.layout.get("text-pitch-alignment")&&(this.layout._values["text-pitch-alignment"]=this.layout.get("text-rotation-alignment")),"auto"===this.layout.get("icon-pitch-alignment")&&(this.layout._values["icon-pitch-alignment"]=this.layout.get("icon-rotation-alignment")),"point"===this.layout.get("symbol-placement")){var n=this.layout.get("text-writing-mode");if(n){for(var i=[],o=0,a=n;o<a.length;o+=1){var s=a[o];i.indexOf(s)<0&&i.push(s)}this.layout._values["text-writing-mode"]=i}else this.layout._values["text-writing-mode"]=["horizontal"]}this._setPaintOverrides()},i.prototype.getValueAndResolveTokens=function(t,e,r){var n=this.layout.get(t).evaluate(e,{},r),i=this._unevaluatedLayout._values[t];return i.isDataDriven()||gc.isExpression(i.value)||!n?n:jd(e.properties,n)},i.prototype.createBucket=function(t){return new Vd(t)},i.prototype.queryRadius=function(){return 0},i.prototype.queryIntersectsFeature=function(){return!1},i.prototype._setPaintOverrides=function(){for(var t=0,e=Qd.paint.overridableProperties;t<e.length;t+=1){var r=e[t];if(i.hasPaintOverride(this.layout,r)){var n=this.paint.get(r);new FormatSectionOverride(n);"constant"===n.value.kind||n.value.kind,this.paint._values[r]=new PossiblyEvaluatedPropertyValue(n.property,null,n.parameters)}}},i.prototype._handleOverridablePaintPropertyUpdate=function(t,e,r){return!(!this.layout||e.isDataDriven()||r.isDataDriven())&&i.hasPaintOverride(this.layout,t)},i.hasPaintOverride=function(t,e){var r=t.get("text-field"),n=Qd.paint.properties[e],i=!1,o=function(t){for(var e=0,r=t;e<r.length;e+=1){var o=r[e];if(n.overrides&&n.overrides.hasOverride(o))return void(i=!0)}};if("constant"===r.value.kind&&r.value.value instanceof ne)o(r.value.value.sections);else if("source"===r.value.kind){var a=function(t){if(!i)if(t instanceof An&&we.typeOf(t.value)===Yd){var e=t.value;o(e.sections)}else t instanceof $r?o(t.sections):t.eachChild(a)},s=r.value;s._styleExpression&&a(s._styleExpression.expression)}return i},i.hasPaintOverrides=function(t){for(var e=0,r=Qd.paint.overridableProperties;e<r.length;e+=1){var n=r[e];if(i.hasPaintOverride(t,n))return!0}return!1},i}(_f),Jd=Kd,Zd=(qd=!0,function(t,e){var r=qd?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return qd=!1,r}),tm=Zd(void 0,(function(){return tm.toString().search("(((.+)+)+)+$").toString().constructor(tm).search("(((.+)+)+)+$")}));tm();var em={circle:wh,fill:Mh,line:qh,symbol:Jd};function rm(t){return em[t.type]?new em[t.type](t):null}var nm,im=(nm=!0,function(t,e){var r=nm?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return nm=!1,r}),om=im(void 0,(function(){return om.toString().search("(((.+)+)+)+$").toString().constructor(om).search("(((.+)+)+)+$")}));om();var am=function(t){this.keyCache={},t&&this.replace(t)};function sm(t){var e=[];for(var r in t)e.push(t[r]);return e}function um(t,r){var n=t.pbfData,i=t.layers,o=t.imageMap,a=ri.deserialize(t.serializeObj).featureIndex,s=t.tileID.z;e.t(a)||((a=new op(t.tileID)).bucketLayerIDs=[]);var u={};try{var l=new am(i);u=cm(lm(n),l,o,a,s,t.indexData),u=ri.serialize(u,r)}catch(t){}return u.pickId=t.pickId,u}function lm(t){if(e.t(t))return new fl(new b(t))}function cm(t,r,n,i,o,a){var s=new Y(Object.keys(t.layers).sort()),u={},l={featureIndex:i,iconDependencies:{},patternDependencies:{},glyphDependencies:{}};for(var c in r.familiesBySource){var p=r.familiesBySource[c];for(var f in p){var h=t.layers[f];if(h){for(var y=s.encode(f),d=[],m=0;m<h.length;m++){var g=h.feature(m);d.push({feature:g,index:m,sourceLayerIndex:y,sourceLayerId:f})}for(var v=0,x=p[f];v<x.length;v+=1){var b=x[v],_=b[0];if("none"!==_.visibility){pm(b,0,null);var w=u[_.id]=_.createBucket({index:i.bucketLayerIDs.length,layers:b,sourceLayerIndex:y});e.t(a)&&e.t(a[_.id])&&(l.indexData=a[_.id]),w.populate(d,l),i.bucketLayerIDs.push(b.map((function(t){return t.id})))}}}}}var S=null;for(var A in u){(w=u[A]).hasPattern&&(w instanceof rs||w instanceof Wu)&&(null==S&&(S=new xt({},n)),w.addFeatures(l,S.patternPositions))}return{buckets:u,imageAtlas:S,featureIndex:i}}function pm(t,e,r){for(var n=new hi(e),i=0,o=t;i<o.length;i+=1){o[i].recalculate(n,r)}}am.prototype.replace=function(t){this._layerConfigs={},this._layers={},this.update(t,[])},am.prototype.update=function(t,e){for(var r=this,n=0,i=t;n<i.length;n+=1){var o=i[n];this._layerConfigs[o.id]=o;var a=rm(o);null!=a&&(this._layers[o.id]=a,a._featureFilter=Yc.createFilter(a.filter),this.keyCache[o.id]&&delete this.keyCache[o.id])}for(var s=0,u=e;s<u.length;s+=1){var l=u[s];delete this.keyCache[l],delete this._layerConfigs[l],delete this._layers[l]}this.familiesBySource={};for(var c=0,p=dp(sm(this._layerConfigs),this.keyCache);c<p.length;c+=1){var f=p[c].map((function(t){return r._layers[t.id]})),h=f[0];if(null!=h&&"none"!==h.visibility){var y=h.source||"",d=this.familiesBySource[y];!d&&(d=this.familiesBySource[y]={});var m=h.sourceLayer,g=d[m];!g&&(g=d[m]=[]),g.push(f)}}},ri.register("FeatureIndex",op,{omit:["rawTileData","sourceLayerCoder"]});var fm=t(um);return fm}));
