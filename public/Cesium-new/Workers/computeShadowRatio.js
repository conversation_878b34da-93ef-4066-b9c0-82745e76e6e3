define(["./Cartographic-1bbcab04","./Cartesian4-034d54d5","./when-515d5295","./Math-5e38123d","./createTaskProcessorWorker","./Check-3aa71481"],(function(r,e,a,t,n,o){"use strict";var i=new e.a(1,1/255,1/65025,1/160581375),s=new e.a,f=1024;function u(a,n,o,u,d,c,h){var v=a.longitude,g=a.latitude,l=a.height;if(v=t.n.toDegrees(v),g=t.n.toDegrees(g),v<n[0]||v>n[2]||g<n[1]||g>n[3])return-1;for(var b=!1,p=0,k=.1*u,m=0;m<=o;m+=u){if(Math.abs(d+m-l)<k){b=!0;break}p++}if(!b)return-1;if(c.length<0)return-1;b=!1;for(var w=0;w<c.length;w+=2){var D=r.a.fromDegrees(v,g,l),x=r.a.fromDegrees(c[w+0],c[w+1],l);if(r.a.distance(D,x)<k){b=!0;break}}if(!b)return-1;var C=n[2]-n[0],y=(l=n[3]-n[1],n[0]-.025*C),M=n[1]-.025*l;C+=.05*C,l+=.05*l;var I=parseInt((v-y)/C*f),P=parseInt((g-M)/l*f);I=I<1?1:I,P=P<1?1:P;var z=h[p],A=0;for(w=-1;w<2;w++)for(var B=-1;B<2;B++){var R=4*(f*(P+B)+(I+w));s.x=z[R],s.y=z[R+1],s.z=z[R+2],s.w=z[R+3],e.a.divideByScalar(s,255,s),A=Math.max(A,e.a.dot(s,i))}return A=A>.999?1:A}return n((function(e,a){for(var t=e.points,n=e.enuPoints,o=e.bounds,i=e.extend,s=e.spacing,f=e.bottom,d=e.pixelsArray,c=[],h=0,v=t.length;h<v;h++){var g=t[h],l=u(r.i.fromCartesian(g),o,i,s,f,n,d);c.push({position:r.a.clone(g),shadowRatio:l})}return{resultData:c}}))}));
