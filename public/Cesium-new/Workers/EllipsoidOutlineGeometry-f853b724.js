define(["exports","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./when-515d5295","./Check-3aa71481","./Rectangle-e170be8b","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004"],(function(i,t,e,a,n,o,r,s,m,u,f,c,d,l){"use strict";var _=new a.a(1,1,1),h=Math.cos,p=Math.sin;function b(i){i=o.e(i,o.e.EMPTY_OBJECT);var t=o.e(i.radii,_),e=o.e(i.innerRadii,t),n=o.e(i.minimumClock,0),s=o.e(i.maximumClock,d.n.TWO_PI),m=o.e(i.minimumCone,0),u=o.e(i.maximumCone,d.n.PI),c=Math.round(o.e(i.stackPartitions,10)),l=Math.round(o.e(i.slicePartitions,8)),h=Math.round(o.e(i.subdivisions,128));if(c<1)throw new r.t("options.stackPartitions cannot be less than 1");if(l<0)throw new r.t("options.slicePartitions cannot be less than 0");if(h<0)throw new r.t("options.subdivisions must be greater than or equal to zero.");if(o.t(i.offsetAttribute)&&i.offsetAttribute===f.I.TOP)throw new r.t("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._radii=a.a.clone(t),this._innerRadii=a.a.clone(e),this._minimumClock=n,this._maximumClock=s,this._minimumCone=m,this._maximumCone=u,this._stackPartitions=c,this._slicePartitions=l,this._subdivisions=h,this._offsetAttribute=i.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}b.packedLength=2*a.a.packedLength+8,b.pack=function(i,t,e){if(!o.t(i))throw new r.t("value is required");if(!o.t(t))throw new r.t("array is required");return e=o.e(e,0),a.a.pack(i._radii,t,e),e+=a.a.packedLength,a.a.pack(i._innerRadii,t,e),e+=a.a.packedLength,t[e++]=i._minimumClock,t[e++]=i._maximumClock,t[e++]=i._minimumCone,t[e++]=i._maximumCone,t[e++]=i._stackPartitions,t[e++]=i._slicePartitions,t[e++]=i._subdivisions,t[e]=o.e(i._offsetAttribute,-1),t};var v=new a.a,k=new a.a,y={radii:v,innerRadii:k,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};b.unpack=function(i,t,e){if(!o.t(i))throw new r.t("array is required");t=o.e(t,0);var n=a.a.unpack(i,t,v);t+=a.a.packedLength;var s=a.a.unpack(i,t,k);t+=a.a.packedLength;var m=i[t++],u=i[t++],f=i[t++],c=i[t++],d=i[t++],l=i[t++],_=i[t++],h=i[t];return o.t(e)?(e._radii=a.a.clone(n,e._radii),e._innerRadii=a.a.clone(s,e._innerRadii),e._minimumClock=m,e._maximumClock=u,e._minimumCone=f,e._maximumCone=c,e._stackPartitions=d,e._slicePartitions=l,e._subdivisions=_,e._offsetAttribute=-1===h?void 0:h,e):(y.minimumClock=m,y.maximumClock=u,y.minimumCone=f,y.maximumCone=c,y.stackPartitions=d,y.slicePartitions=l,y.subdivisions=_,y.offsetAttribute=-1===h?void 0:h,new b(y))},b.createGeometry=function(i){var a=i._radii;if(!(a.x<=0||a.y<=0||a.z<=0)){var r=i._innerRadii;if(!(r.x<=0||r.y<=0||r.z<=0)){var _=i._minimumClock,b=i._maximumClock,v=i._minimumCone,k=i._maximumCone,y=i._subdivisions,C=s.n.fromCartesian3(a),w=i._slicePartitions+1,P=i._stackPartitions+1;(w=Math.round(w*Math.abs(b-_)/d.n.TWO_PI))<2&&(w=2),(P=Math.round(P*Math.abs(k-v)/d.n.PI))<2&&(P=2);var x=0,A=1,g=r.x!==a.x||r.y!==a.y||r.z!==a.z,M=!1,I=!1;g&&(A=2,v>0&&(M=!0,x+=w),k<Math.PI&&(I=!0,x+=w));var O,T,z,D,E=y*A*(P+w),L=new Float64Array(3*E),R=2*(E+x-(w+P)*A),G=c.IndexDatatype.createTypedArray(E,R),N=0,q=new Array(P),U=new Array(P);for(O=0;O<P;O++)D=v+O*(k-v)/(P-1),q[O]=p(D),U[O]=h(D);var B=new Array(y),S=new Array(y);for(O=0;O<y;O++)z=_+O*(b-_)/(y-1),B[O]=p(z),S[O]=h(z);for(O=0;O<P;O++)for(T=0;T<y;T++)L[N++]=a.x*q[O]*S[T],L[N++]=a.y*q[O]*B[T],L[N++]=a.z*U[O];if(g)for(O=0;O<P;O++)for(T=0;T<y;T++)L[N++]=r.x*q[O]*S[T],L[N++]=r.y*q[O]*B[T],L[N++]=r.z*U[O];for(q.length=y,U.length=y,O=0;O<y;O++)D=v+O*(k-v)/(y-1),q[O]=p(D),U[O]=h(D);for(B.length=w,S.length=w,O=0;O<w;O++)z=_+O*(b-_)/(w-1),B[O]=p(z),S[O]=h(z);for(O=0;O<y;O++)for(T=0;T<w;T++)L[N++]=a.x*q[O]*S[T],L[N++]=a.y*q[O]*B[T],L[N++]=a.z*U[O];if(g)for(O=0;O<y;O++)for(T=0;T<w;T++)L[N++]=r.x*q[O]*S[T],L[N++]=r.y*q[O]*B[T],L[N++]=r.z*U[O];for(N=0,O=0;O<P*A;O++){var F=O*y;for(T=0;T<y-1;T++)G[N++]=F+T,G[N++]=F+T+1}var W=P*y*A;for(O=0;O<w;O++)for(T=0;T<y-1;T++)G[N++]=W+O+T*w,G[N++]=W+O+(T+1)*w;if(g)for(W=P*y*A+w*y,O=0;O<w;O++)for(T=0;T<y-1;T++)G[N++]=W+O+T*w,G[N++]=W+O+(T+1)*w;if(g){var Y=P*y*A,J=Y+y*w;if(M)for(O=0;O<w;O++)G[N++]=Y+O,G[N++]=J+O;if(I)for(Y+=y*w-w,J+=y*w-w,O=0;O<w;O++)G[N++]=Y+O,G[N++]=J+O}var j=new u.t({position:new m.r({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:L})});if(o.t(i._offsetAttribute)){var H=L.length,K=new Uint8Array(H/3),Q=i._offsetAttribute===f.I.NONE?0:1;t.d(K,Q),j.applyOffset=new m.r({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:K})}return new m.T({attributes:j,indices:G,primitiveType:l._0x38df4a.LINES,boundingSphere:e.c.fromEllipsoid(C),offsetAttribute:i._offsetAttribute})}}},i.I=b}));
