define(["require","./createTaskProcessorWorker","./Check-3aa71481","./when-515d5295","./FeatureDetection-7fae0d5a","./PrimitiveType-b38a4004","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Cartesian4-034d54d5","./Color-39e7bd91","./ComponentDatatype-d430c7f7","./getStringFromTypedArray-53c2705d","./buildModuleUrl-dba4ec07","./S3MCompressType-d847073b","./IndexDatatype-eefd5922","./RuntimeError-350acae3","./Rectangle-e170be8b","./BoundingRectangle-409afd17","./S3MPixelFormat-f1fedece","./pako_inflate-f73548c4","./arrayFill-4d3cc415","./CompressedTextureBuffer-807fafb6","./PixelFormat-b3c660aa","./Math-5e38123d","./WebGLConstants-77a84876","./Intersect-53434a77","./Event-9821f5d9","./Buffer-72562b71"],(function(t,e,r,a,n,A,o,B,E,i,C,s,y,c,u,l,f,P,d,p,L,T,D,g,F,v,I,M){"use strict";function m(t,e,A){if(r.n.defined("array",t),a.t(e)&&r.n.typeOf.number("begin",e),a.t(A)&&r.n.typeOf.number("end",A),"function"==typeof t.slice)return t.slice(e,A);for(var o=Array.prototype.slice.call(t,e,A),B=n.o.typedArrayTypes,E=B.length,i=0;i<E;++i)if(t instanceof B[i]){o=new B[i](o);break}return o}var _,S=function(){var t,e=(t=!0,function(e,r){var a=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,a}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var a=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,a}}(),a=r(this,(function(){return a.toString().search("(((.+)+)+)+$").toString().constructor(a).search("(((.+)+)+)+$")}));a();var n=!0;return function(t,e){var r=n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return n=!1,r}}(),O=S(void 0,(function(){return O.toString().search("(((.+)+)+)+$").toString().constructor(O).search("(((.+)+)+)+$")}));function N(){}function h(t,e,r){var n,A=t.num_points(),o=r.num_components(),B=new _.AttributeQuantizationTransform;if(B.InitFromAttribute(r)){for(var E=new Array(o),i=0;i<o;++i)E[i]=B.min_value(i);n={quantizationBits:B.quantization_bits(),minValues:E,range:B.range(),octEncoded:!1}}_.destroy(B),(B=new _.AttributeOctahedronTransform).InitFromAttribute(r)&&(n={quantizationBits:B.quantization_bits(),octEncoded:!0}),_.destroy(B);var s,y=A*o;s=a.t(n)?function(t,e,r,a,n){var A,o;a.quantizationBits<=8?(o=new _.DracoUInt8Array,A=new Uint8Array(n),e.GetAttributeUInt8ForAllPoints(t,r,o)):(o=new _.DracoUInt16Array,A=new Uint16Array(n),e.GetAttributeUInt16ForAllPoints(t,r,o));for(var B=0;B<n;++B)A[B]=o.GetValue(B);return _.destroy(o),A}(t,e,r,n,y):function(t,e,r,a){var n,A;switch(r.data_type()){case 1:case 11:A=new _.DracoInt8Array,n=new Int8Array(a),e.GetAttributeInt8ForAllPoints(t,r,A);break;case 2:A=new _.DracoUInt8Array,n=new Uint8Array(a),e.GetAttributeUInt8ForAllPoints(t,r,A);break;case 3:A=new _.DracoInt16Array,n=new Int16Array(a),e.GetAttributeInt16ForAllPoints(t,r,A);break;case 4:A=new _.DracoUInt16Array,n=new Uint16Array(a),e.GetAttributeUInt16ForAllPoints(t,r,A);break;case 5:case 7:A=new _.DracoInt32Array,n=new Int32Array(a),e.GetAttributeInt32ForAllPoints(t,r,A);break;case 6:case 8:A=new _.DracoUInt32Array,n=new Uint32Array(a),e.GetAttributeUInt32ForAllPoints(t,r,A);break;case 9:case 10:A=new _.DracoFloat32Array,n=new Float32Array(a),e.GetAttributeFloatForAllPoints(t,r,A)}for(var o=0;o<a;++o)n[o]=A.GetValue(o);return _.destroy(A),n}(t,e,r,y);var c=C.ComponentDatatype.fromTypedArray(s);return{array:s,data:{componentsPerAttribute:o,componentDatatype:c,byteOffset:r.byte_offset(),byteStride:C.ComponentDatatype.getSizeInBytes(c)*o,normalized:r.normalized(),quantization:n}}}O();var R=new B.a(40680631590769,40680631590769,40408299984661.445),G=new B.a,b=new B.a;function x(t,e,r,n){var A=Math.cos(e);G.x=A*Math.cos(t),G.y=A*Math.sin(t),G.z=Math.sin(e),G=B.a.normalize(G,G),B.a.multiplyComponents(R,G,b);var o=Math.sqrt(B.a.dot(G,b));return b=B.a.divideByScalar(b,o,b),G=B.a.multiplyByScalar(G,r,G),a.t(n)||(n=new B.a),B.a.add(b,G,n)}var U=new A.c,K=new A.c,H=new B.a,w=new B.i;function V(t,e,r,n,i,s,y,u){var l=void 0,P=void 0,d=void 0,p=void 0,L=r.vertexAttributes,T=r.attrLocation;if(r.nCompressOptions=0,a.t(n.posUniqueID)&&n.posUniqueID>=0){a.t(u)||(r.nCompressOptions|=c.VertexCompressOption.SVC_Vertex);var D=e.GetAttribute(t,n.posUniqueID),g=h(t,e,D),F=g.data.componentsPerAttribute;r.verticesCount=g.array.length/F,r.vertCompressConstant=g.data.quantization.range/(1<<g.data.quantization.quantizationBits);var v=g.data.quantization.minValues;r.minVerticesValue=new E.a(v[0],v[1],v[2],1),F>3&&(r.minVerticesValue.w=v[3]);var I=r.verticesCount;if(s&&(l=new B.i,P=new B.i,d=new Float32Array(2*I),p=new Float64Array(2*I)),a.t(u)){var M=g.array,m=3===F?B.a.unpackArray(M):E.a.unpackArray(M);for(let t=0,e=m.length;t<e;t++){let e=m[t];B.a.multiplyByScalar(e,r.vertCompressConstant,e),B.a.add(e,r.minVerticesValue,e)}var _=A.c.multiply(u.sphereMatrix,u.geoMatrix,U),S=A.c.multiply(u.ellipsoidMatrix,u.geoMatrix,K);A.c.inverse(S,S);var O=new f.n(6378137,6378137,6378137);for(let t=0,e=m.length;t<e;t++){let e=m[t];A.c.multiplyByPoint(_,e,H);let r=O.cartesianToCartographic(H,w);s&&(p[2*t]=r.longitude,p[2*t+1]=r.latitude,0===t?(l.longitude=r.longitude,l.latitude=r.latitude,P.longitude=r.longitude,P.latitude=r.latitude):(l.longitude=Math.max(r.longitude,l.longitude),l.latitude=Math.max(r.latitude,l.latitude),P.longitude=Math.min(r.longitude,P.longitude),P.latitude=Math.min(r.latitude,P.latitude)));let a=x(r.longitude,r.latitude,r.height,H);A.c.multiplyByPoint(S,a,e)}var N=new Array(3*m.length);3===F?B.a.packArray(m,N):E.a.packArray(m,N),g.array=new Float32Array(N),g.data.componentDatatype=C.ComponentDatatype.FLOAT,g.data.byteStride=4*F}if(T.aPosition=L.length,L.push({index:T.aPosition,typedArray:g.array,componentsPerAttribute:F,componentDatatype:g.data.componentDatatype,offsetInBytes:g.data.byteOffset,strideInBytes:g.data.byteStride,normalize:g.data.normalized}),!a.t(u)&&s)for(var R=new B.a,G=new B.a,b=new B.i,V=0;V<I;V++)A.c.multiplyByPoint(i,B.a.fromElements(g.array[3*V]*r.vertCompressConstant+v[0],g.array[3*V+1]*r.vertCompressConstant+v[1],g.array[3*V+2]*r.vertCompressConstant+v[2],R),G),b=B.i.fromCartesian(G),p[2*V]=b.longitude,p[2*V+1]=b.latitude,0===V?(l.longitude=b.longitude,l.latitude=b.latitude,P.longitude=b.longitude,P.latitude=b.latitude):(l.longitude=Math.max(b.longitude,l.longitude),l.latitude=Math.max(b.latitude,l.latitude),P.longitude=Math.min(b.longitude,P.longitude),P.latitude=Math.min(b.latitude,P.latitude));if(s){for(V=0;V<I;V++)d[2*V]=p[2*V]-P.longitude,d[2*V+1]=p[2*V+1]-P.latitude;T.img=L.length,L.push({index:T.img,typedArray:d,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),y.max=l,y.min=P}}if(a.t(n.normalUniqueID)&&n.normalUniqueID>=0){r.nCompressOptions|=c.VertexCompressOption.SVC_Normal;var Y=e.GetAttribute(t,n.normalUniqueID),J=h(t,e,Y),k=J.data.quantization;r.normalRangeConstant=(1<<k.quantizationBits)-1,T.aNormal=L.length,L.push({index:T.aNormal,typedArray:J.array,componentsPerAttribute:J.data.componentsPerAttribute,componentDatatype:J.data.componentDatatype,offsetInBytes:J.data.byteOffset,strideInBytes:J.data.byteStride,normalize:J.data.normalized})}if(a.t(n.colorUniqueID)&&n.colorUniqueID>=0){r.nCompressOptions|=c.VertexCompressOption.SVC_VertexColor;var W=e.GetAttribute(t,n.colorUniqueID),Z=h(t,e,W);T.aColor=L.length,L.push({index:T.aColor,typedArray:Z.array,componentsPerAttribute:Z.data.componentsPerAttribute,componentDatatype:Z.data.componentDatatype,offsetInBytes:Z.data.byteOffset,strideInBytes:Z.data.byteStride,normalize:Z.data.normalized})}for(V=0;V<n.texCoordUniqueIDs.length;V++){r.texCoordCompressConstant=[],r.minTexCoordValue=[];var X=n.texCoordUniqueIDs[V];if(!(X<0)){var Q=e.GetAttribute(t,X),z=h(t,e,Q);if(a.t(z.data.quantization)){r.nCompressOptions|=c.VertexCompressOption.SVC_TexutreCoord,r.texCoordCompressConstant.push(z.data.quantization.range/(1<<z.data.quantization.quantizationBits));v=z.data.quantization.minValues;r.minTexCoordValue.push(new o.r(v[0],v[1]))}var j="aTexCoord"+V;T[j]=L.length,L.push({index:T[j],typedArray:z.array,componentsPerAttribute:z.data.componentsPerAttribute,componentDatatype:z.data.componentDatatype,offsetInBytes:z.data.byteOffset,strideInBytes:z.data.byteStride,normalize:z.data.normalized}),r.textureCoordIsW=!0}}for(V=0;V<n.vertexAttrUniqueIDs.length;V++){var q=n.vertexAttrUniqueIDs[V];if(!(q<0)){var $=e.GetAttribute(t,q),tt=h(t,e,$);T.aVertexWeight=L.length,L.push({index:T.aVertexWeight,typedArray:tt.array,componentsPerAttribute:tt.data.componentsPerAttribute,componentDatatype:tt.data.componentDatatype,offsetInBytes:tt.data.byteOffset,strideInBytes:tt.data.byteStride,normalize:tt.data.normalized}),r.customVertexAttribute={VertexWeight:0}}}}N.dracoDecodePointCloud=function(t,e,r,a,n){for(var A=new(_=t).Decoder,o=["POSITION","NORMAL","COLOR"],B=0;B<o.length;++B)A.SkipAttributeTransform(_[o[B]]);var E=new _.DecoderBuffer;if(E.Init(e,r),A.GetEncodedGeometryType(E)!==_.POINT_CLOUD)throw new l.t("Draco geometry type must be POINT_CLOUD.");var i=new _.PointCloud,C=A.DecodeBufferToPointCloud(E,i);if(!C.ok()||0===i.ptr)throw new l.t("Error decoding draco point cloud: "+C.error_msg());_.destroy(E),V(i,A,a,n),_.destroy(i),_.destroy(A)},N.dracoDecodeMesh=function(t,e,r,a,n,o,B,E,i,C){for(var s=new(_=t).Decoder,y=["POSITION","NORMAL","COLOR","TEX_COORD"],c=0;c<y.length;++c)s.SkipAttributeTransform(_[y[c]]);var f=new _.DecoderBuffer;if(f.Init(e,r),s.GetEncodedGeometryType(f)!==_.TRIANGULAR_MESH)throw new l.t("Unsupported draco mesh geometry type.");var P=new _.Mesh;if(!s.DecodeBufferToMesh(f,P).ok()||0===P.ptr)return!1;_.destroy(f),V(P,s,a,o,B,E,i,C);var d=function(t,e){for(var r=t.num_points(),a=t.num_faces(),n=new _.DracoInt32Array,A=3*a,o=u.IndexDatatype.createTypedArray(r,A),B=0,E=0;E<a;++E)e.GetFaceFromMesh(t,E,n),o[B+0]=n.GetValue(0),o[B+1]=n.GetValue(1),o[B+2]=n.GetValue(2),B+=3;var i=u.IndexDatatype.UNSIGNED_SHORT;return o instanceof Uint32Array&&(i=u.IndexDatatype.UNSIGNED_INT),_.destroy(n),{typedArray:o,numberOfIndices:A,indexDataType:i}}(P,s);n.indicesTypedArray=d.typedArray,n.indicesCount=d.numberOfIndices,n.indexType=d.indexDataType,n.primitiveType=A._0x38df4a.TRIANGLES,_.destroy(P),_.destroy(s)};var Y=function(){var t,e=(t=!0,function(e,r){var a=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,a}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var a=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,a}}(),a=r(this,(function(){return a.toString().search("(((.+)+)+)+$").toString().constructor(a).search("(((.+)+)+)+$")}));a();var n=!0;return function(t,e){var r=n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return n=!1,r}}(),J=Y(void 0,(function(){return J.toString().search("(((.+)+)+)+$").toString().constructor(J).search("(((.+)+)+)+$")}));J();var k=Object.freeze({OSGBFile:0,OSGBCacheFile:1,ClampGroundPolygon:2,ClampObjectPolygon:3,ClampGroundLine:4,ClampObjectLine:5,IconPoint:6,Text:7,PointCloudFile:8,ExtendRegion3D:9,ExtendClampPolygonCache:10,PolylineEffect:11,RegionEffect:12,ClampGroundAndObjectLineCache:13,ClampGroundRealtimeRasterCache:14}),W=function(){var t,e=(t=!0,function(e,r){var a=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,a}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var a=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,a}}(),a=r(this,(function(){return a.toString().search("(((.+)+)+)+$").toString().constructor(a).search("(((.+)+)+)+$")}));a();var n=!0;return function(t,e){var r=n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return n=!1,r}}(),Z=W(void 0,(function(){return Z.toString().search("(((.+)+)+)+$").toString().constructor(Z).search("(((.+)+)+)+$")}));function X(){}function Q(t){var e=new y.c,r=t.instanceBounds;if(!a.t(r))return e;var n=new B.a(r[0],r[1],r[2]),A=new B.a(r[3],r[4],r[5]),o=B.a.lerp(n,A,.5,new B.a),E=B.a.distance(o,n);return e.center=o,e.radius=E,e}function z(t){var e,r,n=new y.c,A=new B.a,o=t.vertexAttributes[0],E=o.componentsPerAttribute,i=a.t(t.nCompressOptions)&&(t.nCompressOptions&c.VertexCompressOption.SVC_Vertex)===c.VertexCompressOption.SVC_Vertex,C=1;i?(C=t.vertCompressConstant,e=new B.a(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),r=new Uint16Array(o.typedArray.buffer,o.typedArray.byteOffset,o.typedArray.byteLength/2)):r=new Float32Array(o.typedArray.buffer,o.typedArray.byteOffset,o.typedArray.byteLength/4);for(var s=[],u=0;u<t.verticesCount;u++)B.a.fromArray(r,E*u,A),i&&(A=B.a.multiplyByScalar(A,C,A),A=B.a.add(A,e,A)),s.push(B.a.clone(A));return y.c.fromPoints(s,n),s.length=0,n}function j(t){var e,r,n=new y.c,A=new B.a,o=a.t(t.nCompressOptions)&&(t.nCompressOptions&c.VertexCompressOption.SVC_Vertex)===c.VertexCompressOption.SVC_Vertex,E=t.vertexAttributes[0],i=E.componentsPerAttribute,C=1;o?(C=t.vertCompressConstant,r=new B.a(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/2)):e=new Float32Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/4);for(var s=[],u=0;u<t.verticesCount;u++)B.a.fromArray(e,i*u,A),o&&(A=B.a.multiplyByScalar(A,C,A),A=B.a.add(A,r,A)),s.push(B.a.clone(A));return y.c.fromPoints(s,n),s.length=0,n}function q(t){var e,r,n=a.t(t.nCompressOptions)&&(t.nCompressOptions&c.VertexCompressOption.SVC_Vertex)===c.VertexCompressOption.SVC_Vertex,A=new y.c,o=new B.a,i=new B.a,C=t.vertexAttributes[0],s=C.componentsPerAttribute,u=t.attrLocation.aPosition,l=t.vertexAttributes[u],f=t.attrLocation.aTexCoord5,P=t.vertexAttributes[f],d=P.componentsPerAttribute;n?(s=3,d=3,e=tt(t,l),r=function(t,e,r){for(var a,n,A,o=e.componentsPerAttribute,B=t.texCoordCompressConstant[r],i=new E.a(t.minTexCoordValue[r].x,t.minTexCoordValue[r].y,t.minTexCoordValue[r].z,t.minTexCoordValue[r].w),C=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),s=new Float32Array(3*t.verticesCount),y=0;y<t.verticesCount;y++)a=C[o*y]*B+i.x,n=C[o*y+1]*B+i.y,A=C[o*y+2]*B+i.z,s[3*y]=a,s[3*y+1]=n,s[3*y+2]=A;return s}(t,P,5)):(e=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4),r=new Float32Array(P.typedArray.buffer,P.typedArray.byteOffset,P.typedArray.byteLength/4));for(var p=[],L=0;L<t.verticesCount;L++)B.a.fromArray(e,s*L,o),B.a.fromArray(r,d*L,i),B.a.add(o,i,o),p.push(B.a.clone(o));return y.c.fromPoints(p,A),p.length=0,A}function $(t){var e=A._0x38df4a.TRIANGLES;switch(t){case 1:e=A._0x38df4a.POINTS;break;case 2:e=A._0x38df4a.LINES;break;case 3:e=A._0x38df4a.LINE_STRIP;break;case 4:e=A._0x38df4a.TRIANGLES}return e}function tt(t,e){for(var r,a,n,A=e.componentsPerAttribute,o=t.vertCompressConstant,E=new B.a(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),i=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),C=new Float32Array(3*t.verticesCount),s=0;s<t.verticesCount;s++)r=i[A*s]*o+E.x,a=i[A*s+1]*o+E.y,n=i[A*s+2]*o+E.z,C[3*s]=r,C[3*s+1]=a,C[3*s+2]=n;return C}Z(),X.calcBoundingSphereInWorker=function(t,e){return e.instanceIndex>-1?Q(e):a.t(e.clampRegionEdge)?q(e):t>=k.ClampGroundPolygon&&t<=k.ClampObjectLine?j(e):t==k.ClampGroundAndObjectLineCache?q(e):z(e)},X.calcBoundingSphere=function(t,e,r){var n,A=t._fileType;return n=e.instanceIndex>-1?Q(e):a.t(e.clampRegionEdge)?q(e):A>=k.ClampGroundPolygon&&A<=k.ClampObjectLine?j(e):A==k.ClampGroundAndObjectLineCache?q(e):z(e),y.c.transform(n,r,n),n},X.calcBoundingRectangle=function(t,e){var r;return t._fileType===k.ClampGroundPolygon&&(r=function(t){var e,r,n=a.t(t.nCompressOptions)&&(t.nCompressOptions&c.VertexCompressOption.SVC_Vertex)===c.VertexCompressOption.SVC_Vertex,A=new P.n,E=t.vertexAttributes[0],i=E.componentsPerAttribute,C=1;n?(C=t.vertCompressConstant,r=new B.a(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/2)):e=new Float32Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/4);for(var s=[],y=0;y<t.verticesCount;y++){var u=e[i*y],l=e[i*y+1];n&&(u=C*u+r.x,l=C*l+r.y),s.push(new o.r(u,l))}return P.n.fromPoints(s,A),s.length=0,A}(e)),r},X.createEdge=function(t,e){if(!(e.length<1)){var r=function(t){for(var e=[],r=t.length,a=0;a<r;a++){var n=$(t[a].primitiveType);(n===A._0x38df4a.LINES||n===A._0x38df4a.LINE_STRIP)&&e.push(t[a])}return e}(e);if(0!=r.length){var n,o=function(t){for(var e=0,r=t.length,a=0;a<r;a++){var n=t[a],o=$(n.primitiveType);o==A._0x38df4a.LINES?e+=n.indicesCount/2:o==A._0x38df4a.LINE_STRIP&&e++}return e}(r),E=t.attrLocation.aPosition,i=t.vertexAttributes[E],s=a.t(t.nCompressOptions)&&(t.nCompressOptions&c.VertexCompressOption.SVC_Vertex)===c.VertexCompressOption.SVC_Vertex,y=i.componentsPerAttribute;s?(y=3,n=tt(t,i)):n=new Float32Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/4);for(var u=function(t){for(var e=0,r=t.length,a=0;a<r;a++)e+=t[a].indicesCount;return e}(r),l=function(t,e,r){for(var a,n=[],o=r.length,E=0;E<o;E++){var i,C=r[E];i=0===C.indexType?new Uint16Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/2):new Uint32Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/4);var s=$(C.primitiveType);if(s==A._0x38df4a.LINES)for(a=0;a<C.indicesCount;a+=2){var y=[],c=new B.a;c.x=t[i[a]*e],c.y=t[i[a]*e+1],c.z=t[i[a]*e+2],y.push(c);var u=new B.a;u.x=t[i[a+1]*e],u.y=t[i[a+1]*e+1],u.z=t[i[a+1]*e+2],y.push(u),n.push(y)}else if(s==A._0x38df4a.LINE_STRIP){for(y=[],a=0;a<C.indicesCount;a++){var l=new B.a;l.x=t[i[a]*e],l.y=t[i[a]*e+1],l.z=t[i[a]*e+2],y.push(l)}n.push(y)}}return n}(n,y,r),f=4*u-4*o,P=new Float32Array(3*f),d=new Float32Array(3*f),p=new Float32Array(3*f),L=new Int8Array(2*f),T=0,D=0;D<o;D++){for(var g=l[D].length,F=0;F<g;F++){var v=4*T-4*D,I=3*v+12*F,M=l[D][F];0!=F&&(P[I-6]=M.x,P[I-5]=M.y,P[I-4]=M.z,P[I-3]=M.x,P[I-2]=M.y,P[I-1]=M.z),F!=g-1&&(P[I]=M.x,P[I+1]=M.y,P[I+2]=M.z,P[I+3]=M.x,P[I+4]=M.y,P[I+5]=M.z);var m=M;F+1<g&&(m=l[D][F+1]),0!=F&&(p[I-6]=m.x,p[I-5]=m.y,p[I-4]=m.z,p[I-3]=m.x,p[I-2]=m.y,p[I-1]=m.z),F!=g-1&&(p[I]=m.x,p[I+1]=m.y,p[I+2]=m.z,p[I+3]=m.x,p[I+4]=m.y,p[I+5]=m.z);var _=M;F>=1&&(_=l[D][F-1]),0!=F&&(d[I-6]=_.x,d[I-5]=_.y,d[I-4]=_.z,d[I-3]=_.x,d[I-2]=_.y,d[I-1]=_.z),F!=g-1&&(d[I]=_.x,d[I+1]=_.y,d[I+2]=_.z,d[I+3]=_.x,d[I+4]=_.y,d[I+5]=_.z),I=2*v+8*F,0!=F&&(L[I-4]=-1,L[I-3]=-1,L[I-2]=1,L[I-1]=-1),F!=g-1&&(L[I]=-1,L[I+1]=1,L[I+2]=1,L[I+3]=1)}T+=l[D].length}var S={vertexAttributes:[],attrLocation:{}},O=S.vertexAttributes,N=S.attrLocation;S.instanceCount=0,S.instanceMode=0,N.aPosition=0,O.push({index:N.aPosition,typedArray:P,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aNormal=1,O.push({index:N.aNormal,typedArray:d,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord0=2,O.push({index:N.aTexCoord0,typedArray:p,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord1=3,O.push({index:N.aTexCoord1,typedArray:L,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:2*Int8Array.BYTES_PER_ELEMENT,normalize:!1});for(var h=[],R=0;R<l.length;R++)h.push(l[R].length);return{vertexPackage:S,indexPackage:function(t,e,r,a){var n,o={};o.indicesCount=6*(t-e),o.indexType=a>65535?1:0,o.primitiveType=A._0x38df4a.TRIANGLES,n=0===o.indexType?new Uint16Array(o.indicesCount):new Uint32Array(o.indicesCount);for(var B=0,E=0;E<e;E++){for(var i=0;i<r[E]-1;i++)n[6*(B-E+i)]=4*(B-E+i),n[6*(B-E+i)+1]=4*(B-E+i)+2,n[6*(B-E+i)+2]=4*(B-E+i)+1,n[6*(B-E+i)+3]=4*(B-E+i)+1,n[6*(B-E+i)+4]=4*(B-E+i)+2,n[6*(B-E+i)+5]=4*(B-E+i)+3;B+=r[E]}return o.indicesTypedArray=n,o}(u,o,h,f)}}}};var et=function(){var t,e=(t=!0,function(e,r){var a=t?function(){if(r){var t=r.apply(e,arguments);return r=null,t}}:function(){};return t=!1,a}),r=function(){var t=e(this,(function(){return t.toString().search("(((.+)+)+)+$").toString().constructor(t).search("(((.+)+)+)+$")}));t();var r=!0;return function(t,e){var a=r?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return r=!1,a}}(),a=r(this,(function(){return a.toString().search("(((.+)+)+)+$").toString().constructor(a).search("(((.+)+)+)+$")}));a();var n=!0;return function(t,e){var r=n?function(){if(e){var r=e.apply(t,arguments);return e=null,r}}:function(){};return n=!1,r}}(),rt=et(void 0,(function(){return rt.toString().search("(((.+)+)+)+$").toString().constructor(rt).search("(((.+)+)+)+$")}));rt();var at,nt,At=Object.freeze({S3M:49,S3M4:1}),ot=function(){var t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),e=new Uint8Array([32,0,65,2,1,106,34,33,3,128,11,4,13,64,6,253,10,7,15,116,127,5,8,12,40,16,19,54,20,9,27,255,113,17,42,67,24,23,146,148,18,14,22,45,70,69,56,114,101,21,25,63,75,136,108,28,118,29,73,115]);if("object"!=typeof WebAssembly)return{supported:!1};var r="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";WebAssembly.validate(t)&&(r="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");var a,n=WebAssembly.instantiate(function(t){for(var r=new Uint8Array(t.length),a=0;a<t.length;++a){var n=t.charCodeAt(a);r[a]=n>96?n-71:n>64?n-65:n>47?n+4:n>46?63:62}var A=0;for(a=0;a<t.length;++a)r[A++]=r[a]<60?e[r[a]]:64*(r[a]-60)+r[++a];return r.buffer.slice(0,A)}(r),{}).then((function(t){(a=t.instance).exports.__wasm_call_ctors()}));function A(t,e,r,n,A,o){var B=a.exports.sbrk,E=r+3&-4,i=B(E*n),C=B(A.length),s=new Uint8Array(a.exports.memory.buffer);s.set(A,C);var y=t(i,r,n,C,A.length);if(0==y&&o&&o(i,E,n),e.set(s.subarray(i,i+r*n)),B(i-B(0)),0!=y)throw new Error("Malformed buffer data: "+y)}var o={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},B={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return{ready:n,supported:!0,decodeVertexBuffer:function(t,e,r,n,B){A(a.exports.meshopt_decodeVertexBuffer,t,e,r,n,a.exports[o[B]])},decodeIndexBuffer:function(t,e,r,n){A(a.exports.meshopt_decodeIndexBuffer,t,e,r,n)},decodeIndexSequence:function(t,e,r,n){A(a.exports.meshopt_decodeIndexSequence,t,e,r,n)},decodeGltfBuffer:function(t,e,r,n,E,i){A(a.exports[B[E]],t,e,r,n,a.exports[o[i]])}}}(),Bt=1,Et=2,it={};it[0]=D.PixelFormat.RGB_DXT1,it[Bt]=D.PixelFormat.RGBA_DXT3,it[Et]=D.PixelFormat.RGBA_DXT5;var Ct,st=0,yt=!1;function ct(t,e){var r=t.data,n=r.byteLength,A=new Uint8Array(r),o=Ct._malloc(n);!function(t,e,r,a){var n,A=r/4,o=a%4,B=new Uint32Array(t.buffer,0,(a-o)/4),E=new Uint32Array(e.buffer);for(n=0;n<B.length;n++)E[A+n]=B[n];for(n=a-o;n<a;n++)e[r+n]=t[n]}(A,Ct.HEAPU8,o,n);var B=Ct._crn_get_dxt_format(o,n),E=it[B];if(!a.t(E))throw new l.t("Unsupported compressed format.");var i,C=Ct._crn_get_levels(o,n),s=Ct._crn_get_width(o,n),y=Ct._crn_get_height(o,n),c=0;for(i=0;i<C;++i)c+=D.PixelFormat.compressedTextureSizeInBytes(E,s>>i,y>>i);if(st<c&&(a.t(at)&&Ct._free(at),at=Ct._malloc(c),nt=new Uint8Array(Ct.HEAPU8.buffer,at,c),st=c),Ct._crn_decompress(o,n,at,c,0,C),Ct._free(o),a.e(t.bMipMap,!1)){var u=nt.slice(0,c);return e.push(u.buffer),new T.e(E,s,y,u)}var f=D.PixelFormat.compressedTextureSizeInBytes(E,s,y),P=nt.subarray(0,f),d=new Uint8Array(f);return d.set(P,0),e.push(d.buffer),new T.e(E,s,y,d)}var ut,lt=1,ft=0,Pt=1,dt=2,pt=3,Lt=0,Tt=1,Dt=2;new i.e;var gt,Ft=!1,vt=null;function It(t,e,r,a,n,A){this.left=t,this.bottom=e,this.right=r,this.top=a,this.minHeight=n,this.maxHeight=A,this.width=r-t,this.length=a-e,this.height=A-n}function Mt(t,e,r){var a=r,n=t.getUint32(a,!0),A=a+=Uint32Array.BYTES_PER_ELEMENT,o=new Uint8Array(e,a,n);return{dataViewByteOffset:A,byteOffset:a+=n*Uint8Array.BYTES_PER_ELEMENT,buffer:o}}function mt(t,e,r,a){var n=t.getUint32(a+e,!0);a+=Uint32Array.BYTES_PER_ELEMENT;var A=r.subarray(a,a+n);return{string:s.c(A),bytesOffset:a+=n}}function _t(t,e,r,a,n,A){var o=r,B=t.getUint16(r+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT,A||(o+=Uint16Array.BYTES_PER_ELEMENT);for(var E=0;E<B;E++){var i=t.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var s=t.getUint16(o+a,!0);if(o+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(o+a,!0),o+=Uint16Array.BYTES_PER_ELEMENT,20==s||35==s);else{var y=i*s*Float32Array.BYTES_PER_ELEMENT,c=e.subarray(o,o+y);o+=y;var u="aTexCoord"+E,l=n.vertexAttributes,f=n.attrLocation;f[u]=l.length,l.push({index:f[u],typedArray:c,componentsPerAttribute:s,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:s*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}}return{bytesOffset:o}}function St(t,e,r,a,n){var A=r,o=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var B=n.vertexAttributes,E=n.attrLocation,i=0;i<o;i++){var s=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var y=t.getUint16(A+a,!0);if(A+=Uint16Array.BYTES_PER_ELEMENT,16===y){A-=Uint16Array.BYTES_PER_ELEMENT;var c=s*(y*Float32Array.BYTES_PER_ELEMENT+Uint16Array.BYTES_PER_ELEMENT),u=e.subarray(A,A+c);A+=c;var l=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*y*s);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=l,n.instanceIndex=1;for(var f=Float32Array.BYTES_PER_ELEMENT*y+Uint16Array.BYTES_PER_ELEMENT,P=0;P<s;P++){var d=P*f+Uint16Array.BYTES_PER_ELEMENT,p=u.subarray(d,d+f);l.set(p,P*(f-Uint16Array.BYTES_PER_ELEMENT))}L=16*Float32Array.BYTES_PER_ELEMENT,E.uv2=B.length,B.push({index:E.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:L,instanceDivisor:1}),E.uv3=B.length,B.push({index:E.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv4=B.length,B.push({index:E.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.secondary_colour=B.length,B.push({index:E.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1})}else{t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;c=s*y*Float32Array.BYTES_PER_ELEMENT;if(17===y||29===y){var L;l=e.subarray(A,A+c);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=l,n.instanceIndex=1,17===y?(L=17*Float32Array.BYTES_PER_ELEMENT,E.uv2=B.length,B.push({index:E.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:L,instanceDivisor:1}),E.uv3=B.length,B.push({index:E.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv4=B.length,B.push({index:E.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.secondary_colour=B.length,B.push({index:E.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv6=B.length,B.push({index:E.uv6,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1})):29===y&&(L=29*Float32Array.BYTES_PER_ELEMENT,E.uv1=B.length,B.push({index:E.uv1,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:L,instanceDivisor:1,byteLength:c}),E.uv2=B.length,B.push({index:E.uv2,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv3=B.length,B.push({index:E.uv3,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv4=B.length,B.push({index:E.uv4,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv5=B.length,B.push({index:E.uv5,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv6=B.length,B.push({index:E.uv6,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv7=B.length,B.push({index:E.uv7,componentsPerAttribute:3,componentDatatype:C.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.secondary_colour=B.length,B.push({index:E.secondary_colour,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}),E.uv9=B.length,B.push({index:E.uv9,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:28*Float32Array.BYTES_PER_ELEMENT,strideInBytes:L,instanceDivisor:1}))}else{var T=s*y;n.instanceBounds=new Float32Array(T);for(var D=0;D<T;D++)n.instanceBounds[D]=t.getFloat32(A+a+D*Float32Array.BYTES_PER_ELEMENT,!0)}A+=c}}return{bytesOffset:A}}var Ot=new B.a(40680631590769,40680631590769,40408299984661.445),Nt=new B.a,ht=new B.a;function Rt(t,e,r,n){var A=Math.cos(e);Nt.x=A*Math.cos(t),Nt.y=A*Math.sin(t),Nt.z=Math.sin(e),Nt=B.a.normalize(Nt,Nt),B.a.multiplyComponents(Ot,Nt,ht);var o=Math.sqrt(B.a.dot(Nt,ht));return ht=B.a.divideByScalar(ht,o,ht),Nt=B.a.multiplyByScalar(Nt,r,Nt),a.t(n)||(n=new B.a),B.a.add(ht,Nt,n)}var Gt=new B.a,bt=new B.i,xt=new A.c,Ut=new A.c;function Kt(t,e,r,n,o,i,s,y,c){var u=n,l=e.getUint32(u+r,!0);if(o.verticesCount=l,u+=Uint32Array.BYTES_PER_ELEMENT,l<=0)return{bytesOffset:u};var P=e.getUint16(u+r,!0);u+=Uint16Array.BYTES_PER_ELEMENT;var d=e.getUint16(u+r,!0);d=P*Float32Array.BYTES_PER_ELEMENT,u+=Uint16Array.BYTES_PER_ELEMENT;var p=l*P*Float32Array.BYTES_PER_ELEMENT,L=t.subarray(u,u+p);u+=p;var T=o.vertexAttributes,D=o.attrLocation,g=void 0,F=void 0;if(s){g=new B.i,F=new B.i;var v=new Float32Array(2*l),I=new Float64Array(2*l);if(a.t(y)){var M=new Float32Array(L.byteLength/4),m=new Float32Array(L.buffer,L.byteOffset,L.byteLength/4);k=3===P?B.a.unpackArray(m):E.a.unpackArray(m);var _=A.c.multiply(y.sphereMatrix,y.geoMatrix,xt),S=A.c.multiply(y.ellipsoidMatrix,y.geoMatrix,Ut);A.c.inverse(S,S);for(var O=new f.n(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){var G=k[h];A.c.multiplyByPoint(_,G,Gt);var b=Rt((W=O.cartesianToCartographic(Gt,bt)).longitude,W.latitude,W.height,Gt);A.c.multiplyByPoint(S,b,G),3===P?(B.a.pack(G,M,N),N+=3):(E.a.pack(G,M,N),N+=4),I[2*h]=W.longitude,I[2*h+1]=W.latitude,0===h?(g.longitude=W.longitude,g.latitude=W.latitude,F.longitude=W.longitude,F.latitude=W.latitude):(g.longitude=Math.max(W.longitude,g.longitude),g.latitude=Math.max(W.latitude,g.latitude),F.longitude=Math.min(W.longitude,F.longitude),F.latitude=Math.min(W.latitude,F.latitude))}L=M}else{var x=new B.a,U=new B.a,K=new Float32Array(L.buffer,L.byteOffset,L.byteLength/4),H=new B.i;O=Xt?new f.n(6378137,6378137,6356752.314245179):new f.n(6378137,6378137,6378137);for(var w=0;w<l;w++)A.c.multiplyByPoint(i,B.a.fromElements(K[3*w],K[3*w+1],K[3*w+2],x),U),H=O.cartesianToCartographic(U,bt),I[2*w]=H.longitude,I[2*w+1]=H.latitude,0===w?(g.longitude=H.longitude,g.latitude=H.latitude,F.longitude=H.longitude,F.latitude=H.latitude):(g.longitude=Math.max(H.longitude,g.longitude),g.latitude=Math.max(H.latitude,g.latitude),F.longitude=Math.min(H.longitude,F.longitude),F.latitude=Math.min(H.latitude,F.latitude))}for(w=0;w<l;w++)v[2*w]=I[2*w]-F.longitude,v[2*w+1]=I[2*w+1]-F.latitude;D.aPosition=T.length,T.push({index:D.aPosition,typedArray:L,componentsPerAttribute:P,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:d,normalize:!1}),D.img=T.length,T.push({index:D.img,typedArray:v,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}else{if(3===P&&a.t(i)){U=new B.a,K=new Float32Array(L.buffer,L.byteOffset,L.byteLength/4);for(var V=new Float32Array(L.byteLength/4+l),Y=K.length,J=(w=0,0);w<Y;w+=3,J+=4)V[J]=K[w],V[J+1]=K[w+1],V[J+2]=K[w+2],A.c.multiplyByPoint(i,B.a.fromElements(V[J],V[J+1],V[J+2],x),U),V[J+3]=B.i.fromCartesian(U).height;L=V,d=(P=4)*Float32Array.BYTES_PER_ELEMENT}if(a.t(y)){var k;M=new Float32Array(L.byteLength/4),m=new Float32Array(L.buffer,L.byteOffset,L.byteLength/4);k=3===P?B.a.unpackArray(m):E.a.unpackArray(m);_=A.c.multiply(y.sphereMatrix,y.geoMatrix,xt),S=A.c.multiply(y.ellipsoidMatrix,y.geoMatrix,Ut);A.c.inverse(S,S);for(O=new f.n(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){G=k[h];A.c.multiplyByPoint(_,G,Gt);var W;b=Rt((W=O.cartesianToCartographic(Gt,bt)).longitude,W.latitude,W.height,Gt);A.c.multiplyByPoint(S,b,G),3===P?(B.a.pack(G,M,N),N+=3):(E.a.pack(G,M,N),N+=4)}L=M}D.aPosition=T.length,T.push({index:D.aPosition,typedArray:L,componentsPerAttribute:P,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:d,normalize:!1})}return{bytesOffset:u,cartographicBounds:{max:g,min:F}}}function Ht(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var B=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var E=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var i=o*B*Float32Array.BYTES_PER_ELEMENT,s=t.subarray(A,A+i);if(A+=i,!n.ignoreNormal){var y=n.vertexAttributes,c=n.attrLocation;c.aNormal=y.length,y.push({index:c.aNormal,typedArray:s,componentsPerAttribute:B,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:E,normalize:!1})}return{bytesOffset:A}}var wt={0:Uint32Array.BYTES_PER_ELEMENT,1:Float32Array.BYTES_PER_ELEMENT,2:Float64Array.BYTES_PER_ELEMENT};function Vt(t,e,r,a,n){var A,o=a,B=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,n.verticesCount,B>0){e.getUint16(o+r,!0),o+=Uint16Array.BYTES_PER_ELEMENT,o+=2*Uint8Array.BYTES_PER_ELEMENT;var E=B*Uint8Array.BYTES_PER_ELEMENT*4;A=m(t,o,o+E),o+=E;var i=n.vertexAttributes,s=n.attrLocation;s.aColor=i.length,i.push({index:s.aColor,typedArray:A,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0})}return{bytesOffset:o}}function Yt(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);return A+=Uint32Array.BYTES_PER_ELEMENT,o<=0?{bytesOffset:A}:(e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,A+=2*Uint8Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*Uint8Array.BYTES_PER_ELEMENT*4})}function Jt(t,e,r,a,n){var A=n,o=[],B=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<B;E++){var i={};3===t&&(r.getUint32(A+a,!0),A+=Uint32Array.BYTES_PER_ELEMENT);var C=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var s=r.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,r.getUint8(A+a,!0),A+=Uint8Array.BYTES_PER_ELEMENT;var y=r.getUint8(A+a,!0);if(A+=Uint8Array.BYTES_PER_ELEMENT,A+=Uint8Array.BYTES_PER_ELEMENT,C>0){var c=0,u=null;1===s||3===s?(c=C*Uint32Array.BYTES_PER_ELEMENT,u=e.subarray(A,A+c)):(c=C*Uint16Array.BYTES_PER_ELEMENT,u=e.subarray(A,A+c),C%2!=0&&(c+=2)),i.indicesTypedArray=u,A+=c}i.indicesCount=C,i.indexType=s,i.primitiveType=y;var l=[],f=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var P=0;P<f;P++){var d=mt(r,a,e,A),p=d.string;A=d.bytesOffset,l.push(p),i.materialCode=p}if(0===f&&(i.materialCode="OSGBEmpty"),o.push(i),0!==A%4)A+=4-A%4}return{bytesOffset:A,arrIndexPackage:o}}function kt(t,e,r,n,o,i,s,y,u,l){var P,d,p=n,L=e.getUint32(p+r,!0);return o.nCompressOptions=L,p+=Uint32Array.BYTES_PER_ELEMENT,(L&c.VertexCompressOption.SVC_Vertex)==c.VertexCompressOption.SVC_Vertex?(P=function(t,e,r,n,o,i,s){var y=n,c=e.getUint32(y+r,!0);if(o.verticesCount=c,y+=Uint32Array.BYTES_PER_ELEMENT,c<=0)return{bytesOffset:y};var u=e.getUint16(y+r,!0);y+=Uint16Array.BYTES_PER_ELEMENT;var l=e.getUint16(y+r,!0);l=u*Int16Array.BYTES_PER_ELEMENT,y+=Uint16Array.BYTES_PER_ELEMENT;var P=e.getFloat32(y+r,!0);y+=Float32Array.BYTES_PER_ELEMENT;var d=new E.a;d.x=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,d.y=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,d.z=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,d.w=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,o.vertCompressConstant=P,o.minVerticesValue=d;var p=c*u*Int16Array.BYTES_PER_ELEMENT,L=t.subarray(y,y+p);if(y+=p,a.t(i)){var T=new Uint16Array(L.byteLength/2),D=new Uint16Array(L.buffer,L.byteOffset,L.byteLength/2),g=E.a.unpackArray(D);for(let t=0,e=g.length;t<e;t++){let e=g[t];B.a.multiplyByScalar(e,P,e),B.a.add(e,d,e)}var F=A.c.multiply(i.sphereMatrix,i.geoMatrix,xt),v=A.c.multiply(i.ellipsoidMatrix,i.geoMatrix,Ut);A.c.inverse(v,v);var I=new f.n(6378137,6378137,6378137),M=0;for(let t=0,e=g.length;t<e;t++){let e=g[t];A.c.multiplyByPoint(F,e,Gt);let r=I.cartesianToCartographic(Gt,bt),a=Rt(r.longitude,r.latitude,r.height,Gt);A.c.multiplyByPoint(v,a,e),B.a.subtract(e,d,e),B.a.divideByScalar(e,P,e),E.a.pack(e,T,M),M+=4}L=T}var m=o.vertexAttributes,_=o.attrLocation;return _.aPosition=m.length,m.push({index:_.aPosition,typedArray:L,componentsPerAttribute:u,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:l,normalize:!1}),{bytesOffset:y}}(t,e,r,p,o,u),p=P.bytesOffset):(p=(P=Kt(t,e,r,p,o,s,y,u)).bytesOffset,d=P.cartographicBounds),(L&c.VertexCompressOption.SVC_Normal)==c.VertexCompressOption.SVC_Normal?(P=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var B=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var E=2*o*Int16Array.BYTES_PER_ELEMENT,i=t.subarray(A,A+E);if(A+=E,!n.ignoreNormal){var s=n.vertexAttributes,y=n.attrLocation;y.aNormal=s.length,s.push({index:y.aNormal,typedArray:i,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:B,normalize:!1})}return{bytesOffset:A}}(t,e,r,p,o),p=P.bytesOffset):p=(P=Ht(t,e,r,p,o)).bytesOffset,p=(P=Yt(0,e,r,p=(P=Vt(t,e,r,p,o)).bytesOffset)).bytesOffset,(L&c.VertexCompressOption.SVC_TexutreCoord)==c.VertexCompressOption.SVC_TexutreCoord?(P=function(t,e,r,a,n){n.texCoordCompressConstant=[],n.minTexCoordValue=[];var A=r,o=t.getUint16(r+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var B=0,i=0;i<o;i++){var s=t.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,A+=3*Uint8Array.BYTES_PER_ELEMENT;var y=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var c=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var u=t.getFloat32(A+a,!0);A+=Float32Array.BYTES_PER_ELEMENT,n.texCoordCompressConstant.push(u);var l=new E.a;l.x=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,l.y=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,l.z=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,l.w=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,n.minTexCoordValue.push(l);var f=y*c*Int16Array.BYTES_PER_ELEMENT,P=e.subarray(A,A+f),d=(A+=f)%4;0!==d&&(A+=4-d);var p="aTexCoord"+B,L=n.vertexAttributes,T=n.attrLocation;if(T[p]=L.length,L.push({index:T[p],typedArray:P,componentsPerAttribute:c,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:c*Int16Array.BYTES_PER_ELEMENT,normalize:!1}),s){f=y*Float32Array.BYTES_PER_ELEMENT;var D=e.subarray(A,A+f);A+=f,n.texCoordZMatrix=!0,T[p="aTexCoordZ"+B]=L.length,L.push({index:T[p],typedArray:D,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:Float32Array.BYTES_PER_ELEMENT,normalize:!1})}B++}return{bytesOffset:A}}(e,t,p,r,o),p=P.bytesOffset):p=(P=_t(e,t,p,r,o,i)).bytesOffset,(L&c.VertexCompressOption.SVC_TexutreCoordIsW)==c.VertexCompressOption.SVC_TexutreCoordIsW&&(o.textureCoordIsW=!0),{bytesOffset:p=(P=St(e,t,p,r,o)).bytesOffset,cartographicBounds:d}}function Wt(t,e,r,n,A,o,B,E,i,s,y){3===t&&(r.getUint32(A,!0),A+=Uint32Array.BYTES_PER_ELEMENT,a.t(i)&&i||(E=void 0));var c,u=A;u=(c=Kt(e,r,n,u,o,E,i,s)).bytesOffset;var l=c.cartographicBounds;if(u=(c=Vt(e,r,n,u=(c=Ht(e,r,n,u,o)).bytesOffset,o)).bytesOffset,3!==t&&(u=(c=Yt(0,r,n,u)).bytesOffset),u=(c=St(r,e,u=(c=_t(r,e,u,n,o,B)).bytesOffset,n,o)).bytesOffset,3===t&&(c=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<o;B++){var E=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var y=E*i*wt[s],c=t.subarray(A,A+y);A+=y;var u=n.vertexAttributes,l=n.attrLocation,f="aCustom"+B;l[f]=u.length,u.push({index:l[f],typedArray:c,componentsPerAttribute:i,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1})}return{bytesOffset:A}}(e,r,n,u,o),u=c.bytesOffset),3==t){var f=mt(r,n,e,u);u=f.bytesOffset,o.customVertexAttribute=JSON.parse(f.string);var P="aCustom"+o.customVertexAttribute.TextureCoordMatrix,d="aCustom"+o.customVertexAttribute.VertexWeight,p="aCustom"+o.customVertexAttribute.VertexWeight_1;a.t(o.attrLocation[P])&&(o.attrLocation.aTextureCoordMatrix=o.attrLocation[P],delete o.attrLocation[P]),a.t(o.attrLocation[d])&&(o.attrLocation.aVertexWeight=o.attrLocation[d],delete o.attrLocation[d]),a.t(o.attrLocation[p])&&(o.attrLocation.aVertexWeight_1=o.attrLocation[p],delete o.attrLocation[p]);for(var L=Object.keys(o.attrLocation),T=L.length,D=0;D<T;++D){var g=L[D];-1!==g.indexOf("aCustom")&&delete o.attrLocation[g]}var F=(u+n)%4;F&&(F=4-F),u+=F}return 3===t&&(c=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var B=e.getUint16(A+r,!0);return A+=Uint16Array.BYTES_PER_ELEMENT,e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*B*Float32Array.BYTES_PER_ELEMENT}}(0,r,n,u),u=c.bytesOffset),{bytesOffset:u,cartographicBounds:l}}function Zt(t){return 0!==t.length&&"ClampGroundAndObjectLinePass"===t[0].materialCode}var Xt,Qt=1,zt=4,jt=16,qt=32,$t=64,te=128,ee=512,re=1024;function ae(t,e,r,n,i,s,y,u,l,f,P,d,p){var L=t,T=0,D=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT,f=a.e(f,a.e.EMPTY_OBJECT);for(var g=void 0,F=0;F<D;F++){3===u&&(e.getUint32(T+r,!0),T+=Uint32Array.BYTES_PER_ELEMENT);var v=(Kt=mt(e,r,L,T)).string;if(a.t(l)){var I=a.e(f[v],A.c.IDENTITY);g=new A.c,A.c.multiply(l,I,g)}a.t(p)&&(p.geoMatrix=a.e(f[v],A.c.IDENTITY));var M=(T=Kt.bytesOffset)%4;0!==M&&(T+=4-M);var _=ft;if(_=e.getUint32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,(le={vertexAttributes:[],attrLocation:{},instanceCount:0,instanceMode:0,instanceIndex:-1}).ignoreNormal=n.ignoreNormal,3===u)switch(_){case Lt:_=Pt;break;case Tt:_=pt;break;case Dt:_=dt}if(_===pt){3===u&&(e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT),u>=2&&(e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT);var S,O={};O.posUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,O.normalUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,O.colorUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,O.secondColorUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,3===u?(S=e.getUint32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT):(S=e.getUint16(T+r,!0),T+=Int16Array.BYTES_PER_ELEMENT);for(var h=[],R=0;R<S;R++){var G=e.getInt32(T+r,!0);h.push(G),T+=Int32Array.BYTES_PER_ELEMENT}O.texCoordUniqueIDs=h;var b=[];if(3===u){var x=e.getUint32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;for(var U=0;U<x;U++){var K=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT,b.push(K)}}O.vertexAttrUniqueIDs=b;var H=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;var w=[],V={};if(H>0){var Y=(J=mt(e,r,L,T)).string;T=J.bytesOffset,V.materialCode=Y,w.push(V)}3===u&&((Yt=(T+r)%4)&&(Yt=4-Yt),T+=Yt);var J,k=new Object,W=e.getUint32(T+r,!0),Z=m(L,T+=Int32Array.BYTES_PER_ELEMENT,T+W);if(H>0?N.dracoDecodeMesh(ut,Z,W,le,V,O,g,P,k,p):N.dracoDecodePointCloud(ut,Z,W,le,O),a.t(k.min)&&a.t(k.max)||(k=void 0),T+=W,3===u)(Yt=(T+r)%4)&&(Yt=4-Yt),(Yt=((T=(J=mt(e,r,L,T+=Yt)).bytesOffset)+r)%4)&&(Yt=4-Yt),T+=Yt;n[v]={vertexPackage:le,arrIndexPackage:w,cartographicBounds:k}}else if(_==dt&&3==u){var X=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT;var Q=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT,le.minVerticesValue=new E.a,le.minTexCoordValue=[new o.r,new o.r],le.texCoordCompressConstant=[new B.a,new B.a];w=[];for(var z=0;z<Q;z++){var j=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT,le.vertCompressConstant=e.getFloat32(T+r,!0),T+=Float32Array.BYTES_PER_ELEMENT,le.minVerticesValue.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,le.minVerticesValue.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,le.minVerticesValue.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT;var q=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var $=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var tt=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var et=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var rt=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var at=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var nt=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT;var At=e.getFloat64(T+r,!0);T+=Float64Array.BYTES_PER_ELEMENT,le.minTexCoordValue[0].x=tt,le.minTexCoordValue[0].y=et,le.minTexCoordValue[1].x=nt,le.minTexCoordValue[1].y=At,le.texCoordCompressConstant[0].x=q,le.texCoordCompressConstant[0].y=$,le.texCoordCompressConstant[1].x=rt,le.texCoordCompressConstant[1].y=at;var Bt=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;k=new Object;for(var Et=0;Et<Bt;Et++){var it=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;var Ct=it,st=0;Ct!=ee&&Ct!=re||(st=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT);var yt=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;var ct=new Uint8Array(e.buffer,T+r,yt);(Yt=((T+=Uint8Array.BYTES_PER_ELEMENT*yt)+r)%4)&&(Yt=4-Yt),T+=Yt,Ae(j,Ct,st,ct,le,X,p,P,k,g)}T=(Vt=mt(e,r,L,T)).bytesOffset,le.customVertexAttribute=JSON.parse(Vt.string);var lt="aCustom"+le.customVertexAttribute.TextureCoordMatrix,gt="aCustom"+le.customVertexAttribute.VertexWeight,Ft="aCustom"+le.customVertexAttribute.VertexWeight_1;a.t(le.attrLocation[lt])&&(le.attrLocation.aTextureCoordMatrix=le.attrLocation[lt],z===Q-1&&delete le.attrLocation[lt]),a.t(le.attrLocation[gt])&&(le.attrLocation.aVertexWeight=le.attrLocation[gt],z===Q-1&&delete le.attrLocation[gt]),a.t(le.attrLocation[Ft])&&(le.attrLocation.aVertexWeight_1=le.attrLocation[Ft],z===Q-1&&delete le.attrLocation[Ft]);for(var vt=(Xt=Object.keys(le.attrLocation)).length,It=0;It<vt;++It){-1!==(Qt=Xt[It]).indexOf("aCustom")&&delete le.attrLocation[Qt]}(Yt=(T+r)%4)&&(Yt=4-Yt),T+=Yt;var Mt=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;for(var _t=0;_t<Mt;_t++){V={};var St=e.getInt32(T+r,!0);if(T+=Int32Array.BYTES_PER_ELEMENT,St>0){var Ot=e.getInt8(T+r,!0);T+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(T+r,!0),T+=Int8Array.BYTES_PER_ELEMENT;var Nt=e.getInt8(T+r,!0);T+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(T+r,!0),T+=Int8Array.BYTES_PER_ELEMENT;var ht,Rt,Gt=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT,13!==Nt?(ht=new Uint8Array(e.buffer,T+r,Gt),T+=Uint8Array.BYTES_PER_ELEMENT*Gt):(ht=new Uint32Array(e.buffer,T+r,Gt),T+=Uint32Array.BYTES_PER_ELEMENT*Gt),(Yt=(T+r)%4)&&(Yt=4-Yt),T+=Yt,13!==Nt?(Rt=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,St*Uint32Array.BYTES_PER_ELEMENT),ot.decodeIndexBuffer(Rt,St,Uint32Array.BYTES_PER_ELEMENT,ht)):Rt=ht;var bt,xt=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT,V.indexType=Ot,0===Ot?bt=new Uint16Array(St):1===Ot&&(bt=new Uint32Array(St)),V.indicesCount=St;var Ut=new Uint32Array(Rt.buffer,Rt.byteOffset,Rt.byteLength/4);bt.set(Ut,0),V.indicesTypedArray=bt,V.primitiveType=Nt;for(Et=0;Et<xt;Et++){var Kt;Y=(Kt=mt(e,r,L,T)).string;T=Kt.bytesOffset,V.materialCode=Y}if(w.length>0&&13!==Nt){var Ht=le.preVertexCount;V.indicesTypedArray=Ut.map((function(t){return t+Ht})),V.indexType=1}w.push(V),(Yt=(T+r)%4)&&(Yt=4-Yt),T+=Yt}}}le.nCompressOptions=X,2===w.length&&13===w[1].primitiveType&&w[1].indicesCount>=3&&(wt=c._0x5d8d50.createEdgeDataByIndices(le,w[1],s)),a.t(k.min)&&a.t(k.max)||(k=void 0),n[v]={vertexPackage:le,arrIndexPackage:w,edgeGeometry:wt,cartographicBounds:k}}else{var wt;if(_===Pt||_===ft)T=(Kt=Wt(u,L,e,r,T,le,i,g,P,p)).bytesOffset,k=Kt.cartographicBounds;else if(_===dt&&(T=(Kt=kt(L,e,r,T,le,i,g,P,p)).bytesOffset,k=Kt.cartographicBounds,3==u)){var Vt;T=(Vt=mt(e,r,L,T)).bytesOffset,le.customVertexAttribute=JSON.parse(Vt.string);var Yt;lt="aCustom"+le.customVertexAttribute.TextureCoordMatrix,gt="aCustom"+le.customVertexAttribute.VertexWeight,Ft="aCustom"+le.customVertexAttribute.VertexWeight_1;a.t(le.attrLocation[lt])&&(le.attrLocation.aTextureCoordMatrix=le.attrLocation[lt],delete le.attrLocation[lt]),a.t(le.attrLocation[gt])&&(le.attrLocation.aVertexWeight=le.attrLocation[gt],delete le.attrLocation[gt]),a.t(le.attrLocation[Ft])&&(le.attrLocation.aVertexWeight_1=le.attrLocation[Ft],delete le.attrLocation[Ft]);var Xt;for(vt=(Xt=Object.keys(le.attrLocation)).length,It=0;It<vt;++It){var Qt;-1!==(Qt=Xt[It]).indexOf("aCustom")&&delete le.attrLocation[Qt]}(Yt=(T+r)%4)&&(Yt=4-Yt),T+=Yt}Zt(w=(Kt=Jt(u,L,e,r,T)).arrIndexPackage)&&(le.clampRegionEdge=!0),2===w.length&&13===w[1].primitiveType&&w[1].indicesCount>=3&&(wt=c._0x5d8d50.createEdgeDataByIndices(le,w[1],s)),T=Kt.bytesOffset,a.t(k)&&a.t(k.min)&&a.t(k.max)||(k=void 0),n[v]={vertexPackage:le,arrIndexPackage:w,edgeGeometry:wt,cartographicBounds:k}}if(3!==u&&a.t(y)&&y){var zt=e.getUint16(T+r,!0);if(T+=Uint16Array.BYTES_PER_ELEMENT,1===zt){var jt=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT;var qt,$t=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT,e.getFloat32(T+r,!0),T+=Float32Array.BYTES_PER_ELEMENT;var te=new Array(jt),ae=new Array(jt),ne=new Array(jt),oe=new Array(jt);for(qt=0;qt<jt;qt++){var Be=e.getFloat32(T+r,!0);T+=Float32Array.BYTES_PER_ELEMENT,te[qt]=Be;var Ee=e.getUint16(T+r,!0);T+=Uint16Array.BYTES_PER_ELEMENT,ae[qt]=Ee;var ie=e.getUint16(T+r,!0);T+=Uint16Array.BYTES_PER_ELEMENT,ne[qt]=ie;for(var Ce=ie*$t,se=new Array(Ce),ye=0;ye<Ce;ye++){lt=e.getFloat32(T+r,!0);T+=Float32Array.BYTES_PER_ELEMENT,se[ye]=lt}oe[qt]=se}}var ce=new B.a,ue=new B.a;ce.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,ce.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,ce.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,ue.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,ue.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,ue.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,n[v].min=ce,n[v].max=ue;var le=n[v].vertexPackage;a.t(le.instanceBuffer)&&2===u&&(le.instanceBounds=new Float32Array(6),B.a.pack(ce,le.instanceBounds,0),B.a.pack(ue,le.instanceBounds,3))}if(3===u){var fe=new B.a;fe.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,fe.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,fe.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT;var Pe=new B.a;Pe.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,Pe.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,Pe.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT;var de=new B.a;de.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,de.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,de.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT;var pe=new B.a;pe.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,pe.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,pe.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT}}}function ne(t,e,r){var a=t.typedArray,n=new r(a.length+e.length);n.set(a,0),n.set(e,a.length),t.typedArray=n}function Ae(t,e,r,n,o,E,i,s,y,c){var u,l=0,P=o.vertexAttributes,d=o.attrLocation;switch(e){case zt:case jt:case qt:l=2*Uint16Array.BYTES_PER_ELEMENT,0!=(16&E)||e!==jt&&e!==qt||(l=2*Float32Array.BYTES_PER_ELEMENT),u=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*l);break;case $t:case te:l=4*Uint8Array.BYTES_PER_ELEMENT,u=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,4*t);break;case ee:case re:l=Float32Array.BYTES_PER_ELEMENT*r,u=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*r*4);break;default:l=4*Uint16Array.BYTES_PER_ELEMENT,u=C.ComponentDatatype.createTypedArray(C.ComponentDatatype.UNSIGNED_BYTE,t*l)}switch(ot.decodeVertexBuffer(u,t,l,n,n.length),e){case Qt:var p=new Uint16Array(u.buffer,0,u.length/2),L=C.ComponentDatatype.SHORT;if(a.t(i)){var T=B.a.unpackArray(p);for(let t=0,e=T.length;t<e;t++){let e=T[t];B.a.multiplyByScalar(e,o.vertCompressConstant,e),B.a.add(e,o.minVerticesValue,e)}var D=A.c.multiply(i.sphereMatrix,i.geoMatrix,xt),g=A.c.multiply(i.ellipsoidMatrix,i.geoMatrix,Ut);A.c.inverse(g,g);var F=new f.n(6378137,6378137,6378137);for(let t=0,e=T.length;t<e;t++){let e=T[t];A.c.multiplyByPoint(D,e,Gt);let r=F.cartesianToCartographic(Gt,bt),a=Rt(r.longitude,r.latitude,r.height,Gt);A.c.multiplyByPoint(g,a,e)}var v=new Array(3*T.length);B.a.packArray(T,v),p=new Float32Array(v),L=C.ComponentDatatype.FLOAT}if(void 0!==(R=d.aPosition)?(ne(P[R],p,Uint16Array),o.preVertexCount=o.verticesCount,o.verticesCount+=t):(d.aPosition=P.length,P.push({index:d.aPosition,typedArray:p,componentsPerAttribute:4,componentDatatype:L,offsetInBytes:0,strideInBytes:0,normalize:!1}),o.verticesCount=t),!a.t(i)&&s){var I=new B.i,M=new B.i,m=new Float32Array(2*t),_=new Float64Array(2*t),S=new B.a,O=new B.a,N=new B.i;F=Xt?new f.n(6378137,6378137,6356752.314245179):new f.n(6378137,6378137,6378137);for(var h=0;h<t;h++)A.c.multiplyByPoint(c,B.a.fromElements(p[4*h]*o.vertCompressConstant+o.minVerticesValue.x,p[4*h+1]*o.vertCompressConstant+o.minVerticesValue.y,p[4*h+2]*o.vertCompressConstant+o.minVerticesValue.z,S),O),N=F.cartesianToCartographic(O,bt),_[2*h]=N.longitude,_[2*h+1]=N.latitude,0===h?(I.longitude=N.longitude,I.latitude=N.latitude,M.longitude=N.longitude,M.latitude=N.latitude):(I.longitude=Math.max(N.longitude,I.longitude),I.latitude=Math.max(N.latitude,I.latitude),M.longitude=Math.min(N.longitude,M.longitude),M.latitude=Math.min(N.latitude,M.latitude));for(h=0;h<t;h++)m[2*h]=_[2*h]-M.longitude,m[2*h+1]=_[2*h+1]-M.latitude;d.img=P.length,P.push({index:d.img,typedArray:m,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),y.max=I,y.min=M}break;case zt:var R=d.aNormal,G=new Int16Array(u.buffer,0,u.length/2);void 0!==R?ne(P[R],G,Uint16Array):(d.aNormal=P.length,P.push({index:d.aNormal,typedArray:G,componentsPerAttribute:2,componentDatatype:C.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case jt:var b=new Uint16Array(u.buffer,0,u.length/2),x=(R=d.aTexCoord0,L=C.ComponentDatatype.SHORT,Uint16Array);0==(16&E)&&(L=C.ComponentDatatype.FLOAT,x=Float32Array,b=new Float32Array(u.buffer,0,u.length/4)),void 0!==R?ne(P[R],b,x):(d.aTexCoord0=P.length,P.push({index:d.aTexCoord0,typedArray:b,componentsPerAttribute:2,componentDatatype:L,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case qt:b=new Uint16Array(u.buffer,0,u.length/2),R=d.aTexCoord1,L=C.ComponentDatatype.SHORT,x=Uint16Array;0==(16&E)&&(L=C.ComponentDatatype.FLOAT,x=Float32Array,b=new Float32Array(u.buffer,0,u.length/4)),void 0!==R?ne(P[R],b,x):(d.aTexCoord1=P.length,P.push({index:d.aTexCoord1,typedArray:b,componentsPerAttribute:2,componentDatatype:L,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case $t:void 0!==(R=d.aColor)?ne(P[R],u,Uint8Array):(d.aColor=P.length,P.push({index:d.aColor,typedArray:u,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:0,normalize:!0}));break;case te:void 0!==(R=d.aSecondColor)?ne(P[R],u,Uint8Array):(d.aSecondColor=P.length,P.push({index:d.aSecondColor,typedArray:u,componentsPerAttribute:4,componentDatatype:C.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case ee:b=new Float32Array(u.buffer,0,u.length/4);void 0!==(R=d.aCustom0||d.aVertexWeight)?ne(P[R],b,Float32Array):(d.aCustom0=P.length,P.push({index:d.aCustom0,typedArray:b,componentsPerAttribute:r,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case re:var U=new Float32Array(u.buffer,0,u.length/4);void 0!==(R=d.aCustom1||d.aTextureCoordMatrix)?ne(P[R],U,Float32Array):(d.aCustom1=P.length,P.push({index:d.aCustom1,typedArray:U,componentsPerAttribute:r,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}))}}function oe(t,e,r,n,o){var B={},E=[],i=new A.c,C=t;o=a.e(o,{});for(var s=0;s<16;s++)i[s]=e.getFloat64(r+n,!0),r+=Float64Array.BYTES_PER_ELEMENT;B.matrix=i,B.skeletonNames=E;var y=e.getUint32(r+n,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var c=0;c<y;c++){var u=mt(e,n,C,r),l=u.string;r=u.bytesOffset,E.push(l),o[l]=i}return{byteOffset:r,geode:B}}function Be(t){var e=t.indexOf("Geometry");if(-1===e)return t;var r=t.substring(e,t.length);return t.replace(r,"")}function Ee(t,e,r,n,o,E,i,C){var s={},c=r.getFloat32(n+o,!0);n+=Float32Array.BYTES_PER_ELEMENT;var u=r.getUint16(n+o,!0);n+=Uint16Array.BYTES_PER_ELEMENT,s.rangeMode=u,s.rangeList=c;var l=new B.a;l.x=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,l.y=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,l.z=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT;var P=r.getFloat64(n+o,!0);if(n+=Float64Array.BYTES_PER_ELEMENT,a.t(i)){var d=A.c.clone(i.sphereMatrix,xt),p=A.c.clone(i.ellipsoidMatrix,Ut);A.c.inverse(p,p);var L=new f.n(6378137,6378137,6378137);A.c.multiplyByPoint(d,l,Gt);let t=L.cartesianToCartographic(Gt,bt),e=Rt(t.longitude,t.latitude,t.height,Gt);A.c.multiplyByPoint(p,e,l)}if(s.boundingSphere=new y.c(l,P),3===t){var T=new B.a;T.x=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,T.y=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,T.z=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT;var D=new B.a;D.x=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,D.y=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,D.z=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT;var g=new B.a;g.x=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,g.y=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,g.z=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT;var F=new B.a;F.x=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,F.y=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,F.z=r.getFloat64(n+o,!0),n+=Float64Array.BYTES_PER_ELEMENT,s._obb={xExtent:D,yExtent:g,zExtent:F,obbCenter:T}}var v=e,I=(_=mt(r,o,v,n)).string;n=_.bytesOffset,I=Be(I=(I=I.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")).replace(/\\/gi,"/")),s.childTile=I,s.geodes=[];var M=r.getUint32(n+o,!0);n+=Uint32Array.BYTES_PER_ELEMENT;for(var m=0;m<M;m++){var _;n=(_=oe(e,r,n,o,E)).byteOffset,s.geodes.push(_.geode)}return 3===t&&(n=(_=mt(r,o,v,n)).bytesOffset),{pageLOD:s,bytesOffset:n}}function ie(t,e,r,a,n,A,o){var B=0,E={},i=[],C=r.getUint32(B+a,!0);B+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<C;s++){var y=Ee(t,e,r,B,a,n,A);B=y.bytesOffset,i.push(y.pageLOD)}return E.pageLods=i,E}function Ce(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n["aTextureBatchId"+r]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0})}function se(t,e,r,n){for(var A=r.length,o=0;o<A;o++)for(var B=r[o],E=B.subName.split("_")[0],i=B.subVertexOffsetArr,C=0;C<i.length;C++){var s=i[C],y=s.geoName,c=s.offset,u=s.count,l=s.texUnitIndex,f=e[y].vertexPackage.verticesCount,P=n[y];a.t(P)||(P=n[y]={});var d=P[l];a.t(d)||(d=P[l]=new Float32Array(f),L.d(d,-1));var p=a.t(t)?t[E]:o;L.d(d,p,c,c+u)}}function ye(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n[1===r?"instanceId":"batchId"]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:C.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:r})}function ce(t,e,r,n,A){var o=0,B=t,E=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var i=0;i<E;i++){var C=mt(e,r,B,o),s=C.string;o=C.bytesOffset;var y=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var u={};n[s].pickInfo=u;var l=n[s].edgeGeometry;if(-1==n[s].vertexPackage.instanceIndex){for(var f=new Float32Array(n[s].vertexPackage.verticesCount),P=0;P<y;P++){var d=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=0,D=0;u[d]={batchId:P};for(var g=0;g<p;g++)D=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,T=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,L.d(f,P,D,D+T);u[d].vertexColorOffset=D,u[d].vertexCount=T}ye(n[s].vertexPackage,f,void 0)}else{var F=n[s].vertexPackage.instanceCount;n[s].vertexPackage.instanceBuffer,n[s].vertexPackage.instanceMode;var v=new Float32Array(F),I=0;for(P=0;P<y;P++){var M=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;p=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(g=0;g<p;g++){var m=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,v[I]=I,void 0===u[M]&&(u[M]={vertexColorCount:1,instanceIds:[],vertexColorOffset:I}),u[M].instanceIds.push(m),I++,3===A){T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT}}}ye(n[s].vertexPackage,v,1)}l=n[s].edgeGeometry;if(a.t(l)){var _,S,O=l.regular.instancesData,N=c._0x5d8d50.RegularInstanceStride;if(a.t(O))for(S=O.length,_=0;_<S;_+=N){var h=O[_+9];O[_+9]=f[h]}var R=l.silhouette.instancesData;if(N=c._0x5d8d50.SilhouetteInstanceStride,a.t(R))for(S=R.length,_=0;_<S;_+=N){h=R[_+12];R[_+12]=f[h]}}}}function ue(t){return t<1e-10&&t>-1e-10}function le(t,e,r,n,A,o,B,E){var i=new DataView(t),C=new Uint8Array(t),y=i.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var c=s.c(C,r,y);c=c.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,""),r+=y;var u=i.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var l=0;l<u;l++){var f={},P=i.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;var L=i.getUint16(r,!0);r+=Uint16Array.BYTES_PER_ELEMENT,f.rangeMode=L,f.rangeList=P;var T={};T.x=i.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,T.y=i.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,T.z=i.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT;var D=i.getFloat64(r,!0);r+=Float64Array.BYTES_PER_ELEMENT,f.boundingSphere={center:T,radius:D},y=i.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var g=s.c(C,r,y);r+=y,g=Be(g=g.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),f.childTile=g}var F={},v=i.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;v>=3&&(i.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),i.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var I=i.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var M=new Uint8Array(t,r,I),m=r+I,_=p.pako.inflate(M).buffer;E.push(_),i=new DataView(_);C=new Uint8Array(_);r=0;var S=i.getUint32(r,!0),O=Mt(i,_,r+=Uint32Array.BYTES_PER_ELEMENT),N=O.buffer;r=O.byteOffset;var h=ie(v,N,i,O.dataViewByteOffset),R=r%4;0!==R&&(r+=4-R),ae((O=Mt(i,_,r)).buffer,i,O.dataViewByteOffset,F,!1,void 0,void 0,v),r=O.byteOffset,3!==v&&((O=Mt(i,_,r)).buffer,r=O.byteOffset);var G={};!function(t,e,r,n,A,o,B,E,i,C){var y=E,c=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var u={},l=0;l<c;l++){var f=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var P=s.c(o,y-E,f),p=(y+=f)%4;0!==p&&(y+=4-p),B.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var L=B.getUint8(y,!0);y+=Uint8Array.BYTES_PER_ELEMENT;var T=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var D=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var g=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var F=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var v,I=B.getUint32(y,!0);if(y+=Uint32Array.BYTES_PER_ELEMENT,n&&L){var M=y-E;v=o.subarray(M,M+F),y+=F}var m=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var _=[],S=0;S<m;S++){f=B.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var O=s.c(o,y-E,f);y+=f,_.push(O),r[O]=P}var N=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var h=[];for(S=0;S<N;S++){f=B.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var R=s.c(o,y-E,f);y+=f,h.push(R)}var G=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var b=[],x=void 0,U=P;if(n)x=e[P]={};else{var K=r[P];for(U=K;a.t(K)&&K!==P;)U=K,K=r[K];a.t(U)&&(x=e[U])}var H=0;for(S=0;S<G;S++){f=B.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var w=s.c(o,y-E,f);if(y+=f,n){var V=w.split("_")[0];a.t(x[V])?H++:x[V]=S-H}var Y=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var J=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var k=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var W=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var Z=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var X=[],Q=0;Q<Z;Q++){f=B.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var z=s.c(o,y-E,f);y+=f;var j=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var q=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var $=B.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT,X.push({geoName:z,offset:j,count:q,texUnitIndex:$})}b.push({subName:w,offsetX:Y,offsetY:J,width:k,height:W,subVertexOffsetArr:X})}se(x,t,b,u);var tt=!1;a.t(v)&&g===d.S3MPixelFormat.CRN_DXT5&&yt&&(v=ct({data:v},C).bufferView,tt=!0),i[P]={id:P,rootTextureName:U,width:T,height:D,compressType:g,size:F,format:I,textureData:v,subTexInfos:b,requestNames:h,isDXT:tt}}for(var z in u)if(u.hasOwnProperty(z)){var et=t[z].vertexPackage,rt=u[z];for(var $ in rt)rt.hasOwnProperty($)&&Ce(et,rt[$],$)}}(F,n,A,o,0,(O=Mt(i,_,r)).buffer,i,O.dataViewByteOffset,G,E),r=O.byteOffset;var b=i.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var x=C.subarray(r,r+b),U=s.c(x);r+=b;var K=JSON.parse(U);(3===v&&(S=i.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),(S&lt)==lt)&&(ce((O=Mt(i,_,r)).buffer,i,O.dataViewByteOffset,F,v),r=O.byteOffset);if(1==v){var H=h.pageLods,w=!0;for(l=0;l<H.length;l++){var V=H[l];w=""===V.childTile;for(var Y=V.geodes,J=0;J<Y.length;J++)for(var k=Y[J].skeletonNames,W=0;W<k.length;W++){var Z=k[W];if(w){var Q=F[Z].vertexPackage;Q.boundingSphere=X.calcBoundingSphereInWorker(1,Q)}}}}B[c]={result:!0,groupNode:h,geoPackage:F,matrials:K,texturePackage:G,version:At.S3M4,dataVersion:v,rootBatchIdMap:n,ancestorMap:A},m<e&&le(t,e,m,n,A,!1,B,E)}function fe(e,r){var n=e.buffer;if(Xt=e.ellipsoid,e.isOSGB){if(a.t(gt)||new Promise((function(e,r){t(["./OSGBToS3M-caf1aa52"],e,r)})).then((function(t){(gt=t.default).onRuntimeInitialized=function(){Ft=!0},vt=gt.cwrap("OSGBToS3MB","number",["number","number","number","number"])})),!Ft)return null;var A;switch(e.suffix){case"dae":case"DAE":A=4;break;case"x":case"X":A=2;break;default:A=0}n=function(t,e,r){var a=gt._malloc(20*e),n=gt._malloc(Uint8Array.BYTES_PER_ELEMENT*e);gt.HEAPU8.set(t,n/Uint8Array.BYTES_PER_ELEMENT);var A=vt(n,e,a,r),o=new Uint8Array(gt.HEAPU8.buffer,a,A);return t=null,t=new Uint8Array(o).buffer,gt._free(a),gt._free(n),t}(new Uint8Array(n),n.byteLength,A)}var o=e.isS3MZ,B=e.fileType,E=e.supportCompressType,i=e.bVolume,C=e.isS3MBlock,y=e.modelMatrix,u=e.materialType,l=e.isCoverImageryLayer,f=e.transformPar,P=null,L=null,T=null;if(i&&e.volbuffer.byteLength<8&&(i=!1),i){var D=e.volbuffer,g=new Uint8Array(D,8),F=p.pako.inflate(g).buffer,v=new Float64Array(F,0,1),I=new Uint32Array(F,48,1);if(0===v[0]||3200===I[0]||3201===I[0]){var M=0;0==v[0]&&(M=8),r.push(F);var m=new Float64Array(F,M,6),_=m[0],S=m[1],O=m[2],N=m[3],h=m[4]<m[5]?m[4]:m[5],R=m[4]>m[5]?m[4]:m[5];L={left:_,top:S,right:O,bottom:N,minHeight:h,maxHeight:R,width:(P=new It(_,N,O,S,h,R)).width,length:P.length,height:P.height};var G=new Uint32Array(F,48+M,7),b=G[0],x=G[1],U=G[2],K=G[3];T={nFormat:b,nSideBlockCount:x,nBlockLength:U,nLength:K,nWidth:G[4],nHeight:G[5],nDepth:G[6],imageArray:new Uint8Array(F,76+M,K*K*4)}}}var H=0,w={};w.ignoreNormal=e.ignoreNormal;var V=e.rootBatchIdMap||{},Y=e.ancestorMap||{},J={},k=new DataView(n),W=k.getFloat32(H,!0);if(H+=Float32Array.BYTES_PER_ELEMENT,C)return k.getUint32(H,!0),H+=Uint32Array.BYTES_PER_ELEMENT,le(n,n.byteLength,H,V,Y,e.isRoot,J,r),J;var Z=!1;if(W>=3&&(k.getUint32(H,!0),H+=Uint32Array.BYTES_PER_ELEMENT),W>=2&&(k.getUint32(H,!0),H+=Uint32Array.BYTES_PER_ELEMENT),ue(W-1)||ue(W-2)||ue(W-3)||W>2.09&&W<2.11){var Q=k.getUint32(H,!0);H+=Uint32Array.BYTES_PER_ELEMENT;var z=new Uint8Array(n,H,Q);n=p.pako.inflate(z).buffer,r.push(n),k=new DataView(n),H=0}else if(W>1.199&&W<1.201){Q=k.getUint32(H,!0);H+=Uint32Array.BYTES_PER_ELEMENT,r.push(n)}else{Z=!0,H=0;Q=k.getInt32(H,!0);if(H+=Int32Array.BYTES_PER_ELEMENT,H+=Uint8Array.BYTES_PER_ELEMENT*Q,o){k.getUint32(H,!0),H+=Uint32Array.BYTES_PER_ELEMENT;g=new Uint8Array(n,H);n=p.pako.inflate(g).buffer,r.push(n),k=new DataView(n),H=0}}var j=k.getUint32(H,!0),q=Mt(k,n,H+=Uint32Array.BYTES_PER_ELEMENT),$=q.buffer;H=q.byteOffset;var tt={},et=ie(W,$,k,q.dataViewByteOffset,tt,f),rt=H%4;0!==rt&&(H+=4-rt);var at=W>2.09&&3!==W;if(ae((q=Mt(k,n,H)).buffer,k,q.dataViewByteOffset,w,Z,r,at,W,y,tt,l,e.fileType,f),H=q.byteOffset,at)for(var nt=0;nt<et.pageLods.length;nt++)for(var ot=et.pageLods[nt],Bt=ot.geodes,Et=0;Et<Bt.length;Et++)for(var it=Bt[Et].skeletonNames,Ct=0;Ct<it.length;Ct++){var st=it[Ct];a.t(w[st].max)&&(a.t(ot.max)?(ot.max.x=Math.max(w[st].max.x,ot.max.x),ot.max.y=Math.max(w[st].max.y,ot.max.y),ot.max.z=Math.max(w[st].max.z,ot.max.z),ot.min.x=Math.min(w[st].min.x,ot.min.x),ot.min.y=Math.min(w[st].min.y,ot.min.y),ot.min.z=Math.min(w[st].min.z,ot.min.z)):(ot.max=w[st].max,ot.min=w[st].min))}3!==W&&((q=Mt(k,n,H)).buffer,H=q.byteOffset);var yt={};!function(t,e,r,a,n,A){var o=0,B=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<B;E++){var i=mt(r,a,e,o),C=i.string,s=(o=i.bytesOffset)%4;0!==s&&(o+=4-s);var y=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var u=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var P=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var L=e.subarray(o,o+P);o+=P;var T=null,D=f;f===c.S3MCompressType.enrS3TCDXTN&&1!=t?(c.d.decode(T,u,l,L,p),p>d.S3MPixelFormat.BGR||p===d.S3MPixelFormat.LUMINANCE_ALPHA?(T=new Uint8Array(u*l*4),L=new Uint8Array(e.buffer,L.byteOffset,u*l)):(T=new Uint16Array(u*l),L=new Uint16Array(e.buffer,L.byteOffset,P/2)),c.d.decode(T,u,l,L,p),A.push(T.buffer),f=0):T=L,n[C]={id:C,width:u,height:l,compressType:f,oriCompressType:D,nFormat:p,imageBuffer:T,mipmapLevel:y}}}(E,(q=Mt(k,n,H)).buffer,k,q.dataViewByteOffset,yt,r),H=q.byteOffset;var ct=k.getUint32(H,!0);H+=Uint32Array.BYTES_PER_ELEMENT;var ut=new Uint8Array(n).subarray(H,H+ct),ft=s.c(ut);H+=ct,ft=ft.replace(/\n\0/,"");var Pt=JSON.parse(ft);(3===W&&(j=k.getUint32(H,!0),H+=Uint32Array.BYTES_PER_ELEMENT),(j&lt)==lt)&&ce((q=Mt(k,n,H)).buffer,k,q.dataViewByteOffset,w,W);if(1==W){var dt=et.pageLods,pt=!0;for(nt=0;nt<dt.length;nt++){var Lt=dt[nt];pt=""===Lt.childTile;for(var Tt=Lt.geodes,Dt=0;Dt<Tt.length;Dt++){it=Tt[Dt].skeletonNames;for(var _t=0;_t<it.length;_t++){var St=it[_t];if(pt){var Ot=w[St].vertexPackage;Ot.boundingSphere=X.calcBoundingSphereInWorker(B,Ot)}}}}}return"BatchPBR"===u&&function(t,e,r){for(var n in delete t.ignoreNormal,t)if(t.hasOwnProperty(n)){var A=t[n],o=A.arrIndexPackage;if(o.length<1)continue;if(1===o.length||2===o.length&&13===o[1].primitiveType){var B=A.vertexPackage.attrLocation.aTextureCoordMatrix;if(void 0!==B){if((E=(O=A.vertexPackage.vertexAttributes[B]).typedArray)[0]<0)continue}else if(void 0!==(B=A.vertexPackage.attrLocation.aTextureCoordMatrix||A.vertexPackage.attrLocation.aTexCoord0)){O=A.vertexPackage.vertexAttributes[B];var E=new Float32Array(O.typedArray.buffer,O.typedArray.byteOffset,O.typedArray.byteLength/4);if(3===O.componentsPerAttribute&&E[2]<0)continue}}var i,C,s=0,y={},u=void 0;for(i=0,C=o.length;i<C;i++)13!==(N=o[i]).primitiveType?s+=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4:u=o[i],0===i&&(y.indicesCount=0,y.indexType=o[i].indexType,y.primitiveType=o[i].primitiveType,y.materialCode=o[i].materialCode);y.indicesCount=s;var l=A.vertexPackage.verticesCount>65535?new Uint32Array(s):new Uint16Array(s),f=0;for(i=0,C=o.length;i<C;i++)if(13!==(N=o[i]).primitiveType){var P=0===N.indexType?Uint16Array:Uint32Array,d=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4,p=new P(N.indicesTypedArray.buffer,N.indicesTypedArray.byteOffset,d);l.set(p,f),f+=p.length}y.indicesTypedArray=l,A.arrIndexPackage=[y],a.t(u)&&(A.arrIndexPackage.push(u),A.edgeGeometry=c._0x5d8d50.createEdgeDataByIndices(A.vertexPackage,u));var L=2*o.length*4,T=new Float32Array(L),D={};for(i=0,C=r.material.length;i<C;i++)D[(h=r.material[i].material).id]=h;for(i=0,C=o.length;i<C;i++)if(h=D[(N=o[i]).materialCode]){var g=h.pbrMetallicRoughness;if(g){T[8*i]=g.metallicFactor,T[8*i+1]=g.roughnessFactor,T[8*i+2]=h.alphaCutoff;var F=""===h.alphaMode?0:1,v="none"===h.cullMode?0:1;T[8*i+3]=v|F<<16,T[8*i+4]=g.emissiveFactor.x,T[8*i+5]=g.emissiveFactor.y,T[8*i+6]=g.emissiveFactor.z,T[8*i+7]=0,h.pbrIndex=i}}var I="PBRMaterialParam_"+n;for(i=0,C=r.material.length;i<C;i++)if((h=r.material[i].material).id===y.materialCode){h.textureunitstates.push({textureunitstate:{addressmode:{u:0,v:0,w:0},filteringoption:0,filtermax:2,filtermin:2,id:I,texmodmatrix:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],url:""}});break}var M,m,_=A.vertexPackage,S=_.attrLocation.aTexCoord1;if(void 0!==S){var O=_.vertexAttributes[S];M=new Float32Array(2*_.verticesCount),O.typedArray=M}else M=new Float32Array(2*_.verticesCount),S=_.vertexAttributes.length,_.attrLocation.aTexCoord1=S,_.vertexAttributes.push({index:S,typedArray:M,componentsPerAttribute:2,componentDatatype:5126,offsetInBytes:0,strideInBytes:8,normalize:!1});for(void 0!==(S=_.attrLocation.aColor)&&(m=(O=_.vertexAttributes[S]).typedArray),i=0,C=o.length;i<C;i++){var N,h;if((h=D[(N=o[i]).materialCode])&&h.pbrMetallicRoughness)for(var R=h.pbrMetallicRoughness.baseColor,G=void 0!==m,b=h.pbrIndex,x=(l=N.indicesTypedArray,0),U=(l=0===N.indexType?new Uint16Array(l.buffer,l.byteOffset,l.byteLength/2):new Uint32Array(l.buffer,l.byteOffset,l.byteLength/4)).length;x<U;x++){var K=l[x];M[2*K]=b,G&&(m[4*K]=255*R.x,m[4*K+1]=255*R.y,m[4*K+2]=255*R.z,m[4*K+3]=255*R.w)}}e[I]={id:I,width:2*o.length,height:1,compressType:0,nFormat:25,imageBuffer:T,mipmapLevel:0}}}(w,yt,Pt),{result:!0,groupNode:et,geoPackage:w,matrials:Pt,texturePackage:yt,version:At.S3M4,dataVersion:W,volImageBuffer:T,volBounds:L}}function Pe(){(a.t(Ct)||"undefined"==typeof WebAssembly)&&a.t(ut)&&(Ct.onRuntimeInitialized=function(){yt=!0},self.onmessage=e(fe),self.postMessage(!0))}return function(r){if("undefined"==typeof WebAssembly)return self.onmessage=e(fe),void self.postMessage(!0);var A=r.data.webAssemblyConfig;return a.t(A)?n.o.isInternetExplorer()?t([y.o("ThirdParty/Workers/ie-webworker-promise-polyfill.js")],(function(e){return self.Promise=e,-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){a.t(A.wasmBinaryFile)?(a.t(t)||(t=self.Module),Ct=t,Pe()):(Ct=t,Pe())})):t([A.modulePath],(function(t){a.t(A.wasmBinaryFile)?(a.t(t)||(t=self.DracoDecoderModule),t(A).then((function(t){ut=t,Pe()}))):(ut=t(),Pe())}))})):-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){a.t(A.wasmBinaryFile)?(a.t(t)||(t=self.Module),Ct=t,Pe()):(Ct=t,Pe())})):t([A.modulePath],(function(t){a.t(A.wasmBinaryFile)?(a.t(t)||(t=self.DracoDecoderModule),t(A).then((function(t){ut=t,Pe()}))):(ut=t(),Pe())})):void 0}}));
