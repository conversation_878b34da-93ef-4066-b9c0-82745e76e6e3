define(["exports","./Cartographic-1bbcab04","./when-515d5295","./Check-3aa71481","./Intersect-53434a77","./Math-5e38123d","./GeometryAttribute-9bc31a7f","./Rectangle-e170be8b"],(function(n,t,a,r,e,o,s,i){"use strict";var h=Math.cos,g=Math.sin,u=Math.sqrt,c={computePosition:function(n,t,r,e,o,s,i){var c=t.radiiSquared,l=n.nwCorner,C=n.boundingRectangle,S=l.latitude-n.granYCos*e+o*n.granXSin,w=h(S),d=g(S),X=c.z*d,Y=l.longitude+e*n.granYSin+o*n.granXCos,O=w*h(Y),v=w*g(Y),_=c.x*O,p=c.y*v,M=u(_*O+p*v+X*d);if(s.x=_/M,s.y=p/M,s.z=X/M,r){var f=n.stNwCorner;a.t(f)?(S=f.latitude-n.stGranYCos*e+o*n.stGranXSin,Y=f.longitude+e*n.stGranYSin+o*n.stGranXCos,i.x=(Y-n.stWest)*n.lonScalar,i.y=(S-n.stSouth)*n.latScalar):(i.x=(Y-C.west)*n.lonScalar,i.y=(S-C.south)*n.latScalar)}}},l=new s.u,C=new t.a,S=new t.i,w=new t.a,d=new e.s;function X(n,a,r,e,o,i,h){var g=Math.cos(a),u=e*g,c=r*g,S=Math.sin(a),X=e*S,Y=r*S;C=d.project(n,C),C=t.a.subtract(C,w,C);var O=s.u.fromRotation(a,l);C=s.u.multiplyByVector(O,C,C),C=t.a.add(C,w,C),i-=1,h-=1;var v=(n=d.unproject(C,n)).latitude,_=v+i*Y,p=v-u*h,M=v-u*h+i*Y,f=Math.max(v,_,p,M),R=Math.min(v,_,p,M),W=n.longitude,b=W+i*c,I=W+h*X,m=W+h*X+i*c;return{north:f,south:R,east:Math.max(W,b,I,m),west:Math.min(W,b,I,m),granYCos:u,granYSin:X,granXCos:c,granXSin:Y,nwCorner:n}}c.computeOptions=function(n,t,a,e,s,h,g){var u=n.east,c=n.west,l=n.north,C=n.south,Y=!1,O=!1;l===o.n.PI_OVER_TWO&&(Y=!0),C===-o.n.PI_OVER_TWO&&(O=!0);var v,_,p,M,f,R=l-C;p=(f=c>u?o.n.TWO_PI-c+u:u-c)/((v=Math.ceil(f/t)+1)-1),M=R/((_=Math.ceil(R/t)+1)-1);var W=i.s.northwest(n,h),b=i.s.center(n,S);(0!==a||0!==e)&&(b.longitude<W.longitude&&(b.longitude+=o.n.TWO_PI),w=d.project(b,w));var I=M,m=p,G=i.s.clone(n,s),P={granYCos:I,granYSin:0,granXCos:m,granXSin:0,nwCorner:W,boundingRectangle:G,width:v,height:_,northCap:Y,southCap:O};if(0!==a){var T=X(W,a,p,M,0,v,_);if(l=T.north,C=T.south,u=T.east,c=T.west,l<-o.n.PI_OVER_TWO||l>o.n.PI_OVER_TWO||C<-o.n.PI_OVER_TWO||C>o.n.PI_OVER_TWO)throw new r.t("Rotated rectangle is invalid.  It crosses over either the north or south pole.");P.granYCos=T.granYCos,P.granYSin=T.granYSin,P.granXCos=T.granXCos,P.granXSin=T.granXSin,G.north=l,G.south=C,G.east=u,G.west=c}if(0!==e){a-=e;var x=i.s.northwest(G,g),y=X(x,a,p,M,0,v,_);P.stGranYCos=y.granYCos,P.stGranXCos=y.granXCos,P.stGranYSin=y.granYSin,P.stGranXSin=y.granXSin,P.stNwCorner=x,P.stWest=y.west,P.stSouth=y.south}return P},n.W=c}));
