define(["exports","./Cartographic-1bbcab04","./when-515d5295","./Check-3aa71481","./buildModuleUrl-dba4ec07","./Math-5e38123d","./PrimitiveType-b38a4004"],(function(r,e,t,n,a,i,o){"use strict";var u={};function s(r,e,t){var n=r+e;return i.n.sign(r)!==i.n.sign(e)&&Math.abs(n/Math.max(Math.abs(r),Math.abs(e)))<t?0:n}u.computeDiscriminant=function(r,e,t){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");return e*e-4*r*t},u.computeRealRoots=function(r,e,t){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");var a;if(0===r)return 0===e?[]:[-t/e];if(0===e){if(0===t)return[0,0];var o=Math.abs(t),u=Math.abs(r);if(o<u&&o/u<i.n.EPSILON14)return[0,0];if(o>u&&u/o<i.n.EPSILON14)return[];if((a=-t/r)<0)return[];var f=Math.sqrt(a);return[-f,f]}if(0===t)return(a=-e/r)<0?[a,0]:[0,a];var c=s(e*e,-(4*r*t),i.n.EPSILON14);if(c<0)return[];var w=-.5*s(e,i.n.sign(e)*Math.sqrt(c),i.n.EPSILON14);return e>0?[w/r,t/w]:[t/w,w/r]};var f={};function c(r,e,t,n){var a,i,o=r,u=e/3,s=t/3,f=n,c=o*s,w=u*f,h=u*u,l=s*s,d=o*s-h,m=o*f-u*s,p=u*f-l,b=4*d*p-m*m;if(b<0){var g,v,q;h*w>=c*l?(g=o,v=d,q=-2*u*d+o*m):(g=f,v=p,q=-f*m+2*s*p);var M=-(q<0?-1:1)*Math.abs(g)*Math.sqrt(-b),y=(i=-q+M)/2,R=y<0?-Math.pow(-y,1/3):Math.pow(y,1/3),S=i===M?-R:-v/R;return a=v<=0?R+S:-q/(R*R+S*S+v),h*w>=c*l?[(a-u)/o]:[-f/(a+s)]}var O=d,P=-2*u*d+o*m,N=p,L=-f*m+2*s*p,E=Math.sqrt(b),I=Math.sqrt(3)/2,C=Math.abs(Math.atan2(o*E,-P)/3);a=2*Math.sqrt(-O);var x=Math.cos(C);i=a*x;var z=a*(-x/2-I*Math.sin(C)),T=i+z>2*u?i-u:z-u,U=o,W=T/U;C=Math.abs(Math.atan2(f*E,-L)/3);var B=-f,Z=(i=(a=2*Math.sqrt(-N))*(x=Math.cos(C)))+(z=a*(-x/2-I*Math.sin(C)))<2*s?i+s:z+s,V=B/Z,A=-T*Z-U*B,D=(s*A-u*(T*B))/(-u*A+s*(U*Z));return W<=D?W<=V?D<=V?[W,D,V]:[W,V,D]:[V,W,D]:W<=V?[D,W,V]:D<=V?[D,V,W]:[V,D,W]}f.computeDiscriminant=function(r,e,t,a){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");if("number"!=typeof a)throw new n.t("d is a required number.");var i=e*e,o=t*t;return 18*r*e*t*a+i*o-27*(r*r)*(a*a)-4*(r*o*t+i*e*a)},f.computeRealRoots=function(r,e,t,a){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");if("number"!=typeof a)throw new n.t("d is a required number.");var i,o;if(0===r)return u.computeRealRoots(e,t,a);if(0===e){if(0===t){if(0===a)return[0,0,0];var s=(o=-a/r)<0?-Math.pow(-o,1/3):Math.pow(o,1/3);return[s,s,s]}return 0===a?0===(i=u.computeRealRoots(r,0,t)).Length?[0]:[i[0],0,i[1]]:c(r,0,t,a)}return 0===t?0===a?(o=-e/r)<0?[o,0,0]:[0,0,o]:c(r,e,0,a):0===a?0===(i=u.computeRealRoots(r,e,t)).length?[0]:i[1]<=0?[i[0],i[1],0]:i[0]>=0?[0,i[0],i[1]]:[i[0],0,i[1]]:c(r,e,t,a)};var w={};function h(r,e,t,n){var a=r*r,o=e-3*a/8,s=t-e*r/2+a*r/8,c=n-t*r/4+e*a/16-3*a*a/256,w=f.computeRealRoots(1,2*o,o*o-4*c,-s*s);if(w.length>0){var h=-r/4,l=w[w.length-1];if(Math.abs(l)<i.n.EPSILON14){var d=u.computeRealRoots(1,o,c);if(2===d.length){var m,p=d[0],b=d[1];if(p>=0&&b>=0){var g=Math.sqrt(p),v=Math.sqrt(b);return[h-v,h-g,h+g,h+v]}if(p>=0&&b<0)return[h-(m=Math.sqrt(p)),h+m];if(p<0&&b>=0)return[h-(m=Math.sqrt(b)),h+m]}return[]}if(l>0){var q=Math.sqrt(l),M=(o+l-s/q)/2,y=(o+l+s/q)/2,R=u.computeRealRoots(1,q,M),S=u.computeRealRoots(1,-q,y);return 0!==R.length?(R[0]+=h,R[1]+=h,0!==S.length?(S[0]+=h,S[1]+=h,R[1]<=S[0]?[R[0],R[1],S[0],S[1]]:S[1]<=R[0]?[S[0],S[1],R[0],R[1]]:R[0]>=S[0]&&R[1]<=S[1]?[S[0],R[0],R[1],S[1]]:S[0]>=R[0]&&S[1]<=R[1]?[R[0],S[0],S[1],R[1]]:R[0]>S[0]&&R[0]<S[1]?[S[0],R[0],S[1],R[1]]:[R[0],S[0],R[1],S[1]]):R):0!==S.length?(S[0]+=h,S[1]+=h,S):[]}}return[]}function l(r,e,t,n){var a=r*r,o=-2*e,s=t*r+e*e-4*n,c=a*n-t*e*r+t*t,w=f.computeRealRoots(1,o,s,c);if(w.length>0){var h,l,d,m,p,b,g=w[0],v=e-g,q=v*v,M=r/2,y=v/2,R=q-4*n,S=q+4*Math.abs(n),O=a-4*g,P=a+4*Math.abs(g);if(g<0||R*P<O*S){var N=Math.sqrt(O);h=N/2,l=0===N?0:(r*y-t)/N}else{var L=Math.sqrt(R);h=0===L?0:(r*y-t)/L,l=L/2}0===M&&0===h?(d=0,m=0):i.n.sign(M)===i.n.sign(h)?m=g/(d=M+h):d=g/(m=M-h),0===y&&0===l?(p=0,b=0):i.n.sign(y)===i.n.sign(l)?b=n/(p=y+l):p=n/(b=y-l);var E=u.computeRealRoots(1,d,p),I=u.computeRealRoots(1,m,b);if(0!==E.length)return 0!==I.length?E[1]<=I[0]?[E[0],E[1],I[0],I[1]]:I[1]<=E[0]?[I[0],I[1],E[0],E[1]]:E[0]>=I[0]&&E[1]<=I[1]?[I[0],E[0],E[1],I[1]]:I[0]>=E[0]&&I[1]<=E[1]?[E[0],I[0],I[1],E[1]]:E[0]>I[0]&&E[0]<I[1]?[I[0],E[0],I[1],E[1]]:[E[0],I[0],E[1],I[1]]:E;if(0!==I.length)return I}return[]}function d(r,n){n=e.a.clone(t.e(n,e.a.ZERO)),e.a.equals(n,e.a.ZERO)||e.a.normalize(n,n),this.origin=e.a.clone(t.e(r,e.a.ZERO)),this.direction=n}w.computeDiscriminant=function(r,e,t,a,i){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");if("number"!=typeof a)throw new n.t("d is a required number.");if("number"!=typeof i)throw new n.t("e is a required number.");var o=r*r,u=e*e,s=u*e,f=t*t,c=f*t,w=a*a,h=w*a,l=i*i;return u*f*w-4*s*h-4*r*c*w+18*r*e*t*h-27*o*w*w+256*(o*r)*(l*i)+i*(18*s*t*a-4*u*c+16*r*f*f-80*r*e*f*a-6*r*u*w+144*o*t*w)+l*(144*r*u*t-27*u*u-128*o*f-192*o*e*a)},w.computeRealRoots=function(r,e,t,a,o){if("number"!=typeof r)throw new n.t("a is a required number.");if("number"!=typeof e)throw new n.t("b is a required number.");if("number"!=typeof t)throw new n.t("c is a required number.");if("number"!=typeof a)throw new n.t("d is a required number.");if("number"!=typeof o)throw new n.t("e is a required number.");if(Math.abs(r)<i.n.EPSILON15)return f.computeRealRoots(e,t,a,o);var u=e/r,s=t/r,c=a/r,w=o/r,d=u<0?1:0;switch(d+=s<0?d+1:d,d+=c<0?d+1:d,d+=w<0?d+1:d){case 0:case 3:case 4:case 6:case 7:case 9:case 10:case 12:case 13:case 14:case 15:return h(u,s,c,w);case 1:case 2:case 5:case 8:case 11:return l(u,s,c,w);default:return}},d.clone=function(r,n){if(t.t(r))return t.t(n)?(n.origin=e.a.clone(r.origin),n.direction=e.a.clone(r.direction),n):new d(r.origin,r.direction)},d.getPoint=function(r,a,i){return n.n.typeOf.object("ray",r),n.n.typeOf.number("t",a),t.t(i)||(i=new e.a),i=e.a.multiplyByScalar(r.direction,a,i),e.a.add(r.origin,i,i)};var m={rayPlane:function(r,a,o){if(!t.t(r))throw new n.t("ray is required.");if(!t.t(a))throw new n.t("plane is required.");t.t(o)||(o=new e.a);var u=r.origin,s=r.direction,f=a.normal,c=e.a.dot(f,s);if(!(Math.abs(c)<i.n.EPSILON15)){var w=(-a.distance-e.a.dot(f,u))/c;if(!(w<0))return o=e.a.multiplyByScalar(s,w,o),e.a.add(u,o,o)}}},p=new e.a,b=new e.a,g=new e.a,v=new e.a,q=new e.a;m.rayTriangleParametric=function(r,a,o,u,s){if(!t.t(r))throw new n.t("ray is required.");if(!t.t(a))throw new n.t("p0 is required.");if(!t.t(o))throw new n.t("p1 is required.");if(!t.t(u))throw new n.t("p2 is required.");s=t.e(s,!1);var f,c,w,h,l,d=r.origin,m=r.direction,M=e.a.subtract(o,a,p),y=e.a.subtract(u,a,b),R=e.a.cross(m,y,g),S=e.a.dot(M,R);if(s){if(S<i.n.EPSILON6||(f=e.a.subtract(d,a,v),(w=e.a.dot(f,R))<0||w>S)||(c=e.a.cross(f,M,q),(h=e.a.dot(m,c))<0||w+h>S))return;l=e.a.dot(y,c)/S}else{if(Math.abs(S)<i.n.EPSILON6)return;var O=1/S;if(f=e.a.subtract(d,a,v),(w=e.a.dot(f,R)*O)<0||w>1||(c=e.a.cross(f,M,q),(h=e.a.dot(m,c)*O)<0||w+h>1))return;l=e.a.dot(y,c)*O}return l},m.rayTriangle=function(r,n,a,i,o,u){var s=m.rayTriangleParametric(r,n,a,i,o);if(t.t(s)&&!(s<0))return t.t(u)||(u=new e.a),e.a.multiplyByScalar(r.direction,s,u),e.a.add(r.origin,u,u)};var M=new d;m.lineSegmentTriangle=function(r,a,i,o,u,s,f){if(!t.t(r))throw new n.t("v0 is required.");if(!t.t(a))throw new n.t("v1 is required.");if(!t.t(i))throw new n.t("p0 is required.");if(!t.t(o))throw new n.t("p1 is required.");if(!t.t(u))throw new n.t("p2 is required.");var c=M;e.a.clone(r,c.origin),e.a.subtract(a,r,c.direction),e.a.normalize(c.direction,c.direction);var w=m.rayTriangleParametric(c,i,o,u,s);if(!(!t.t(w)||w<0||w>e.a.distance(r,a)))return t.t(f)||(f=new e.a),e.a.multiplyByScalar(c.direction,w,f),e.a.add(c.origin,f,f)};var y={root0:0,root1:0};function R(r,n,i){t.t(i)||(i=new a.i);var o=r.origin,u=r.direction,s=n.center,f=n.radius*n.radius,c=e.a.subtract(o,s,g),w=function(r,e,t,n){var a=e*e-4*r*t;if(!(a<0)){if(a>0){var i=1/(2*r),o=Math.sqrt(a),u=(-e+o)*i,s=(-e-o)*i;return u<s?(n.root0=u,n.root1=s):(n.root0=s,n.root1=u),n}var f=-e/(2*r);if(0!==f)return n.root0=n.root1=f,n}}(e.a.dot(u,u),2*e.a.dot(u,c),e.a.magnitudeSquared(c)-f,y);if(t.t(w))return i.start=w.root0,i.stop=w.root1,i}m.raySphere=function(r,e,a){if(!t.t(r))throw new n.t("ray is required.");if(!t.t(e))throw new n.t("sphere is required.");if(a=R(r,e,a),t.t(a)&&!(a.stop<0))return a.start=Math.max(a.start,0),a};var S=new d;m.lineSegmentSphere=function(r,a,i,o){if(!t.t(r))throw new n.t("p0 is required.");if(!t.t(a))throw new n.t("p1 is required.");if(!t.t(i))throw new n.t("sphere is required.");var u=S;e.a.clone(r,u.origin);var s=e.a.subtract(a,r,u.direction),f=e.a.magnitude(s);if(e.a.normalize(s,s),o=R(u,i,o),!(!t.t(o)||o.stop<0||o.start>f))return o.start=Math.max(o.start,0),o.stop=Math.min(o.stop,f),o};var O=new e.a,P=new e.a;function N(r,e,t){var n=r+e;return i.n.sign(r)!==i.n.sign(e)&&Math.abs(n/Math.max(Math.abs(r),Math.abs(e)))<t?0:n}function L(r,t,n,a,s){var f,c=a*a,h=s*s,l=(r[o.r.COLUMN1ROW1]-r[o.r.COLUMN2ROW2])*h,d=s*(a*N(r[o.r.COLUMN1ROW0],r[o.r.COLUMN0ROW1],i.n.EPSILON15)+t.y),m=r[o.r.COLUMN0ROW0]*c+r[o.r.COLUMN2ROW2]*h+a*t.x+n,p=h*N(r[o.r.COLUMN2ROW1],r[o.r.COLUMN1ROW2],i.n.EPSILON15),b=s*(a*N(r[o.r.COLUMN2ROW0],r[o.r.COLUMN0ROW2])+t.z),g=[];if(0===b&&0===p){if(0===(f=u.computeRealRoots(l,d,m)).length)return g;var v=f[0],q=Math.sqrt(Math.max(1-v*v,0));if(g.push(new e.a(a,s*v,s*-q)),g.push(new e.a(a,s*v,s*q)),2===f.length){var M=f[1],y=Math.sqrt(Math.max(1-M*M,0));g.push(new e.a(a,s*M,s*-y)),g.push(new e.a(a,s*M,s*y))}return g}var R=b*b,S=p*p,O=b*p,P=l*l+S,L=2*(d*l+O),E=2*m*l+d*d-S+R,I=2*(m*d-O),C=m*m-R;if(0===P&&0===L&&0===E&&0===I)return g;var x=(f=w.computeRealRoots(P,L,E,I,C)).length;if(0===x)return g;for(var z=0;z<x;++z){var T=f[z],U=T*T,W=Math.max(1-U,0),B=Math.sqrt(W),Z=(i.n.sign(l)===i.n.sign(m)?N(l*U+m,d*T,i.n.EPSILON12):i.n.sign(m)===i.n.sign(d*T)?N(l*U,d*T+m,i.n.EPSILON12):N(l*U+d*T,m,i.n.EPSILON12))*N(p*T,b,i.n.EPSILON15);Z<0?g.push(new e.a(a,s*T,s*B)):Z>0?g.push(new e.a(a,s*T,s*-B)):0!==B?(g.push(new e.a(a,s*T,s*-B)),g.push(new e.a(a,s*T,s*B)),++z):g.push(new e.a(a,s*T,s*B))}return g}m.rayEllipsoid=function(r,i){if(!t.t(r))throw new n.t("ray is required.");if(!t.t(i))throw new n.t("ellipsoid is required.");var o,u,s,f,c,w=i.oneOverRadii,h=e.a.multiplyComponents(w,r.origin,O),l=e.a.multiplyComponents(w,r.direction,P),d=e.a.magnitudeSquared(h),m=e.a.dot(h,l);if(d>1){if(m>=0)return;var p=m*m;if(o=d-1,p<(s=(u=e.a.magnitudeSquared(l))*o))return;if(p>s){f=m*m-s;var b=(c=-m+Math.sqrt(f))/u,g=o/c;return b<g?new a.i(b,g):{start:g,stop:b}}var v=Math.sqrt(o/u);return new a.i(v,v)}return d<1?(o=d-1,f=m*m-(s=(u=e.a.magnitudeSquared(l))*o),c=-m+Math.sqrt(f),new a.i(0,c/u)):m<0?(u=e.a.magnitudeSquared(l),new a.i(0,-m/u)):void 0};var E=new e.a,I=new e.a,C=new e.a,x=new e.a,z=new e.a,T=new o.r,U=new o.r,W=new o.r,B=new o.r,Z=new o.r,V=new o.r,A=new o.r,D=new e.a,j=new e.a,k=new e.i;m.grazingAltitudeLocation=function(r,a){if(!t.t(r))throw new n.t("ray is required.");if(!t.t(a))throw new n.t("ellipsoid is required.");var u=r.origin,s=r.direction;if(!e.a.equals(u,e.a.ZERO)){var f=a.geodeticSurfaceNormal(u,E);if(e.a.dot(s,f)>=0)return u}var c=t.t(this.rayEllipsoid(r,a)),w=a.transformPositionToScaledSpace(s,E),h=e.a.normalize(w,w),l=e.a.mostOrthogonalAxis(w,x),d=e.a.normalize(e.a.cross(l,h,I),I),m=e.a.normalize(e.a.cross(h,d,C),C),p=T;p[0]=h.x,p[1]=h.y,p[2]=h.z,p[3]=d.x,p[4]=d.y,p[5]=d.z,p[6]=m.x,p[7]=m.y,p[8]=m.z;var b=o.r.transpose(p,U),g=o.r.fromScale(a.radii,W),v=o.r.fromScale(a.oneOverRadii,B),q=Z;q[0]=0,q[1]=-s.z,q[2]=s.y,q[3]=s.z,q[4]=0,q[5]=-s.x,q[6]=-s.y,q[7]=s.x,q[8]=0;var M,y,R=o.r.multiply(o.r.multiply(b,v,V),q,V),S=o.r.multiply(o.r.multiply(R,g,A),p,A),O=o.r.multiplyByVector(R,u,z),P=L(S,e.a.negate(O,E),0,0,1),N=P.length;if(N>0){for(var F=e.a.clone(e.a.ZERO,j),G=Number.NEGATIVE_INFINITY,Y=0;Y<N;++Y){M=o.r.multiplyByVector(g,o.r.multiplyByVector(p,P[Y],D),D);var _=e.a.normalize(e.a.subtract(M,u,x),x),H=e.a.dot(_,s);H>G&&(G=H,F=e.a.clone(M,F))}var J=a.cartesianToCartographic(F,k);return G=i.n.clamp(G,0,1),y=e.a.magnitude(e.a.subtract(F,u,x))*Math.sqrt(1-G*G),y=c?-y:y,J.height=y,a.cartographicToCartesian(J,new e.a)}};var F=new e.a;m.lineSegmentPlane=function(r,a,o,u){if(!t.t(r))throw new n.t("endPoint0 is required.");if(!t.t(a))throw new n.t("endPoint1 is required.");if(!t.t(o))throw new n.t("plane is required.");t.t(u)||(u=new e.a);var s=e.a.subtract(a,r,F),f=o.normal,c=e.a.dot(f,s);if(!(Math.abs(c)<i.n.EPSILON6)){var w=e.a.dot(f,r),h=-(o.distance+w)/c;if(!(h<0||h>1))return e.a.multiplyByScalar(s,h,u),e.a.add(r,u,u),u}},m.trianglePlaneIntersection=function(r,a,i,o){if(!(t.t(r)&&t.t(a)&&t.t(i)&&t.t(o)))throw new n.t("p0, p1, p2, and plane are required.");var u,s,f=o.normal,c=o.distance,w=e.a.dot(f,r)+c<0,h=e.a.dot(f,a)+c<0,l=e.a.dot(f,i)+c<0,d=0;if(d+=w?1:0,d+=h?1:0,(1===(d+=l?1:0)||2===d)&&(u=new e.a,s=new e.a),1===d){if(w)return m.lineSegmentPlane(r,a,o,u),m.lineSegmentPlane(r,i,o,s),{positions:[r,a,i,u,s],indices:[0,3,4,1,2,4,1,4,3]};if(h)return m.lineSegmentPlane(a,i,o,u),m.lineSegmentPlane(a,r,o,s),{positions:[r,a,i,u,s],indices:[1,3,4,2,0,4,2,4,3]};if(l)return m.lineSegmentPlane(i,r,o,u),m.lineSegmentPlane(i,a,o,s),{positions:[r,a,i,u,s],indices:[2,3,4,0,1,4,0,4,3]}}else if(2===d){if(!w)return m.lineSegmentPlane(a,r,o,u),m.lineSegmentPlane(i,r,o,s),{positions:[r,a,i,u,s],indices:[1,2,4,1,4,3,0,3,4]};if(!h)return m.lineSegmentPlane(i,a,o,u),m.lineSegmentPlane(r,a,o,s),{positions:[r,a,i,u,s],indices:[2,0,4,2,4,3,1,3,4]};if(!l)return m.lineSegmentPlane(r,i,o,u),m.lineSegmentPlane(a,i,o,s),{positions:[r,a,i,u,s],indices:[0,1,4,0,4,3,2,3,4]}}},r.h=m,r.n=d}));
