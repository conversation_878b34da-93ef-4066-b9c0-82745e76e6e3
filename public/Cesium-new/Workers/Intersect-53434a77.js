define(["exports","./Cartographic-1bbcab04","./when-515d5295","./Check-3aa71481","./Rectangle-e170be8b"],(function(e,i,t,r,o){"use strict";function s(e){this._ellipsoid=t.e(e,o.n.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(s.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),s.prototype.project=function(e,r){var o=this._semimajorAxis,s=e.longitude*o,n=e.latitude*o,a=e.height;return t.t(r)?(r.x=s,r.y=n,r.z=a,r):new i.a(s,n,a)},s.prototype.unproject=function(e,o){if(!t.t(e))throw new r.t("cartesian is required");var s=this._oneOverSemimajorAxis,n=e.x*s,a=e.y*s,u=e.z;return t.t(o)?(o.longitude=n,o.latitude=a,o.height=u,o):new i.i(n,a,u)};var n=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});e.S=n,e.s=s}));
