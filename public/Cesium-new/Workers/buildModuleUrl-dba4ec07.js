define(["exports","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./Intersect-53434a77","./Math-5e38123d","./PrimitiveType-b38a4004","./Event-9821f5d9","./RuntimeError-350acae3"],(function(e,t,r,n,a,i,o,s,u,c){"use strict";function p(e,t){this.start=n.e(e,0),this.stop=n.e(t,0)}function f(e,r){this.center=t.a.clone(n.e(e,t.a.ZERO)),this.radius=n.e(r,0)}var l=new t.a,h=new t.a,d=new t.a,m=new t.a,y=new t.a,v=new t.a,g=new t.a,q=new t.a,b=new t.a,w=new t.a,R=new t.a,O=new t.a,A=4/3*o.n.PI;f.fromPoints=function(e,r){if(n.t(r)||(r=new f),!n.t(e)||0===e.length)return r.center=t.a.clone(t.a.ZERO,r.center),r.radius=0,r;var a,i=t.a.clone(e[0],g),o=t.a.clone(i,l),s=t.a.clone(i,h),u=t.a.clone(i,d),c=t.a.clone(i,m),p=t.a.clone(i,y),A=t.a.clone(i,v),x=e.length;for(a=1;a<x;a++){t.a.clone(e[a],i);var S=i.x,E=i.y,_=i.z;S<o.x&&t.a.clone(i,o),S>c.x&&t.a.clone(i,c),E<s.y&&t.a.clone(i,s),E>p.y&&t.a.clone(i,p),_<u.z&&t.a.clone(i,u),_>A.z&&t.a.clone(i,A)}var C=t.a.magnitudeSquared(t.a.subtract(c,o,q)),I=t.a.magnitudeSquared(t.a.subtract(p,s,q)),T=t.a.magnitudeSquared(t.a.subtract(A,u,q)),P=o,U=c,B=C;I>B&&(B=I,P=s,U=p),T>B&&(B=T,P=u,U=A);var k=b;k.x=.5*(P.x+U.x),k.y=.5*(P.y+U.y),k.z=.5*(P.z+U.z);var j=t.a.magnitudeSquared(t.a.subtract(U,k,q)),D=Math.sqrt(j),z=w;z.x=o.x,z.y=s.y,z.z=u.z;var L=R;L.x=c.x,L.y=p.y,L.z=A.z;var K=t.a.midpoint(z,L,O),M=0;for(a=0;a<x;a++){t.a.clone(e[a],i);var F=t.a.magnitude(t.a.subtract(i,K,q));F>M&&(M=F);var N=t.a.magnitudeSquared(t.a.subtract(i,k,q));if(N>j){var H=Math.sqrt(N);j=(D=.5*(D+H))*D;var V=H-D;k.x=(D*k.x+V*i.x)/H,k.y=(D*k.y+V*i.y)/H,k.z=(D*k.z+V*i.z)/H}}return D<M?(t.a.clone(k,r.center),r.radius=D):(t.a.clone(K,r.center),r.radius=M),r};var x=new i.s,S=new t.a,E=new t.a,_=new t.i,C=new t.i;f.fromRectangle2D=function(e,t,r){return f.fromRectangleWithHeights2D(e,t,0,0,r)},f.fromRectangleWithHeights2D=function(e,r,i,o,s){if(n.t(s)||(s=new f),!n.t(e))return s.center=t.a.clone(t.a.ZERO,s.center),s.radius=0,s;r=n.e(r,x),a.s.southwest(e,_),_.height=i,a.s.northeast(e,C),C.height=o;var u=r.project(_,S),c=r.project(C,E),p=c.x-u.x,l=c.y-u.y,h=c.z-u.z;s.radius=.5*Math.sqrt(p*p+l*l+h*h);var d=s.center;return d.x=u.x+.5*p,d.y=u.y+.5*l,d.z=u.z+.5*h,s};var I=[];f.fromRectangle3D=function(e,r,i,o){if(r=n.e(r,a.n.WGS84),i=n.e(i,0),n.t(o)||(o=new f),!n.t(e))return o.center=t.a.clone(t.a.ZERO,o.center),o.radius=0,o;var s=a.s.subsample(e,r,i,I);return f.fromPoints(s,o)},f.fromVertices=function(e,a,i,o){if(n.t(o)||(o=new f),!n.t(e)||0===e.length)return o.center=t.a.clone(t.a.ZERO,o.center),o.radius=0,o;a=n.e(a,t.a.ZERO),i=n.e(i,3),r.n.typeOf.number.greaterThanOrEquals("stride",i,3);var s=g;s.x=e[0]+a.x,s.y=e[1]+a.y,s.z=e[2]+a.z;var u,c=t.a.clone(s,l),p=t.a.clone(s,h),A=t.a.clone(s,d),x=t.a.clone(s,m),S=t.a.clone(s,y),E=t.a.clone(s,v),_=e.length;for(u=0;u<_;u+=i){var C=e[u]+a.x,I=e[u+1]+a.y,T=e[u+2]+a.z;s.x=C,s.y=I,s.z=T,C<c.x&&t.a.clone(s,c),C>x.x&&t.a.clone(s,x),I<p.y&&t.a.clone(s,p),I>S.y&&t.a.clone(s,S),T<A.z&&t.a.clone(s,A),T>E.z&&t.a.clone(s,E)}var P=t.a.magnitudeSquared(t.a.subtract(x,c,q)),U=t.a.magnitudeSquared(t.a.subtract(S,p,q)),B=t.a.magnitudeSquared(t.a.subtract(E,A,q)),k=c,j=x,D=P;U>D&&(D=U,k=p,j=S),B>D&&(D=B,k=A,j=E);var z=b;z.x=.5*(k.x+j.x),z.y=.5*(k.y+j.y),z.z=.5*(k.z+j.z);var L=t.a.magnitudeSquared(t.a.subtract(j,z,q)),K=Math.sqrt(L),M=w;M.x=c.x,M.y=p.y,M.z=A.z;var F=R;F.x=x.x,F.y=S.y,F.z=E.z;var N=t.a.midpoint(M,F,O),H=0;for(u=0;u<_;u+=i){s.x=e[u]+a.x,s.y=e[u+1]+a.y,s.z=e[u+2]+a.z;var V=t.a.magnitude(t.a.subtract(s,N,q));V>H&&(H=V);var Y=t.a.magnitudeSquared(t.a.subtract(s,z,q));if(Y>L){var J=Math.sqrt(Y);L=(K=.5*(K+J))*K;var X=J-K;z.x=(K*z.x+X*s.x)/J,z.y=(K*z.y+X*s.y)/J,z.z=(K*z.z+X*s.z)/J}}return K<H?(t.a.clone(z,o.center),o.radius=K):(t.a.clone(N,o.center),o.radius=H),o},f.fromEncodedCartesianVertices=function(e,r,a){if(n.t(a)||(a=new f),!n.t(e)||!n.t(r)||e.length!==r.length||0===e.length)return a.center=t.a.clone(t.a.ZERO,a.center),a.radius=0,a;var i=g;i.x=e[0]+r[0],i.y=e[1]+r[1],i.z=e[2]+r[2];var o,s=t.a.clone(i,l),u=t.a.clone(i,h),c=t.a.clone(i,d),p=t.a.clone(i,m),A=t.a.clone(i,y),x=t.a.clone(i,v),S=e.length;for(o=0;o<S;o+=3){var E=e[o]+r[o],_=e[o+1]+r[o+1],C=e[o+2]+r[o+2];i.x=E,i.y=_,i.z=C,E<s.x&&t.a.clone(i,s),E>p.x&&t.a.clone(i,p),_<u.y&&t.a.clone(i,u),_>A.y&&t.a.clone(i,A),C<c.z&&t.a.clone(i,c),C>x.z&&t.a.clone(i,x)}var I=t.a.magnitudeSquared(t.a.subtract(p,s,q)),T=t.a.magnitudeSquared(t.a.subtract(A,u,q)),P=t.a.magnitudeSquared(t.a.subtract(x,c,q)),U=s,B=p,k=I;T>k&&(k=T,U=u,B=A),P>k&&(k=P,U=c,B=x);var j=b;j.x=.5*(U.x+B.x),j.y=.5*(U.y+B.y),j.z=.5*(U.z+B.z);var D=t.a.magnitudeSquared(t.a.subtract(B,j,q)),z=Math.sqrt(D),L=w;L.x=s.x,L.y=u.y,L.z=c.z;var K=R;K.x=p.x,K.y=A.y,K.z=x.z;var M=t.a.midpoint(L,K,O),F=0;for(o=0;o<S;o+=3){i.x=e[o]+r[o],i.y=e[o+1]+r[o+1],i.z=e[o+2]+r[o+2];var N=t.a.magnitude(t.a.subtract(i,M,q));N>F&&(F=N);var H=t.a.magnitudeSquared(t.a.subtract(i,j,q));if(H>D){var V=Math.sqrt(H);D=(z=.5*(z+V))*z;var Y=V-z;j.x=(z*j.x+Y*i.x)/V,j.y=(z*j.y+Y*i.y)/V,j.z=(z*j.z+Y*i.z)/V}}return z<F?(t.a.clone(j,a.center),a.radius=z):(t.a.clone(M,a.center),a.radius=F),a},f.fromCornerPoints=function(e,a,i){r.n.typeOf.object("corner",e),r.n.typeOf.object("oppositeCorner",a),n.t(i)||(i=new f);var o=t.a.midpoint(e,a,i.center);return i.radius=t.a.distance(o,a),i},f.fromEllipsoid=function(e,a){return r.n.typeOf.object("ellipsoid",e),n.t(a)||(a=new f),t.a.clone(t.a.ZERO,a.center),a.radius=e.maximumRadius,a};var T=new t.a;f.fromBoundingSpheres=function(e,r){if(n.t(r)||(r=new f),!n.t(e)||0===e.length)return r.center=t.a.clone(t.a.ZERO,r.center),r.radius=0,r;var a=e.length;if(1===a)return f.clone(e[0],r);if(2===a)return f.union(e[0],e[1],r);var i,o=[];for(i=0;i<a;i++)o.push(e[i].center);var s=(r=f.fromPoints(o,r)).center,u=r.radius;for(i=0;i<a;i++){var c=e[i];u=Math.max(u,t.a.distance(s,c.center,T)+c.radius)}return r.radius=u,r};var P=new t.a,U=new t.a,B=new t.a;f.fromOrientedBoundingBox=function(e,a){r.n.defined("orientedBoundingBox",e),n.t(a)||(a=new f);var i=e.halfAxes,o=s.r.getColumn(i,0,P),u=s.r.getColumn(i,1,U),c=s.r.getColumn(i,2,B);return t.a.add(o,u,o),t.a.add(o,c,o),a.center=t.a.clone(e.center,a.center),a.radius=t.a.magnitude(o),a},f.clone=function(e,r){if(n.t(e))return n.t(r)?(r.center=t.a.clone(e.center,r.center),r.radius=e.radius,r):new f(e.center,e.radius)},f.packedLength=4,f.pack=function(e,t,a){r.n.typeOf.object("value",e),r.n.defined("array",t),a=n.e(a,0);var i=e.center;return t[a++]=i.x,t[a++]=i.y,t[a++]=i.z,t[a]=e.radius,t},f.unpack=function(e,t,a){r.n.defined("array",e),t=n.e(t,0),n.t(a)||(a=new f);var i=a.center;return i.x=e[t++],i.y=e[t++],i.z=e[t++],a.radius=e[t],a};var k=new t.a,j=new t.a;f.union=function(e,a,i){r.n.typeOf.object("left",e),r.n.typeOf.object("right",a),n.t(i)||(i=new f);var o=e.center,s=e.radius,u=a.center,c=a.radius,p=t.a.subtract(u,o,k),l=t.a.magnitude(p);if(s>=l+c)return e.clone(i),i;if(c>=l+s)return a.clone(i),i;var h=.5*(s+l+c),d=t.a.multiplyByScalar(p,(-s+h)/l,j);return t.a.add(d,o,d),t.a.clone(d,i.center),i.radius=h,i};var D=new t.a;f.expand=function(e,n,a){r.n.typeOf.object("sphere",e),r.n.typeOf.object("point",n),a=f.clone(e,a);var i=t.a.magnitude(t.a.subtract(n,a.center,D));return i>a.radius&&(a.radius=i),a},f.intersectPlane=function(e,n){r.n.typeOf.object("sphere",e),r.n.typeOf.object("plane",n);var a=e.center,o=e.radius,s=n.normal,u=t.a.dot(s,a)+n.distance;return u<-o?i.S.OUTSIDE:u<o?i.S.INTERSECTING:i.S.INSIDE},f.transform=function(e,t,a){return r.n.typeOf.object("sphere",e),r.n.typeOf.object("transform",t),n.t(a)||(a=new f),a.center=s.c.multiplyByPoint(t,e.center,a.center),a.radius=s.c.getMaximumScale(t)*e.radius,a};var z=new t.a;f.distanceSquaredTo=function(e,n){r.n.typeOf.object("sphere",e),r.n.typeOf.object("cartesian",n);var a=t.a.subtract(e.center,n,z);return t.a.magnitudeSquared(a)-e.radius*e.radius},f.transformWithoutScale=function(e,t,a){return r.n.typeOf.object("sphere",e),r.n.typeOf.object("transform",t),n.t(a)||(a=new f),a.center=s.c.multiplyByPoint(t,e.center,a.center),a.radius=e.radius,a};var L=new t.a;f.computePlaneDistances=function(e,a,i,o){r.n.typeOf.object("sphere",e),r.n.typeOf.object("position",a),r.n.typeOf.object("direction",i),n.t(o)||(o=new p);var s=t.a.subtract(e.center,a,L),u=t.a.dot(i,s);return o.start=u-e.radius,o.stop=u+e.radius,o};for(var K=new t.a,M=new t.a,F=new t.a,N=new t.a,H=new t.a,V=new t.i,Y=new Array(8),J=0;J<8;++J)Y[J]=new t.a;var X=new i.s;
/**
	 * @license
	 *
	 * Grauw URI utilities
	 *
	 * See: http://hg.grauw.nl/grauw-lib/file/tip/src/uri.js
	 *
	 * <AUTHOR> Holst (http://www.grauw.nl/)
	 *
	 *   Copyright 2012 Laurens Holst
	 *
	 *   Licensed under the Apache License, Version 2.0 (the "License");
	 *   you may not use this file except in compliance with the License.
	 *   You may obtain a copy of the License at
	 *
	 *       http://www.apache.org/licenses/LICENSE-2.0
	 *
	 *   Unless required by applicable law or agreed to in writing, software
	 *   distributed under the License is distributed on an "AS IS" BASIS,
	 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	 *   See the License for the specific language governing permissions and
	 *   limitations under the License.
	 *
	 */
function G(e){if(e instanceof G)this.scheme=e.scheme,this.authority=e.authority,this.path=e.path,this.query=e.query,this.fragment=e.fragment;else if(e){var t=Q.exec(e);this.scheme=t[1],this.authority=t[2],this.path=t[3],this.query=t[4],this.fragment=t[5]}}f.projectTo2D=function(e,a,i){r.n.typeOf.object("sphere",e);var o,s=(a=n.e(a,X)).ellipsoid,u=e.center,c=e.radius;o=t.a.equals(u,t.a.ZERO)?t.a.clone(t.a.UNIT_X,K):s.geodeticSurfaceNormal(u,K);var p=t.a.cross(t.a.UNIT_Z,o,M);t.a.normalize(p,p);var l=t.a.cross(o,p,F);t.a.normalize(l,l),t.a.multiplyByScalar(o,c,o),t.a.multiplyByScalar(l,c,l),t.a.multiplyByScalar(p,c,p);var h=t.a.negate(l,H),d=t.a.negate(p,N),m=Y,y=m[0];t.a.add(o,l,y),t.a.add(y,p,y),y=m[1],t.a.add(o,l,y),t.a.add(y,d,y),y=m[2],t.a.add(o,h,y),t.a.add(y,d,y),y=m[3],t.a.add(o,h,y),t.a.add(y,p,y),t.a.negate(o,o),y=m[4],t.a.add(o,l,y),t.a.add(y,p,y),y=m[5],t.a.add(o,l,y),t.a.add(y,d,y),y=m[6],t.a.add(o,h,y),t.a.add(y,d,y),y=m[7],t.a.add(o,h,y),t.a.add(y,p,y);for(var v=m.length,g=0;g<v;++g){var q=m[g];t.a.add(u,q,q);var b=s.cartesianToCartographic(q,V);a.project(b,q)}var w=(u=(i=f.fromPoints(m,i)).center).x,R=u.y,O=u.z;return u.x=O,u.y=w,u.z=R,i},f.isOccluded=function(e,t){return r.n.typeOf.object("sphere",e),r.n.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)},f.equals=function(e,r){return e===r||n.t(e)&&n.t(r)&&t.a.equals(e.center,r.center)&&e.radius===r.radius},f.prototype.intersectPlane=function(e){return f.intersectPlane(this,e)},f.prototype.distanceSquaredTo=function(e){return f.distanceSquaredTo(this,e)},f.prototype.computePlaneDistances=function(e,t,r){return f.computePlaneDistances(this,e,t,r)},f.prototype.isOccluded=function(e){return f.isOccluded(this,e)},f.prototype.equals=function(e){return f.equals(this,e)},f.prototype.clone=function(e){return f.clone(this,e)},f.prototype.volume=function(){var e=this.radius;return A*e*e*e},G.prototype.scheme=null,G.prototype.authority=null,G.prototype.path="",G.prototype.query=null,G.prototype.fragment=null;var Q=new RegExp("^(?:([^:/?#]+):)?(?://([^/?#]*))?([^?#]*)(?:\\?([^#]*))?(?:#(.*))?$");G.prototype.getScheme=function(){return this.scheme},G.prototype.getAuthority=function(){return this.authority},G.prototype.getPath=function(){return this.path},G.prototype.getQuery=function(){return this.query},G.prototype.getFragment=function(){return this.fragment},G.prototype.isAbsolute=function(){return!!this.scheme&&!this.fragment},G.prototype.isSameDocumentAs=function(e){return e.scheme==this.scheme&&e.authority==this.authority&&e.path==this.path&&e.query==this.query},G.prototype.equals=function(e){return this.isSameDocumentAs(e)&&e.fragment==this.fragment},G.prototype.normalize=function(){this.removeDotSegments(),this.scheme&&(this.scheme=this.scheme.toLowerCase()),this.authority&&(this.authority=this.authority.replace($,te).replace(Z,ee)),this.path&&(this.path=this.path.replace(Z,ee)),this.query&&(this.query=this.query.replace(Z,ee)),this.fragment&&(this.fragment=this.fragment.replace(Z,ee))};var Z=/%[0-9a-z]{2}/gi,W=/[a-zA-Z0-9\-\._~]/,$=/(.*@)?([^@:]*)(:.*)?/;function ee(e){var t=unescape(e);return W.test(t)?t:e.toUpperCase()}function te(e,t,r,n){return(t||"")+r.toLowerCase()+(n||"")}function re(e,t){if(null===e||"object"!=typeof e)return e;t=n.e(t,!1);var r=new e.constructor;for(var a in e)if(e.hasOwnProperty(a)){var i=e[a];t&&(i=re(i,t)),r[a]=i}return r}function ne(e,t,r){r=n.e(r,!1);var a,i,o,s={},u=n.t(e),c=n.t(t);if(u)for(a in e)e.hasOwnProperty(a)&&(i=e[a],c&&r&&"object"==typeof i&&t.hasOwnProperty(a)?(o=t[a],s[a]="object"==typeof o?ne(i,o,r):i):s[a]=i);if(c)for(a in t)t.hasOwnProperty(a)&&!s.hasOwnProperty(a)&&(o=t[a],s[a]=o);return s}function ae(e,t){var r;return typeof document<"u"&&(r=document),ae._implementation(e,t,r)}G.prototype.resolve=function(e){var t=new G;return this.scheme?(t.scheme=this.scheme,t.authority=this.authority,t.path=this.path,t.query=this.query):(t.scheme=e.scheme,this.authority?(t.authority=this.authority,t.path=this.path,t.query=this.query):(t.authority=e.authority,""==this.path?(t.path=e.path,t.query=this.query||e.query):("/"==this.path.charAt(0)?(t.path=this.path,t.removeDotSegments()):(e.authority&&""==e.path?t.path="/"+this.path:t.path=e.path.substring(0,e.path.lastIndexOf("/")+1)+this.path,t.removeDotSegments()),t.query=this.query))),t.fragment=this.fragment,t},G.prototype.removeDotSegments=function(){var e,t=this.path.split("/"),r=[],n=""==t[0];n&&t.shift();for(""==t[0]&&t.shift();t.length;)".."==(e=t.shift())?r.pop():"."!=e&&r.push(e);("."==e||".."==e)&&r.push(""),n&&r.unshift(""),this.path=r.join("/")},G.prototype.toString=function(){var e="";return this.scheme&&(e+=this.scheme+":"),this.authority&&(e+="//"+this.authority),e+=this.path,this.query&&(e+="?"+this.query),this.fragment&&(e+="#"+this.fragment),e},ae._implementation=function(e,t,a){if(!n.t(e))throw new r.t("relative uri is required.");if(!n.t(t)){if(typeof a>"u")return e;t=n.e(a.baseURI,a.location.href)}var i=new G(t);return new G(e).resolve(i).toString()};var ie,oe=/^blob:/i;function se(e){return r.n.typeOf.string("uri",e),oe.test(e)}var ue=/^data:/i;function ce(e){return r.n.typeOf.string("uri",e),ue.test(e)}var pe=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5}),fe=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3,PACK:4,BLOCK:5,BLOCKPACK:6});function le(e){e=n.e(e,n.e.EMPTY_OBJECT);var t=n.e(e.throttleByServer,!1),r=n.e(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=n.e(e.priority,0),this.throttle=r,this.throttleByServer=t,this.type=n.e(e.type,fe.OTHER),this.serverKey=void 0,this.state=pe.UNISSUED,this.deferred=void 0,this.cancelled=!1}function he(e,t,r){this.statusCode=e,this.response=t,this.responseHeaders=r,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){var t={};if(!e)return t;for(var r=e.split("\r\n"),n=0;n<r.length;++n){var a=r[n],i=a.indexOf(": ");if(i>0){var o=a.substring(0,i),s=a.substring(i+2);t[o]=s}}return t}(this.responseHeaders))}function de(e){r.n.typeOf.object("options",e),r.n.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function me(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}le.prototype.cancel=function(){this.cancelled=!0},le.prototype.clone=function(e){return n.t(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=this.RequestState.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new le(this)},he.prototype.toString=function(){var e="Request has failed.";return n.t(this.statusCode)&&(e+=" Status Code: "+this.statusCode),e},Object.defineProperties(de.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){this._maximumLength=e,this._length>e&&e>0&&(this._length=e,this._array.length=e)}},comparator:{get:function(){return this._comparator}}}),de.prototype.reserve=function(e){e=n.e(e,this._length),this._array.length=e},de.prototype.heapify=function(e){e=n.e(e,0);for(var t=this._length,r=this._comparator,a=this._array,i=-1,o=!0;o;){var s=2*(e+1),u=s-1;i=u<t&&r(a[u],a[e])<0?u:e,s<t&&r(a[s],a[i])<0&&(i=s),i!==e?(me(a,i,e),e=i):o=!1}},de.prototype.resort=function(){for(var e=this._length,t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},de.prototype.insert=function(e){r.n.defined("element",e);var t,a=this._array,i=this._comparator,o=this._maximumLength,s=this._length++;for(s<a.length?a[s]=e:a.push(e);0!==s;){var u=Math.floor((s-1)/2);if(!(i(a[s],a[u])<0))break;me(a,s,u),s=u}return n.t(o)&&this._length>o&&(t=a[o],a.pop(),this._length=o),t},de.prototype.pop=function(e){if(e=n.e(e,0),0!==this._length){r.n.typeOf.number.lessThan("index",e,this._length);var t=this._array,a=t[e];return me(t,e,--this._length),t[this._length]=void 0,this.heapify(e),a}};var ye=typeof performance<"u"&&"function"==typeof performance.now&&isFinite(performance.now())?function(){return performance.now()}:function(){return Date.now()};function ve(e,t){return e.priority-t.priority}var ge={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0,totalRequestTime:0},qe=20,be=new de({comparator:ve});be.maximumLength=qe,be.reserve(qe);var we=[],Re={},Oe=typeof document<"u"?new G(document.location.href):new G,Ae=new u.o;function xe(){}function Se(e){n.t(e.priorityFunction)&&(e.priority=e.priorityFunction())}function Ee(e){var t=n.e(xe.requestsByServer[e],xe.maximumRequestsPerServer);return Re[e]<t}function _e(e){return n.t(e.packKey)||(e.packKey=e.serverKey+"_"+e.providerName),e.packKey}function Ce(e){return n.t(e.blockKey)||(e.blockKey=e.serverKey+"_"+e.providerName+"_"+e.quadKey+e.url.substring(e.url.indexOf("dataVersion"))),e.blockKey}function Ie(e){var t=_e(e);return n.t(xe.packRequestGroup[t])||(xe.packRequestGroup[t]=[]),n.t(xe.packRequestQuadKey[t])||(xe.packRequestQuadKey[t]=""),n.t(xe.packRequestPromise[t])||(xe.packRequestPromise[t]=n.c.defer()),n.t(xe.quadKeyIndex[t])||(xe.quadKeyIndex[t]=0),e.quadKeyIndex=xe.quadKeyIndex[t]++,e.deferred=xe.packRequestPromise[t],e.state=pe.ISSUED,xe.packRequestGroup[t].push(e),e.deferred.promise}function Te(e){for(var t=0,r=e.length;t<r;t++){e[t].state=pe.CANCELLED}}function Pe(e){for(var t=[],r={},n=0,a=e.length;n<a;n++){var i=e[n];if(!i.cancelled){var o=i.quadKey;r[o]||(r[o]=!0,t.push(o))}}return t}function Ue(){var e=xe.packRequestGroup;for(var t in e)if(e.hasOwnProperty(t)){var r=e[t];if(r.length<1)continue;var a=r[0].clone(),i=-1!==a.url.indexOf("rest/maps");a.serverKey=r[0].serverKey,a.state=r[0].state;var o=a.url,s=Pe(r);if(s.length<1)continue;xe.packRequestQuadKey[t]=i?s.join(","):s.join(";");var u=xe.packRequestQuadKey[t];if(a.throttleByServer&&!Ee(a.serverKey)){Te(r),xe.packRequestPromise[t].reject();continue}a.deferred=xe.packRequestPromise[t];var c=new G(o);c.query=i?n.t(c.query)?c.query+"&tiles="+u:"tiles="+u:n.t(c.query)?c.query+"&extratiles="+u:"extratiles="+u,a.url=c.toString(),je(a,a.url)}xe.packRequestGroup={},xe.packRequestPromise={},xe.packRequestQuadKey={},xe.quadKeyIndex={}}function Be(){var e=xe.blockRequest;for(var t in e)if(e.hasOwnProperty(t)){je(e[t])}xe.blockRequest={}}function ke(e){if(e.state===pe.UNISSUED)if(e.state=pe.ISSUED,e.type===fe.PACK||e.type===fe.BLOCKPACK){var t=_e(e);n.t(xe.packRequestPromise[t])||(xe.packRequestPromise[t]=n.c.defer()),e.deferred=xe.packRequestPromise[t]}else e.deferred=n.c.defer();return e.deferred.promise}function je(e,t){var r=ke(e);return e.state=pe.ACTIVE,we.push(e),++ge.numberOfActiveRequests,++ge.numberOfActiveRequestsEver,++Re[e.serverKey],e.startTime=ye(),e.requestFunction(t).then(function(e){return function(t){if(e.state!==pe.CANCELLED&&(--ge.numberOfActiveRequests,--Re[e.serverKey],Ae.raiseEvent(),e.state=pe.RECEIVED,e.deferred.resolve(t),e.endTime=ye(),(xe.statisticRequestTime>0||e.type!==fe.OTHER)&&(ge.totalRequestTime+=e.endTime-e.startTime),e.type===fe.BLOCK||e.type===fe.BLOCKPACK)){var r=Ce(e);n.t(xe.blockDefer[r])&&(xe.blockDefer[r]=void 0,delete xe.blockDefer[r])}}}(e)).otherwise(function(e){return function(t){e.state!==pe.CANCELLED&&(++ge.numberOfFailedRequests,--ge.numberOfActiveRequests,--Re[e.serverKey],Ae.raiseEvent(t),e.state=pe.FAILED,e.deferred.reject(t))}}(e)),r}function De(e){var t=e.state===pe.ACTIVE;e.state=pe.CANCELLED,++ge.numberOfCancelledRequests,e.deferred.reject(),t&&(--ge.numberOfActiveRequests,--Re[e.serverKey],++ge.numberOfCancelledActiveRequests),n.t(e.cancelFunction)&&e.cancelFunction()}xe.TIMEOUT=5e3,xe.CANCLE_COUNT=3,xe.statisticRequestTime=-1,xe.maximumRequests=50,xe.maximumRequestsPerServer=6,xe.perPacketCount=20,xe.requestsByServer={"api.cesium.com:443":18,"assets.cesium.com:443":18},xe.throttleRequests=!0,xe.debugShowStatistics=!1,xe.requestCompletedEvent=Ae,Object.defineProperties(xe,{activeRequestLength:{get:function(){return we.length}},statistics:{get:function(){return ge}},priorityHeapLength:{get:function(){return qe},set:function(e){if(e<qe)for(;be.length>e;){De(be.pop())}qe=e,be.maximumLength=e,be.reserve(e)}}}),xe.packRequestGroup={},xe.packRequestPromise={},xe.packRequestQuadKey={},xe.quadKeyIndex={},xe.packRequestHeap={},xe.blockDefer={},xe.blockRequest={},xe.update=function(){var e,t,r=0,n=we.length;for(e=0;e<n;++e)(t=we[e]).cancelled&&De(t),t.state===pe.ACTIVE?r>0&&(we[e-r]=t):++r;we.length-=r;var a=be.internalArray,i=be.length;for(e=0;e<i;++e)Se(a[e]);be.resort(),function(){for(var e in xe.packRequestHeap)if(xe.packRequestHeap.hasOwnProperty(e)){for(var t=xe.packRequestHeap[e],r=t.internalArray,n=t.length,a=0;a<n;++a)Se(r[a]);t.resort()}}(),Be(),function(){for(var e in xe.packRequestHeap)if(xe.packRequestHeap.hasOwnProperty(e))for(var t=xe.packRequestHeap[e];t.length>0;){var r=t.pop();r.cancelled?De(r):Ie(r)}Ue()}();for(var o=Math.max(xe.maximumRequests-we.length,0),s=0;s<o&&be.length>0;)(t=be.pop()).cancelled?De(t):!t.throttleByServer||Ee(t.serverKey)?(je(t),++s):De(t);!xe.debugShowStatistics||(0===ge.numberOfActiveRequests&&ge.lastNumberOfActiveRequests>0&&(ge.numberOfAttemptedRequests>0&&(console.log("Number of attempted requests: "+ge.numberOfAttemptedRequests),ge.numberOfAttemptedRequests=0),ge.numberOfCancelledRequests>0&&(console.log("Number of cancelled requests: "+ge.numberOfCancelledRequests),ge.numberOfCancelledRequests=0),ge.numberOfCancelledActiveRequests>0&&(console.log("Number of cancelled active requests: "+ge.numberOfCancelledActiveRequests),ge.numberOfCancelledActiveRequests=0),ge.numberOfFailedRequests>0&&(console.log("Number of failed requests: "+ge.numberOfFailedRequests),ge.numberOfFailedRequests=0)),ge.lastNumberOfActiveRequests=ge.numberOfActiveRequests)},xe.getServerKey=function(e){r.n.typeOf.string("url",e);var t=new G(e).resolve(Oe);t.normalize();var a=t.authority;/:/.test(a)||(a=a+":"+("https"===t.scheme?"443":"80"));var i=Re[a];return n.t(i)||(Re[a]=0),a},xe.request=function(e){if(r.n.typeOf.object("request",e),r.n.typeOf.string("request.url",e.url),r.n.typeOf.func("request.requestFunction",e.requestFunction),ce(e.url)||se(e.url))return Ae.raiseEvent(),e.state=pe.RECEIVED,e.requestFunction();if(++ge.numberOfAttemptedRequests,n.t(e.serverKey)||(e.serverKey=xe.getServerKey(e.url)),e.type===fe.BLOCK)return function(e){var t=Ce(e),r=xe.blockDefer[t];return n.t(r)||(r=xe.blockDefer[t]=n.c.defer(),xe.blockRequest[t]=e),e.deferred=r,e.state=pe.ISSUED,e.deferred.promise}(e);if(!e.throttleByServer||Ee(e.serverKey)){if(!xe.throttleRequests||!e.throttle)return je(e);if(!(we.length>=xe.maximumRequests)){var t;if(Se(e),e.type===fe.PACK||e.type===fe.BLOCKPACK){var a=function(e){var t=_e(e),r=xe.packRequestHeap[t];return n.t(r)||((r=xe.packRequestHeap[t]=new de({comparator:ve})).maximumLength=xe.perPacketCount,r.reserve(qe)),r}(e),i=!0;if(e.type===fe.BLOCKPACK)for(var o=0;o<a.length;o++)if(a._array[o].quadKey===e.quadKey){e.blockRequest=a._array[o],i=!1;break}i&&(t=a.insert(e))}else t=be.insert(e);if(n.t(t)){if(t===e)return;De(t)}return ke(e)}}},xe.clearForSpecs=function(){for(;be.length>0;){De(be.pop())}for(var e=we.length,t=0;t<e;++t)De(we[t]);we.length=0,Re={},ge.numberOfAttemptedRequests=0,ge.numberOfActiveRequests=0,ge.numberOfCancelledRequests=0,ge.numberOfCancelledActiveRequests=0,ge.numberOfFailedRequests=0,ge.numberOfActiveRequestsEver=0,ge.lastNumberOfActiveRequests=0,ge.totalRequestTime=0},xe.numberOfActiveRequestsByServer=function(e){return Re[e]},xe.requestHeap=be;var ze={},Le={};ze.add=function(e,t){if(!n.t(e))throw new r.t("host is required.");if(!n.t(t)||t<=0)throw new r.t("port is required to be greater than 0.");var a=e.toLowerCase()+":"+t;n.t(Le[a])||(Le[a]=!0)},ze.remove=function(e,t){if(!n.t(e))throw new r.t("host is required.");if(!n.t(t)||t<=0)throw new r.t("port is required to be greater than 0.");var a=e.toLowerCase()+":"+t;n.t(Le[a])&&delete Le[a]},ze.contains=function(e){if(!n.t(e))throw new r.t("url is required.");var t=function(e){var t=new G(e);t.normalize();var r=t.getAuthority();if(n.t(r)){if(-1!==r.indexOf("@")){var a=r.split("@");r=a[1]}if(-1===r.indexOf(":")){var i=t.getScheme();if(n.t(i)||(i=(i=window.location.protocol).substring(0,i.length-1)),"http"===i)r+=":80";else{if("https"!==i)return;r+=":443"}}return r}}(e);return!(!n.t(t)||!n.t(Le[t]))},ze.clear=function(){Le={}};var Ke={};function Me(e,t){if(!n.t(e))throw new r.t("identifier is required.");n.t(Ke[e])||(Ke[e]=!0,console.warn(n.e(t,e)))}Me.geometryOutlines="Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.",Me.geometryZIndex="Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored",Me.geometryHeightReference="Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored",Me.geometryExtrudedHeightReference="Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored";var Fe,Ne=function(){try{var e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch{return!1}}();function He(e,t,a,i){var o,s=e.query;if(!n.t(s)||0===s.length)return{};if(-1===s.indexOf("=")){var u={};u[s]=void 0,o=u}else o=function(e){if(!n.t(e))throw new r.t("queryString is required.");var t={};if(""===e)return t;for(var a=e.replace(/\+/g,"%20").split(/[&;]/),i=0,o=a.length;i<o;++i){var s=a[i].split("=");if(s.length>2){var u=a[i].indexOf("=");s=[a[i].substring(0,u),a[i].substring(u+1,a[i].length)]}var c=decodeURIComponent(s[0]),p=s[1];p=n.t(p)?decodeURIComponent(p):"";var f=t[c];"string"==typeof f?t[c]=[f,p]:Array.isArray(f)?f.push(p):t[c]=p}return t}(s);t._queryParameters=a?Xe(o,t._queryParameters,i):o,e.query=void 0}function Ve(e,t){var a=t._queryParameters,i=Object.keys(a);1!==i.length||n.t(a[i[0]])?e.query=function(e,t){if(!n.t(e))throw new r.t("obj is required.");var a="";for(var i in e)if(e.hasOwnProperty(i)){var o=e[i],s=encodeURIComponent(i)+"=";if(Array.isArray(o))for(var u=0,c=o.length;u<c;++u)a+=!0===t?s+encodeURI(o[u])+"&":s+encodeURIComponent(o[u])+"&";else a+=!0===t?s+encodeURI(o)+"&":s+encodeURIComponent(o)+"&"}return a.slice(0,-1)}(a):e.query=i[0]}function Ye(e,t){return n.t(e)?n.t(e.clone)?e.clone():re(e):t}function Je(e){if(e.state===pe.ISSUED||e.state===pe.ACTIVE)throw new c.t("The Resource is already being fetched.");e.state=pe.UNISSUED,e.deferred=void 0}function Xe(e,t,r){if(!r)return ne(e,t);var a=re(e,!0);for(var i in t)if(t.hasOwnProperty(i)){var o=a[i],s=t[i];n.t(o)?(Array.isArray(o)||(o=a[i]=[o]),a[i]=o.concat(s)):a[i]=Array.isArray(s)?s.slice():s}return a}function Ge(e){"string"==typeof(e=n.e(e,n.e.EMPTY_OBJECT))&&(e={url:e}),r.n.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Ye(e.templateValues,{}),this._queryParameters=Ye(e.queryParameters,{}),this.headers=Ye(e.headers,{}),this.request=n.e(e.request,new le),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=n.e(e.retryAttempts,0),this._retryCount=0;var t=new G(e.url);He(t,this,!0,!0),t.fragment=void 0,this._url=t.toString()}function Qe(e){var t=e.resource,r=e.flipY,a=e.preferImageBitmap,i=t.request;i.url=t.url,i.requestFunction=function(){var e=!1;!t.isDataUri&&!t.isBlobUri&&(e=t.isCrossOriginUrl);var o=n.c.defer();return Ge._Implementations.createImage(i,e,o,r,a),o.promise};var o=xe.request(i);if(n.t(o))return o.otherwise((function(e){return i.state!==pe.FAILED?n.c.reject(e):t.retryOnError(e).then((function(o){return o?(i.state=pe.UNISSUED,i.deferred=void 0,Qe({resource:t,flipY:r,preferImageBitmap:a})):n.c.reject(e)}))}))}function Ze(e,t,r){var a={};a[t]=r,e.setQueryParameters(a);var i=e.request;i.url=e.url,i.requestFunction=function(){var t=n.c.defer();return window[r]=function(e){t.resolve(e);try{delete window[r]}catch{window[r]=void 0}},Ge._Implementations.loadAndExecuteScript(e.url,r,t),t.promise};var o=xe.request(i);if(n.t(o))return o.otherwise((function(a){return i.state!==pe.FAILED?n.c.reject(a):e.retryOnError(a).then((function(o){return o?(i.state=pe.UNISSUED,i.deferred=void 0,Ze(e,t,r)):n.c.reject(a)}))}))}Ge.createIfNeeded=function(e){return e instanceof Ge?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new Ge({url:e})},Ge.supportsImageBitmapOptions=function(){if(n.t(Fe))return Fe;if("function"!=typeof createImageBitmap)return Fe=n.c.resolve(!1);return Fe=Ge.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWP4////fwAJ+wP9CNHoHgAAAABJRU5ErkJggg=="}).then((function(e){return createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none"})})).then((function(e){return!0})).otherwise((function(){return!1})),Fe},Object.defineProperties(Ge,{isBlobSupported:{get:function(){return Ne}}}),Object.defineProperties(Ge.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){var t=new G(e);He(t,this,!1),t.fragment=void 0,this._url=t.toString()}},extension:{get:function(){return function(e){if(!n.t(e))throw new r.t("uri is required.");var t=new G(e);t.normalize();var a=t.path,i=a.lastIndexOf("/");return-1!==i&&(a=a.substr(i+1)),i=a.lastIndexOf("."),-1===i?"":a.substr(i+1)}(this._url)}},isDataUri:{get:function(){return ce(this._url)}},isBlobUri:{get:function(){return se(this._url)}},isCrossOriginUrl:{get:function(){return function(e){n.t(ie)||(ie=document.createElement("a")),ie.href=window.location.href;var t=ie.host,r=ie.protocol;return ie.href=e,ie.href=ie.href,r!==ie.protocol||t!==ie.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}}}),Ge.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;var r=new G(this._url);e&&Ve(r,this);var a=r.toString().replace(/%7B/g,"{").replace(/%7D/g,"}"),i=this._templateValues;return a=a.replace(/{(.*?)}/g,(function(e,t){var r=i[t];return n.t(r)&&"s"==t?encodeURI(r):n.t(r)?encodeURIComponent(r):e})),t&&n.t(this.proxy)&&(a=this.proxy.getURL(a)),a},Ge.prototype.setQueryParameters=function(e,t){this._queryParameters=t?Xe(this._queryParameters,e,!1):Xe(e,this._queryParameters,!1)},Ge.prototype.appendQueryParameters=function(e){this._queryParameters=Xe(e,this._queryParameters,!0)},Ge.prototype.setTemplateValues=function(e,t){this._templateValues=t?ne(this._templateValues,e):ne(e,this._templateValues)},Ge.prototype.getDerivedResource=function(e){var t=this.clone();if(t._retryCount=0,n.t(e.url)){var r=new G(e.url);He(r,t,!0,n.e(e.preserveQueryParameters,!1)),r.fragment=void 0,t._url=r.resolve(new G(ae(this._url))).toString()}return n.t(e.queryParameters)&&(t._queryParameters=ne(e.queryParameters,t._queryParameters)),n.t(e.templateValues)&&(t._templateValues=ne(e.templateValues,t.templateValues)),n.t(e.headers)&&(t.headers=ne(e.headers,t.headers)),n.t(e.proxy)&&(t.proxy=e.proxy),n.t(e.request)&&(t.request=e.request),n.t(e.retryCallback)&&(t.retryCallback=e.retryCallback),n.t(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},Ge.prototype.retryOnError=function(e){var t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return n.c(!1);var r=this;return n.c(t(this,e)).then((function(e){return++r._retryCount,e}))},Ge.prototype.clone=function(e){return n.t(e)||(e=new Ge({url:this._url})),e._url=this._url,e._queryParameters=re(this._queryParameters),e._templateValues=re(this._templateValues),e.headers=re(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e},Ge.prototype.getBaseUri=function(e){return function(e,t){if(!n.t(e))throw new r.t("uri is required.");var a="",i=e.lastIndexOf("/");return-1!==i&&(a=e.substring(0,i+1)),t&&(e=new G(e),n.t(e.query)&&(a+="?"+e.query),n.t(e.fragment)&&(a+="#"+e.fragment)),a}(this.getUrlComponent(e),e)},Ge.prototype.appendForwardSlash=function(){this._url=function(e){return(0===e.length||"/"!==e[e.length-1])&&(e+="/"),e}(this._url)},Ge.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},Ge.fetchArrayBuffer=function(e){return new Ge(e).fetchArrayBuffer()},Ge.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},Ge.fetchBlob=function(e){return new Ge(e).fetchBlob()},Ge.prototype.fetchImage=function(e){e=n.e(e,n.e.EMPTY_OBJECT);var t=n.e(e.preferImageBitmap,!1),r=n.e(e.preferBlob,!1),a=n.e(e.flipY,!1);if(Je(this.request),!Ne||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!r)return Qe({resource:this,flipY:a,preferImageBitmap:t});var i,o,s,u=this.fetchBlob();return n.t(u)?Ge.supportsImageBitmapOptions().then((function(e){return i=e&&t,u})).then((function(e){if(n.t(e)){if(s=e,i)return Ge.createImageBitmapFromBlob(e,{flipY:a,premultiplyAlpha:!1});var t=window.URL.createObjectURL(e);return Qe({resource:o=new Ge({url:t}),flipY:a,preferImageBitmap:!1})}})).then((function(e){if(n.t(e))return e.blob=s,i||window.URL.revokeObjectURL(o.url),e})).otherwise((function(e){return n.t(o)&&window.URL.revokeObjectURL(o.url),e.blob=s,n.c.reject(e)})):void 0},Ge.fetchImage=function(e){return new Ge(e).fetchImage({flipY:e.flipY,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},Ge.prototype.fetchText=function(){return this.fetch({responseType:"text"})},Ge.fetchText=function(e){return new Ge(e).fetchText()},Ge.prototype.fetchJson=function(){var e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(n.t(e))return e.then((function(e){if(n.t(e))return JSON.parse(e)}))},Ge.fetchJson=function(e){return new Ge(e).fetchJson()},Ge.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},Ge.fetchXML=function(e){return new Ge(e).fetchXML()},Ge.prototype.fetchJsonp=function(e){var t;e=n.e(e,"callback"),Je(this.request);do{t="loadJsonp"+Math.random().toString().substring(2,8)}while(n.t(window[t]));return Ze(this,e,t)},Ge.fetchJsonp=function(e){return new Ge(e).fetchJsonp(e.callbackParameterName)},Ge.prototype._makeRequest=function(e){var t=this;Je(t.request);var r=t.request;r.url=t.url,r.requestFunction=function(a){var i=e.responseType,o=ne(e.headers,t.headers),s=e.overrideMimeType,u=e.method,c=e.data,p=n.c.defer(),f=n.t(a)?a:t.url,l=Ge._Implementations.loadWithXhr(f,i,u,c,o,p,s);return n.t(l)&&n.t(l.abort)&&(r.cancelFunction=function(){l.abort()}),p.promise};var a=xe.request(r);if(n.t(a))return a.then((function(e){return e})).otherwise((function(a){return r.state!==pe.FAILED?n.c.reject(a):t.retryOnError(a).then((function(i){return i?(r.state=pe.UNISSUED,r.deferred=void 0,t.fetch(e)):n.c.reject(a)}))}))};var We=/^data:(.*?)(;base64)?,(.*)$/;function $e(e,t){var r=decodeURIComponent(t);return e?atob(r):r}function et(e,t){for(var r=$e(e,t),n=new ArrayBuffer(r.length),a=new Uint8Array(n),i=0;i<r.length;i++)a[i]=r.charCodeAt(i);return n}function tt(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}Ge.prototype.fetch=function(e){return(e=Ye(e,{})).method="GET",this._makeRequest(e)},Ge.fetch=function(e){return new Ge(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge.prototype.delete=function(e){return(e=Ye(e,{})).method="DELETE",this._makeRequest(e)},Ge.delete=function(e){return new Ge(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},Ge.prototype.head=function(e){return(e=Ye(e,{})).method="HEAD",this._makeRequest(e)},Ge.head=function(e){return new Ge(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge.prototype.options=function(e){return(e=Ye(e,{})).method="OPTIONS",this._makeRequest(e)},Ge.options=function(e){return new Ge(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge.prototype.post=function(e,t){return r.n.defined("data",e),(t=Ye(t,{})).method="POST",t.data=e,this._makeRequest(t)},Ge.post=function(e){return new Ge(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge.prototype.put=function(e,t){return r.n.defined("data",e),(t=Ye(t,{})).method="PUT",t.data=e,this._makeRequest(t)},Ge.put=function(e){return new Ge(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge.prototype.patch=function(e,t){return r.n.defined("data",e),(t=Ye(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},Ge.patch=function(e){return new Ge(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Ge._Implementations={},Ge._Implementations.createImage=function(e,t,r,a,i){var o=e.url;Ge.supportsImageBitmapOptions().then((function(s){if(s&&i){var u=n.c.defer(),p=Ge._Implementations.loadWithXhr(o,"blob","GET",void 0,void 0,u,void 0,void 0,void 0);return n.t(p)&&n.t(p.abort)&&(e.cancelFunction=function(){p.abort()}),u.promise.then((function(e){if(n.t(e))return Ge.createImageBitmapFromBlob(e,{flipY:a,premultiplyAlpha:!1});r.reject(new c.t("Successfully retrieved "+o+" but it contained no content."))})).then(r.resolve)}!function(e,t,r){var n=new Image;n.onload=function(){r.resolve(n)},n.onerror=function(e){r.reject(e)},t&&(ze.contains(e)?n.crossOrigin="use-credentials":n.crossOrigin=""),n.src=e}(o,t,r)})).otherwise(r.reject)},Ge.createImageBitmapFromBlob=function(e,t){return r.n.defined("options",t),r.n.typeOf.bool("options.flipY",t.flipY),r.n.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none"})};var rt=typeof XMLHttpRequest>"u";Ge._Implementations.loadWithXhr=function(e,t,a,i,o,s,u){var p=We.exec(e);if(null===p){if(!rt){var f=new XMLHttpRequest;if(ze.contains(e)&&(f.withCredentials=!0),e=e.replace(/{/g,"%7B").replace(/}/g,"%7D"),f.open(a,e,!0),n.t(u)&&n.t(f.overrideMimeType)&&f.overrideMimeType(u),n.t(o))for(var l in o)o.hasOwnProperty(l)&&f.setRequestHeader(l,o[l]);n.t(t)&&(f.responseType=t);var h=!1;return"string"==typeof e&&(h=0===e.indexOf("file://")||typeof window<"u"&&"file://"===window.location.origin),f.onload=function(){if(!(f.status<200||f.status>=300)||h&&0===f.status){var e=f.response,r=f.responseType;if("HEAD"===a||"OPTIONS"===a){var i=f.getAllResponseHeaders().trim().split(/[\r\n]+/),o={};return i.forEach((function(e){var t=e.split(": "),r=t.shift();o[r]=t.join(": ")})),void s.resolve(o)}if(204===f.status)s.resolve();else if(!n.t(e)||n.t(t)&&r!==t)if("json"===t&&"string"==typeof e)try{s.resolve(JSON.parse(e))}catch(e){s.reject(e)}else(""===r||"document"===r)&&n.t(f.responseXML)&&f.responseXML.hasChildNodes()?s.resolve(f.responseXML):""!==r&&"text"!==r||!n.t(f.responseText)?s.reject(new c.t("Invalid XMLHttpRequest response type.")):s.resolve(f.responseText);else s.resolve(e)}else s.reject(new he(f.status,f.response,f.getAllResponseHeaders()))},f.onerror=function(e){s.reject(new he)},f.send(i),f}!function(e,t,r,n,a,i,o){var s=require("url").parse(e),u="https:"===s.protocol?require("https"):require("http"),p=require("zlib"),f={protocol:s.protocol,hostname:s.hostname,port:s.port,path:s.path,query:s.query,method:r,headers:a};u.request(f).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)i.reject(new he(e.statusCode,e,e.headers));else{var r=[];e.on("data",(function(e){r.push(e)})),e.on("end",(function(){var n=Buffer.concat(r);"gzip"===e.headers["content-encoding"]?p.gunzip(n,(function(e,r){e?i.reject(new c.t("Error decompressing response.")):i.resolve(tt(r,t))})):i.resolve(tt(n,t))}))}})).on("error",(function(e){i.reject(new he)})).end()}(e,t,a,0,o,s)}else s.resolve(function(e,t){t=n.e(t,"");var a=e[1],i=!!e[2],o=e[3];switch(t){case"":case"text":return $e(i,o);case"arraybuffer":return et(i,o);case"blob":var s=et(i,o);return new Blob([s],{type:a});case"document":return(new DOMParser).parseFromString($e(i,o),a);case"json":return JSON.parse($e(i,o));default:throw new r.t("Unhandled responseType: "+t)}}(p,t))},Ge._Implementations.loadAndExecuteScript=function(e,t,r){return function(e){var t=n.c.defer(),r=document.createElement("script");r.async=!0,r.src=e;var a=document.getElementsByTagName("head")[0];return r.onload=function(){r.onload=void 0,a.removeChild(r),t.resolve()},r.onerror=function(e){t.reject(e)},a.appendChild(r),t.promise}(e).otherwise(r.reject)},Ge._DefaultImplementations={},Ge._DefaultImplementations.createImage=Ge._Implementations.createImage,Ge._DefaultImplementations.loadWithXhr=Ge._Implementations.loadWithXhr,Ge._DefaultImplementations.loadAndExecuteScript=Ge._Implementations.loadAndExecuteScript,Ge.DEFAULT=Object.freeze(new Ge({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var nt,at,it,ot=/((?:.*\/)|^)Cesium\.js$/;function st(e){return typeof document>"u"?e:(n.t(nt)||(nt=document.createElement("a")),nt.href=e,nt.href=nt.href,nt.href)}function ut(){if(n.t(at))return at;var e;if(e=typeof CESIUM_BASE_URL<"u"?CESIUM_BASE_URL:"object"==typeof define&&n.t(define.amd)&&!define.amd.toUrlUndefined&&n.t(require.toUrl)?ae("..",ft("Core/buildModuleUrl.js")):function(){for(var e=document.getElementsByTagName("script"),t=0,r=e.length;t<r;++t){var n=e[t].getAttribute("src"),a=ot.exec(n);if(null!==a)return a[1]}}(),!n.t(e))throw new r.t("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return(at=new Ge({url:st(e)})).appendForwardSlash(),at}function ct(e){return st(require.toUrl("../"+e))}function pt(e){return ut().getDerivedResource({url:e}).url}function ft(e){return n.t(it)||(it="object"==typeof define&&n.t(define.amd)&&!define.amd.toUrlUndefined&&n.t(require.toUrl)?ct:pt),it(e)}ft._cesiumScriptRegex=ot,ft._buildModuleUrlFromBaseUrl=pt,ft._clearBaseResource=function(){at=void 0},ft.setBaseUrl=function(e){at=Ge.DEFAULT.getDerivedResource({url:e})},ft.getCesiumBaseUrl=ut,e.c=f,e.e=Me,e.i=p,e.o=ft,e.t=Ge}));
