define(["exports","./Check-3aa71481","./when-515d5295","./FeatureDetection-7fae0d5a","./Math-5e38123d"],(function(e,r,t,o,n){"use strict";function s(e,r,t){return t<0&&(t+=1),t>1&&(t-=1),6*t<1?e+6*(r-e)*t:2*t<1?r:3*t<2?e+(r-e)*(2/3-t)*6:e}function f(e,r,o,n){this.red=t.e(e,1),this.green=t.e(r,1),this.blue=t.e(o,1),this.alpha=t.e(n,1)}var l,C,i;f.fromCartesian4=function(e,o){return r.n.typeOf.object("cartesian",e),t.t(o)?(o.red=e.x,o.green=e.y,o.blue=e.z,o.alpha=e.w,o):new f(e.x,e.y,e.z,e.w)},f.fromBytes=function(e,r,o,n,s){return e=f.byteToFloat(t.e(e,255)),r=f.byteToFloat(t.e(r,255)),o=f.byteToFloat(t.e(o,255)),n=f.byteToFloat(t.e(n,255)),t.t(s)?(s.red=e,s.green=r,s.blue=o,s.alpha=n,s):new f(e,r,o,n)},f.fromAlpha=function(e,o,n){return r.n.typeOf.object("color",e),r.n.typeOf.number("alpha",o),t.t(n)?(n.red=e.red,n.green=e.green,n.blue=e.blue,n.alpha=o,n):new f(e.red,e.green,e.blue,o)},o.o.supportsTypedArrays()&&(l=new ArrayBuffer(4),C=new Uint32Array(l),i=new Uint8Array(l)),f.fromRgba=function(e,r){return C[0]=e,f.fromBytes(i[0],i[1],i[2],i[3],r)},f.byteToRgba=function(e,r,t,o){return i[0]=e,i[1]=r,i[2]=t,i[3]=o,C[0]},f.fromHsl=function(e,r,o,n,l){e=t.e(e,0)%1,r=t.e(r,0),o=t.e(o,0),n=t.e(n,1);var C=o,i=o,a=o;if(0!==r){var O,b=2*o-(O=o<.5?o*(1+r):o+r-o*r);C=s(b,O,e+1/3),i=s(b,O,e),a=s(b,O,e-1/3)}return t.t(l)?(l.red=C,l.green=i,l.blue=a,l.alpha=n,l):new f(C,i,a,n)},f.fromRandom=function(e,o){var s=(e=t.e(e,t.e.EMPTY_OBJECT)).red;if(!t.t(s)){var l=t.e(e.minimumRed,0),C=t.e(e.maximumRed,1);r.n.typeOf.number.lessThanOrEquals("minimumRed",l,C),s=l+n.n.nextRandomNumber()*(C-l)}var i=e.green;if(!t.t(i)){var a=t.e(e.minimumGreen,0),O=t.e(e.maximumGreen,1);r.n.typeOf.number.lessThanOrEquals("minimumGreen",a,O),i=a+n.n.nextRandomNumber()*(O-a)}var b=e.blue;if(!t.t(b)){var E=t.e(e.minimumBlue,0),c=t.e(e.maximumBlue,1);r.n.typeOf.number.lessThanOrEquals("minimumBlue",E,c),b=E+n.n.nextRandomNumber()*(c-E)}var g=e.alpha;if(!t.t(g)){var m=t.e(e.minimumAlpha,0),S=t.e(e.maximumAlpha,1);r.n.typeOf.number.lessThanOrEquals("minumumAlpha",m,S),g=m+n.n.nextRandomNumber()*(S-m)}return t.t(o)?(o.red=s,o.green=i,o.blue=b,o.alpha=g,o):new f(s,i,b,g)};var a=/^#([0-9a-f])([0-9a-f])([0-9a-f])$/i,O=/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i,b=/^rgba?\(\s*([0-9.]+%?)\s*,\s*([0-9.]+%?)\s*,\s*([0-9.]+%?)(?:\s*,\s*([0-9.]+))?\s*\)$/i,E=/^hsla?\(\s*([0-9.]+)\s*,\s*([0-9.]+%)\s*,\s*([0-9.]+%)(?:\s*,\s*([0-9.]+))?\s*\)$/i;f.fromCssColorString=function(e,o){r.n.typeOf.string("color",e),t.t(o)||(o=new f);var n=f[e.toUpperCase()];if(t.t(n))return f.clone(n,o),o;var s=a.exec(e);return null!==s?(o.red=parseInt(s[1],16)/15,o.green=parseInt(s[2],16)/15,o.blue=parseInt(s[3],16)/15,o.alpha=1,o):null!==(s=O.exec(e))?(o.red=parseInt(s[1],16)/255,o.green=parseInt(s[2],16)/255,o.blue=parseInt(s[3],16)/255,o.alpha=1,o):null!==(s=b.exec(e))?(o.red=parseFloat(s[1])/("%"===s[1].substr(-1)?100:255),o.green=parseFloat(s[2])/("%"===s[2].substr(-1)?100:255),o.blue=parseFloat(s[3])/("%"===s[3].substr(-1)?100:255),o.alpha=parseFloat(t.e(s[4],"1.0")),o):null!==(s=E.exec(e))?f.fromHsl(parseFloat(s[1])/360,parseFloat(s[2])/100,parseFloat(s[3])/100,parseFloat(t.e(s[4],"1.0")),o):o=void 0},f.packedLength=4,f.pack=function(e,o,n){return r.n.typeOf.object("value",e),r.n.defined("array",o),n=t.e(n,0),o[n++]=e.red,o[n++]=e.green,o[n++]=e.blue,o[n]=e.alpha,o},f.unpack=function(e,o,n){return r.n.defined("array",e),o=t.e(o,0),t.t(n)||(n=new f),n.red=e[o++],n.green=e[o++],n.blue=e[o++],n.alpha=e[o],n},f.byteToFloat=function(e){return e/255},f.floatToByte=function(e){return 1===e?255:256*e|0},f.clone=function(e,r){if(t.t(e))return t.t(r)?(r.red=e.red,r.green=e.green,r.blue=e.blue,r.alpha=e.alpha,r):new f(e.red,e.green,e.blue,e.alpha)},f.equals=function(e,r){return e===r||t.t(e)&&t.t(r)&&e.red===r.red&&e.green===r.green&&e.blue===r.blue&&e.alpha===r.alpha},f.equalsArray=function(e,r,t){return e.red===r[t]&&e.green===r[t+1]&&e.blue===r[t+2]&&e.alpha===r[t+3]},f.prototype.clone=function(e){return f.clone(this,e)},f.prototype.equals=function(e){return f.equals(this,e)},f.prototype.equalsEpsilon=function(e,r){return this===e||t.t(e)&&Math.abs(this.red-e.red)<=r&&Math.abs(this.green-e.green)<=r&&Math.abs(this.blue-e.blue)<=r&&Math.abs(this.alpha-e.alpha)<=r},f.prototype.toString=function(){return"("+this.red+", "+this.green+", "+this.blue+", "+this.alpha+")"},f.prototype.toCssColorString=function(){var e=f.floatToByte(this.red),r=f.floatToByte(this.green),t=f.floatToByte(this.blue);return 1===this.alpha?"rgb("+e+","+r+","+t+")":"rgba("+e+","+r+","+t+","+this.alpha+")"},f.prototype.toCssHexString=function(){let e=f.floatToByte(this.red).toString(16);e.length<2&&(e=`0${e}`);let r=f.floatToByte(this.green).toString(16);r.length<2&&(r=`0${r}`);let t=f.floatToByte(this.blue).toString(16);if(t.length<2&&(t=`0${t}`),this.alpha<1){let o=f.floatToByte(this.alpha).toString(16);return o.length<2&&(o=`0${o}`),`#${e}${r}${t}${o}`}return`#${e}${r}${t}`},f.prototype.toBytes=function(e){var r=f.floatToByte(this.red),o=f.floatToByte(this.green),n=f.floatToByte(this.blue),s=f.floatToByte(this.alpha);return t.t(e)?(e[0]=r,e[1]=o,e[2]=n,e[3]=s,e):[r,o,n,s]},f.prototype.toRgba=function(){return i[0]=f.floatToByte(this.red),i[1]=f.floatToByte(this.green),i[2]=f.floatToByte(this.blue),i[3]=f.floatToByte(this.alpha),C[0]},f.prototype.brighten=function(e,t){return r.n.typeOf.number("magnitude",e),r.n.typeOf.number.greaterThanOrEquals("magnitude",e,0),r.n.typeOf.object("result",t),e=1-e,t.red=1-(1-this.red)*e,t.green=1-(1-this.green)*e,t.blue=1-(1-this.blue)*e,t.alpha=this.alpha,t},f.prototype.darken=function(e,t){return r.n.typeOf.number("magnitude",e),r.n.typeOf.number.greaterThanOrEquals("magnitude",e,0),r.n.typeOf.object("result",t),e=1-e,t.red=this.red*e,t.green=this.green*e,t.blue=this.blue*e,t.alpha=this.alpha,t},f.prototype.withAlpha=function(e,r){return f.fromAlpha(this,e,r)},f.add=function(e,t,o){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",o),o.red=e.red+t.red,o.green=e.green+t.green,o.blue=e.blue+t.blue,o.alpha=e.alpha+t.alpha,o},f.subtract=function(e,t,o){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",o),o.red=e.red-t.red,o.green=e.green-t.green,o.blue=e.blue-t.blue,o.alpha=e.alpha-t.alpha,o},f.multiply=function(e,t,o){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",o),o.red=e.red*t.red,o.green=e.green*t.green,o.blue=e.blue*t.blue,o.alpha=e.alpha*t.alpha,o},f.divide=function(e,t,o){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",o),o.red=e.red/t.red,o.green=e.green/t.green,o.blue=e.blue/t.blue,o.alpha=e.alpha/t.alpha,o},f.mod=function(e,t,o){return r.n.typeOf.object("left",e),r.n.typeOf.object("right",t),r.n.typeOf.object("result",o),o.red=e.red%t.red,o.green=e.green%t.green,o.blue=e.blue%t.blue,o.alpha=e.alpha%t.alpha,o},f.lerp=function(e,t,o,s){return r.n.typeOf.object("start",e),r.n.typeOf.object("end",t),r.n.typeOf.number("t",o),r.n.typeOf.object("result",s),s.red=n.n.lerp(e.red,t.red,o),s.green=n.n.lerp(e.green,t.green,o),s.blue=n.n.lerp(e.blue,t.blue,o),s.alpha=n.n.lerp(e.alpha,t.alpha,o),s},f.multiplyByScalar=function(e,t,o){return r.n.typeOf.object("color",e),r.n.typeOf.number("scalar",t),r.n.typeOf.object("result",o),o.red=e.red*t,o.green=e.green*t,o.blue=e.blue*t,o.alpha=e.alpha*t,o},f.divideByScalar=function(e,t,o){return r.n.typeOf.object("color",e),r.n.typeOf.number("scalar",t),r.n.typeOf.object("result",o),o.red=e.red/t,o.green=e.green/t,o.blue=e.blue/t,o.alpha=e.alpha/t,o},f.ALICEBLUE=Object.freeze(f.fromCssColorString("#F0F8FF")),f.ANTIQUEWHITE=Object.freeze(f.fromCssColorString("#FAEBD7")),f.AQUA=Object.freeze(f.fromCssColorString("#00FFFF")),f.AQUAMARINE=Object.freeze(f.fromCssColorString("#7FFFD4")),f.AZURE=Object.freeze(f.fromCssColorString("#F0FFFF")),f.BEIGE=Object.freeze(f.fromCssColorString("#F5F5DC")),f.BISQUE=Object.freeze(f.fromCssColorString("#FFE4C4")),f.BLACK=Object.freeze(f.fromCssColorString("#000000")),f.BLANCHEDALMOND=Object.freeze(f.fromCssColorString("#FFEBCD")),f.BLUE=Object.freeze(f.fromCssColorString("#0000FF")),f.BLUEVIOLET=Object.freeze(f.fromCssColorString("#8A2BE2")),f.BROWN=Object.freeze(f.fromCssColorString("#A52A2A")),f.BURLYWOOD=Object.freeze(f.fromCssColorString("#DEB887")),f.CADETBLUE=Object.freeze(f.fromCssColorString("#5F9EA0")),f.CHARTREUSE=Object.freeze(f.fromCssColorString("#7FFF00")),f.CHOCOLATE=Object.freeze(f.fromCssColorString("#D2691E")),f.CORAL=Object.freeze(f.fromCssColorString("#FF7F50")),f.CORNFLOWERBLUE=Object.freeze(f.fromCssColorString("#6495ED")),f.CORNSILK=Object.freeze(f.fromCssColorString("#FFF8DC")),f.CRIMSON=Object.freeze(f.fromCssColorString("#DC143C")),f.CYAN=Object.freeze(f.fromCssColorString("#00FFFF")),f.DARKBLUE=Object.freeze(f.fromCssColorString("#00008B")),f.DARKCYAN=Object.freeze(f.fromCssColorString("#008B8B")),f.DARKGOLDENROD=Object.freeze(f.fromCssColorString("#B8860B")),f.DARKGRAY=Object.freeze(f.fromCssColorString("#A9A9A9")),f.DARKGREEN=Object.freeze(f.fromCssColorString("#006400")),f.DARKGREY=f.DARKGRAY,f.DARKKHAKI=Object.freeze(f.fromCssColorString("#BDB76B")),f.DARKMAGENTA=Object.freeze(f.fromCssColorString("#8B008B")),f.DARKOLIVEGREEN=Object.freeze(f.fromCssColorString("#556B2F")),f.DARKORANGE=Object.freeze(f.fromCssColorString("#FF8C00")),f.DARKORCHID=Object.freeze(f.fromCssColorString("#9932CC")),f.DARKRED=Object.freeze(f.fromCssColorString("#8B0000")),f.DARKSALMON=Object.freeze(f.fromCssColorString("#E9967A")),f.DARKSEAGREEN=Object.freeze(f.fromCssColorString("#8FBC8F")),f.DARKSLATEBLUE=Object.freeze(f.fromCssColorString("#483D8B")),f.DARKSLATEGRAY=Object.freeze(f.fromCssColorString("#2F4F4F")),f.DARKSLATEGREY=f.DARKSLATEGRAY,f.DARKTURQUOISE=Object.freeze(f.fromCssColorString("#00CED1")),f.DARKVIOLET=Object.freeze(f.fromCssColorString("#9400D3")),f.DEEPPINK=Object.freeze(f.fromCssColorString("#FF1493")),f.DEEPSKYBLUE=Object.freeze(f.fromCssColorString("#00BFFF")),f.DIMGRAY=Object.freeze(f.fromCssColorString("#696969")),f.DIMGREY=f.DIMGRAY,f.DODGERBLUE=Object.freeze(f.fromCssColorString("#1E90FF")),f.FIREBRICK=Object.freeze(f.fromCssColorString("#B22222")),f.FLORALWHITE=Object.freeze(f.fromCssColorString("#FFFAF0")),f.FORESTGREEN=Object.freeze(f.fromCssColorString("#228B22")),f.FUCHSIA=Object.freeze(f.fromCssColorString("#FF00FF")),f.GAINSBORO=Object.freeze(f.fromCssColorString("#DCDCDC")),f.GHOSTWHITE=Object.freeze(f.fromCssColorString("#F8F8FF")),f.GOLD=Object.freeze(f.fromCssColorString("#FFD700")),f.GOLDENROD=Object.freeze(f.fromCssColorString("#DAA520")),f.GRAY=Object.freeze(f.fromCssColorString("#808080")),f.GREEN=Object.freeze(f.fromCssColorString("#008000")),f.GREENYELLOW=Object.freeze(f.fromCssColorString("#ADFF2F")),f.GREY=f.GRAY,f.HONEYDEW=Object.freeze(f.fromCssColorString("#F0FFF0")),f.HOTPINK=Object.freeze(f.fromCssColorString("#FF69B4")),f.INDIANRED=Object.freeze(f.fromCssColorString("#CD5C5C")),f.INDIGO=Object.freeze(f.fromCssColorString("#4B0082")),f.IVORY=Object.freeze(f.fromCssColorString("#FFFFF0")),f.KHAKI=Object.freeze(f.fromCssColorString("#F0E68C")),f.LAVENDER=Object.freeze(f.fromCssColorString("#E6E6FA")),f.LAVENDAR_BLUSH=Object.freeze(f.fromCssColorString("#FFF0F5")),f.LAWNGREEN=Object.freeze(f.fromCssColorString("#7CFC00")),f.LEMONCHIFFON=Object.freeze(f.fromCssColorString("#FFFACD")),f.LIGHTBLUE=Object.freeze(f.fromCssColorString("#ADD8E6")),f.LIGHTCORAL=Object.freeze(f.fromCssColorString("#F08080")),f.LIGHTCYAN=Object.freeze(f.fromCssColorString("#E0FFFF")),f.LIGHTGOLDENRODYELLOW=Object.freeze(f.fromCssColorString("#FAFAD2")),f.LIGHTGRAY=Object.freeze(f.fromCssColorString("#D3D3D3")),f.LIGHTGREEN=Object.freeze(f.fromCssColorString("#90EE90")),f.LIGHTGREY=f.LIGHTGRAY,f.LIGHTPINK=Object.freeze(f.fromCssColorString("#FFB6C1")),f.LIGHTSEAGREEN=Object.freeze(f.fromCssColorString("#20B2AA")),f.LIGHTSKYBLUE=Object.freeze(f.fromCssColorString("#87CEFA")),f.LIGHTSLATEGRAY=Object.freeze(f.fromCssColorString("#778899")),f.LIGHTSLATEGREY=f.LIGHTSLATEGRAY,f.LIGHTSTEELBLUE=Object.freeze(f.fromCssColorString("#B0C4DE")),f.LIGHTYELLOW=Object.freeze(f.fromCssColorString("#FFFFE0")),f.LIME=Object.freeze(f.fromCssColorString("#00FF00")),f.LIMEGREEN=Object.freeze(f.fromCssColorString("#32CD32")),f.LINEN=Object.freeze(f.fromCssColorString("#FAF0E6")),f.MAGENTA=Object.freeze(f.fromCssColorString("#FF00FF")),f.MAROON=Object.freeze(f.fromCssColorString("#800000")),f.MEDIUMAQUAMARINE=Object.freeze(f.fromCssColorString("#66CDAA")),f.MEDIUMBLUE=Object.freeze(f.fromCssColorString("#0000CD")),f.MEDIUMORCHID=Object.freeze(f.fromCssColorString("#BA55D3")),f.MEDIUMPURPLE=Object.freeze(f.fromCssColorString("#9370DB")),f.MEDIUMSEAGREEN=Object.freeze(f.fromCssColorString("#3CB371")),f.MEDIUMSLATEBLUE=Object.freeze(f.fromCssColorString("#7B68EE")),f.MEDIUMSPRINGGREEN=Object.freeze(f.fromCssColorString("#00FA9A")),f.MEDIUMTURQUOISE=Object.freeze(f.fromCssColorString("#48D1CC")),f.MEDIUMVIOLETRED=Object.freeze(f.fromCssColorString("#C71585")),f.MIDNIGHTBLUE=Object.freeze(f.fromCssColorString("#191970")),f.MINTCREAM=Object.freeze(f.fromCssColorString("#F5FFFA")),f.MISTYROSE=Object.freeze(f.fromCssColorString("#FFE4E1")),f.MOCCASIN=Object.freeze(f.fromCssColorString("#FFE4B5")),f.NAVAJOWHITE=Object.freeze(f.fromCssColorString("#FFDEAD")),f.NAVY=Object.freeze(f.fromCssColorString("#000080")),f.OLDLACE=Object.freeze(f.fromCssColorString("#FDF5E6")),f.OLIVE=Object.freeze(f.fromCssColorString("#808000")),f.OLIVEDRAB=Object.freeze(f.fromCssColorString("#6B8E23")),f.ORANGE=Object.freeze(f.fromCssColorString("#FFA500")),f.ORANGERED=Object.freeze(f.fromCssColorString("#FF4500")),f.ORCHID=Object.freeze(f.fromCssColorString("#DA70D6")),f.PALEGOLDENROD=Object.freeze(f.fromCssColorString("#EEE8AA")),f.PALEGREEN=Object.freeze(f.fromCssColorString("#98FB98")),f.PALETURQUOISE=Object.freeze(f.fromCssColorString("#AFEEEE")),f.PALEVIOLETRED=Object.freeze(f.fromCssColorString("#DB7093")),f.PAPAYAWHIP=Object.freeze(f.fromCssColorString("#FFEFD5")),f.PEACHPUFF=Object.freeze(f.fromCssColorString("#FFDAB9")),f.PERU=Object.freeze(f.fromCssColorString("#CD853F")),f.PINK=Object.freeze(f.fromCssColorString("#FFC0CB")),f.PLUM=Object.freeze(f.fromCssColorString("#DDA0DD")),f.POWDERBLUE=Object.freeze(f.fromCssColorString("#B0E0E6")),f.PURPLE=Object.freeze(f.fromCssColorString("#800080")),f.RED=Object.freeze(f.fromCssColorString("#FF0000")),f.ROSYBROWN=Object.freeze(f.fromCssColorString("#BC8F8F")),f.ROYALBLUE=Object.freeze(f.fromCssColorString("#4169E1")),f.SADDLEBROWN=Object.freeze(f.fromCssColorString("#8B4513")),f.SALMON=Object.freeze(f.fromCssColorString("#FA8072")),f.SANDYBROWN=Object.freeze(f.fromCssColorString("#F4A460")),f.SEAGREEN=Object.freeze(f.fromCssColorString("#2E8B57")),f.SEASHELL=Object.freeze(f.fromCssColorString("#FFF5EE")),f.SIENNA=Object.freeze(f.fromCssColorString("#A0522D")),f.SILVER=Object.freeze(f.fromCssColorString("#C0C0C0")),f.SKYBLUE=Object.freeze(f.fromCssColorString("#87CEEB")),f.SLATEBLUE=Object.freeze(f.fromCssColorString("#6A5ACD")),f.SLATEGRAY=Object.freeze(f.fromCssColorString("#708090")),f.SLATEGREY=f.SLATEGRAY,f.SNOW=Object.freeze(f.fromCssColorString("#FFFAFA")),f.SPRINGGREEN=Object.freeze(f.fromCssColorString("#00FF7F")),f.STEELBLUE=Object.freeze(f.fromCssColorString("#4682B4")),f.TAN=Object.freeze(f.fromCssColorString("#D2B48C")),f.TEAL=Object.freeze(f.fromCssColorString("#008080")),f.THISTLE=Object.freeze(f.fromCssColorString("#D8BFD8")),f.TOMATO=Object.freeze(f.fromCssColorString("#FF6347")),f.TURQUOISE=Object.freeze(f.fromCssColorString("#40E0D0")),f.VIOLET=Object.freeze(f.fromCssColorString("#EE82EE")),f.WHEAT=Object.freeze(f.fromCssColorString("#F5DEB3")),f.WHITE=Object.freeze(f.fromCssColorString("#FFFFFF")),f.WHITESMOKE=Object.freeze(f.fromCssColorString("#F5F5F5")),f.YELLOW=Object.freeze(f.fromCssColorString("#FFFF00")),f.YELLOWGREEN=Object.freeze(f.fromCssColorString("#9ACD32")),f.TRANSPARENT=Object.freeze(new f(0,0,0,0)),e.e=f}));
