define(["exports","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Cartesian4-034d54d5","./Check-3aa71481","./ComponentDatatype-d430c7f7","./when-515d5295","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./PrimitiveType-b38a4004","./Math-5e38123d","./Intersect-53434a77","./Plane-92c15089","./VertexFormat-e844760b"],(function(t,e,r,a,i,n,o,s,f,h,u,p,c,l){"use strict";function m(t){this.planes=o.e(t,[])}var w=[new r.a,new r.a,new r.a];r.a.clone(r.a.UNIT_X,w[0]),r.a.clone(r.a.UNIT_Y,w[1]),r.a.clone(r.a.UNIT_Z,w[2]);var d=new r.a,_=new r.a,y=new c.n(new r.a(1,0,0),0);function g(t){t=o.e(t,o.e.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=o.e(t.near,1),this._near=this.near,this.far=o.e(t.far,5e8),this._far=this.far,this._cullingVolume=new m,this._orthographicMatrix=new h.c}function v(t){if(!(o.t(t.right)&&o.t(t.left)&&o.t(t.top)&&o.t(t.bottom)&&o.t(t.near)&&o.t(t.far)))throw new i.t("right, left, top, bottom, near, or far parameters are not set.");if(t.top!==t._top||t.bottom!==t._bottom||t.left!==t._left||t.right!==t._right||t.near!==t._near||t.far!==t._far){if(t.left>t.right)throw new i.t("right must be greater than left.");if(t.bottom>t.top)throw new i.t("top must be greater than bottom.");if(t.near<=0||t.near>t.far)throw new i.t("near must be greater than zero and less than far.");t._left=t.left,t._right=t.right,t._top=t.top,t._bottom=t.bottom,t._near=t.near,t._far=t.far,t._orthographicMatrix=h.c.computeOrthographicOffCenter(t.left,t.right,t.bottom,t.top,t.near,t.far,t._orthographicMatrix)}}m.fromBoundingSphere=function(t,e){if(!o.t(t))throw new i.t("boundingSphere is required.");o.t(e)||(e=new m);var n=w.length,s=e.planes;s.length=2*n;for(var f=t.center,h=t.radius,u=0,p=0;p<n;++p){var c=w[p],l=s[u],y=s[u+1];o.t(l)||(l=s[u]=new a.a),o.t(y)||(y=s[u+1]=new a.a),r.a.multiplyByScalar(c,-h,d),r.a.add(f,d,d),l.x=c.x,l.y=c.y,l.z=c.z,l.w=-r.a.dot(c,d),r.a.multiplyByScalar(c,h,d),r.a.add(f,d,d),y.x=-c.x,y.y=-c.y,y.z=-c.z,y.w=-r.a.dot(r.a.negate(c,_),d),u+=2}return e},m.prototype.computeVisibility=function(t){if(!o.t(t))throw new i.t("boundingVolume is required.");for(var e=this.planes,r=!1,a=0,n=e.length;a<n;++a){var s=t.intersectPlane(c.n.fromCartesian4(e[a],y));if(s===p.S.OUTSIDE)return p.S.OUTSIDE;s===p.S.INTERSECTING&&(r=!0)}return r?p.S.INTERSECTING:p.S.INSIDE},m.prototype.computeVisibilityWithPlaneMask=function(t,e){if(!o.t(t))throw new i.t("boundingVolume is required.");if(!o.t(e))throw new i.t("parentPlaneMask is required.");if(e===m.MASK_OUTSIDE||e===m.MASK_INSIDE)return e;for(var r=m.MASK_INSIDE,a=this.planes,n=0,s=a.length;n<s;++n){var f=n<31?1<<n:0;if(!(n<31&&0==(e&f))){var h=t.intersectPlane(c.n.fromCartesian4(a[n],y));if(h===p.S.OUTSIDE)return m.MASK_OUTSIDE;h===p.S.INTERSECTING&&(r|=f)}}return r},m.MASK_OUTSIDE=4294967295,m.MASK_INSIDE=0,m.MASK_INDETERMINATE=2147483647,Object.defineProperties(g.prototype,{projectionMatrix:{get:function(){return v(this),this._orthographicMatrix}}});var b=new r.a,x=new r.a,z=new r.a,C=new r.a;function O(t){t=o.e(t,o.e.EMPTY_OBJECT),this._offCenterFrustum=new g,this.width=t.width,this._width=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=o.e(t.near,1),this._near=this.near,this.far=o.e(t.far,5e8),this._far=this.far}function P(t){if(!(o.t(t.width)&&o.t(t.aspectRatio)&&o.t(t.near)&&o.t(t.far)))throw new i.t("width, aspectRatio, near, or far parameters are not set.");var e=t._offCenterFrustum;if(t.width!==t._width||t.aspectRatio!==t._aspectRatio||t.near!==t._near||t.far!==t._far){if(t.aspectRatio<0)throw new i.t("aspectRatio must be positive.");if(t.near<0||t.near>t.far)throw new i.t("near must be greater than zero and less than far.");t._aspectRatio=t.aspectRatio,t._width=t.width,t._near=t.near,t._far=t.far;var r=1/t.aspectRatio;e.right=.5*t.width,e.left=-e.right,e.top=r*e.right,e.bottom=-e.top,e.near=t.near,e.far=t.far}}function R(t){t=o.e(t,o.e.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=o.e(t.near,1),this._near=this.near,this.far=o.e(t.far,5e8),this._far=this.far,this._cullingVolume=new m,this._perspectiveMatrix=new h.c,this._infinitePerspective=new h.c}function E(t){if(!(o.t(t.right)&&o.t(t.left)&&o.t(t.top)&&o.t(t.bottom)&&o.t(t.near)&&o.t(t.far)))throw new i.t("right, left, top, bottom, near, or far parameters are not set.");var e=t.top,r=t.bottom,a=t.right,n=t.left,s=t.near,f=t.far;if(e!==t._top||r!==t._bottom||n!==t._left||a!==t._right||s!==t._near||f!==t._far){if(t.near<=0||t.near>t.far)throw new i.t("near must be greater than zero and less than far.");t._left=n,t._right=a,t._top=e,t._bottom=r,t._near=s,t._far=f,t._perspectiveMatrix=h.c.computePerspectiveOffCenter(n,a,r,e,s,f,t._perspectiveMatrix),t._infinitePerspective=h.c.computeInfinitePerspectiveOffCenter(n,a,r,e,s,t._infinitePerspective)}}g.prototype.computeCullingVolume=function(t,e,n){if(!o.t(t))throw new i.t("position is required.");if(!o.t(e))throw new i.t("direction is required.");if(!o.t(n))throw new i.t("up is required.");var s=this._cullingVolume.planes,f=this.top,h=this.bottom,u=this.right,p=this.left,c=this.near,l=this.far,m=r.a.cross(e,n,b);r.a.normalize(m,m);var w=x;r.a.multiplyByScalar(e,c,w),r.a.add(t,w,w);var d=z;r.a.multiplyByScalar(m,p,d),r.a.add(w,d,d);var _=s[0];return o.t(_)||(_=s[0]=new a.a),_.x=m.x,_.y=m.y,_.z=m.z,_.w=-r.a.dot(m,d),r.a.multiplyByScalar(m,u,d),r.a.add(w,d,d),_=s[1],o.t(_)||(_=s[1]=new a.a),_.x=-m.x,_.y=-m.y,_.z=-m.z,_.w=-r.a.dot(r.a.negate(m,C),d),r.a.multiplyByScalar(n,h,d),r.a.add(w,d,d),_=s[2],o.t(_)||(_=s[2]=new a.a),_.x=n.x,_.y=n.y,_.z=n.z,_.w=-r.a.dot(n,d),r.a.multiplyByScalar(n,f,d),r.a.add(w,d,d),_=s[3],o.t(_)||(_=s[3]=new a.a),_.x=-n.x,_.y=-n.y,_.z=-n.z,_.w=-r.a.dot(r.a.negate(n,C),d),_=s[4],o.t(_)||(_=s[4]=new a.a),_.x=e.x,_.y=e.y,_.z=e.z,_.w=-r.a.dot(e,w),r.a.multiplyByScalar(e,l,d),r.a.add(t,d,d),_=s[5],o.t(_)||(_=s[5]=new a.a),_.x=-e.x,_.y=-e.y,_.z=-e.z,_.w=-r.a.dot(r.a.negate(e,C),d),this._cullingVolume},g.prototype.getPixelDimensions=function(t,e,r,a,n){if(v(this),!o.t(t)||!o.t(e))throw new i.t("Both drawingBufferWidth and drawingBufferHeight are required.");if(t<=0)throw new i.t("drawingBufferWidth must be greater than zero.");if(e<=0)throw new i.t("drawingBufferHeight must be greater than zero.");if(!o.t(r))throw new i.t("distance is required.");if(!o.t(a))throw new i.t("pixelRatio is required.");if(a<=0)throw new i.t("pixelRatio must be greater than zero.");if(!o.t(n))throw new i.t("A result object is required.");var s=a*(this.right-this.left)/t,f=a*(this.top-this.bottom)/e;return n.x=s,n.y=f,n},g.prototype.clone=function(t){return o.t(t)||(t=new g),t.left=this.left,t.right=this.right,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},g.prototype.equals=function(t){return o.t(t)&&t instanceof g&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},g.prototype.equalsEpsilon=function(t,e,r){return t===this||o.t(t)&&t instanceof g&&u.n.equalsEpsilon(this.right,t.right,e,r)&&u.n.equalsEpsilon(this.left,t.left,e,r)&&u.n.equalsEpsilon(this.top,t.top,e,r)&&u.n.equalsEpsilon(this.bottom,t.bottom,e,r)&&u.n.equalsEpsilon(this.near,t.near,e,r)&&u.n.equalsEpsilon(this.far,t.far,e,r)},O.packedLength=4,O.pack=function(t,e,r){return i.n.typeOf.object("value",t),i.n.defined("array",e),r=o.e(r,0),e[r++]=t.width,e[r++]=t.aspectRatio,e[r++]=t.near,e[r]=t.far,e},O.unpack=function(t,e,r){return i.n.defined("array",t),e=o.e(e,0),o.t(r)||(r=new O),r.width=t[e++],r.aspectRatio=t[e++],r.near=t[e++],r.far=t[e],r},Object.defineProperties(O.prototype,{projectionMatrix:{get:function(){return P(this),this._offCenterFrustum.projectionMatrix}}}),O.prototype.computeCullingVolume=function(t,e,r){return P(this),this._offCenterFrustum.computeCullingVolume(t,e,r)},O.prototype.getPixelDimensions=function(t,e,r,a,i){return P(this),this._offCenterFrustum.getPixelDimensions(t,e,r,a,i)},O.prototype.clone=function(t){return o.t(t)||(t=new O),t.aspectRatio=this.aspectRatio,t.width=this.width,t.near=this.near,t.far=this.far,t._aspectRatio=void 0,t._width=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},O.prototype.equals=function(t){return!!(o.t(t)&&t instanceof O)&&(P(this),P(t),this.width===t.width&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},O.prototype.equalsEpsilon=function(t,e,r){return!!(o.t(t)&&t instanceof O)&&(P(this),P(t),u.n.equalsEpsilon(this.width,t.width,e,r)&&u.n.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,r)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,r))},Object.defineProperties(R.prototype,{projectionMatrix:{get:function(){return E(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return E(this),this._infinitePerspective}}});var M=new r.a,F=new r.a,S=new r.a,q=new r.a;function T(t){t=o.e(t,o.e.EMPTY_OBJECT),this._offCenterFrustum=new R,this.fov=t.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=o.e(t.near,1),this._near=this.near,this.far=o.e(t.far,5e8),this._far=this.far,this.xOffset=o.e(t.xOffset,0),this._xOffset=this.xOffset,this.yOffset=o.e(t.yOffset,0),this._yOffset=this.yOffset,this.reflect=!1}function k(t){if(!(o.t(t.fov)&&o.t(t.aspectRatio)&&o.t(t.near)&&o.t(t.far)))throw new i.t("fov, aspectRatio, near, or far parameters are not set.");var e=t._offCenterFrustum;if(t.fov!==t._fov||t.aspectRatio!==t._aspectRatio||t.near!==t._near||t.far!==t._far||t.xOffset!==t._xOffset||t.yOffset!==t._yOffset){if(t.fov<0||t.fov>=Math.PI)throw new i.t("fov must be in the range [0, PI).");if(t.aspectRatio<0)throw new i.t("aspectRatio must be positive.");if(t.near<0||t.near>t.far)throw new i.t("near must be greater than zero and less than far.");t._aspectRatio=t.aspectRatio,t._fov=t.fov,t._fovy=t.aspectRatio<=1?t.fov:2*Math.atan(Math.tan(.5*t.fov)/t.aspectRatio),t._near=t.near,t._far=t.far,t._sseDenominator=2*Math.tan(.5*t._fovy),t._xOffset=t.xOffset,t._yOffset=t.yOffset,e.top=t.near*Math.tan(.5*t._fovy),e.bottom=-e.top,e.right=t.aspectRatio*e.top,e.left=-e.right,e.near=t.near,e.far=t.far,e.right+=t.xOffset,e.left+=t.xOffset,e.top+=t.yOffset,e.bottom+=t.yOffset}}R.prototype.resetProjectionMatrix=function(){if(!(o.t(this.right)&&o.t(this.left)&&o.t(this.top)&&o.t(this.bottom)&&o.t(this.near)&&o.t(this.far)))throw new i.t("right, left, top, bottom, near, or far parameters are not set.");var t=this.top,e=this.bottom,r=this.right,a=this.left,n=this.near,s=this.far;if(this.near<=0||this.near>this.far)throw new i.t("near must be greater than zero and less than far.");this._left=a,this._right=r,this._top=t,this._bottom=e,this._near=n,this._far=s,this._perspectiveMatrix=h.c.computePerspectiveOffCenter(a,r,e,t,n,s,this._perspectiveMatrix),this._infinitePerspective=h.c.computeInfinitePerspectiveOffCenter(a,r,e,t,n,this._infinitePerspective)},R.prototype.computeCullingVolume=function(t,e,n,s){if(!o.t(t))throw new i.t("position is required.");if(!o.t(e))throw new i.t("direction is required.");if(!o.t(n))throw new i.t("up is required.");var f=this._cullingVolume.planes,h=o.e(s,0);h=Math.min(h,.5),h=Math.max(h,0);var u=this.top+this.top*h,p=this.bottom-this.top*h,c=this.right+this.right*h,l=this.left-this.right*h,m=this.near,w=this.far,d=r.a.cross(e,n,M),_=F;r.a.multiplyByScalar(e,m,_),r.a.add(t,_,_);var y=S;r.a.multiplyByScalar(e,w,y),r.a.add(t,y,y);var g=q;r.a.multiplyByScalar(d,l,g),r.a.add(_,g,g),r.a.subtract(g,t,g),r.a.normalize(g,g),r.a.cross(g,n,g),r.a.normalize(g,g);var v=f[0];return o.t(v)||(v=f[0]=new a.a),v.x=g.x,v.y=g.y,v.z=g.z,v.w=-r.a.dot(g,t),r.a.multiplyByScalar(d,c,g),r.a.add(_,g,g),r.a.subtract(g,t,g),r.a.cross(n,g,g),r.a.normalize(g,g),v=f[1],o.t(v)||(v=f[1]=new a.a),v.x=g.x,v.y=g.y,v.z=g.z,v.w=-r.a.dot(g,t),r.a.multiplyByScalar(n,p,g),r.a.add(_,g,g),r.a.subtract(g,t,g),r.a.cross(d,g,g),r.a.normalize(g,g),v=f[2],o.t(v)||(v=f[2]=new a.a),v.x=g.x,v.y=g.y,v.z=g.z,v.w=-r.a.dot(g,t),r.a.multiplyByScalar(n,u,g),r.a.add(_,g,g),r.a.subtract(g,t,g),r.a.cross(g,d,g),r.a.normalize(g,g),v=f[3],o.t(v)||(v=f[3]=new a.a),v.x=g.x,v.y=g.y,v.z=g.z,v.w=-r.a.dot(g,t),v=f[4],o.t(v)||(v=f[4]=new a.a),v.x=e.x,v.y=e.y,v.z=e.z,v.w=-r.a.dot(e,_),r.a.negate(e,g),v=f[5],o.t(v)||(v=f[5]=new a.a),v.x=g.x,v.y=g.y,v.z=g.z,v.w=-r.a.dot(g,y),this._cullingVolume},R.prototype.getPixelDimensions=function(t,e,r,a,n){if(E(this),!o.t(t)||!o.t(e))throw new i.t("Both drawingBufferWidth and drawingBufferHeight are required.");if(t<=0)throw new i.t("drawingBufferWidth must be greater than zero.");if(e<=0)throw new i.t("drawingBufferHeight must be greater than zero.");if(!o.t(r))throw new i.t("distance is required.");if(!o.t(a))throw new i.t("pixelRatio is required");if(a<=0)throw new i.t("pixelRatio must be greater than zero.");if(!o.t(n))throw new i.t("A result object is required.");var s=1/this.near,f=this.top*s,h=2*a*r*f/e,u=2*a*r*(f=this.right*s)/t;return n.x=u,n.y=h,n},R.prototype.clone=function(t){return o.t(t)||(t=new R),t.right=this.right,t.left=this.left,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},R.prototype.equals=function(t){return o.t(t)&&t instanceof R&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},R.prototype.equalsEpsilon=function(t,e,r){return t===this||o.t(t)&&t instanceof R&&u.n.equalsEpsilon(this.right,t.right,e,r)&&u.n.equalsEpsilon(this.left,t.left,e,r)&&u.n.equalsEpsilon(this.top,t.top,e,r)&&u.n.equalsEpsilon(this.bottom,t.bottom,e,r)&&u.n.equalsEpsilon(this.near,t.near,e,r)&&u.n.equalsEpsilon(this.far,t.far,e,r)},T.packedLength=6,T.pack=function(t,e,r){return i.n.typeOf.object("value",t),i.n.defined("array",e),r=o.e(r,0),e[r++]=t.fov,e[r++]=t.aspectRatio,e[r++]=t.near,e[r++]=t.far,e[r++]=t.xOffset,e[r]=t.yOffset,e},T.unpack=function(t,e,r){return i.n.defined("array",t),e=o.e(e,0),o.t(r)||(r=new T),r.fov=t[e++],r.aspectRatio=t[e++],r.near=t[e++],r.far=t[e++],r.xOffset=t[e++],r.yOffset=t[e],r},Object.defineProperties(T.prototype,{projectionMatrix:{get:function(){return k(this),this.reflect&&function(t){if(o.t(t.clipPlane)&&o.t(t.currentViewMatrix)){var e=t.currentViewMatrix,r=t._offCenterFrustum.projectionMatrix;h.c.multiplyByPlane(e,t.clipPlane,B),D.x=(u.n.sign(B.normal.x)+r[8])/r[0],D.y=(u.n.sign(B.normal.y)+r[9])/r[5],D.z=-1,D.w=(1+r[10])/r[14],A.x=B.normal.x,A.y=B.normal.y,A.z=B.normal.z,A.w=B.distance,a.a.multiplyByScalar(A,2/a.a.dot(A,D),I),r[2]=I.x,r[6]=I.y,r[10]=I.z+1,r[14]=I.w}}(this),this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return k(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return k(this),this._fovy}},sseDenominator:{get:function(){return k(this),this._sseDenominator}}}),T.prototype.resetProjectionMatrix=function(){return this._offCenterFrustum.resetProjectionMatrix()},T.prototype.computeCullingVolume=function(t,e,r,a){return k(this),this._offCenterFrustum.computeCullingVolume(t,e,r,a)},T.prototype.getPixelDimensions=function(t,e,r,a,i){return k(this),this._offCenterFrustum.getPixelDimensions(t,e,r,a,i)},T.prototype.clone=function(t){return o.t(t)||(t=new T),t.aspectRatio=this.aspectRatio,t.fov=this.fov,t.near=this.near,t.far=this.far,t.reflect=this.reflect,t.clipPlane=this.clipPlane,t.currentViewMatrix=this.currentViewMatrix,t._aspectRatio=void 0,t._fov=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},T.prototype.equals=function(t){return!!(o.t(t)&&t instanceof T)&&(k(this),k(t),this.fov===t.fov&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},T.prototype.equalsEpsilon=function(t,e,r){return!!(o.t(t)&&t instanceof T)&&(k(this),k(t),u.n.equalsEpsilon(this.fov,t.fov,e,r)&&u.n.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,r)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,r))};var B=new c.n(r.a.UNIT_Z,1),D=new a.a,A=new a.a,I=new a.a;function j(t){i.n.typeOf.object("options",t),i.n.typeOf.object("options.frustum",t.frustum),i.n.typeOf.object("options.origin",t.origin),i.n.typeOf.object("options.orientation",t.orientation);var e,a,n=t.frustum,f=t.orientation,h=t.origin,u=o.e(t.vertexFormat,l.n.DEFAULT),p=o.e(t._drawNearPlane,!0);n instanceof T?(e=0,a=T.packedLength):n instanceof O&&(e=1,a=O.packedLength),this._frustumType=e,this._frustum=n.clone(),this._origin=r.a.clone(h),this._orientation=s.a.clone(f),this._drawNearPlane=p,this._vertexFormat=u,this._workerName="createFrustumGeometry",this.packedLength=2+a+r.a.packedLength+s.a.packedLength+l.n.packedLength}j.pack=function(t,e,a){i.n.typeOf.object("value",t),i.n.defined("array",e),a=o.e(a,0);var n=t._frustumType,f=t._frustum;return e[a++]=n,0===n?(T.pack(f,e,a),a+=T.packedLength):(O.pack(f,e,a),a+=O.packedLength),r.a.pack(t._origin,e,a),a+=r.a.packedLength,s.a.pack(t._orientation,e,a),a+=s.a.packedLength,l.n.pack(t._vertexFormat,e,a),e[a+=l.n.packedLength]=t._drawNearPlane?1:0,e};var N=new T,L=new O,V=new s.a,U=new r.a,G=new l.n;function K(t,e,r,a,i,n,s,f){for(var h=t/3*2,u=0;u<4;++u)o.t(e)&&(e[t]=n.x,e[t+1]=n.y,e[t+2]=n.z),o.t(r)&&(r[t]=s.x,r[t+1]=s.y,r[t+2]=s.z),o.t(a)&&(a[t]=f.x,a[t+1]=f.y,a[t+2]=f.z),t+=3;i[h]=0,i[h+1]=0,i[h+2]=1,i[h+3]=0,i[h+4]=1,i[h+5]=1,i[h+6]=0,i[h+7]=1}j.unpack=function(t,e,a){i.n.defined("array",t),e=o.e(e,0);var n,f=t[e++];0===f?(n=T.unpack(t,e,N),e+=T.packedLength):(n=O.unpack(t,e,L),e+=O.packedLength);var h=r.a.unpack(t,e,U);e+=r.a.packedLength;var u=s.a.unpack(t,e,V);e+=s.a.packedLength;var p=l.n.unpack(t,e,G),c=1===t[e+=l.n.packedLength];if(!o.t(a))return new j({frustum:n,origin:h,orientation:u,vertexFormat:p,_drawNearPlane:c});var m=f===a._frustumType?a._frustum:void 0;return a._frustum=n.clone(m),a._frustumType=f,a._origin=r.a.clone(h,a._origin),a._orientation=s.a.clone(u,a._orientation),a._vertexFormat=l.n.clone(p,a._vertexFormat),a._drawNearPlane=c,a};var W=new h.r,Y=new h.c,H=new h.c,J=new r.a,Z=new r.a,Q=new r.a,X=new r.a,$=new r.a,tt=new r.a,et=new Array(3),rt=new Array(4);rt[0]=new a.a(-1,-1,1,1),rt[1]=new a.a(1,-1,1,1),rt[2]=new a.a(1,1,1,1),rt[3]=new a.a(-1,1,1,1);for(var at=new Array(4),it=0;it<4;++it)at[it]=new a.a;j._computeNearFarPlanes=function(t,e,i,n,s,f,u,p){var c=h.r.fromQuaternion(e,W),l=o.e(f,J),m=o.e(u,Z),w=o.e(p,Q);l=h.r.getColumn(c,0,l),m=h.r.getColumn(c,1,m),w=h.r.getColumn(c,2,w),r.a.normalize(l,l),r.a.normalize(m,m),r.a.normalize(w,w),r.a.negate(l,l);var d,_,y=h.c.computeView(t,w,m,l,Y);if(0===i){var g=n.projectionMatrix,v=h.c.multiply(g,y,H);_=h.c.inverse(v,H)}else d=h.c.inverseTransformation(y,H);o.t(_)?(et[0]=n.near,et[1]=n.far):(et[0]=0,et[1]=n.near,et[2]=n.far);for(var b=0;b<2;++b)for(var x=0;x<4;++x){var z=a.a.clone(rt[x],at[x]);if(o.t(_)){var C=1/(z=h.c.multiplyByVector(_,z,z)).w;r.a.multiplyByScalar(z,C,z),r.a.subtract(z,t,z),r.a.normalize(z,z);var O=r.a.dot(w,z);r.a.multiplyByScalar(z,et[b]/O,z),r.a.add(z,t,z)}else{o.t(n._offCenterFrustum)&&(n=n._offCenterFrustum);var P=et[b],R=et[b+1];z.x=.5*(z.x*(n.right-n.left)+n.left+n.right),z.y=.5*(z.y*(n.top-n.bottom)+n.bottom+n.top),z.z=.5*(z.z*(P-R)-P-R),z.w=1,h.c.multiplyByVector(d,z,z)}s[12*b+3*x]=z.x,s[12*b+3*x+1]=z.y,s[12*b+3*x+2]=z.z}},j.createGeometry=function(t){var a=t._frustumType,i=t._frustum,u=t._origin,p=t._orientation,c=t._drawNearPlane,l=t._vertexFormat,m=c?6:5,w=new Float64Array(72);j._computeNearFarPlanes(u,p,a,i,w);var d=24;w[d]=w[12],w[d+1]=w[13],w[d+2]=w[14],w[d+3]=w[0],w[d+4]=w[1],w[d+5]=w[2],w[d+6]=w[9],w[d+7]=w[10],w[d+8]=w[11],w[d+9]=w[21],w[d+10]=w[22],w[d+11]=w[23],w[d+=12]=w[15],w[d+1]=w[16],w[d+2]=w[17],w[d+3]=w[3],w[d+4]=w[4],w[d+5]=w[5],w[d+6]=w[0],w[d+7]=w[1],w[d+8]=w[2],w[d+9]=w[12],w[d+10]=w[13],w[d+11]=w[14],w[d+=12]=w[3],w[d+1]=w[4],w[d+2]=w[5],w[d+3]=w[15],w[d+4]=w[16],w[d+5]=w[17],w[d+6]=w[18],w[d+7]=w[19],w[d+8]=w[20],w[d+9]=w[6],w[d+10]=w[7],w[d+11]=w[8],w[d+=12]=w[6],w[d+1]=w[7],w[d+2]=w[8],w[d+3]=w[18],w[d+4]=w[19],w[d+5]=w[20],w[d+6]=w[21],w[d+7]=w[22],w[d+8]=w[23],w[d+9]=w[9],w[d+10]=w[10],w[d+11]=w[11],c||(w=w.subarray(12));var _=new f.t({position:new s.r({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:w})});if(o.t(l.normal)||o.t(l.tangent)||o.t(l.bitangent)||o.t(l.st)){var y=o.t(l.normal)?new Float32Array(12*m):void 0,g=o.t(l.tangent)?new Float32Array(12*m):void 0,v=o.t(l.bitangent)?new Float32Array(12*m):void 0,b=o.t(l.st)?new Float32Array(8*m):void 0,x=J,z=Z,C=Q,O=r.a.negate(x,X),P=r.a.negate(z,$),R=r.a.negate(C,tt);d=0,c&&(K(d,y,g,v,b,R,x,z),d+=12),K(d,y,g,v,b,C,O,z),K(d+=12,y,g,v,b,O,R,z),K(d+=12,y,g,v,b,P,R,O),K(d+=12,y,g,v,b,x,C,z),K(d+=12,y,g,v,b,z,C,O),o.t(y)&&(_.normal=new s.r({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),o.t(g)&&(_.tangent=new s.r({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:g})),o.t(v)&&(_.bitangent=new s.r({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),o.t(b)&&(_.st=new s.r({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:b}))}for(var E=new Uint16Array(6*m),M=0;M<m;++M){var F=6*M,S=4*M;E[F]=S,E[F+1]=S+1,E[F+2]=S+2,E[F+3]=S,E[F+4]=S+2,E[F+5]=S+3}return new s.T({attributes:_,indices:E,primitiveType:h._0x38df4a.TRIANGLES,boundingSphere:e.c.fromVertices(w)})},t.D=j,t.f=T,t.r=O}));
