define(["./when-515d5295","./Cartographic-1bbcab04","./Check-3aa71481","./EllipsoidOutlineGeometry-f853b724","./Math-5e38123d","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Rectangle-e170be8b","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922"],(function(e,t,i,a,r,n,s,o,c,d,u,l,b,f,p,y,m,v,k,G,P){"use strict";function _(i){var r=e.e(i.radius,1),n={radii:new t.a(r,r,r),stackPartitions:i.stackPartitions,slicePartitions:i.slicePartitions,subdivisions:i.subdivisions};this._ellipsoidGeometry=new a.I(n),this._workerName="createSphereOutlineGeometry"}_.packedLength=a.I.packedLength,_.pack=function(e,t,r){return i.n.typeOf.object("value",e),a.I.pack(e._ellipsoidGeometry,t,r)};var h=new a.I,I={radius:void 0,radii:new t.a,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};return _.unpack=function(i,r,n){var s=a.I.unpack(i,r,h);return I.stackPartitions=s._stackPartitions,I.slicePartitions=s._slicePartitions,I.subdivisions=s._subdivisions,e.t(n)?(t.a.clone(s._radii,I.radii),n._ellipsoidGeometry=new a.I(I),n):(I.radius=s._radii.x,new _(I))},_.createGeometry=function(e){return a.I.createGeometry(e._ellipsoidGeometry)},function(t,i){return e.t(i)&&(t=_.unpack(t,i)),_.createGeometry(t)}}));
