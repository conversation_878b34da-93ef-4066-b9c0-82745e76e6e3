define(["exports","./Check-3aa71481","./when-515d5295","./IndexDatatype-eefd5922","./WebGLConstants-77a84876"],(function(e,t,n,r,s){"use strict";var i={STREAM_DRAW:s.t.STREAM_DRAW,STATIC_DRAW:s.t.STATIC_DRAW,DYNAMIC_DRAW:s.t.DYNAMIC_DRAW,validate:function(e){return e===i.STREAM_DRAW||e===i.STATIC_DRAW||e===i.DYNAMIC_DRAW}};function f(){return!0}function a(e){if(e=n.e(e,n.e.EMPTY_OBJECT),t.n.defined("options.context",e.context),!n.t(e.typedArray)&&!n.t(e.sizeInBytes))throw new t.t("Either options.sizeInBytes or options.typedArray is required.");if(n.t(e.typedArray)&&n.t(e.sizeInBytes))throw new t.t("Cannot pass in both options.sizeInBytes and options.typedArray.");if(n.t(e.typedArray)&&(t.n.typeOf.object("options.typedArray",e.typedArray),t.n.typeOf.number("options.typedArray.byteLength",e.typedArray.byteLength)),!i.validate(e.usage))throw new t.t("usage is invalid.");var r=e.context._gl,s=e.bufferTarget,f=e.typedArray,a=e.sizeInBytes,o=e.usage,u=n.t(f);u&&(a=f.byteLength),t.n.typeOf.number.greaterThan("sizeInBytes",a,0);var y=r.createBuffer();r.bindBuffer(s,y),r.bufferData(s,u?f:a,o),r.bindBuffer(s,null),this._gl=r,this._webgl2=e.context._webgl2,this._bufferTarget=s,this._sizeInBytes=a,this._usage=o,this._buffer=y,this.vertexArrayDestroyable=!0,this.context=e.context,e.context.memorySize+=a}a.createVertexBuffer=function(e){return t.n.defined("options.context",e.context),new a({context:e.context,bufferTarget:s.t.ARRAY_BUFFER,typedArray:e.typedArray,sizeInBytes:e.sizeInBytes,usage:e.usage})},a.createIndexBuffer=function(e){if(t.n.defined("options.context",e.context),!r.IndexDatatype.validate(e.indexDatatype))throw new t.t("Invalid indexDatatype.");if(e.indexDatatype===r.IndexDatatype.UNSIGNED_INT&&!e.context.elementIndexUint)throw new t.t("IndexDatatype.UNSIGNED_INT requires OES_element_index_uint, which is not supported on this system.  Check context.elementIndexUint.");var n=e.context,i=e.indexDatatype,f=r.IndexDatatype.getSizeInBytes(i),o=new a({context:n,bufferTarget:s.t.ELEMENT_ARRAY_BUFFER,typedArray:e.typedArray,sizeInBytes:e.sizeInBytes,usage:e.usage}),u=o.sizeInBytes/f;return Object.defineProperties(o,{indexDatatype:{get:function(){return i}},bytesPerIndex:{get:function(){return f}},numberOfIndices:{get:function(){return u}}}),o},Object.defineProperties(a.prototype,{sizeInBytes:{get:function(){return this._sizeInBytes}},usage:{get:function(){return this._usage}}}),a.prototype._getBuffer=function(){return this._buffer},a.prototype.copyFromArrayView=function(e,r){r=n.e(r,0),t.n.defined("arrayView",e),t.n.typeOf.number.lessThanOrEquals("offsetInBytes + arrayView.byteLength",r+e.byteLength,this._sizeInBytes);var s=this._gl,i=this._bufferTarget;s.bindBuffer(i,this._buffer),s.bufferSubData(i,r,e),s.bindBuffer(i,null)},a.prototype.copyFromBuffer=function(e,r,i,f){if(!this._webgl2)throw new t.t("A WebGL 2 context is required.");if(!n.t(e))throw new t.t("readBuffer must be defined.");if(!n.t(f)||f<=0)throw new t.t("sizeInBytes must be defined and be greater than zero.");if(!n.t(r)||r<0||r+f>e._sizeInBytes)throw new t.t("readOffset must be greater than or equal to zero and readOffset + sizeInBytes must be less than of equal to readBuffer.sizeInBytes.");if(!n.t(i)||i<0||i+f>this._sizeInBytes)throw new t.t("writeOffset must be greater than or equal to zero and writeOffset + sizeInBytes must be less than of equal to this.sizeInBytes.");if(this._buffer===e._buffer&&(i>=r&&i<r+f||r>i&&r<i+f))throw new t.t("When readBuffer is equal to this, the ranges [readOffset + sizeInBytes) and [writeOffset, writeOffset + sizeInBytes) must not overlap.");if(this._bufferTarget===s.t.ELEMENT_ARRAY_BUFFER&&e._bufferTarget!==s.t.ELEMENT_ARRAY_BUFFER||this._bufferTarget!==s.t.ELEMENT_ARRAY_BUFFER&&e._bufferTarget===s.t.ELEMENT_ARRAY_BUFFER)throw new t.t("Can not copy an index buffer into another buffer type.");var a=s.t.COPY_READ_BUFFER,o=s.t.COPY_WRITE_BUFFER,u=this._gl;u.bindBuffer(o,this._buffer),u.bindBuffer(a,e._buffer),u.copyBufferSubData(a,o,r,i,f),u.bindBuffer(o,null),u.bindBuffer(a,null)},a.prototype.getBufferData=function(e,r,i,f){if(r=n.e(r,0),i=n.e(i,0),!this._webgl2)throw new t.t("A WebGL 2 context is required.");if(!n.t(e))throw new t.t("arrayView is required.");var a,o,u=e.byteLength;if(n.t(f)?(a=f,n.t(u)?o=1:(u=e.length,o=e.BYTES_PER_ELEMENT)):n.t(u)?(a=u-i,o=1):(a=(u=e.length)-i,o=e.BYTES_PER_ELEMENT),i<0||i>u)throw new t.t("destinationOffset must be greater than zero and less than the arrayView length.");if(i+a>u)throw new t.t("destinationOffset + length must be less than or equal to the arrayViewLength.");if(r<0||r>this._sizeInBytes)throw new t.t("sourceOffset must be greater than zero and less than the buffers size.");if(r+a*o>this._sizeInBytes)throw new t.t("sourceOffset + length must be less than the buffers size.");var y=this._gl,h=s.t.COPY_READ_BUFFER;y.bindBuffer(h,this._buffer),y.getBufferSubData(h,r,e,i,f),y.bindBuffer(h,null)},a.prototype.isDestroyed=function(){return!1},a.prototype.destroy=function(){return this._gl.deleteBuffer(this._buffer),this.context.memorySize-=this.sizeInBytes,function(e,r){function s(){throw new t.t(r)}for(var i in r=n.e(r,"This object was destroyed, i.e., destroy() was called."),e)"function"==typeof e[i]&&(e[i]=s);e.isDestroyed=f}(this)},e.A=i,e.u=a}));
