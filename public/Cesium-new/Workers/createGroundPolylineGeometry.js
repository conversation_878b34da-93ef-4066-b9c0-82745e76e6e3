define(["./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./Intersect-53434a77","./Math-5e38123d","./ArcType-98a7a011","./arrayRemoveDuplicates-a4c6347e","./ComponentDatatype-d430c7f7","./EllipsoidGeodesic-e5406761","./EllipsoidRhumbLine-f50fdea6","./EncodedCartesian3-d74c1b81","./GeometryAttribute-9bc31a7f","./IntersectionTests-5fa33dbd","./PrimitiveType-b38a4004","./Plane-92c15089","./WebMercatorProjection-aa5a37a5","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5"],(function(e,a,t,n,i,r,o,s,l,c,u,h,p,d,g,f,w,m,v,y,_,T,b,D){"use strict";function E(e){e=i.e(e,{}),this._ellipsoid=i.e(e.ellipsoid,r.n.WGS84),this._rectangle=i.e(e.rectangle,r.s.MAX_VALUE),this._projection=new o.s(this._ellipsoid),this._numberOfLevelZeroTilesX=i.e(e.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=i.e(e.numberOfLevelZeroTilesY,1),this._customDPI=e.customDPI,this._scaleDenominators=e.scaleDenominators,this._tileWidth=i.e(e.tileWidth,256),this._tileHeight=i.e(e.tileHeight,256),this._beginLevel=i.e(e.beginLevel,0)}Object.defineProperties(E.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}},beginLevel:{get:function(){return this._beginLevel}}}),E.prototype.getNumberOfXTilesAtLevel=function(e){if(i.t(this._customDPI)&&i.t(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileWidth*a.x;return Math.ceil(this._rectangle.width/t)}return this._numberOfLevelZeroTilesX<<e-this._beginLevel},E.prototype.getNumberOfYTilesAtLevel=function(e){if(i.t(this._customDPI)&&i.t(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileHeight*a.y;return Math.ceil(this._rectangle.height/t)}return this._numberOfLevelZeroTilesY<<e-this._beginLevel},E.prototype.rectangleToNativeRectangle=function(e,a){n.n.defined("rectangle",e);var t=s.n.toDegrees(e.west),o=s.n.toDegrees(e.south),l=s.n.toDegrees(e.east),c=s.n.toDegrees(e.north);return i.t(a)?(a.west=t,a.south=o,a.east=l,a.north=c,a):new r.s(t,o,l,c)},E.prototype.tileXYToNativeRectangle=function(e,a,t,n){var i=this.tileXYToRectangle(e,a,t,n);return i.west=s.n.toDegrees(i.west),i.south=s.n.toDegrees(i.south),i.east=s.n.toDegrees(i.east),i.north=s.n.toDegrees(i.north),i},E.prototype.tileXYToRectangle=function(e,a,t,n){var o=this._rectangle;if(i.t(this._customDPI)&&i.t(this._scaleDenominators)){var s=this.calculateResolution(t),l=o.west+e*this._tileWidth*s.x,c=o.west+(e+1)*this._tileWidth*s.x,u=o.north-a*this._tileHeight*s.y,h=o.north-(a+1)*this._tileHeight*s.y;return i.t(n)?(n.west=l,n.south=h,n.east=c,n.north=u,n):new r.s(l,h,c,u)}var p=this.getNumberOfXTilesAtLevel(t),d=this.getNumberOfYTilesAtLevel(t),g=o.width/p,f=(l=e*g+o.west,c=(e+1)*g+o.west,o.height/d);u=o.north-a*f,h=o.north-(a+1)*f;return i.t(n)||(n=new r.s(l,h,c,u)),n.west=l,n.south=h,n.east=c,n.north=u,n},E.prototype.positionToTileXY=function(e,t,n){var o=this._rectangle;if(r.s.contains(o,e)){var l=this.getNumberOfXTilesAtLevel(t),c=this.getNumberOfYTilesAtLevel(t),u=o.width/l,h=o.height/c;if(i.t(this._customDPI)&&i.t(this._scaleDenominators)){var p=this.calculateResolution(t);u=this._tileWidth*p.x,h=this._tileHeight*p.y}var d=e.longitude;o.east<o.west&&(d+=s.n.TWO_PI);var g=(d-o.west)/u|0;g>=l&&(g=l-1);var f=(o.north-e.latitude)/h|0;return f>=c&&(f=c-1),i.t(n)?(n.x=g,n.y=f,n):new a.r(g,f)}},E.prototype.calculateResolution=function(e){var t=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.x,n=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.y,i=r.n.WGS84.maximumRadius;return new a.r(t/i,n/i)};var O=new t.a,I=new t.a,P=new t.i,L=new t.a,k=new t.a,N=new e.c,x=new E,A=[new t.i,new t.i,new t.i,new t.i],S=new a.r,H={};function C(e){t.i.fromRadians(e.east,e.north,0,A[0]),t.i.fromRadians(e.west,e.north,0,A[1]),t.i.fromRadians(e.east,e.south,0,A[2]),t.i.fromRadians(e.west,e.south,0,A[3]);var a,n=0,i=0,r=0,o=0,s=H._terrainHeightsMaxLevel;for(a=0;a<=s;++a){for(var l=!1,c=0;c<4;++c){var u=A[c];if(x.positionToTileXY(u,a,S),0===c)r=S.x,o=S.y;else if(r!==S.x||o!==S.y){l=!0;break}}if(l)break;n=r,i=o}if(0!==a)return{x:n,y:i,level:a>s?s:a-1}}H.initialize=function(){var a=H._initPromise;return i.t(a)||(a=e.t.fetchJson(e.o("Assets/approximateTerrainHeights.json")).then((function(e){H._terrainHeights=e})),H._initPromise=a),a},H.getMinimumMaximumHeights=function(e,a){if(n.n.defined("rectangle",e),!i.t(H._terrainHeights))throw new n.t("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");a=i.e(a,r.n.WGS84);var o=C(e),s=H._defaultMinTerrainHeight,l=H._defaultMaxTerrainHeight;if(i.t(o)){var c=o.level+"-"+o.x+"-"+o.y,u=H._terrainHeights[c];i.t(u)&&(s=u[0],l=u[1]),a.cartographicToCartesian(r.s.northeast(e,P),O),a.cartographicToCartesian(r.s.southwest(e,P),I),t.a.midpoint(I,O,L);var h=a.scaleToGeodeticSurface(L,k);if(i.t(h)){var p=t.a.distance(L,h);s=Math.min(s,-p)}else s=H._defaultMinTerrainHeight}return{minimumTerrainHeight:s=Math.max(H._defaultMinTerrainHeight,s),maximumTerrainHeight:l}},H.getBoundingSphere=function(a,t){if(n.n.defined("rectangle",a),!i.t(H._terrainHeights))throw new n.t("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");t=i.e(t,r.n.WGS84);var o=C(a),s=H._defaultMaxTerrainHeight;if(i.t(o)){var l=o.level+"-"+o.x+"-"+o.y,c=H._terrainHeights[l];i.t(c)&&(s=c[1])}var u=e.c.fromRectangle3D(a,t,0);return e.c.fromRectangle3D(a,t,s,N),e.c.union(u,N,u)},H._terrainHeightsMaxLevel=6,H._defaultMaxTerrainHeight=9e3,H._defaultMinTerrainHeight=-1e5,H._terrainHeights=void 0,H._initPromise=void 0,Object.defineProperties(H,{initialized:{get:function(){return i.t(H._terrainHeights)}}});var M=[o.s,v.e],R=M.length,z=Math.cos(s.n.toRadians(30)),j=Math.cos(s.n.toRadians(150));function Y(e){var a=(e=i.e(e,i.e.EMPTY_OBJECT)).positions;if(!i.t(a)||a.length<2)throw new n.t("At least two positions are required.");if(i.t(e.arcType)&&e.arcType!==l.D.GEODESIC&&e.arcType!==l.D.RHUMB)throw new n.t("Valid options for arcType are ArcType.GEODESIC and ArcType.RHUMB.");this.width=i.e(e.width,1),this._positions=a,this.granularity=i.e(e.granularity,9999),this.loop=i.e(e.loop,!1),this.arcType=i.e(e.arcType,l.D.GEODESIC),this._ellipsoid=i.e(e.ellipsoid,r.n.WGS84),this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(Y.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+r.n.packedLength+1+1}}}),Y.setProjectionAndEllipsoid=function(e,a){for(var t=0,n=0;n<R;n++)if(a instanceof M[n]){t=n;break}e._projectionIndex=t,e._ellipsoid=a.ellipsoid};var G=new t.a,W=new t.a,F=new t.a;function B(e,a,n,i,r){var o=Q(i,e,0,G),s=Q(i,e,n,W),l=Q(i,a,0,F),c=K(s,o,W),u=K(l,o,F);return t.a.cross(u,c,r),t.a.normalize(r,r)}var q=new t.i,X=new t.a,U=new t.a,V=new t.a;function Z(e,a,n,i,r,o,s,c,u,d,g){if(0!==r){var f;o===l.D.GEODESIC?f=new h.D(e,a,s):o===l.D.RHUMB&&(f=new p.M(e,a,s));var w=f.surfaceDistance;if(!(w<r))for(var m=B(e,a,i,s,V),v=Math.ceil(w/r),y=w/v,_=y,T=v-1,b=c.length,D=0;D<T;D++){var E=f.interpolateUsingSurfaceDistance(_,q),O=Q(s,E,n,X),I=Q(s,E,i,U);t.a.pack(m,c,b),t.a.pack(O,u,b),t.a.pack(I,d,b),g.push(E.latitude),g.push(E.longitude),b+=3,_+=y}}}var J=new t.i;function Q(e,a,n,i){return t.i.clone(a,J),J.height=n,t.i.toCartesian(J,e,i)}function K(e,a,n){return t.a.subtract(e,a,n),t.a.normalize(n,n),n}Y.pack=function(e,a,o){n.n.typeOf.object("value",e),n.n.defined("array",a);var s=i.e(o,0),l=e._positions,c=l.length;a[s++]=c;for(var u=0;u<c;++u){var h=l[u];t.a.pack(h,a,s),s+=3}return a[s++]=e.granularity,a[s++]=e.loop?1:0,a[s++]=e.arcType,r.n.pack(e._ellipsoid,a,s),s+=r.n.packedLength,a[s++]=e._projectionIndex,a[s++]=e._scene3DOnly?1:0,a},Y.unpack=function(e,a,o){n.n.defined("array",e);for(var s=i.e(a,0),l=e[s++],c=new Array(l),u=0;u<l;u++)c[u]=t.a.unpack(e,s),s+=3;var h=e[s++],p=1===e[s++],d=e[s++],g=r.n.unpack(e,s);s+=r.n.packedLength;var f=e[s++],w=1===e[s++];if(!i.t(o)){var m=new Y({positions:c,granularity:h,loop:p,arcType:d,ellipsoid:g});return m._projectionIndex=f,m._scene3DOnly=w,m}return o._positions=c,o.granularity=h,o.loop=p,o.arcType=d,o._ellipsoid=g,o._projectionIndex=f,o._scene3DOnly=w,o};var $=new t.a,ee=new t.a,ae=new t.a,te=new t.a,ne=new m.n(t.a.UNIT_X,0),ie=new t.a;function re(e,a,n,i,r){var o=K(n,a,ie),l=K(e,a,$),c=K(i,a,ee),u=t.a.cross(o,l,te);u=t.a.normalize(u,u);var h=m.n.fromPointNormal(a,u,ne),p=m.n.getPointDistance(h,i);if(s.n.equalsEpsilon(p,0,s.n.EPSILON7))return t.a.clone(u,r),r;r=t.a.add(c,l,r),r=t.a.normalize(r,r);var d=t.a.cross(o,r,ae);return t.a.normalize(d,d),t.a.cross(d,o,r),t.a.normalize(r,r),t.a.dot(c,d)<0&&(r=t.a.negate(r,r)),r}var oe=m.n.fromPointNormal(t.a.ZERO,t.a.UNIT_Y),se=new t.a,le=new t.a,ce=new t.a,ue=new t.a,he=new t.a,pe=new t.a,de=new t.i,ge=new t.i,fe=new t.i;Y.createGeometry=function(a){var n,o,h=!a._scene3DOnly,w=a.loop,m=a._ellipsoid,v=a.granularity,y=a.arcType,_=new M[a._projectionIndex](m),T=1e3,b=a._positions,D=b.length;2===D&&(w=!1);var E,O,I,P,L,k,N,x=new p.M(void 0,void 0,m),A=[b[0]];for(o=0;o<D-1;o++)E=b[o],O=b[o+1],L=f.h.lineSegmentPlane(E,O,oe,pe),i.t(L)&&!t.a.equalsEpsilon(L,E,s.n.EPSILON7)&&!t.a.equalsEpsilon(L,O,s.n.EPSILON7)&&(a.arcType===l.D.GEODESIC?A.push(t.a.clone(L)):a.arcType===l.D.RHUMB&&(N=m.cartesianToCartographic(L,de).longitude,I=m.cartesianToCartographic(E,de),P=m.cartesianToCartographic(O,ge),x.setEndPoints(I,P),k=x.findIntersectionWithLongitude(N,fe),L=m.cartographicToCartesian(k,pe),i.t(L)&&!t.a.equalsEpsilon(L,E,s.n.EPSILON7)&&!t.a.equalsEpsilon(L,O,s.n.EPSILON7)&&A.push(t.a.clone(L)))),A.push(O);w&&(E=b[D-1],O=b[0],L=f.h.lineSegmentPlane(E,O,oe,pe),i.t(L)&&!t.a.equalsEpsilon(L,E,s.n.EPSILON7)&&!t.a.equalsEpsilon(L,O,s.n.EPSILON7)&&(a.arcType===l.D.GEODESIC?A.push(t.a.clone(L)):a.arcType===l.D.RHUMB&&(N=m.cartesianToCartographic(L,de).longitude,I=m.cartesianToCartographic(E,de),P=m.cartesianToCartographic(O,ge),x.setEndPoints(I,P),k=x.findIntersectionWithLongitude(N,fe),L=m.cartographicToCartesian(k,pe),i.t(L)&&!t.a.equalsEpsilon(L,E,s.n.EPSILON7)&&!t.a.equalsEpsilon(L,O,s.n.EPSILON7)&&A.push(t.a.clone(L)))));var S=A.length,C=new Array(S);for(o=0;o<S;o++){var R=t.i.fromCartesian(A[o],m);R.height=0,C[o]=R}if(!((S=(C=c.u(C,t.i.equalsEpsilon)).length)<2)){var j=[],Y=[],G=[],W=[],F=se,q=le,X=ce,U=ue,V=he,J=C[0],$=C[1];for(F=Q(m,C[S-1],0,F),U=Q(m,$,0,U),q=Q(m,J,0,q),X=Q(m,J,T,X),V=w?re(F,q,X,U,V):B(J,$,T,m,V),t.a.pack(V,Y,0),t.a.pack(q,G,0),t.a.pack(X,W,0),j.push(J.latitude),j.push(J.longitude),Z(J,$,0,T,v,y,m,Y,G,W,j),o=1;o<S-1;++o){F=t.a.clone(q,F),q=t.a.clone(U,q);var ee=C[o];Q(m,ee,T,X),Q(m,C[o+1],0,U),re(F,q,X,U,V),n=Y.length,t.a.pack(V,Y,n),t.a.pack(q,G,n),t.a.pack(X,W,n),j.push(ee.latitude),j.push(ee.longitude),Z(C[o],C[o+1],0,T,v,y,m,Y,G,W,j)}var ae=C[S-1],te=C[S-2];if(q=Q(m,ae,0,q),X=Q(m,ae,T,X),w){var ne=C[0];V=re(F=Q(m,te,0,F),q,X,U=Q(m,ne,0,U),V)}else V=B(te,ae,T,m,V);if(n=Y.length,t.a.pack(V,Y,n),t.a.pack(q,G,n),t.a.pack(X,W,n),j.push(ae.latitude),j.push(ae.longitude),w){for(Z(ae,J,0,T,v,y,m,Y,G,W,j),n=Y.length,o=0;o<3;++o)Y[n+o]=Y[o],G[n+o]=G[o],W[n+o]=W[o];j.push(J.latitude),j.push(J.longitude)}return function(a,n,i,o,l,c,h){var p,f,w,m,v,y,_=n._ellipsoid,T=i.length/3-1,b=8*T,D=4*b,E=36*T,O=b>65535?new Uint32Array(E):new Uint16Array(E),I=new Float64Array(3*b),P=new Float32Array(D),L=new Float32Array(D),k=new Float32Array(D),N=new Float32Array(D),x=new Float32Array(D);h&&(w=new Float32Array(D),m=new Float32Array(D),v=new Float32Array(D),y=new Float32Array(2*b));var A=c.length/2,S=0,C=Ne;C.height=0;var M=xe;M.height=0;var R=Ae,j=Se;if(h)for(f=0,p=1;p<A;p++)C.latitude=c[f],C.longitude=c[f+1],M.latitude=c[f+2],M.longitude=c[f+3],R=n.project(C,R),j=n.project(M,j),S+=t.a.distance(R,j),f+=2;var Y=o.length/3;j=t.a.unpack(o,0,j);var G,W=0;for(f=3,p=1;p<Y;p++)R=t.a.clone(j,R),j=t.a.unpack(o,f,j),W+=t.a.distance(R,j),f+=3;f=3;var F=0,B=0,q=0,X=0,U=!1,V=t.a.unpack(i,0,Ce),Z=t.a.unpack(o,0,Se),J=t.a.unpack(l,0,Re);if(a){ye(J,t.a.unpack(i,i.length-6,He),V,Z)&&(J=t.a.negate(J,J))}var Q=0,$=0,ee=0;for(p=0;p<T;p++){var ae,te,ne,ie,re=t.a.clone(V,He),oe=t.a.clone(Z,Ae),se=t.a.clone(J,Me);if(U&&(se=t.a.negate(se,se)),V=t.a.unpack(i,f,Ce),Z=t.a.unpack(o,f,Se),U=ye(J=t.a.unpack(l,f,Re),re,V,Z),C.latitude=c[F],C.longitude=c[F+1],M.latitude=c[F+2],M.longitude=c[F+3],h){var le=ke(C,M);ae=n.project(C,Be);var ce=K(te=n.project(M,qe),ae,ta);ce.y=Math.abs(ce.y),ne=Xe,ie=Ue,0===le||t.a.dot(ce,t.a.UNIT_Y)>z?(ne=De(n,C,se,ae,Xe),ie=De(n,M,J,te,Ue)):1===le?(ie=De(n,M,J,te,Ue),ne.x=0,ne.y=s.n.sign(C.longitude-Math.abs(M.longitude)),ne.z=0):(ne=De(n,C,se,ae,Xe),ie.x=0,ie.y=s.n.sign(C.longitude-M.longitude),ie.z=0)}var ue=t.a.distance(oe,Z),he=d.t.fromCartesian(re,ea),pe=t.a.subtract(V,re,Ve),de=t.a.normalize(pe,Qe),ge=t.a.subtract(oe,re,Ze);ge=t.a.normalize(ge,ge);var fe=t.a.cross(de,ge,Qe);fe=t.a.normalize(fe,fe);var we=t.a.cross(ge,se,Ke);we=t.a.normalize(we,we);var me=t.a.subtract(Z,V,Je);me=t.a.normalize(me,me);var ve=t.a.cross(J,me,$e);ve=t.a.normalize(ve,ve);var _e,Te,be,Ee=ue/W,Oe=Q/W,Pe=0,ca=0,ua=0;if(h){Pe=t.a.distance(ae,te),_e=d.t.fromCartesian(ae,aa),Te=t.a.subtract(te,ae,ta);var ha=(be=t.a.normalize(Te,na)).x;be.x=be.y,be.y=-ha,ca=Pe/S,ua=$/S}for(G=0;G<8;G++){var pa=X+4*G,da=B+2*G,ga=pa+3,fa=G<4?1:-1,wa=2===G||3===G||6===G||7===G?1:-1;t.a.pack(he.high,P,pa),P[ga]=pe.x,t.a.pack(he.low,L,pa),L[ga]=pe.y,t.a.pack(we,k,pa),k[ga]=pe.z,t.a.pack(ve,N,pa),N[ga]=Ee*fa,t.a.pack(fe,x,pa);var ma=Oe*wa;0===ma&&wa<0&&(ma=Number.POSITIVE_INFINITY),x[ga]=ma,h&&(w[pa]=_e.high.x,w[pa+1]=_e.high.y,w[pa+2]=_e.low.x,w[pa+3]=_e.low.y,v[pa]=-ne.y,v[pa+1]=ne.x,v[pa+2]=ie.y,v[pa+3]=-ie.x,m[pa]=Te.x,m[pa+1]=Te.y,m[pa+2]=be.x,m[pa+3]=be.y,y[da]=ca*fa,0===(ma=ua*wa)&&wa<0&&(ma=Number.POSITIVE_INFINITY),y[da+1]=ma)}var va=We,ya=Fe,_a=Ye,Ta=Ge,ba=r.s.fromCartographicArray(ze,je),Da=H.getMinimumMaximumHeights(ba,_),Ea=Da.minimumTerrainHeight,Oa=Da.maximumTerrainHeight;ee+=Ea,ee+=Oa,Ie(re,oe,Ea,Oa,va,_a),Ie(V,Z,Ea,Oa,ya,Ta);var Ia=t.a.multiplyByScalar(fe,s.n.EPSILON5,ia);t.a.add(va,Ia,va),t.a.add(ya,Ia,ya),t.a.add(_a,Ia,_a),t.a.add(Ta,Ia,Ta),Le(va,ya),Le(_a,Ta),t.a.pack(va,I,q),t.a.pack(ya,I,q+3),t.a.pack(Ta,I,q+6),t.a.pack(_a,I,q+9),Ia=t.a.multiplyByScalar(fe,-2*s.n.EPSILON5,ia),t.a.add(va,Ia,va),t.a.add(ya,Ia,ya),t.a.add(_a,Ia,_a),t.a.add(Ta,Ia,Ta),Le(va,ya),Le(_a,Ta),t.a.pack(va,I,q+12),t.a.pack(ya,I,q+15),t.a.pack(Ta,I,q+18),t.a.pack(_a,I,q+21),F+=2,f+=3,B+=16,q+=24,X+=32,Q+=ue,$+=Pe}f=0;var Pa=0;for(p=0;p<T;p++){for(G=0;G<sa;G++)O[f+G]=oa[G]+Pa;Pa+=8,f+=sa}var La=ra;e.c.fromVertices(i,t.a.ZERO,3,La[0]),e.c.fromVertices(o,t.a.ZERO,3,La[1]);var ka=e.c.fromBoundingSpheres(La);ka.radius+=ee/(2*T);var Na={position:new g.r({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:I}),startHiAndForwardOffsetX:la(P),startLoAndForwardOffsetY:la(L),startNormalAndForwardOffsetZ:la(k),endNormalAndTextureCoordinateNormalizationX:la(N),rightNormalAndTextureCoordinateNormalizationY:la(x)};return h&&(Na.startHiLo2D=la(w),Na.offsetAndRight2D=la(m),Na.startEndNormals2D=la(v),Na.texcoordNormalization2D=new g.r({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:y})),new g.T({attributes:Na,indices:O,boundingSphere:ka})}(w,_,G,W,Y,j,h)}};var we=new t.a,me=new w.r,ve=new g.a;function ye(e,a,n,i){var r=K(n,a,we),o=t.a.dot(r,e);if(o>z||o<j){var l=K(i,n,ie),c=o<j?s.n.PI_OVER_TWO:-s.n.PI_OVER_TWO,u=g.a.fromAxisAngle(l,c,ve),h=w.r.fromQuaternion(u,me);return w.r.multiplyByVector(h,e,e),!0}return!1}var _e=new t.i,Te=new t.a,be=new t.a;function De(e,a,n,i,r){var o=t.i.toCartesian(a,e._ellipsoid,Te),l=t.a.add(o,n,be),c=!1,u=e._ellipsoid,h=u.cartesianToCartographic(l,_e);Math.abs(a.longitude-h.longitude)>s.n.PI_OVER_TWO&&(c=!0,l=t.a.subtract(o,n,be),h=u.cartesianToCartographic(l,_e)),h.height=0;var p=e.project(h,r);return(r=t.a.subtract(p,i,r)).z=0,r=t.a.normalize(r,r),c&&t.a.negate(r,r),r}var Ee=new t.a,Oe=new t.a;function Ie(e,a,n,i,r,o){var s=t.a.subtract(a,e,Ee);t.a.normalize(s,s);var l=n-0,c=t.a.multiplyByScalar(s,l,Oe);t.a.add(e,c,r);var u=i-1e3;c=t.a.multiplyByScalar(s,u,Oe),t.a.add(a,c,o)}var Pe=new t.a;function Le(e,a){var n=m.n.getPointDistance(oe,e),i=m.n.getPointDistance(oe,a),r=Pe;s.n.equalsEpsilon(n,0,s.n.EPSILON2)?(r=K(a,e,r),t.a.multiplyByScalar(r,s.n.EPSILON2,r),t.a.add(e,r,e)):s.n.equalsEpsilon(i,0,s.n.EPSILON2)&&(r=K(e,a,r),t.a.multiplyByScalar(r,s.n.EPSILON2,r),t.a.add(a,r,a))}function ke(e,a){var t=Math.abs(e.longitude),n=Math.abs(a.longitude);if(s.n.equalsEpsilon(t,s.n.PI,s.n.EPSILON11)){var i=s.n.sign(a.longitude);return e.longitude=i*(t-s.n.EPSILON11),1}if(s.n.equalsEpsilon(n,s.n.PI,s.n.EPSILON11)){var r=s.n.sign(e.longitude);return a.longitude=r*(n-s.n.EPSILON11),2}return 0}var Ne=new t.i,xe=new t.i,Ae=new t.a,Se=new t.a,He=new t.a,Ce=new t.a,Me=new t.a,Re=new t.a,ze=[Ne,xe],je=new r.s,Ye=new t.a,Ge=new t.a,We=new t.a,Fe=new t.a,Be=new t.a,qe=new t.a,Xe=new t.a,Ue=new t.a,Ve=new t.a,Ze=new t.a,Je=new t.a,Qe=new t.a,Ke=new t.a,$e=new t.a,ea=new d.t,aa=new d.t,ta=new t.a,na=new t.a,ia=new t.a,ra=[new e.c,new e.c],oa=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],sa=oa.length;function la(e){return new g.r({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return Y._projectNormal=De,function(e,a){return H.initialize().then((function(){return i.t(a)&&(e=Y.unpack(e,a)),Y.createGeometry(e)}))}}));
