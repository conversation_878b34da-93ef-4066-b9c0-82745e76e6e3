define(["./when-515d5295","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./PrimitiveType-b38a4004","./VertexFormat-e844760b","./Rectangle-e170be8b","./Math-5e38123d","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5"],(function(e,t,n,a,r,o,i,p,c,m,u,y,s,b,v,d,f,w){"use strict";function l(t){t=e.e(t,e.e.EMPTY_OBJECT);var n=e.e(t.vertexFormat,c.n.DEFAULT);this._vertexFormat=n,this._workerName="createPlaneGeometry"}l.packedLength=c.n.packedLength,l.pack=function(t,n,r){return a.n.typeOf.object("value",t),a.n.defined("array",n),r=e.e(r,0),c.n.pack(t._vertexFormat,n,r),n};var A=new c.n,F={vertexFormat:A};l.unpack=function(t,n,r){a.n.defined("array",t),n=e.e(n,0);var o=c.n.unpack(t,n,A);return e.t(r)?(r._vertexFormat=c.n.clone(o,r._vertexFormat),r):new l(F)};var D=new n.a(-.5,-.5,0),x=new n.a(.5,.5,0);return l.createGeometry=function(e){var a,c,m=e._vertexFormat,u=new i.t;if(m.position){if((c=new Float64Array(12))[0]=D.x,c[1]=D.y,c[2]=0,c[3]=x.x,c[4]=D.y,c[5]=0,c[6]=x.x,c[7]=x.y,c[8]=0,c[9]=D.x,c[10]=x.y,c[11]=0,u.position=new o.r({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c}),m.normal){var y=new Float32Array(12);y[0]=0,y[1]=0,y[2]=1,y[3]=0,y[4]=0,y[5]=1,y[6]=0,y[7]=0,y[8]=1,y[9]=0,y[10]=0,y[11]=1,u.normal=new o.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})}if(m.st){var s=new Float32Array(8);s[0]=0,s[1]=0,s[2]=1,s[3]=0,s[4]=1,s[5]=1,s[6]=0,s[7]=1,u.st=new o.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:s})}if(m.tangent){var b=new Float32Array(12);b[0]=1,b[1]=0,b[2]=0,b[3]=1,b[4]=0,b[5]=0,b[6]=1,b[7]=0,b[8]=0,b[9]=1,b[10]=0,b[11]=0,u.tangent=new o.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:b})}if(m.bitangent){var v=new Float32Array(12);v[0]=0,v[1]=1,v[2]=0,v[3]=0,v[4]=1,v[5]=0,v[6]=0,v[7]=1,v[8]=0,v[9]=0,v[10]=1,v[11]=0,u.bitangent=new o.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})}(a=new Uint16Array(6))[0]=0,a[1]=1,a[2]=2,a[3]=0,a[4]=2,a[5]=3}return new o.T({attributes:u,indices:a,primitiveType:p._0x38df4a.TRIANGLES,boundingSphere:new t.c(n.a.ZERO,Math.sqrt(2))})},function(t,n){return e.t(n)&&(t=l.unpack(t,n)),l.createGeometry(t)}}));
