define(["exports","./buildModuleUrl-dba4ec07","./ComponentDatatype-d430c7f7","./when-515d5295","./Check-3aa71481","./Rectangle-e170be8b","./Intersect-53434a77","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryPipeline-137aa28e","./IndexDatatype-eefd5922","./PrimitiveType-b38a4004","./WebMercatorProjection-aa5a37a5"],(function(e,t,r,n,o,i,a,s,c,p,u,f,m){"use strict";function d(e,t,r){e=n.e(e,0),t=n.e(t,0),r=n.e(r,0),this.value=new Float32Array([e,t,r])}function h(e,t){var n=e.attributes,o=n.position,i=o.values.length/o.componentsPerAttribute;n.batchId=new s.r({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(i)});for(var a=n.batchId.values,c=0;c<i;++c)a[c]=t}function l(e){var i,a,s,c=e.instances,u=e.projection,m=e.elementIndexUintSupported,d=e.scene3DOnly,l=e.vertexCacheOptimize,g=e.compressVertices,y=e.modelMatrix,v=c.length;for(i=0;i<v;++i)if(n.t(c[i].geometry)){s=c[i].geometry.primitiveType;break}for(i=1;i<v;++i)if(n.t(c[i].geometry)&&c[i].geometry.primitiveType!==s)throw new o.t("All instance geometries must have the same primitiveType.");if(function(e,t,r){var o,i=!r,a=e.length;if(!i&&a>1){var s=e[0].modelMatrix;for(o=1;o<a;++o)if(!f.c.equals(s,e[o].modelMatrix)){i=!0;break}}if(i)for(o=0;o<a;++o)n.t(e[o].geometry)&&p.F.transformToWorldCoordinates(e[o]);else f.c.multiplyTransformation(t,e[0].modelMatrix,t)}(c,y,d),!d)for(i=0;i<v;++i)n.t(c[i].geometry)&&p.F.splitLongitude(c[i]);if(function(e){for(var t=e.length,r=0;r<t;++r){var o=e[r];n.t(o.geometry)?h(o.geometry,r):n.t(o.westHemisphereGeometry)&&n.t(o.eastHemisphereGeometry)&&(h(o.westHemisphereGeometry,r),h(o.eastHemisphereGeometry,r))}}(c),l)for(i=0;i<v;++i){var b=c[i];n.t(b.geometry)?(p.F.reorderForPostVertexCache(b.geometry),p.F.reorderForPreVertexCache(b.geometry)):n.t(b.westHemisphereGeometry)&&n.t(b.eastHemisphereGeometry)&&(p.F.reorderForPostVertexCache(b.westHemisphereGeometry),p.F.reorderForPreVertexCache(b.westHemisphereGeometry),p.F.reorderForPostVertexCache(b.eastHemisphereGeometry),p.F.reorderForPreVertexCache(b.eastHemisphereGeometry))}var x=p.F.combineInstances(c);for(v=x.length,i=0;i<v;++i){var k,C=(a=x[i]).attributes;if(d)for(k in C)C.hasOwnProperty(k)&&C[k].componentDatatype===r.ComponentDatatype.DOUBLE&&p.F.encodeAttribute(a,k,k+"3DHigh",k+"3DLow");else for(k in C)if(C.hasOwnProperty(k)&&C[k].componentDatatype===r.ComponentDatatype.DOUBLE){var w=k+"3D",S=k+"2D";p.F.projectTo2D(a,k,w,S,u),n.t(a.boundingSphere)&&"position"===k&&(a.boundingSphereCV=t.c.fromVertices(a.attributes.position2D.values)),p.F.encodeAttribute(a,w,w+"High",w+"Low"),p.F.encodeAttribute(a,S,S+"High",S+"Low")}g&&p.F.compressVertices(a)}if(!m){var A=[];for(v=x.length,i=0;i<v;++i)a=x[i],A=A.concat(p.F.fitToUnsignedShortIndices(a));x=A}return x}function g(e,t,r,o){var i,a,s,c=o.length-1;if(c>=0){var p=o[c];i=p.offset+p.count,a=r[s=p.index].indices.length}else i=0,a=r[s=0].indices.length;for(var u=e.length,f=0;f<u;++f){var m=e[f][t];if(n.t(m)){var d=m.indices.length;i+d>a&&(i=0,a=r[++s].indices.length),o.push({index:s,offset:i,count:d}),i+=d}}}Object.defineProperties(d.prototype,{componentDatatype:{get:function(){return r.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),d.fromCartesian3=function(e){return o.n.defined("offset",e),new d(e.x,e.y,e.z)},d.toValue=function(e,t){return o.n.defined("offset",e),n.t(t)||(t=new Float32Array([e.x,e.y,e.z])),t[0]=e.x,t[1]=e.y,t[2]=e.z,t};var y={};function v(e,t){var r=e.attributes;for(var o in r)if(r.hasOwnProperty(o)){var i=r[o];n.t(i)&&n.t(i.values)&&t.push(i.values.buffer)}n.t(e.indices)&&t.push(e.indices.buffer)}function b(e,t){var r=e.length,o=new Float64Array(1+19*r),i=0;o[i++]=r;for(var a=0;a<r;a++){var s=e[a];if(f.c.pack(s.modelMatrix,o,i),i+=f.c.packedLength,n.t(s.attributes)&&n.t(s.attributes.offset)){var c=s.attributes.offset.value;o[i]=c[0],o[i+1]=c[1],o[i+2]=c[2]}i+=3}return t.push(o.buffer),o}function x(e){for(var t=e,r=new Array(t[0]),o=0,i=1;i<t.length;){var a,s=f.c.unpack(t,i);i+=f.c.packedLength,n.t(t[i])&&(a={offset:new d(t[i],t[i+1],t[i+2])}),i+=3,r[o++]={modelMatrix:s,attributes:a}}return r}function k(e){var r=e.length,o=1+(t.c.packedLength+1)*r,i=new Float32Array(o),a=0;i[a++]=r;for(var s=0;s<r;++s){var c=e[s];n.t(c)?(i[a++]=1,t.c.pack(e[s],i,a)):i[a++]=0,a+=t.c.packedLength}return i}function C(e){for(var r=new Array(e[0]),n=0,o=1;o<e.length;)1===e[o++]&&(r[n]=t.c.unpack(e,o)),++n,o+=t.c.packedLength;return r}y.combineGeometry=function(e){var r,o,i,a,s=e.instances,c=s.length,u=!1;c>0&&((r=l(e)).length>0&&(o=p.F.createAttributeLocations(r[0]),e.createPickOffsets&&(i=function(e,t){var r=[];return g(e,"geometry",t,r),g(e,"westHemisphereGeometry",t,r),g(e,"eastHemisphereGeometry",t,r),r}(s,r))),n.t(s[0].attributes)&&n.t(s[0].attributes.offset)&&(a=new Array(c),u=!0));for(var f=new Array(c),m=new Array(c),d=0;d<c;++d){var h=s[d],y=h.geometry;n.t(y)&&(f[d]=y.boundingSphere,m[d]=y.boundingSphereCV,u&&(a[d]=h.geometry.offsetAttribute));var v=h.eastHemisphereGeometry,b=h.westHemisphereGeometry;n.t(v)&&n.t(b)&&(n.t(v.boundingSphere)&&n.t(b.boundingSphere)&&(f[d]=t.c.union(v.boundingSphere,b.boundingSphere)),n.t(v.boundingSphereCV)&&n.t(b.boundingSphereCV)&&(m[d]=t.c.union(v.boundingSphereCV,b.boundingSphereCV)))}return{geometries:r,modelMatrix:e.modelMatrix,attributeLocations:o,pickOffsets:i,offsetInstanceExtend:a,boundingSpheres:f,boundingSpheresCV:m}},y.packCreateGeometryResults=function(e,r){var o=new Float64Array(function(e){for(var r=1,o=e.length,i=0;i<o;i++){var a=e[i];if(++r,n.t(a)){var s=a.attributes;for(var c in r+=7+2*t.c.packedLength+(n.t(a.indices)?a.indices.length:0),s)s.hasOwnProperty(c)&&n.t(s[c])&&(r+=6+s[c].values.length)}}return r}(e)),i=[],a={},s=e.length,c=0;o[c++]=s;for(var p=0;p<s;p++){var u=e[p],f=n.t(u);if(o[c++]=f?1:0,f){o[c++]=u.primitiveType,o[c++]=u.geometryType,o[c++]=n.e(u.offsetAttribute,-1);var m=n.t(u.boundingSphere)?1:0;o[c++]=m,m&&t.c.pack(u.boundingSphere,o,c),c+=t.c.packedLength;var d=n.t(u.boundingSphereCV)?1:0;o[c++]=d,d&&t.c.pack(u.boundingSphereCV,o,c),c+=t.c.packedLength;var h=u.attributes,l=[];for(var g in h)h.hasOwnProperty(g)&&n.t(h[g])&&(l.push(g),n.t(a[g])||(a[g]=i.length,i.push(g)));o[c++]=l.length;for(var y=0;y<l.length;y++){var v=l[y],b=h[v];o[c++]=a[v],o[c++]=b.componentDatatype,o[c++]=b.componentsPerAttribute,o[c++]=b.normalize?1:0,o[c++]=b.isInstanceAttribute?1:0,o[c++]=b.values.length,o.set(b.values,c),c+=b.values.length}var x=n.t(u.indices)?u.indices.length:0;o[c++]=x,x>0&&(o.set(u.indices,c),c+=x)}}return r.push(o.buffer),{stringTable:i,packedData:o}},y.unpackCreateGeometryResults=function(e){for(var n,o=e.stringTable,i=e.packedData,a=new Array(i[0]),p=0,f=1;f<i.length;){if(1===i[f++]){var m,d,h=i[f++],l=i[f++],g=i[f++];-1===g&&(g=void 0),1===i[f++]&&(m=t.c.unpack(i,f)),f+=t.c.packedLength,1===i[f++]&&(d=t.c.unpack(i,f)),f+=t.c.packedLength;var y,v,b,x,k=new c.t,C=i[f++];for(n=0;n<C;n++){var w=o[i[f++]],S=i[f++];b=i[f++];var A=0!==i[f++],G=0!==i[f++];y=i[f++],v=r.ComponentDatatype.createTypedArray(S,y);for(var D=0;D<y;D++)v[D]=i[f++];k[w]=new s.r({componentDatatype:S,componentsPerAttribute:b,normalize:A,values:v}),G&&(k[w].isInstanceAttribute=!0)}if((y=i[f++])>0){var F=v.length/b;for(x=u.IndexDatatype.createTypedArray(F,y),n=0;n<y;n++)x[n]=i[f++]}a[p++]=new s.T({primitiveType:h,geometryType:l,boundingSphere:m,boundingSphereCV:d,indices:x,attributes:k,offsetAttribute:g})}else a[p++]=void 0}return a},y.packCombineGeometryParameters=function(e,t){for(var r=e.createGeometryResults,n=r.length,o=0;o<n;o++)t.push(r[o].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:b(e.instances,t),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof a.s,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},y.unpackCombineGeometryParameters=function(e){for(var t=x(e.packedInstances),r=e.createGeometryResults,n=r.length,o=0,s=0;s<n;s++)for(var c=y.unpackCreateGeometryResults(r[s]),p=c.length,u=0;u<p;u++){var d=c[u];t[o].geometry=d,++o}var h=i.n.clone(e.ellipsoid);return{instances:t,ellipsoid:h,projection:e.isGeographic?new a.s(h):new m.e(h),elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:f.c.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},y.packCombineGeometryResults=function(e,t){n.t(e.geometries)&&function(e,t){for(var r=e.length,n=0;n<r;++n)v(e[n],t)}(e.geometries,t);var r=k(e.boundingSpheres),o=k(e.boundingSpheresCV);return t.push(r.buffer,o.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:r,boundingSpheresCV:o}},y.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:C(e.boundingSpheres),boundingSpheresCV:C(e.boundingSpheresCV)}},e.S=y}));
