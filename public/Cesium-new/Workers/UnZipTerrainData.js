define(["./createTaskProcessorWorker","./pako_inflate-f73548c4","./when-515d5295"],(function(e,t,n){"use strict";if(typeof WebAssembly<"u"&&"object"!=typeof window){let e=function(e){return i.locateFile?i.locateFile(e,f):f+e},t=function(e){t.shown||(t.shown={}),t.shown[e]||(t.shown[e]=1)},n=function(e,t,n){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":return g[e>>0];case"i16":return M[e>>1];case"i32":case"i64":return b[e>>2];case"float":return F[e>>2];case"double":return v[e>>3];default:ft("invalid type for getValue: "+t)}return null},ye=function(e,t){e||ft("Assertion failed: "+t)},he=function(e){var t=i["_"+e];return ye(t,"Cannot call unknown function "+e+", make sure it is exported"),t},me=function(e,t,n,r,i){var o={string:function(e){var t=0;if(null!=e&&0!==e){var n=1+(e.length<<2);t=Ee(n),Oe(e,t,n)}return t},array:function(e){var t=Ee(e.length);return Me(e,t),t}};var a=he(e),s=[],u=0;if(ye("array"!==t,'Return type should not be "array".'),r)for(var l=0;l<r.length;l++){var c=o[n[l]];c?(0===u&&(u=_e()),s[l]=c(r[l])):s[l]=r[l]}var d,f=a.apply(null,s);return d=f,f="string"===t?Ae(d):"boolean"===t?Boolean(d):d,0!==u&&Te(u),f},Re=function(e,t,n,r){return function(){return me(e,t,n,arguments)}},we=function(e,n,r){for(var i=n+r,o=n;e[o]&&!(o>=i);)++o;if(o-n>16&&e.subarray&&w)return w.decode(e.subarray(n,o));for(var a="";n<o;){var s=e[n++];if(128&s){var u=63&e[n++];if(192!=(224&s)){var l=63&e[n++];if(224==(240&s)?s=(15&s)<<12|u<<6|l:(240!=(248&s)&&t("Invalid UTF-8 leading byte 0x"+s.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),s=(7&s)<<18|u<<12|l<<6|63&e[n++]),s<65536)a+=String.fromCharCode(s);else{var c=s-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&s)<<6|u)}else a+=String.fromCharCode(s)}return a},Ae=function(e,t){return e?we(O,e,t):""},ge=function(e,n,r,i){if(!(i>0))return 0;for(var o=r,a=r+i-1,s=0;s<e.length;++s){var u=e.charCodeAt(s);if(u>=55296&&u<=57343)u=65536+((1023&u)<<10)|1023&e.charCodeAt(++s);if(u<=127){if(r>=a)break;n[r++]=u}else if(u<=2047){if(r+1>=a)break;n[r++]=192|u>>6,n[r++]=128|63&u}else if(u<=65535){if(r+2>=a)break;n[r++]=224|u>>12,n[r++]=128|u>>6&63,n[r++]=128|63&u}else{if(r+3>=a)break;u>=2097152&&t("Invalid Unicode code point 0x"+u.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),n[r++]=240|u>>18,n[r++]=128|u>>12&63,n[r++]=128|u>>6&63,n[r++]=128|63&u}}return n[r]=0,r-o},Oe=function(e,t,n){return ye("number"==typeof n,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),ge(e,O,t,n)},Me=function(e,t){ye(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),g.set(e,t)},be=function(e){return e.replace(/__Z[\w\d_]+/g,(function(e){return e===e?e:e+" ["+e+"]"}))},Se=function(){var e=new Error;if(!e.stack){try{throw new Error(0)}catch(t){e=t}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()},Fe=function(){var e=Se();return i.extraStackTrace&&(e+="\n"+i.extraStackTrace()),be(e)},ve=function(e,t){return e%t>0&&(e+=t-e%t),e},Ie=function(){i.HEAP8=g=new Int8Array(A),i.HEAP16=M=new Int16Array(A),i.HEAP32=b=new Int32Array(A),i.HEAPU8=O=new Uint8Array(A),i.HEAPU16=new Uint16Array(A),i.HEAPU32=S=new Uint32Array(A),i.HEAPF32=F=new Float32Array(A),i.HEAPF64=v=new Float64Array(A)},Ne=function(){ye(0==(3&U)),S[(U>>2)-1]=34821223,S[(U>>2)-2]=2310721022},Ue=function(){(34821223!=S[(U>>2)-1]||2310721022!=S[(U>>2)-2])&&ft("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x02135467, but received 0x"+S[(U>>2)-2].toString(16)+" "+S[(U>>2)-1].toString(16)),1668509029!==b[0]&&ft("Runtime error: The application has corrupted its heap memory area (address zero)!")},xe=function(e){ft("Stack overflow! Attempted to allocate "+e+" bytes on the stack, but stack has only "+(U-_e()+e)+" bytes available!")},De=function(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?i.dynCall_v(n):i.dynCall_vi(n,t.arg):n(void 0===t.arg?null:t.arg)}else t()}},Xe=function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)He(i.preRun.shift());De(L)},Pe=function(){Ue(),!Q&&(Q=!0,De(k))},Le=function(){Ue(),De(H)},ke=function(){if(Ue(),i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)Ce(i.postRun.shift());De(C)},He=function(e){L.unshift(e)},Ce=function(e){C.unshift(e)},Qe=function(e){Y++,i.monitorRunDependencies&&i.monitorRunDependencies(Y),e&&(ye(!j[e]),j[e]=1,null===W&&typeof setInterval<"u"&&(W=setInterval((function(){if(R)return clearInterval(W),void(W=null)}),1e4)))},Be=function(e){if(Y--,i.monitorRunDependencies&&i.monitorRunDependencies(Y),e?(ye(j[e]),delete j[e]):T("warning: run dependency removed without ID"),0==Y&&(null!==W&&(clearInterval(W),W=null),z)){var t=z;z=null,t()}},Ye=function(e){return String.prototype.startsWith?e.startsWith(G):0===e.indexOf(G)},We=function(){try{if(i.wasmBinary)return new Uint8Array(i.wasmBinary);if(i.readBinary)return i.readBinary(q);throw"both async and sync fetching of the wasm failed"}catch(e){ft(e)}},ze=function(){return i.wasmBinary||!a&&!s||"function"!=typeof fetch?new Promise((function(e,t){e(We())})):fetch(q,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+q+"'";return e.arrayBuffer()})).catch((function(){return We()}))},je=function(e){var t={env:e,global:{NaN:NaN,Infinity:1/0},"global.Math":Math,asm2wasm:_};function n(e,t){var n=e.exports;i.asm=n,Be("wasm-instantiate")}Qe("wasm-instantiate");var r=i;function o(e){ye(i===r,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),r=null,n(e.instance)}function a(e){return ze().then((function(e){return WebAssembly.instantiate(e,t)})).then(e,(function(e){}))}if(i.instantiateWasm)try{return i.instantiateWasm(t,n)}catch{return!1}return function(){if(i.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||Ye(q)||"function"!=typeof fetch)return a(o);fetch(q,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(o,(function(e){a(o)}))}))}(),{}},Ve=function(){},Ge=function(e,t){K.varargs=t;try{K.getStreamFromFD(),K.get(),K.get(),K.get(),K.get();return ft("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return(typeof V>"u"||!(e instanceof V.ErrnoError))&&ft(e),-e.errno}},qe=function(){var e=i._fflush;e&&e(0);var t=K.buffers;t[1].length&&K.printChar(1,10),t[2].length&&K.printChar(2,10)},Je=function(e,t){K.varargs=t;try{for(var n=K.get(),r=K.get(),i=K.get(),o=0,a=0;a<i;a++){for(var s=b[r+8*a>>2],u=b[r+(8*a+4)>>2],l=0;l<u;l++)K.printChar(n,O[s+l]);o+=u}return o}catch(e){return(typeof V>"u"||!(e instanceof V.ErrnoError))&&ft(e),-e.errno}},Ke=function(e,t){K.varargs=t;try{return 0}catch(e){return(typeof V>"u"||!(e instanceof V.ErrnoError))&&ft(e),-e.errno}},Ze=function(e,t){K.varargs=t;try{K.getStreamFromFD();return ft("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return(typeof V>"u"||!(e instanceof V.ErrnoError))&&ft(e),-e.errno}},$e=function(){},et=function(){return g.length},tt=function(e,t,n){O.set(O.subarray(t,t+n),e)},nt=function(e){return i.___errno_location?b[i.___errno_location()>>2]=e:T("failed to set errno from JS"),e},rt=function(e){ft("Cannot enlarge memory arrays to size "+e+" bytes (OOM). Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+g.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},it=function(e){e=ve(e,65536);var t=A.byteLength;try{return-1!==m.grow((e-t)/65536)&&(A=m.buffer,!0)}catch(n){return console.error("emscripten_realloc_buffer: Attempted to grow from "+t+" bytes to "+e+" bytes, but got error: "+n),!1}},ot=function(e){var n=et();ye(e>n);var r=65536,i=2147418112;if(e>i)return T("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+i+" bytes!"),!1;for(var o=Math.max(n,16777216);o<e;)(o=o<=536870912?ve(2*o,r):Math.min(ve((3*o+2147483648)/4,r),i))===n&&t("Cannot ask for more memory since we reached the practical limit in browsers (which is just below 2GB), so the request would have failed. Requesting only "+g.length);return it(o)?(Ie(),!0):(T("Failed to grow the heap from "+n+" bytes to "+o+" bytes, not enough memory!"),!1)},at=function(e){T("Invalid function pointer called with signature 'ii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),ft(e)},st=function(e){T("Invalid function pointer called with signature 'iiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),ft(e)},ut=function(e){T("Invalid function pointer called with signature 'jiji'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),ft(e)},lt=function(e){T("Invalid function pointer called with signature 'vii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),ft(e)},ct=function(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e},dt=function(e){function t(){i.calledRun||(i.calledRun=!0,!R&&(Pe(),Le(),i.onRuntimeInitialized&&i.onRuntimeInitialized(),ye(!i._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),ke()))}e=e||i.arguments,Y>0||(Ne(),Xe(),Y>0)||i.calledRun||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),t()}),1)):t(),Ue())},ft=function(e){i.onAbort&&i.onAbort(e),R=!0;var t="abort("+(e=void 0!==e?'"'+e+'"':"")+") at "+Fe();throw pe&&pe.forEach((function(n){t=n(t,e)})),t};var r,i=typeof i<"u"?i:{},o={};for(r in i)i.hasOwnProperty(r)&&(o[r]=i[r]);i.arguments=[],i.thisProgram="./this.program",i.quit=function(e,t){throw t},i.preRun=[],i.postRun=[];var a=!1,s=!1,u=!1,l=!1;if(a="object"==typeof window,s="function"==typeof importScripts,u="object"==typeof process&&"function"==typeof require&&!a&&!s,l=!a&&!u&&!s,i.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var c,d,f="";if(u)f=__dirname+"/",i.read=function(e,t){var n;return c||(c=require("fs")),d||(d=require("path")),e=d.normalize(e),n=c.readFileSync(e),t?n:n.toString()},i.readBinary=function(e){var t=i.read(e,!0);return t.buffer||(t=new Uint8Array(t)),ye(t.buffer),t},process.argv.length>1&&(i.thisProgram=process.argv[1].replace(/\\/g,"/")),i.arguments=process.argv.slice(2),typeof module<"u"&&(module.exports=i),process.on("uncaughtException",(function(e){if(!(e instanceof ct))throw e})),process.on("unhandledRejection",ft),i.quit=function(e){process.exit(e)},i.inspect=function(){return"[Emscripten Module object]"};else if(l)typeof read<"u"&&(i.read=function(e){return read(e)}),i.readBinary=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(t=read(e,"binary"),ye("object"==typeof t),t)},typeof scriptArgs<"u"?i.arguments=scriptArgs:typeof arguments<"u"&&(i.arguments=arguments),"function"==typeof quit&&(i.quit=function(e){quit(e)});else{if(!a&&!s)throw new Error("environment detection error");s?f=self.location.href:document.currentScript&&(f=document.currentScript.src),f=0!==f.indexOf("blob:")?f.substr(0,f.indexOf("/Workers")+1):"",i.read=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},s&&(i.readBinary=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),i.readAsync=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)},i.setWindowTitle=function(e){document.title=e}}var E=i.print||(typeof console<"u"?console.log.bind(console):typeof print<"u"?print:null),T=i.printErr||(typeof printErr<"u"?printErr:typeof console<"u"&&console.warn.bind(console)||E);for(r in o)o.hasOwnProperty(r)&&(i[r]=o[r]);o=void 0,ye(typeof i.memoryInitializerPrefixURL>"u","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),ye(typeof i.pthreadMainPrefixURL>"u","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),ye(typeof i.cdInitializerPrefixURL>"u","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),ye(typeof i.filePackagePrefixURL>"u","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),_e=Te=Ee=function(){ft("cannot use the stack before compiled code is ready to run, and has provided stack access")};var _={"f64-rem":function(e,t){return e%t},debugger:function(){}};new Array(0);var p=0,y=function(e){p=e},h=function(){return p};"object"!=typeof WebAssembly&&ft("No WebAssembly support found. Build with -s WASM=0 to target JavaScript instead.");var m,R=!1,w=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;typeof TextDecoder<"u"&&new TextDecoder("utf-16le");var A,g,O,M,b,S,F,v,I=65536,N=15104,U=5257984,x=5257984,D=15072;ye(N%16==0,"stack must start aligned"),ye(x%16==0,"heap must start aligned");var X=5242880;i.TOTAL_STACK&&ye(X===i.TOTAL_STACK,"the stack size can no longer be determined at runtime");var P=i.TOTAL_MEMORY||16777216;if(P<X&&T("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+P+"! (TOTAL_STACK="+X+")"),ye(typeof Int32Array<"u"&&typeof Float64Array<"u"&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),i.buffer?(A=i.buffer,ye(A.byteLength===P,"provided buffer should be "+P+" bytes, but it is "+A.byteLength)):("object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(ye(P%I==0),m=new WebAssembly.Memory({initial:P/I}),A=m.buffer):A=new ArrayBuffer(P),ye(A.byteLength===P)),Ie(),b[D>>2]=x,b[0]=1668509029,M[1]=25459,115!==O[2]||99!==O[3])throw"Runtime error: expected the system to be little-endian!";var L=[],k=[],H=[],C=[],Q=!1,B=!1;ye(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),ye(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),ye(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),ye(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Y=0,W=null,z=null,j={};i.preloadedImages={},i.preloadedAudios={};var V={error:function(){ft("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){V.error()},createDataFile:function(){V.error()},createPreloadedFile:function(){V.error()},createLazyFile:function(){V.error()},open:function(){V.error()},mkdev:function(){V.error()},registerDevice:function(){V.error()},analyzePath:function(){V.error()},loadFilesFromDB:function(){V.error()},ErrnoError:function(){V.error()}};i.FS_createDataFile=V.createDataFile,i.FS_createPreloadedFile=V.createPreloadedFile;var G="data:application/octet-stream;base64,",q="ThirdParty/unzip.wasm";Ye(q)||(q=e(q)),i.asm=function(e,t,n){t.memory=m,t.table=new WebAssembly.Table({initial:22,maximum:22,element:"anyfunc"}),t.__memory_base=1024,t.__table_base=0;var r=je(t);return ye(r,"binaryen setup failed (no wasm support?)"),r};var J=15088;ye(J%8==0);var K={buffers:[null,[],[]],printChar:function(e,t){var n=K.buffers[e];ye(n),0===t||10===t?((1===e?E:T)(we(n,0)),n.length=0):n.push(t)},varargs:0,get:function(e){return K.varargs+=4,b[K.varargs-4>>2]},getStr:function(){return Ae(K.get())},get64:function(){var e=K.get(),t=K.get();return ye(e>=0?0===t:-1===t),e},getZero:function(){ye(0===K.get())}},Z={},$={abort:ft,setTempRet0:y,getTempRet0:h,abortStackOverflow:xe,nullFunc_ii:at,nullFunc_iiii:st,nullFunc_jiji:ut,nullFunc_vii:lt,___lock:Ve,___setErrNo:nt,___syscall140:Ge,___syscall146:Je,___syscall54:Ke,___syscall6:Ze,___unlock:$e,_emscripten_get_heap_size:et,_emscripten_memcpy_big:tt,_emscripten_resize_heap:ot,abortOnCannotGrowMemory:rt,emscripten_realloc_buffer:it,flush_NO_FILESYSTEM:qe,tempDoublePtr:J,DYNAMICTOP_PTR:D},ee=i.asm(Z,$,A),te=ee.___errno_location;ee.___errno_location=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),te.apply(null,arguments)};var ne=ee._fflush;ee._fflush=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ne.apply(null,arguments)};var re=ee._free;ee._free=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),re.apply(null,arguments)};var ie=ee._freePointer;ee._freePointer=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ie.apply(null,arguments)};var oe=ee._llvm_bswap_i32;ee._llvm_bswap_i32=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),oe.apply(null,arguments)};var ae=ee._malloc;ee._malloc=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ae.apply(null,arguments)};var se=ee._sbrk;ee._sbrk=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),se.apply(null,arguments)};var ue=ee._unzip;ee._unzip=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ue.apply(null,arguments)};var le=ee.establishStackSpace;ee.establishStackSpace=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),le.apply(null,arguments)};var ce=ee.stackAlloc;ee.stackAlloc=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ce.apply(null,arguments)};var de=ee.stackRestore;ee.stackRestore=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),de.apply(null,arguments)};var fe=ee.stackSave;ee.stackSave=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),fe.apply(null,arguments)},i.asm=ee,i.___errno_location=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.___errno_location.apply(null,arguments)},i._emscripten_replace_memory=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._emscripten_replace_memory.apply(null,arguments)},i._fflush=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._fflush.apply(null,arguments)},i._free=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._free.apply(null,arguments)},i._freePointer=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._freePointer.apply(null,arguments)},i._llvm_bswap_i32=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._llvm_bswap_i32.apply(null,arguments)},i._malloc=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._malloc.apply(null,arguments)},i._memcpy=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._memcpy.apply(null,arguments)},i._memset=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._memset.apply(null,arguments)},i._sbrk=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._sbrk.apply(null,arguments)},i._unzip=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._unzip.apply(null,arguments)},i.establishStackSpace=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.establishStackSpace.apply(null,arguments)};var Ee=i.stackAlloc=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackAlloc.apply(null,arguments)},Te=i.stackRestore=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackRestore.apply(null,arguments)},_e=i.stackSave=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackSave.apply(null,arguments)};i.dynCall_ii=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_ii.apply(null,arguments)},i.dynCall_iiii=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_iiii.apply(null,arguments)},i.dynCall_jiji=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_jiji.apply(null,arguments)},i.dynCall_vii=function(){return ye(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),ye(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_vii.apply(null,arguments)},i.asm=ee,i.intArrayFromString||(i.intArrayFromString=function(){ft("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.intArrayToString||(i.intArrayToString=function(){ft("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.ccall=me,i.cwrap=Re,i.setValue||(i.setValue=function(){ft("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getValue=n,i.allocate||(i.allocate=function(){ft("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getMemory||(i.getMemory=function(){ft("'getMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.AsciiToString||(i.AsciiToString=function(){ft("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToAscii||(i.stringToAscii=function(){ft("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF8ArrayToString||(i.UTF8ArrayToString=function(){ft("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF8ToString||(i.UTF8ToString=function(){ft("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF8Array||(i.stringToUTF8Array=function(){ft("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF8||(i.stringToUTF8=function(){ft("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF8||(i.lengthBytesUTF8=function(){ft("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF16ToString||(i.UTF16ToString=function(){ft("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF16||(i.stringToUTF16=function(){ft("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF16||(i.lengthBytesUTF16=function(){ft("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF32ToString||(i.UTF32ToString=function(){ft("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF32||(i.stringToUTF32=function(){ft("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF32||(i.lengthBytesUTF32=function(){ft("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.allocateUTF8||(i.allocateUTF8=function(){ft("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackTrace||(i.stackTrace=function(){ft("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPreRun||(i.addOnPreRun=function(){ft("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnInit||(i.addOnInit=function(){ft("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPreMain||(i.addOnPreMain=function(){ft("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnExit||(i.addOnExit=function(){ft("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPostRun||(i.addOnPostRun=function(){ft("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeStringToMemory||(i.writeStringToMemory=function(){ft("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeArrayToMemory||(i.writeArrayToMemory=function(){ft("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeAsciiToMemory||(i.writeAsciiToMemory=function(){ft("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addRunDependency||(i.addRunDependency=function(){ft("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.removeRunDependency||(i.removeRunDependency=function(){ft("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.ENV||(i.ENV=function(){ft("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.FS||(i.FS=function(){ft("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.FS_createFolder||(i.FS_createFolder=function(){ft("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createPath||(i.FS_createPath=function(){ft("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createDataFile||(i.FS_createDataFile=function(){ft("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createPreloadedFile||(i.FS_createPreloadedFile=function(){ft("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createLazyFile||(i.FS_createLazyFile=function(){ft("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createLink||(i.FS_createLink=function(){ft("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createDevice||(i.FS_createDevice=function(){ft("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_unlink||(i.FS_unlink=function(){ft("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.GL||(i.GL=function(){ft("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.dynamicAlloc||(i.dynamicAlloc=function(){ft("'dynamicAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.warnOnce||(i.warnOnce=function(){ft("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.loadDynamicLibrary||(i.loadDynamicLibrary=function(){ft("'loadDynamicLibrary' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.loadWebAssemblyModule||(i.loadWebAssemblyModule=function(){ft("'loadWebAssemblyModule' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getLEB||(i.getLEB=function(){ft("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getFunctionTables||(i.getFunctionTables=function(){ft("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.alignFunctionTables||(i.alignFunctionTables=function(){ft("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.registerFunctions||(i.registerFunctions=function(){ft("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addFunction||(i.addFunction=function(){ft("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.removeFunction||(i.removeFunction=function(){ft("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getFuncWrapper||(i.getFuncWrapper=function(){ft("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.prettyPrint||(i.prettyPrint=function(){ft("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.makeBigInt||(i.makeBigInt=function(){ft("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.dynCall||(i.dynCall=function(){ft("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getCompilerSetting||(i.getCompilerSetting=function(){ft("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackSave||(i.stackSave=function(){ft("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackRestore||(i.stackRestore=function(){ft("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackAlloc||(i.stackAlloc=function(){ft("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.establishStackSpace||(i.establishStackSpace=function(){ft("'establishStackSpace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.print||(i.print=function(){ft("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.printErr||(i.printErr=function(){ft("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getTempRet0||(i.getTempRet0=function(){ft("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.setTempRet0||(i.setTempRet0=function(){ft("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.Pointer_stringify||(i.Pointer_stringify=function(){ft("'Pointer_stringify' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.ALLOC_NORMAL||Object.defineProperty(i,"ALLOC_NORMAL",{get:function(){ft("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_STACK||Object.defineProperty(i,"ALLOC_STACK",{get:function(){ft("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_DYNAMIC||Object.defineProperty(i,"ALLOC_DYNAMIC",{get:function(){ft("'ALLOC_DYNAMIC' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_NONE||Object.defineProperty(i,"ALLOC_NONE",{get:function(){ft("'ALLOC_NONE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),ct.prototype=new Error,ct.prototype.constructor=ct,z=function e(){i.calledRun||dt(),i.calledRun||(z=e)},i.run=dt;var pe=[];if(i.abort=ft,i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();i.noExitRuntime=!0,dt()}else i=null;var ye=i,he=!1;if("undefined"!=typeof WebAssembly){ye.onRuntimeInitialized=function(){he=!0};var me=ye.cwrap("unzip","number",["number","number","number","number"]),Re=ye.cwrap("freePointer",null,["number"])}function we(e){var t=4*e.length,n=ye._malloc(Uint8Array.BYTES_PER_ELEMENT*t),r=new Uint8Array(t);ye.HEAPU8.set(r,n/Uint8Array.BYTES_PER_ELEMENT);var i,o=ye._malloc(Uint8Array.BYTES_PER_ELEMENT*e.length);for(ye.HEAPU8.set(e,o/Uint8Array.BYTES_PER_ELEMENT);0==(i=me(n,t,o,e.length));)Re(n),t*=4,n=ye._malloc(Uint8Array.BYTES_PER_ELEMENT*t),r=new Uint8Array(t),ye.HEAPU8.set(r,n/Uint8Array.BYTES_PER_ELEMENT);var a=new Uint8Array(ye.HEAPU8.buffer,n,i);e=null,r=null;var s=new Uint8Array(a);return Re(n),Re(o),s}function Ae(e,n){var r,i=e.data,o=new Uint8Array(i);return!0===he?{data:r=we(o)}:(r=t.pako.inflate(o).buffer,n.push(r),{data:new Uint8Array(r)})}var ge=e(Ae);return ge}));
