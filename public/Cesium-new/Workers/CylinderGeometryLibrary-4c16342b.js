define(["exports","./Math-5e38123d"],(function(t,n){"use strict";var r={computePositions:function(t,r,o,a,e){var i,s=.5*t,c=-s,u=a+a,f=new Float64Array(3*(e?2*u:u)),h=0,v=0,M=e?3*u:0,d=e?3*(u+a):3*a;for(i=0;i<a;i++){var p=i/a*n.n.TWO_PI,x=Math.cos(p),P=Math.sin(p),l=x*o,m=P*o,w=x*r,y=P*r;f[v+M]=l,f[v+M+1]=m,f[v+M+2]=c,f[v+d]=w,f[v+d+1]=y,f[v+d+2]=s,v+=3,e&&(f[h++]=l,f[h++]=m,f[h++]=c,f[h++]=w,f[h++]=y,f[h++]=s)}return f}};t.x=r}));
