define(["./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./EllipseGeometry-2d9feab2","./Rectangle-e170be8b","./VertexFormat-e844760b","./Math-5e38123d","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./Cartesian2-1b9b0d8a","./ComponentDatatype-d430c7f7","./EllipseGeometryLibrary-497ff3d7","./GeometryAttribute-9bc31a7f","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryInstance-c11993d9","./GeometryOffsetAttribute-800f7650","./GeometryPipeline-137aa28e","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./IndexDatatype-eefd5922","./IntersectionTests-5fa33dbd","./Plane-92c15089"],(function(e,t,r,i,o,n,a,s,l,d,c,m,u,p,y,_,h,x,f,g,G,b,v,w,A,M,H,C,R){"use strict";function F(e){var o=(e=r.e(e,r.e.EMPTY_OBJECT)).radius;t.n.typeOf.number("radius",o);var n={center:e.center,semiMajorAxis:o,semiMinorAxis:o,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new i.H(n),this._workerName="createCircleGeometry"}F.packedLength=i.H.packedLength,F.pack=function(e,r,o){return t.n.typeOf.object("value",e),i.H.pack(e._ellipseGeometry,r,o)};var j=new i.H({center:new e.a,semiMajorAxis:1,semiMinorAxis:1}),k={center:new e.a,radius:void 0,ellipsoid:o.n.clone(o.n.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new n.n,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return F.unpack=function(t,a,s){var l=i.H.unpack(t,a,j);return k.center=e.a.clone(l._center,k.center),k.ellipsoid=o.n.clone(l._ellipsoid,k.ellipsoid),k.height=l._height,k.extrudedHeight=l._extrudedHeight,k.granularity=l._granularity,k.vertexFormat=n.n.clone(l._vertexFormat,k.vertexFormat),k.stRotation=l._stRotation,k.shadowVolume=l._shadowVolume,r.t(s)?(k.semiMajorAxis=l._semiMajorAxis,k.semiMinorAxis=l._semiMinorAxis,s._ellipseGeometry=new i.H(k),s):(k.radius=l._semiMajorAxis,new F(k))},F.createGeometry=function(e){return i.H.createGeometry(e._ellipseGeometry)},F.createShadowVolume=function(e,t,r){var i=e._ellipseGeometry._granularity,o=e._ellipseGeometry._ellipsoid,a=t(i,o),s=r(i,o);return new F({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:o,stRotation:e._ellipseGeometry._stRotation,granularity:i,extrudedHeight:a,height:s,vertexFormat:n.n.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(F.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(t,i){return r.t(i)&&(t=F.unpack(t,i)),t._ellipseGeometry._center=e.a.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=o.n.clone(t._ellipseGeometry._ellipsoid),F.createGeometry(t)}}));
