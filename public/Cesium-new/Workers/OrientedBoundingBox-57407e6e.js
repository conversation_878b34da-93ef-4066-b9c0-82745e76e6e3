define(["exports","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Cartesian4-034d54d5","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./EllipsoidTangentPlane-fd839d7b","./Intersect-53434a77","./Math-5e38123d","./PrimitiveType-b38a4004","./Plane-92c15089","./PolygonPipeline-b8b35011"],(function(a,t,e,n,r,i,o,s,d,u,c,h,m,l){"use strict";var w=[];function f(a,t){this.center=n.a.clone(o.e(a,n.a.ZERO)),this.halfAxes=h.r.clone(o.e(t,h.r.ZERO)),this.areaDirty=1}w[0]={num:0,des:"inside"},w[1]={num:4,data:[0,4,7,3],des:"left"},w[2]={num:4,data:[1,2,6,5],des:"right"},w[3]={num:0},w[4]={num:4,data:[0,1,5,4],des:"bottom"},w[5]={num:6,data:[0,1,5,4,7,3],des:"bottom, left"},w[6]={num:6,data:[0,1,2,6,5,4],des:"bottom, right"},w[7]={num:0},w[8]={num:4,data:[2,3,7,6],des:"top"},w[9]={num:6,data:[4,7,6,2,3,0],des:"top, left"},w[10]={num:6,data:[2,3,7,6,5,1],des:"top, right"},w[11]={num:0},w[12]={num:0},w[13]={num:0},w[14]={num:0},w[15]={num:0},w[16]={num:4,data:[0,3,2,1],des:"front"},w[17]={num:6,data:[0,4,7,3,2,1],des:"front, left"},w[18]={num:6,data:[0,3,2,6,5,1],des:"front, right"},w[19]={num:0},w[20]={num:6,data:[0,3,2,1,5,4],des:"front, bottom"},w[21]={num:6,data:[2,1,5,4,7,3],des:"front, bottom, left"},w[22]={num:6,data:[0,3,2,6,5,4],des:"front, bottom, right"},w[23]={num:0},w[24]={num:6,data:[0,3,7,6,2,1],des:"front, top"},w[25]={num:6,data:[0,4,7,6,2,1],des:"front, top, left"},w[26]={num:6,data:[0,3,7,6,5,1],des:"front, top, right"},w[27]={num:0},w[28]={num:0},w[29]={num:0},w[30]={num:0},w[31]={num:0},w[32]={num:4,data:[4,5,6,7],des:"back"},w[33]={num:6,data:[4,5,6,7,3,0],des:"back, left"},w[34]={num:6,data:[1,2,6,7,4,5],des:"back, right"},w[35]={num:0},w[36]={num:6,data:[0,1,5,6,7,4],des:"back, bottom"},w[37]={num:6,data:[0,1,5,6,7,3],des:"back, bottom, left"},w[38]={num:6,data:[0,1,2,6,7,4],des:"back, bottom, right"},w[39]={num:0},w[40]={num:6,data:[2,3,7,4,5,6],des:"back, top"},w[41]={num:6,data:[0,4,5,6,2,3],des:"back, top, left"},w[42]={num:6,data:[1,2,3,7,4,5],des:"back, top, right"},f.packedLength=n.a.packedLength+h.r.packedLength,f.pack=function(a,t,e){return i.n.typeOf.object("value",a),i.n.defined("array",t),e=o.e(e,0),n.a.pack(a.center,t,e),h.r.pack(a.halfAxes,t,e+n.a.packedLength),t},f.unpack=function(a,t,e){return i.n.defined("array",a),t=o.e(t,0),o.t(e)||(e=new f),n.a.unpack(a,t,e.center),h.r.unpack(a,t+n.a.packedLength,e.halfAxes),e};var p=new n.a,b=new n.a,x=new n.a,y=new n.a,g=new n.a,M=new n.a,v=new h.r,O={unitary:new h.r,diagonal:new h.r},z=new n.a,P=new n.a,C=new n.a;f.fromPoints=function(a,t){if(o.t(t)||(t=new f),!o.t(a)||0===a.length)return t.halfAxes=h.r.ZERO,t.center=n.a.ZERO,t;var e,r=a.length,i=n.a.clone(a[0],p);for(e=1;e<r;e++)n.a.add(i,a[e],i);var s=1/r;n.a.multiplyByScalar(i,s,i);var d,u=0,c=0,m=0,l=0,w=0,z=0;for(e=0;e<r;e++)u+=(d=n.a.subtract(a[e],i,b)).x*d.x,c+=d.x*d.y,m+=d.x*d.z,l+=d.y*d.y,w+=d.y*d.z,z+=d.z*d.z;u*=s,c*=s,m*=s,l*=s,w*=s,z*=s;var P=v;P[0]=u,P[1]=c,P[2]=m,P[3]=c,P[4]=l,P[5]=w,P[6]=m,P[7]=w,P[8]=z;var C=h.r.computeEigenDecomposition(P,O),A=h.r.clone(C.unitary,t.halfAxes),N=h.r.getColumn(A,0,y),T=h.r.getColumn(A,1,g),R=h.r.getColumn(A,2,M),E=-Number.MAX_VALUE,I=-Number.MAX_VALUE,L=-Number.MAX_VALUE,S=Number.MAX_VALUE,k=Number.MAX_VALUE,U=Number.MAX_VALUE;for(e=0;e<r;e++)d=a[e],E=Math.max(n.a.dot(N,d),E),I=Math.max(n.a.dot(T,d),I),L=Math.max(n.a.dot(R,d),L),S=Math.min(n.a.dot(N,d),S),k=Math.min(n.a.dot(T,d),k),U=Math.min(n.a.dot(R,d),U);N=n.a.multiplyByScalar(N,.5*(S+E),N),T=n.a.multiplyByScalar(T,.5*(k+I),T),R=n.a.multiplyByScalar(R,.5*(U+L),R);var q=n.a.add(N,T,t.center);n.a.add(q,R,q);var B=x;return B.x=E-S,B.y=I-k,B.z=L-U,n.a.multiplyByScalar(B,.5,B),h.r.multiplyByScale(t.halfAxes,B,t.halfAxes),t};var A=new n.a,N=new n.a;function T(a,t,e,r,s,d,u,c,m,l,w){if(!(o.t(s)&&o.t(d)&&o.t(u)&&o.t(c)&&o.t(m)&&o.t(l)))throw new i.t("all extents (minimum/maximum X/Y/Z) are required.");o.t(w)||(w=new f);var p=w.halfAxes;h.r.setColumn(p,0,t,p),h.r.setColumn(p,1,e,p),h.r.setColumn(p,2,r,p);var b=A;b.x=(s+d)/2,b.y=(u+c)/2,b.z=(m+l)/2;var x=N;x.x=(d-s)/2,x.y=(c-u)/2,x.z=(l-m)/2;var y=w.center;return b=h.r.multiplyByVector(p,b,b),n.a.add(a,b,y),h.r.multiplyByScale(p,x,p),w}var R=new n.i,E=new n.a,I=new n.i,L=new n.i,S=new n.i,k=new n.i,U=new n.i,q=new n.a,B=new n.a,_=new n.a,D=new n.a,X=new n.a,V=new e.r,W=new e.r,j=new e.r,Z=new e.r,F=new e.r,G=new n.a,Y=new n.a,H=new n.a,J=new n.a,K=new e.r,Q=new n.a,$=new n.a,aa=new n.a,ta=new m.n(n.a.UNIT_X,0);f.fromRectangle=function(a,t,e,r,u){if(!o.t(a))throw new i.t("rectangle is required");if(a.width<0||a.width>c.n.TWO_PI)throw new i.t("Rectangle width must be between 0 and 2*pi");if(a.height<0||a.height>c.n.PI)throw new i.t("Rectangle height must be between 0 and pi");if(o.t(r)&&!c.n.equalsEpsilon(r.radii.x,r.radii.y,c.n.EPSILON15))throw new i.t("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");var h,l,w,f,p,b,x;if(t=o.e(t,0),e=o.e(e,0),r=o.e(r,s.n.WGS84),a.width<=c.n.PI){var y=s.s.center(a,R),g=r.cartographicToCartesian(y,E),M=new d.s(g,r);x=M.plane;var v=y.longitude,O=a.south<0&&a.north>0?0:y.latitude,z=n.i.fromRadians(v,a.north,e,I),P=n.i.fromRadians(a.west,a.north,e,L),C=n.i.fromRadians(a.west,O,e,S),A=n.i.fromRadians(a.west,a.south,e,k),N=n.i.fromRadians(v,a.south,e,U),ea=r.cartographicToCartesian(z,q),na=r.cartographicToCartesian(P,B),ra=r.cartographicToCartesian(C,_),ia=r.cartographicToCartesian(A,D),oa=r.cartographicToCartesian(N,X),sa=M.projectPointToNearestOnPlane(ea,V),da=M.projectPointToNearestOnPlane(na,W),ua=M.projectPointToNearestOnPlane(ra,j),ca=M.projectPointToNearestOnPlane(ia,Z),ha=M.projectPointToNearestOnPlane(oa,F);return l=-(h=Math.min(da.x,ua.x,ca.x)),f=Math.max(da.y,sa.y),w=Math.min(ca.y,ha.y),P.height=A.height=t,na=r.cartographicToCartesian(P,B),ia=r.cartographicToCartesian(A,D),p=Math.min(m.n.getPointDistance(x,na),m.n.getPointDistance(x,ia)),b=e,T(M.origin,M.xAxis,M.yAxis,M.zAxis,h,l,w,f,p,b,u)}var ma=a.south>0,la=a.north<0,wa=ma?a.south:la?a.north:0,fa=s.s.center(a,R).longitude,pa=n.a.fromRadians(fa,wa,e,r,G);pa.z=0;var ba=Math.abs(pa.x)<c.n.EPSILON10&&Math.abs(pa.y)<c.n.EPSILON10?n.a.UNIT_X:n.a.normalize(pa,Y),xa=n.a.UNIT_Z,ya=n.a.cross(ba,xa,H);x=m.n.fromPointNormal(pa,ba,ta);var ga=n.a.fromRadians(fa+c.n.PI_OVER_TWO,wa,e,r,J);h=-(l=n.a.dot(m.n.projectPointOntoPlane(x,ga,K),ya)),f=n.a.fromRadians(0,a.north,la?t:e,r,Q).z,w=n.a.fromRadians(0,a.south,ma?t:e,r,$).z;var Ma=n.a.fromRadians(a.east,wa,e,r,aa);return T(pa,ya,xa,ba,h,l,w,f,p=m.n.getPointDistance(x,Ma),b=0,u)},f.clone=function(a,t){if(o.t(a))return o.t(t)?(n.a.clone(a.center,t.center),h.r.clone(a.halfAxes,t.halfAxes),t.areaDirty=1,t):new f(a.center,a.halfAxes)},f.intersectPlane=function(a,t){if(!o.t(a))throw new i.t("box is required.");if(!o.t(t))throw new i.t("plane is required.");var e=a.center,r=t.normal,s=a.halfAxes,d=r.x,c=r.y,m=r.z,l=Math.abs(d*s[h.r.COLUMN0ROW0]+c*s[h.r.COLUMN0ROW1]+m*s[h.r.COLUMN0ROW2])+Math.abs(d*s[h.r.COLUMN1ROW0]+c*s[h.r.COLUMN1ROW1]+m*s[h.r.COLUMN1ROW2])+Math.abs(d*s[h.r.COLUMN2ROW0]+c*s[h.r.COLUMN2ROW1]+m*s[h.r.COLUMN2ROW2]),w=n.a.dot(r,e)+t.distance;return w<=-l?u.S.OUTSIDE:w>=l?u.S.INSIDE:u.S.INTERSECTING};var ea=new n.a,na=new n.a,ra=new n.a,ia=new n.a,oa=new n.a;f.distanceSquaredTo=function(a,t){if(!o.t(a))throw new i.t("box is required.");if(!o.t(t))throw new i.t("cartesian is required.");var e=n.a.subtract(t,a.center,A),r=a.halfAxes,s=h.r.getColumn(r,0,ea),d=h.r.getColumn(r,1,na),u=h.r.getColumn(r,2,ra),c=n.a.magnitude(s),m=n.a.magnitude(d),l=n.a.magnitude(u);n.a.normalize(s,s),n.a.normalize(d,d),n.a.normalize(u,u);var w=ia;w.x=n.a.dot(e,s),w.y=n.a.dot(e,d),w.z=n.a.dot(e,u);var f,p=0;return w.x<-c?p+=(f=w.x+c)*f:w.x>c&&(p+=(f=w.x-c)*f),w.y<-m?p+=(f=w.y+m)*f:w.y>m&&(p+=(f=w.y-m)*f),w.z<-l?p+=(f=w.z+l)*f:w.z>l&&(p+=(f=w.z-l)*f),p};var sa=new n.a,da=new n.a;f.computePlaneDistances=function(a,e,r,s){if(!o.t(a))throw new i.t("box is required.");if(!o.t(e))throw new i.t("position is required.");if(!o.t(r))throw new i.t("direction is required.");o.t(s)||(s=new t.i);var d=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,c=a.center,m=a.halfAxes,l=h.r.getColumn(m,0,ea),w=h.r.getColumn(m,1,na),f=h.r.getColumn(m,2,ra),p=n.a.add(l,w,sa);n.a.add(p,f,p),n.a.add(p,c,p);var b=n.a.subtract(p,e,da),x=n.a.dot(r,b);return d=Math.min(x,d),u=Math.max(x,u),n.a.add(c,l,p),n.a.add(p,w,p),n.a.subtract(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.add(c,l,p),n.a.subtract(p,w,p),n.a.add(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.add(c,l,p),n.a.subtract(p,w,p),n.a.subtract(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.subtract(c,l,p),n.a.add(p,w,p),n.a.add(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.subtract(c,l,p),n.a.add(p,w,p),n.a.subtract(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.subtract(c,l,p),n.a.subtract(p,w,p),n.a.add(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),n.a.subtract(c,l,p),n.a.subtract(p,w,p),n.a.subtract(p,f,p),n.a.subtract(p,e,b),x=n.a.dot(r,b),d=Math.min(x,d),u=Math.max(x,u),s.start=d,s.stop=u,s};var ua=new t.c;f.isOccluded=function(a,e){if(!o.t(a))throw new i.t("box is required.");if(!o.t(e))throw new i.t("occluder is required.");var n=t.c.fromOrientedBoundingBox(a,ua);return!e.isBoundingSphereVisible(n)},f.prototype.intersectPlane=function(a){return f.intersectPlane(this,a)},f.prototype.distanceSquaredTo=function(a){return f.distanceSquaredTo(this,a)},f.prototype.computePlaneDistances=function(a,t,e){return f.computePlaneDistances(this,a,t,e)},f.prototype.isOccluded=function(a){return f.isOccluded(this,a)},f.equals=function(a,t){return a===t||o.t(a)&&o.t(t)&&n.a.equals(a.center,t.center)&&h.r.equals(a.halfAxes,t.halfAxes)},f.prototype.clone=function(a){return f.clone(this,a)},f.prototype.equals=function(a){return f.equals(this,a)};var ca=new r.a;f.prototype._updateBBox=function(){if(1==this.areaDirty){var a=h.r.getColumn(this.halfAxes,0,ea),t=n.a.clone(n.a.negate(a,z)),e=h.r.getColumn(this.halfAxes,1,na),r=n.a.clone(n.a.negate(e,z)),i=h.r.getColumn(this.halfAxes,2,ra),o=n.a.clone(n.a.negate(i,z));this.bbox=[],n.a.add(this.center,e,z),n.a.add(z,o,P),n.a.add(P,t,C),this.bbox[0]=new n.a(C.x,C.y,C.z),n.a.add(P,a,C),this.bbox[1]=new n.a(C.x,C.y,C.z),n.a.add(z,i,P),n.a.add(P,a,C),this.bbox[2]=new n.a(C.x,C.y,C.z),n.a.add(P,t,C),this.bbox[3]=new n.a(C.x,C.y,C.z),n.a.add(this.center,r,z),n.a.add(z,o,P),n.a.add(P,t,C),this.bbox[4]=new n.a(C.x,C.y,C.z),n.a.add(P,a,C),this.bbox[5]=new n.a(C.x,C.y,C.z),n.a.add(z,i,P),n.a.add(P,a,C),this.bbox[6]=new n.a(C.x,C.y,C.z),n.a.add(P,t,C),this.bbox[7]=new n.a(C.x,C.y,C.z);var s=n.a.magnitude(a),d=n.a.magnitude(e),u=n.a.magnitude(i),c=new n.a(-s,-d,-u),m=new n.a(s,d,u);if(s*d*u==0)return void(this.areaDirty=-1);n.a.normalize(a,a),n.a.normalize(e,e),n.a.normalize(i,i),this.u=n.a.clone(a),this.v=n.a.clone(e),this.w=n.a.clone(i),this.posMin=c,this.posMaX=m,this.areaDirty=0}};var ha=[];ha.push(new e.r),ha.push(new e.r),ha.push(new e.r),ha.push(new e.r),ha.push(new e.r),ha.push(new e.r);var ma=new n.i,la=new n.a;f.prototype.calculateBoxArea=function(a,t,e,i,s,d,u,m){this._updateBBox();var f=a,p=n.a.subtract(f,this.center,oa);if(-1==this.areaDirty){var b=s/i*(N=-1!=d?d:.5*n.a.distance(this.posMaX,this.posMin))/e;return c.n.PI*b*b}var x=n.a.fromElements(n.a.dot(p,this.u),n.a.dot(p,this.v),n.a.dot(p,this.w),sa),y=(x.x<this.posMin.x?1:0)+((x.x>this.posMaX.x?1:0)<<1)+((x.z<this.posMin.z?1:0)<<2)+((x.z>this.posMaX.z?1:0)<<3)+((x.y>this.posMaX.y?1:0)<<4)+((x.y<this.posMin.y?1:0)<<5);if(y>42)return console.log("area calculation is wrong"),-100;var g=w[y];if(0==g.num){b=s/i*(N=-1!=d?d:.5*n.a.distance(this.posMaX,this.posMin))/e;return c.n.PI*b*b}if(0==g.num)return console.log("area calculation is wrong"),-100;for(var M,v=[],O=u,z=0;z<g.num;z++){var P=ha[z],C=this.bbox[g.data[z]];M=!1;var A,N,T=c.n.PI;if(3===t)(A=h.c.multiplyByVector(O,r.a.fromElements(C.x,C.y,C.z,1),ca)).z<0&&(M=!0,-1==N&&(T=c.n.PI_OVER_FOUR,e=n.a.magnitude(p)));else{var R=m,E=R.ellipsoid.cartesianToCartographic(C,ma);o.t(E)?(R.project(E,la),(A=h.c.multiplyByVector(O,r.a.fromElements(la.z,la.x,la.y,1),ca)).z<0&&(M=!0)):M=!0}if(1==M)return T*(b=s/i*(N=-1!=d?d:.5*n.a.distance(this.posMaX,this.posMin))/e)*b;P.x=A.x/A.w,P.y=s-A.y/A.w,v.push(P)}return Math.abs(l.T.computeArea2D(v))},a.b=f}));
