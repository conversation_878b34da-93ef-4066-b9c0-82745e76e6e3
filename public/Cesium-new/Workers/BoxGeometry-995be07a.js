define(["exports","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./when-515d5295","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./PrimitiveType-b38a4004","./VertexFormat-e844760b"],(function(t,e,n,a,r,o,i,m,u,y,p,s){"use strict";var f=new a.a;function c(t){var e=(t=i.e(t,i.e.EMPTY_OBJECT)).minimum,n=t.maximum;if(r.n.typeOf.object("min",e),r.n.typeOf.object("max",n),i.t(t.offsetAttribute)&&t.offsetAttribute===y.I.TOP)throw new r.t("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");var o=i.e(t.vertexFormat,s.n.DEFAULT);this._minimum=a.a.clone(e),this._maximum=a.a.clone(n),this._vertexFormat=o,this._offsetAttribute=t.offsetAttribute,this._workerName="createBoxGeometry"}c.fromDimensions=function(t){var e=(t=i.e(t,i.e.EMPTY_OBJECT)).dimensions;r.n.typeOf.object("dimensions",e),r.n.typeOf.number.greaterThanOrEquals("dimensions.x",e.x,0),r.n.typeOf.number.greaterThanOrEquals("dimensions.y",e.y,0),r.n.typeOf.number.greaterThanOrEquals("dimensions.z",e.z,0);var n=a.a.multiplyByScalar(e,.5,new a.a);return new c({minimum:a.a.negate(n,new a.a),maximum:n,vertexFormat:t.vertexFormat,offsetAttribute:t.offsetAttribute})},c.fromAxisAlignedBoundingBox=function(t){return r.n.typeOf.object("boundingBox",t),new c({minimum:t.minimum,maximum:t.maximum})},c.packedLength=2*a.a.packedLength+s.n.packedLength+1,c.pack=function(t,e,n){return r.n.typeOf.object("value",t),r.n.defined("array",e),n=i.e(n,0),a.a.pack(t._minimum,e,n),a.a.pack(t._maximum,e,n+a.a.packedLength),s.n.pack(t._vertexFormat,e,n+2*a.a.packedLength),e[n+2*a.a.packedLength+s.n.packedLength]=i.e(t._offsetAttribute,-1),e};var x,b=new a.a,A=new a.a,d=new s.n,l={minimum:b,maximum:A,vertexFormat:d,offsetAttribute:void 0};c.unpack=function(t,e,n){r.n.defined("array",t),e=i.e(e,0);var o=a.a.unpack(t,e,b),m=a.a.unpack(t,e+a.a.packedLength,A),u=s.n.unpack(t,e+2*a.a.packedLength,d),y=t[e+2*a.a.packedLength+s.n.packedLength];return i.t(n)?(n._minimum=a.a.clone(o,n._minimum),n._maximum=a.a.clone(m,n._maximum),n._vertexFormat=s.n.clone(u,n._vertexFormat),n._offsetAttribute=-1===y?void 0:y,n):(l.offsetAttribute=-1===y?void 0:y,new c(l))},c.createGeometry=function(t){var r=t._minimum,s=t._maximum,c=t._vertexFormat;if(!a.a.equals(r,s)){var x,b,A=new u.t;if(c.position&&(c.st||c.normal||c.tangent||c.bitangent)){if(c.position&&((b=new Float64Array(72))[0]=r.x,b[1]=r.y,b[2]=s.z,b[3]=s.x,b[4]=r.y,b[5]=s.z,b[6]=s.x,b[7]=s.y,b[8]=s.z,b[9]=r.x,b[10]=s.y,b[11]=s.z,b[12]=r.x,b[13]=r.y,b[14]=r.z,b[15]=s.x,b[16]=r.y,b[17]=r.z,b[18]=s.x,b[19]=s.y,b[20]=r.z,b[21]=r.x,b[22]=s.y,b[23]=r.z,b[24]=s.x,b[25]=r.y,b[26]=r.z,b[27]=s.x,b[28]=s.y,b[29]=r.z,b[30]=s.x,b[31]=s.y,b[32]=s.z,b[33]=s.x,b[34]=r.y,b[35]=s.z,b[36]=r.x,b[37]=r.y,b[38]=r.z,b[39]=r.x,b[40]=s.y,b[41]=r.z,b[42]=r.x,b[43]=s.y,b[44]=s.z,b[45]=r.x,b[46]=r.y,b[47]=s.z,b[48]=r.x,b[49]=s.y,b[50]=r.z,b[51]=s.x,b[52]=s.y,b[53]=r.z,b[54]=s.x,b[55]=s.y,b[56]=s.z,b[57]=r.x,b[58]=s.y,b[59]=s.z,b[60]=r.x,b[61]=r.y,b[62]=r.z,b[63]=s.x,b[64]=r.y,b[65]=r.z,b[66]=s.x,b[67]=r.y,b[68]=s.z,b[69]=r.x,b[70]=r.y,b[71]=s.z,A.position=new m.r({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:b})),c.normal){var d=new Float32Array(72);d[0]=0,d[1]=0,d[2]=1,d[3]=0,d[4]=0,d[5]=1,d[6]=0,d[7]=0,d[8]=1,d[9]=0,d[10]=0,d[11]=1,d[12]=0,d[13]=0,d[14]=-1,d[15]=0,d[16]=0,d[17]=-1,d[18]=0,d[19]=0,d[20]=-1,d[21]=0,d[22]=0,d[23]=-1,d[24]=1,d[25]=0,d[26]=0,d[27]=1,d[28]=0,d[29]=0,d[30]=1,d[31]=0,d[32]=0,d[33]=1,d[34]=0,d[35]=0,d[36]=-1,d[37]=0,d[38]=0,d[39]=-1,d[40]=0,d[41]=0,d[42]=-1,d[43]=0,d[44]=0,d[45]=-1,d[46]=0,d[47]=0,d[48]=0,d[49]=1,d[50]=0,d[51]=0,d[52]=1,d[53]=0,d[54]=0,d[55]=1,d[56]=0,d[57]=0,d[58]=1,d[59]=0,d[60]=0,d[61]=-1,d[62]=0,d[63]=0,d[64]=-1,d[65]=0,d[66]=0,d[67]=-1,d[68]=0,d[69]=0,d[70]=-1,d[71]=0,A.normal=new m.r({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:d})}if(c.st){var l=new Float32Array(72),v=0;l[v++]=0,l[v++]=0,l[v++]=-1,l[v++]=1,l[v++]=0,l[v++]=-1,l[v++]=1,l[v++]=1,l[v++]=-1,l[v++]=0,l[v++]=1,l[v++]=-1,l[v++]=1,l[v++]=0,l[v++]=-1,l[v++]=0,l[v++]=0,l[v++]=-1,l[v++]=0,l[v++]=1,l[v++]=-1,l[v++]=1,l[v++]=1,l[v++]=-1,l[v++]=0,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=1,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=0,l[v++]=0,l[v++]=0,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=1,l[v++]=1,l[v++]=1,l[v++]=1,l[v++]=0,l[v++]=0,l[v++]=1,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=1,l[v++]=1,l[v++]=1,l[v++]=0,l[v++]=1,l[v++]=1,A.st=new m.r({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:l})}if(c.tangent){var z=new Float32Array(72);z[0]=1,z[1]=0,z[2]=0,z[3]=1,z[4]=0,z[5]=0,z[6]=1,z[7]=0,z[8]=0,z[9]=1,z[10]=0,z[11]=0,z[12]=-1,z[13]=0,z[14]=0,z[15]=-1,z[16]=0,z[17]=0,z[18]=-1,z[19]=0,z[20]=0,z[21]=-1,z[22]=0,z[23]=0,z[24]=0,z[25]=1,z[26]=0,z[27]=0,z[28]=1,z[29]=0,z[30]=0,z[31]=1,z[32]=0,z[33]=0,z[34]=1,z[35]=0,z[36]=0,z[37]=-1,z[38]=0,z[39]=0,z[40]=-1,z[41]=0,z[42]=0,z[43]=-1,z[44]=0,z[45]=0,z[46]=-1,z[47]=0,z[48]=-1,z[49]=0,z[50]=0,z[51]=-1,z[52]=0,z[53]=0,z[54]=-1,z[55]=0,z[56]=0,z[57]=-1,z[58]=0,z[59]=0,z[60]=1,z[61]=0,z[62]=0,z[63]=1,z[64]=0,z[65]=0,z[66]=1,z[67]=0,z[68]=0,z[69]=1,z[70]=0,z[71]=0,A.tangent=new m.r({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:z})}if(c.bitangent){var w=new Float32Array(72);w[0]=0,w[1]=1,w[2]=0,w[3]=0,w[4]=1,w[5]=0,w[6]=0,w[7]=1,w[8]=0,w[9]=0,w[10]=1,w[11]=0,w[12]=0,w[13]=1,w[14]=0,w[15]=0,w[16]=1,w[17]=0,w[18]=0,w[19]=1,w[20]=0,w[21]=0,w[22]=1,w[23]=0,w[24]=0,w[25]=0,w[26]=1,w[27]=0,w[28]=0,w[29]=1,w[30]=0,w[31]=0,w[32]=1,w[33]=0,w[34]=0,w[35]=1,w[36]=0,w[37]=0,w[38]=1,w[39]=0,w[40]=0,w[41]=1,w[42]=0,w[43]=0,w[44]=1,w[45]=0,w[46]=0,w[47]=1,w[48]=0,w[49]=0,w[50]=1,w[51]=0,w[52]=0,w[53]=1,w[54]=0,w[55]=0,w[56]=1,w[57]=0,w[58]=0,w[59]=1,w[60]=0,w[61]=0,w[62]=1,w[63]=0,w[64]=0,w[65]=1,w[66]=0,w[67]=0,w[68]=1,w[69]=0,w[70]=0,w[71]=1,A.bitangent=new m.r({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})}(x=new Uint16Array(36))[0]=0,x[1]=1,x[2]=2,x[3]=0,x[4]=2,x[5]=3,x[6]=6,x[7]=5,x[8]=4,x[9]=7,x[10]=6,x[11]=4,x[12]=8,x[13]=9,x[14]=10,x[15]=8,x[16]=10,x[17]=11,x[18]=14,x[19]=13,x[20]=12,x[21]=15,x[22]=14,x[23]=12,x[24]=18,x[25]=17,x[26]=16,x[27]=19,x[28]=18,x[29]=16,x[30]=20,x[31]=21,x[32]=22,x[33]=20,x[34]=22,x[35]=23}else(b=new Float64Array(24))[0]=r.x,b[1]=r.y,b[2]=r.z,b[3]=s.x,b[4]=r.y,b[5]=r.z,b[6]=s.x,b[7]=s.y,b[8]=r.z,b[9]=r.x,b[10]=s.y,b[11]=r.z,b[12]=r.x,b[13]=r.y,b[14]=s.z,b[15]=s.x,b[16]=r.y,b[17]=s.z,b[18]=s.x,b[19]=s.y,b[20]=s.z,b[21]=r.x,b[22]=s.y,b[23]=s.z,A.position=new m.r({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:b}),(x=new Uint16Array(36))[0]=4,x[1]=5,x[2]=6,x[3]=4,x[4]=6,x[5]=7,x[6]=1,x[7]=0,x[8]=3,x[9]=1,x[10]=3,x[11]=2,x[12]=1,x[13]=6,x[14]=5,x[15]=1,x[16]=2,x[17]=6,x[18]=2,x[19]=3,x[20]=7,x[21]=2,x[22]=7,x[23]=6,x[24]=3,x[25]=0,x[26]=4,x[27]=3,x[28]=4,x[29]=7,x[30]=0,x[31]=1,x[32]=5,x[33]=0,x[34]=5,x[35]=4;var g=a.a.subtract(s,r,f),O=.5*a.a.magnitude(g);if(i.t(t._offsetAttribute)){var _=b.length,h=new Uint8Array(_/3),F=t._offsetAttribute===y.I.NONE?0:1;e.d(h,F),A.applyOffset=new m.r({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:h})}return new m.T({attributes:A,indices:x,primitiveType:p._0x38df4a.TRIANGLES,boundingSphere:new n.c(a.a.ZERO,O),offsetAttribute:t._offsetAttribute})}},c.getUnitBox=function(){return i.t(x)||(x=c.createGeometry(c.fromDimensions({dimensions:new a.a(1,1,1),vertexFormat:s.n.POSITION_ONLY}))),x},t.c=c}));
