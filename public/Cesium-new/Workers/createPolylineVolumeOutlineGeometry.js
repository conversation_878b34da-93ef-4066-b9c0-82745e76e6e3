define(["./when-515d5295","./Rectangle-e170be8b","./arrayRemoveDuplicates-a4c6347e","./BoundingRectangle-409afd17","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./PolylineVolumeGeometryLibrary-ae5f3405","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./IndexDatatype-eefd5922","./Math-5e38123d","./PolygonPipeline-b8b35011","./PrimitiveType-b38a4004","./WindingOrder-8479ef05","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian4-034d54d5","./EllipsoidTangentPlane-fd839d7b","./IntersectionTests-5fa33dbd","./Plane-92c15089","./PolylinePipeline-bf1462fc","./EllipsoidGeodesic-e5406761","./EllipsoidRhumbLine-f50fdea6","./FeatureDetection-7fae0d5a"],(function(e,n,t,i,r,a,o,s,p,l,c,d,u,h,y,f,g,v,_,b,m,P,k,w,E,T,L,D,C){"use strict";function G(t){var i=(t=e.e(t,e.e.EMPTY_OBJECT)).polylinePositions,r=t.shapePositions;if(!e.t(i))throw new l.t("options.polylinePositions is required.");if(!e.t(r))throw new l.t("options.shapePositions is required.");this._positions=i,this._shape=r,this._ellipsoid=n.n.clone(e.e(t.ellipsoid,n.n.WGS84)),this._cornerType=e.e(t.cornerType,p.O.ROUNDED),this._granularity=e.e(t.granularity,h.n.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";var s=1+i.length*o.a.packedLength;s+=1+r.length*a.r.packedLength,this.packedLength=s+n.n.packedLength+2}G.pack=function(t,i,r){if(!e.t(t))throw new l.t("value is required");if(!e.t(i))throw new l.t("array is required");r=e.e(r,0);var s,p=t._positions,c=p.length;for(i[r++]=c,s=0;s<c;++s,r+=o.a.packedLength)o.a.pack(p[s],i,r);var d=t._shape;for(c=d.length,i[r++]=c,s=0;s<c;++s,r+=a.r.packedLength)a.r.pack(d[s],i,r);return n.n.pack(t._ellipsoid,i,r),r+=n.n.packedLength,i[r++]=t._cornerType,i[r]=t._granularity,i};var R=n.n.clone(n.n.UNIT_SPHERE),A={polylinePositions:void 0,shapePositions:void 0,ellipsoid:R,height:void 0,cornerType:void 0,granularity:void 0};G.unpack=function(t,i,r){if(!e.t(t))throw new l.t("array is required");i=e.e(i,0);var s,p=t[i++],c=new Array(p);for(s=0;s<p;++s,i+=o.a.packedLength)c[s]=o.a.unpack(t,i);p=t[i++];var d=new Array(p);for(s=0;s<p;++s,i+=a.r.packedLength)d[s]=a.r.unpack(t,i);var u=n.n.unpack(t,i,R);i+=n.n.packedLength;var h=t[i++],y=t[i];return e.t(r)?(r._positions=c,r._shape=d,r._ellipsoid=n.n.clone(u,r._ellipsoid),r._cornerType=h,r._granularity=y,r):(A.polylinePositions=c,A.shapePositions=d,A.cornerType=h,A.granularity=y,new G(A))};var I=new i.n;return G.createGeometry=function(e){var n=e._positions,a=t.u(n,o.a.equalsEpsilon),l=e._shape;if(l=p.K.removeDuplicatesFromShape(l),!(a.length<2||l.length<3)){y.T.computeWindingOrder2D(l)===g.F.CLOCKWISE&&l.reverse();var h=i.n.fromPoints(l,I);return function(e,n){var t=new d.t;t.position=new c.r({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});var i,a,o=n.length,p=t.position.values.length/3,l=e.length/3/o,h=u.IndexDatatype.createTypedArray(p,2*o*(l+1)),y=0,g=(i=0)*o;for(a=0;a<o-1;a++)h[y++]=a+g,h[y++]=a+g+1;for(h[y++]=o-1+g,h[y++]=g,g=(i=l-1)*o,a=0;a<o-1;a++)h[y++]=a+g,h[y++]=a+g+1;for(h[y++]=o-1+g,h[y++]=g,i=0;i<l-1;i++){var v=o*i,_=v+o;for(a=0;a<o;a++)h[y++]=a+v,h[y++]=a+_}return new c.T({attributes:t,indices:u.IndexDatatype.createTypedArray(p,h),boundingSphere:r.c.fromVertices(e),primitiveType:f._0x38df4a.LINES})}(p.K.computePositions(a,l,h,e,!1),l)}},function(t,i){return e.t(i)&&(t=G.unpack(t,i)),t._ellipsoid=n.n.clone(t._ellipsoid),G.createGeometry(t)}}));
