define(["exports","./ArcType-98a7a011","./arrayRemoveDuplicates-a4c6347e","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./when-515d5295","./Rectangle-e170be8b","./EllipsoidRhumbLine-f50fdea6","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryPipeline-137aa28e","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./PolygonPipeline-b8b35011","./WindingOrder-8479ef05"],(function(e,t,r,a,n,i,o,s,u,h,l,c,f,p,v,g,d){"use strict";function y(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(y.prototype,{length:{get:function(){return this._length}}}),y.prototype.enqueue=function(e){this._array.push(e),this._length++},y.prototype.dequeue=function(){if(0!==this._length){var e=this._array,t=this._offset,r=e[t];return e[t]=void 0,++t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,r}},y.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},y.prototype.contains=function(e){return-1!==this._array.indexOf(e)},y.prototype.clear=function(){this._array.length=this._offset=this._length=0},y.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};var m={computeHierarchyPackedLength:function(e){for(var t=0,r=[e];r.length>0;){var a=r.pop();if(o.t(a)){t+=2;var i=a.positions,s=a.holes;if(o.t(i)&&(t+=i.length*n.a.packedLength),o.t(s))for(var u=s.length,h=0;h<u;++h)r.push(s[h])}}return t},packPolygonHierarchy:function(e,t,r){for(var a=[e];a.length>0;){var i=a.pop();if(o.t(i)){var s=i.positions,u=i.holes;if(t[r++]=o.t(s)?s.length:0,t[r++]=o.t(u)?u.length:0,o.t(s))for(var h=s.length,l=0;l<h;++l,r+=3)n.a.pack(s[l],t,r);if(o.t(u))for(var c=u.length,f=0;f<c;++f)a.push(u[f])}}return r},unpackPolygonHierarchy:function(e,t){for(var r=e[t++],a=e[t++],i=new Array(r),o=a>0?new Array(a):void 0,s=0;s<r;++s,t+=n.a.packedLength)i[s]=n.a.unpack(e,t);for(var u=0;u<a;++u)o[u]=m.unpackPolygonHierarchy(e,t),t=o[u].startingIndex,delete o[u].startingIndex;return{positions:i,holes:o,startingIndex:t}}},b=new n.a;function w(e,t,r,a){return n.a.subtract(t,e,b),n.a.multiplyByScalar(b,r/a,b),n.a.add(e,b,b),[b.x,b.y,b.z]}m.subdivideLineCount=function(e,t,r){var a=n.a.distance(e,t)/r,i=Math.max(0,Math.ceil(p.n.log2(a)));return Math.pow(2,i)};var I=new n.i,T=new n.i,_=new n.i,x=new n.a;m.subdivideRhumbLineCount=function(e,t,r,a){var n=e.cartesianToCartographic(t,I),i=e.cartesianToCartographic(r,T),o=new u.M(n,i,e).surfaceDistance/a,s=Math.max(0,Math.ceil(p.n.log2(o)));return Math.pow(2,s)},m.subdivideLine=function(e,t,r,a){var i=m.subdivideLineCount(e,t,r),s=n.a.distance(e,t),u=s/i;o.t(a)||(a=[]);var h=a;h.length=3*i;for(var l=0,c=0;c<i;c++){var f=w(e,t,c*u,s);h[l++]=f[0],h[l++]=f[1],h[l++]=f[2]}return h},m.subdivideRhumbLine=function(e,t,r,a,n){var i=e.cartesianToCartographic(t,I),s=e.cartesianToCartographic(r,T),h=new u.M(i,s,e),l=h.surfaceDistance/a,c=Math.max(0,Math.ceil(p.n.log2(l))),f=Math.pow(2,c),v=h.surfaceDistance/f;o.t(n)||(n=[]);var g=n;g.length=3*f;for(var d=0,y=0;y<f;y++){var m=h.interpolateUsingSurfaceDistance(y*v,_),b=e.cartographicToCartesian(m,x);g[d++]=b.x,g[d++]=b.y,g[d++]=b.z}return g};var E=new n.a,D=new n.a,S=new n.a,L=new n.a;m.scaleToGeodeticHeightExtruded=function(e,t,r,a,i){a=o.e(a,s.n.WGS84);var u=E,h=D,l=S,c=L;if(o.t(e)&&o.t(e.attributes)&&o.t(e.attributes.position))for(var f=e.attributes.position.values,p=f.length/2,v=0;v<p;v+=3)n.a.fromArray(f,v,l),a.geodeticSurfaceNormal(l,u),c=a.scaleToGeodeticSurface(l,c),h=n.a.multiplyByScalar(u,r,h),h=n.a.add(c,h,h),f[v+p]=h.x,f[v+1+p]=h.y,f[v+2+p]=h.z,i&&(c=n.a.clone(l,c)),h=n.a.multiplyByScalar(u,t,h),h=n.a.add(c,h,h),f[v]=h.x,f[v+1]=h.y,f[v+2]=h.z;return e},m.polygonOutlinesFromHierarchy=function(e,t,a){var i,s,u,h=[],l=new y;for(l.enqueue(e);0!==l.length;){var c=l.dequeue(),f=c.positions;if(t)for(u=f.length,i=0;i<u;i++)a.scaleToGeodeticSurface(f[i],f[i]);if(!((f=r.u(f,n.a.equalsEpsilon,!0)).length<3)){var p=c.holes?c.holes.length:0;for(i=0;i<p;i++){var v=c.holes[i],g=v.positions;if(t)for(u=g.length,s=0;s<u;++s)a.scaleToGeodeticSurface(g[s],g[s]);if(!((g=r.u(g,n.a.equalsEpsilon,!0)).length<3)){h.push(g);var d=0;for(o.t(v.holes)&&(d=v.holes.length),s=0;s<d;s++)l.enqueue(v.holes[s])}}h.push(f)}}return h};var C=new n.a(6378137,6378137,6378137);m.polygonsFromHierarchy=function(e,t,a,i){var s=[],u=[],h=new y;for(h.enqueue(e);0!==h.length;){var l,c,f,v=h.dequeue(),m=v.positions,b=v.holes,w=m.slice();if(a)for(c=m.length,l=0;l<c;l++)i.scaleToGeodeticSurface(m[l],w[l]);if(o.t(i)&&!n.a.equals(i._radii,C)&&(f=p.n.EPSILON7),!((m=r.u(w,n.a.equalsEpsilon,!0,f)).length<3)){var I=t(m);if(o.t(I)){var T=[],_=g.T.computeWindingOrder2D(I);_===d.F.CLOCKWISE&&(I.reverse(),m=m.slice().reverse());var x,E=m.slice(),D=o.t(b)?b.length:0,S=[];for(l=0;l<D;l++){var L=b[l],N=L.positions;if(a)for(c=N.length,x=0;x<c;++x)i.scaleToGeodeticSurface(N[x],N[x]);if(!((N=r.u(N,n.a.equalsEpsilon,!0,p.n.EPSILON7)).length<3)){var A=t(N);if(o.t(A)){(_=g.T.computeWindingOrder2D(A))===d.F.CLOCKWISE&&(A.reverse(),N=N.slice().reverse()),S.push(N),T.push(E.length),E=E.concat(N),I=I.concat(A);var G=0;for(o.t(L.holes)&&(G=L.holes.length),x=0;x<G;x++)h.enqueue(L.holes[x])}}}s.push({outerRing:m,holes:S}),u.push({positions:E,positions2D:I,holes:T})}}}return{hierarchy:s,polygons:u}};var N=new a.r,A=new n.a,G=new h.a,M=new v.r;m.computeBoundingRectangle=function(e,t,r,a,i){for(var s=h.a.fromAxisAngle(e,a,G),u=v.r.fromQuaternion(s,M),l=Number.POSITIVE_INFINITY,c=Number.NEGATIVE_INFINITY,f=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY,g=r.length,d=0;d<g;++d){var y=n.a.clone(r[d],A);v.r.multiplyByVector(u,y,y);var m=t(y,N);o.t(m)&&(l=Math.min(l,m.x),c=Math.max(c,m.x),f=Math.min(f,m.y),p=Math.max(p,m.y))}return i.x=l,i.y=f,i.width=c-l,i.height=p-f,i},m.createGeometryFromPositions=function(e,r,a,n,o,s){var u=g.T.triangulate(r.positions2D,r.holes);u.length<3&&(u=[0,1,2]);var l=r.positions;if(n){for(var f=l.length,p=new Array(3*f),d=0,y=0;y<f;y++){var m=l[y];p[d++]=m.x,p[d++]=m.y,p[d++]=m.z}var b=new h.T({attributes:{position:new h.r({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p})},indices:u,primitiveType:v._0x38df4a.TRIANGLES});return o.normal?c.F.computeNormal(b):b}return s===t.D.GEODESIC?g.T.computeSubdivision(e,l,u,a):s===t.D.RHUMB?g.T.computeRhumbLineSubdivision(e,l,u,a):void 0};var O=[],P=new n.a,R=new n.a;m.computeWallGeometry=function(e,r,a,o,s,u){var c,g,d,y,b,w=u?1:0,I=e.length,T=0;if(o)for(g=3*(I-w)*2,c=new Array(2*g),d=0;d<I-w;d++)y=e[d],b=e[(d+1)%I],c[T]=c[T+g]=y.x,c[++T]=c[T+g]=y.y,c[++T]=c[T+g]=y.z,c[++T]=c[T+g]=b.x,c[++T]=c[T+g]=b.y,c[++T]=c[T+g]=b.z,++T;else{var _=p.n.chordLength(a,r.maximumRadius),x=0;if(s===t.D.GEODESIC)for(d=0;d<I;d++)x+=m.subdivideLineCount(e[d],e[(d+1)%I],_);else if(s===t.D.RHUMB)for(d=0;d<I;d++)x+=m.subdivideRhumbLineCount(r,e[d],e[(d+1)%I],_);for(g=3*(x+I),c=new Array(2*g),d=0;d<I;d++){var E;y=e[d],b=e[(d+1)%I],s===t.D.GEODESIC?E=m.subdivideLine(y,b,_,O):s===t.D.RHUMB&&(E=m.subdivideRhumbLine(r,y,b,_,O));for(var D=E.length,S=0;S<D;++S,++T)c[T]=E[S],c[T+g]=E[S];c[T]=b.x,c[T+g]=b.x,c[++T]=b.y,c[T+g]=b.y,c[++T]=b.z,c[T+g]=b.z,++T}}I=c.length;var L=f.IndexDatatype.createTypedArray(I/3,I-6*(e.length-w)),C=0;for(I/=6,d=0;d<I;d++){var N=d,A=N+1,G=N+I,M=G+1;y=n.a.fromArray(c,3*N,P),b=n.a.fromArray(c,3*A,R),!n.a.equalsEpsilon(y,b,p.n.EPSILON10,p.n.EPSILON10)&&(L[C++]=N,L[C++]=G,L[C++]=A,L[C++]=A,L[C++]=G,L[C++]=M)}return new h.T({attributes:new l.t({position:new h.r({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:L,primitiveType:v._0x38df4a.TRIANGLES})},e.g=m}));
