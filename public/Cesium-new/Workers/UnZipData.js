define(["./createTaskProcessorWorker","./pako_inflate-f73548c4","./CompressedTextureBuffer-807fafb6","./when-515d5295","./PixelFormat-b3c660aa","./RuntimeError-350acae3","./S3MPixelFormat-f1fedece","./WebGLConstants-77a84876"],(function(e,r,t,n,a,i,f,E){"use strict";var o,u,_=1,s=2,c={};c[0]=a.PixelFormat.RGB_DXT1,c[_]=a.PixelFormat.RGBA_DXT3,c[s]=a.PixelFormat.RGBA_DXT5;var y,T=0;function l(e){var r=e.data,f=r.byteLength,E=new Uint8Array(r,e.offset),_=y._malloc(f);!function(e,r,t,n){var a,i=t/4,f=n%4,E=new Uint32Array(e.buffer,0,(n-f)/4),o=new Uint32Array(r.buffer);for(a=0;a<E.length;a++)o[i+a]=E[a];for(a=n-f;a<n;a++)r[t+a]=e[a]}(E,y.HEAPU8,_,f);var s=y._crn_get_dxt_format(_,f),l=c[s];if(!n.t(l))throw new i.t("Unsupported compressed format.");var U,m=y._crn_get_levels(_,f),A=y._crn_get_width(_,f),p=y._crn_get_height(_,f),w=0;for(U=0;U<m;++U)w+=a.PixelFormat.compressedTextureSizeInBytes(l,A>>U,p>>U);if(T<w&&(n.t(o)&&y._free(o),o=y._malloc(w),u=new Uint8Array(y.HEAPU8.buffer,o,w),T=w),y._crn_decompress(_,f,o,w,0,m),y._free(_),n.e(e.bMipMap,!1)){var B=u.slice(0,w);return new t.e(l,A,p,B)}var P=a.PixelFormat.compressedTextureSizeInBytes(l,A,p),g=u.subarray(0,P),d=new Uint8Array(P);return d.set(g,0),new t.e(l,A,p,d)}function U(e){var r=new DataView(e),t=0,n=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var a=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var i=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var E=r.getUint32(t,!0);t+=Uint32Array.BYTES_PER_ELEMENT;var o=l({data:e.slice(t,t+E)}).bufferView,u=new ArrayBuffer(t+o.byteLength),_=new Uint8Array(u),s=new Uint32Array(u);return t=0,s[0]=n,t+=Uint32Array.BYTES_PER_ELEMENT,s[1]=a,t+=Uint32Array.BYTES_PER_ELEMENT,s[2]=i,t+=Uint32Array.BYTES_PER_ELEMENT,s[3]=f,t+=Uint32Array.BYTES_PER_ELEMENT,s[4]=o.byteLength,t+=Uint32Array.BYTES_PER_ELEMENT,_.set(o,t),u}function m(e,t){for(var n=e.data,a=[],i=0;i<n.length;i++){var E,o=n[i];try{var u=new Uint8Array(o.zipBuffer);E=r.pako.inflate(u).buffer,new DataView(E).getUint32(0,!0)===f.S3MPixelFormat.CRN_DXT5&&(E=U(E)),t.push(E),a.push({unzipBuffer:E,name:o.name})}catch(r){o.unzipLength===o.zippedLength&&(E=o.zipBuffer.buffer,e.isCRN&&(E=U(E)),t.push(E),a.push({unzipBuffer:E,name:o.name}));continue}}return{data:a}}function A(){self.onmessage=e(m),self.postMessage(!0)}return function(e){var r=e.data.webAssemblyConfig;if(n.t(r))return require([r.modulePath],(function(e){n.t(r.wasmBinaryFile)?(n.t(e)||(e=self.Module),y=e,A()):(y=e,A())}))}}));
