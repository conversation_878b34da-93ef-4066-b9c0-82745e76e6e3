define(["exports","./Check-3aa71481","./when-515d5295"],(function(e,t,s){"use strict";function i(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function n(e,t){return t-e}Object.defineProperties(i.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),i.prototype.addEventListener=function(e,s){t.n.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(s);var i=this;return function(){i.removeEventListener(e,s)}},i.prototype.removeEventListener=function(e,s){t.n.typeOf.func("listener",e);for(var i=this._listeners,n=this._scopes,r=-1,o=0;o<i.length;o++)if(i[o]===e&&n[o]===s){r=o;break}return-1!==r&&(this._insideRaiseEvent?(this._toRemove.push(r),i[r]=void 0,n[r]=void 0):(i.splice(r,1),n.splice(r,1)),!0)},i.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;var e,t=this._listeners,i=this._scopes,r=t.length;for(e=0;e<r;e++){var o=t[e];s.t(o)&&t[e].apply(i[e],arguments)}var h=this._toRemove;if((r=h.length)>0){for(h.sort(n),e=0;e<r;e++){var p=h[e];t.splice(p,1),i.splice(p,1)}h.length=0}this._insideRaiseEvent=!1},e.o=i}));
