define(["exports","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./when-515d5295","./Check-3aa71481","./EllipseGeometryLibrary-497ff3d7","./Rectangle-e170be8b","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004"],(function(e,t,i,r,n,a,o,s,u,d,l,c,h,p,f){"use strict";var m=new r.a,_=new r.a;var A=new i.c,b=new i.c;function g(e){var t=(e=a.e(e,a.e.EMPTY_OBJECT)).center,i=a.e(e.ellipsoid,u.n.WGS84),n=e.semiMajorAxis,s=e.semiMinorAxis,d=a.e(e.granularity,p.n.RADIANS_PER_DEGREE);if(!a.t(t))throw new o.t("center is required.");if(!a.t(n))throw new o.t("semiMajorAxis is required.");if(!a.t(s))throw new o.t("semiMinorAxis is required.");if(n<s)throw new o.t("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(d<=0)throw new o.t("granularity must be greater than zero.");var l=a.e(e.height,0),c=a.e(e.extrudedHeight,l);this._center=r.a.clone(t),this._semiMajorAxis=n,this._semiMinorAxis=s,this._ellipsoid=u.n.clone(i),this._rotation=a.e(e.rotation,0),this._height=Math.max(c,l),this._granularity=d,this._extrudedHeight=Math.min(c,l),this._numberOfVerticalLines=Math.max(a.e(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._outlineWidth=a.e(e.outlineWidth,1),this._workerName="createEllipseOutlineGeometry"}g.packedLength=r.a.packedLength+u.n.packedLength+9,g.pack=function(e,t,i){if(!a.t(e))throw new o.t("value is required");if(!a.t(t))throw new o.t("array is required");return i=a.e(i,0),r.a.pack(e._center,t,i),i+=r.a.packedLength,u.n.pack(e._ellipsoid,t,i),i+=u.n.packedLength,t[i++]=e._semiMajorAxis,t[i++]=e._semiMinorAxis,t[i++]=e._rotation,t[i++]=e._height,t[i++]=e._granularity,t[i++]=e._extrudedHeight,t[i++]=e._numberOfVerticalLines,t[i++]=a.e(e._offsetAttribute,-1),t[i]=e._outlineWidth,t};var y=new r.a,v=new u.n,x={center:y,ellipsoid:v,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0,outlineWidth:void 0};g.unpack=function(e,t,i){if(!a.t(e))throw new o.t("array is required");t=a.e(t,0);var n=r.a.unpack(e,t,y);t+=r.a.packedLength;var s=u.n.unpack(e,t,v);t+=u.n.packedLength;var d=e[t++],l=e[t++],c=e[t++],h=e[t++],p=e[t++],f=e[t++],m=e[t++],_=e[t++],A=e[t];return a.t(i)?(i._center=r.a.clone(n,i._center),i._ellipsoid=u.n.clone(s,i._ellipsoid),i._semiMajorAxis=d,i._semiMinorAxis=l,i._rotation=c,i._height=h,i._granularity=p,i._extrudedHeight=f,i._numberOfVerticalLines=m,i._offsetAttribute=-1===_?void 0:_,i._outlineWidth=A,i):(x.height=h,x.extrudedHeight=f,x.granularity=p,x.rotation=c,x.semiMajorAxis=d,x.semiMinorAxis=l,x.numberOfVerticalLines=m,x.offsetAttribute=-1===_?void 0:_,x.outlineWidth=A,new g(x))},g.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var o=e._height,u=e._extrudedHeight,g=!p.n.equalsEpsilon(o,u,0,p.n.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var y,v={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:o,granularity:e._granularity,outlineWidth:e._outlineWidth,numberOfVerticalLines:e._numberOfVerticalLines};if(g)v.extrudedHeight=u,v.offsetAttribute=e._offsetAttribute,y=function(e){var o=e.center,u=e.ellipsoid,f=e.semiMajorAxis,_=r.a.multiplyByScalar(u.geodeticSurfaceNormal(o,m),e.height,m);A.center=r.a.add(o,_,A.center),A.radius=f,_=r.a.multiplyByScalar(u.geodeticSurfaceNormal(o,_),e.extrudedHeight,_),b.center=r.a.add(o,_,b.center),b.radius=f;var g=s.N.computeEllipsePositions(e,!1,!0).outerPositions,y=new l.t({position:new d.r({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:s.N.raisePositionsToHeight(g,e,!0)})});g=y.position.values;var v=i.c.union(A,b),x=g.length/3;if(a.t(e.offsetAttribute)){var w=new Uint8Array(x);if(e.offsetAttribute===c.I.TOP)w=t.d(w,1,0,x/2);else{var M=e.offsetAttribute===c.I.NONE?0:1;w=t.d(w,M)}y.applyOffset=new d.r({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:w})}var L=a.e(e.numberOfVerticalLines,16);L=p.n.clamp(L,0,x/2);var E=h.IndexDatatype.createTypedArray(x,2*x+2*L);x/=2;var O,D,N=0;for(O=0;O<x;++O)E[N++]=O,E[N++]=(O+1)%x,E[N++]=O+x,E[N++]=(O+1)%x+x;if(L>0){var S=Math.min(L,x);D=Math.round(x/S);var P=Math.min(D*L,x);for(O=0;O<P;O+=D)E[N++]=O,E[N++]=O+x}return{boundingSphere:v,attributes:y,indices:E}}(v);else if(y=function(e){var t=e.center;_=r.a.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,_),e.height,_),_=r.a.add(t,_,_);var o=new i.c(_,e.semiMajorAxis),u=s.N.computeEllipsePositions(e,!1,!0).outerPositions,c=a.e(e.outlineWidth,1);c>1&&u.push(u[0],u[1],u[2]);var p=new l.t({position:new d.r({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:s.N.raisePositionsToHeight(u,e,!1)})});c>1&&(p.sideness=new d.r({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array([0,0,0,1,1,1,1,0])}),p.sideness.isInstanceAttribute=!0);for(var f=u.length/3,m=h.IndexDatatype.createTypedArray(f,2*f),A=0,b=0;b<f;++b)m[A++]=b,m[A++]=(b+1)%f;return{boundingSphere:o,attributes:p,indices:m}}(v),a.t(e._offsetAttribute)){var x=y.attributes.position.values.length,w=new Uint8Array(x/3),M=e._offsetAttribute===c.I.NONE?0:1;t.d(w,M),y.attributes.applyOffset=new d.r({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:w})}return new d.T({attributes:y.attributes,indices:y.indices,primitiveType:e._outlineWidth>1?f._0x38df4a.TRIANGLES:f._0x38df4a.LINES,boundingSphere:y.boundingSphere,offsetAttribute:e._offsetAttribute})}},e.w=g}));
