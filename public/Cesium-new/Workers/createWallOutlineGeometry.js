define(["./when-515d5295","./Rectangle-e170be8b","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./WallGeometryLibrary-240d09b5","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5","./arrayRemoveDuplicates-a4c6347e","./PolylinePipeline-bf1462fc","./EllipsoidGeodesic-e5406761","./EllipsoidRhumbLine-f50fdea6","./IntersectionTests-5fa33dbd","./Plane-92c15089"],(function(e,i,t,n,a,r,o,s,m,l,p,u,h,d,c,g,f,y,v,w,_,b,H,E,k){"use strict";var A=new n.a,x=new n.a;function L(t){var a=(t=e.e(t,e.e.EMPTY_OBJECT)).positions,o=t.maximumHeights,s=t.minimumHeights;if(!e.t(a))throw new r.t("options.positions is required.");if(e.t(o)&&o.length!==a.length)throw new r.t("options.positions and options.maximumHeights must have the same length.");if(e.t(s)&&s.length!==a.length)throw new r.t("options.positions and options.minimumHeights must have the same length.");var m=e.e(t.granularity,l.n.RADIANS_PER_DEGREE),p=e.e(t.ellipsoid,i.n.WGS84);this._positions=a,this._minimumHeights=s,this._maximumHeights=o,this._granularity=m,this._ellipsoid=i.n.clone(p),this._workerName="createWallOutlineGeometry";var u=1+a.length*n.a.packedLength+2;e.t(s)&&(u+=s.length),e.t(o)&&(u+=o.length),this.packedLength=u+i.n.packedLength+1}L.pack=function(t,a,o){if(!e.t(t))throw new r.t("value is required");if(!e.t(a))throw new r.t("array is required");o=e.e(o,0);var s,m=t._positions,l=m.length;for(a[o++]=l,s=0;s<l;++s,o+=n.a.packedLength)n.a.pack(m[s],a,o);var p=t._minimumHeights;if(l=e.t(p)?p.length:0,a[o++]=l,e.t(p))for(s=0;s<l;++s)a[o++]=p[s];var u=t._maximumHeights;if(l=e.t(u)?u.length:0,a[o++]=l,e.t(u))for(s=0;s<l;++s)a[o++]=u[s];return i.n.pack(t._ellipsoid,a,o),a[o+=i.n.packedLength]=t._granularity,a};var P=i.n.clone(i.n.UNIT_SPHERE),C={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:P,granularity:void 0};return L.unpack=function(t,a,o){if(!e.t(t))throw new r.t("array is required");a=e.e(a,0);var s,m,l,p=t[a++],u=new Array(p);for(s=0;s<p;++s,a+=n.a.packedLength)u[s]=n.a.unpack(t,a);if((p=t[a++])>0)for(m=new Array(p),s=0;s<p;++s)m[s]=t[a++];if((p=t[a++])>0)for(l=new Array(p),s=0;s<p;++s)l[s]=t[a++];var h=i.n.unpack(t,a,P),d=t[a+=i.n.packedLength];return e.t(o)?(o._positions=u,o._minimumHeights=m,o._maximumHeights=l,o._ellipsoid=i.n.clone(h,o._ellipsoid),o._granularity=d,o):(C.positions=u,C.minimumHeights=m,C.maximumHeights=l,C.granularity=d,new L(C))},L.fromConstantHeights=function(i){var t=(i=e.e(i,e.e.EMPTY_OBJECT)).positions;if(!e.t(t))throw new r.t("options.positions is required.");var n,a,o=i.minimumHeight,s=i.maximumHeight,m=e.t(o),l=e.t(s);if(m||l){var p=t.length;n=m?new Array(p):void 0,a=l?new Array(p):void 0;for(var u=0;u<p;++u)m&&(n[u]=o),l&&(a[u]=s)}return new L({positions:t,maximumHeights:a,minimumHeights:n,ellipsoid:i.ellipsoid})},L.createGeometry=function(i){var r=i._positions,h=i._minimumHeights,d=i._maximumHeights,c=i._granularity,g=i._ellipsoid,f=u.B.computePositions(g,r,d,h,c,!1);if(e.t(f)){var y,v=f.pos.bottomPositions,w=f.pos.topPositions,_=w.length,b=2*_,H=new Float64Array(b),E=0;for(_/=3,y=0;y<_;++y){var k=3*y,L=n.a.fromArray(w,k,A),P=n.a.fromArray(v,k,x);H[E++]=P.x,H[E++]=P.y,H[E++]=P.z,H[E++]=L.x,H[E++]=L.y,H[E++]=L.z}var C=new s.t({position:new o.r({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:H})}),D=b/3;b=2*D-4+D;var G=m.IndexDatatype.createTypedArray(D,b),T=0;for(y=0;y<D-2;y+=2){var I=y,R=y+2,q=n.a.fromArray(H,3*I,A),S=n.a.fromArray(H,3*R,x);if(!n.a.equalsEpsilon(q,S,l.n.EPSILON10)){var N=y+1,O=y+3;G[T++]=N,G[T++]=I,G[T++]=N,G[T++]=O,G[T++]=I,G[T++]=R}}return G[T++]=D-2,G[T++]=D-1,new o.T({attributes:C,indices:G,primitiveType:p._0x38df4a.LINES,boundingSphere:new t.c.fromVertices(H)})}},function(t,n){return e.t(n)&&(t=L.unpack(t,n)),t._ellipsoid=i.n.clone(t._ellipsoid),L.createGeometry(t)}}));
