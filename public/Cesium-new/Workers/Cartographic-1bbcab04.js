define(["exports","./Check-3aa71481","./when-515d5295","./Math-5e38123d"],(function(t,e,n,r){"use strict";function a(t,e,r){this.x=n.e(t,0),this.y=n.e(e,0),this.z=n.e(r,0)}a.fromSpherical=function(t,r){e.n.typeOf.object("spherical",t),n.t(r)||(r=new a);var i=t.clock,o=t.cone,u=n.e(t.magnitude,1),f=u*Math.sin(o);return r.x=f*Math.cos(i),r.y=f*Math.sin(i),r.z=u*Math.cos(o),r},a.fromElements=function(t,e,r,i){return n.t(i)?(i.x=t,i.y=e,i.z=r,i):new a(t,e,r)},a.clone=function(t,e){if(n.t(t))return n.t(e)?(e.x=t.x,e.y=t.y,e.z=t.z,e):new a(t.x,t.y,t.z)},a.fromCartesian4=a.clone,a.packedLength=3,a.pack=function(t,r,a){return e.n.typeOf.object("value",t),e.n.defined("array",r),a=n.e(a,0),r[a++]=t.x,r[a++]=t.y,r[a]=t.z,r},a.unpack=function(t,r,i){return e.n.defined("array",t),r=n.e(r,0),n.t(i)||(i=new a),i.x=t[r++],i.y=t[r++],i.z=t[r],i},a.packArray=function(t,r){e.n.defined("array",t);var i=t.length;n.t(r)?r.length=3*i:r=new Array(3*i);for(var o=0;o<i;++o)a.pack(t[o],r,3*o);return r},a.unpackArray=function(t,r){if(e.n.defined("array",t),e.n.typeOf.number.greaterThanOrEquals("array.length",t.length,3),t.length%3!=0)throw new e.t("array length must be a multiple of 3.");var i=t.length;n.t(r)?r.length=i/3:r=new Array(i/3);for(var o=0;o<i;o+=3){var u=o/3;r[u]=a.unpack(t,o,r[u])}return r},a.fromArray=a.unpack,a.maximumComponent=function(t){return e.n.typeOf.object("cartesian",t),Math.max(t.x,t.y,t.z)},a.minimumComponent=function(t){return e.n.typeOf.object("cartesian",t),Math.min(t.x,t.y,t.z)},a.minimumByComponent=function(t,n,r){return e.n.typeOf.object("first",t),e.n.typeOf.object("second",n),e.n.typeOf.object("result",r),r.x=Math.min(t.x,n.x),r.y=Math.min(t.y,n.y),r.z=Math.min(t.z,n.z),r},a.maximumByComponent=function(t,n,r){return e.n.typeOf.object("first",t),e.n.typeOf.object("second",n),e.n.typeOf.object("result",r),r.x=Math.max(t.x,n.x),r.y=Math.max(t.y,n.y),r.z=Math.max(t.z,n.z),r},a.magnitudeSquared=function(t){return e.n.typeOf.object("cartesian",t),t.x*t.x+t.y*t.y+t.z*t.z},a.magnitude=function(t){return Math.sqrt(a.magnitudeSquared(t))};var i=new a;a.distance=function(t,n){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),a.subtract(t,n,i),a.magnitude(i)},a.distanceSquared=function(t,n){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),a.subtract(t,n,i),a.magnitudeSquared(i)},a.normalize=function(t,n){e.n.typeOf.object("cartesian",t),e.n.typeOf.object("result",n);var r=a.magnitude(t);if(n.x=t.x/r,n.y=t.y/r,n.z=t.z/r,isNaN(n.x)||isNaN(n.y)||isNaN(n.z))throw new e.t("normalized result is not a number");return n},a.dot=function(t,n){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),t.x*n.x+t.y*n.y+t.z*n.z},a.multiplyComponents=function(t,n,r){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r),r.x=t.x*n.x,r.y=t.y*n.y,r.z=t.z*n.z,r},a.divideComponents=function(t,n,r){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r),r.x=t.x/n.x,r.y=t.y/n.y,r.z=t.z/n.z,r},a.add=function(t,n,r){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r),r.x=t.x+n.x,r.y=t.y+n.y,r.z=t.z+n.z,r},a.subtract=function(t,n,r){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r),r.x=t.x-n.x,r.y=t.y-n.y,r.z=t.z-n.z,r},a.multiplyByScalar=function(t,n,r){return e.n.typeOf.object("cartesian",t),e.n.typeOf.number("scalar",n),e.n.typeOf.object("result",r),r.x=t.x*n,r.y=t.y*n,r.z=t.z*n,r},a.divideByScalar=function(t,n,r){return e.n.typeOf.object("cartesian",t),e.n.typeOf.number("scalar",n),e.n.typeOf.object("result",r),r.x=t.x/n,r.y=t.y/n,r.z=t.z/n,r},a.negate=function(t,n){return e.n.typeOf.object("cartesian",t),e.n.typeOf.object("result",n),n.x=-t.x,n.y=-t.y,n.z=-t.z,n},a.abs=function(t,n){return e.n.typeOf.object("cartesian",t),e.n.typeOf.object("result",n),n.x=Math.abs(t.x),n.y=Math.abs(t.y),n.z=Math.abs(t.z),n};var o=new a;a.lerp=function(t,n,r,i){return e.n.typeOf.object("start",t),e.n.typeOf.object("end",n),e.n.typeOf.number("t",r),e.n.typeOf.object("result",i),a.multiplyByScalar(n,r,o),i=a.multiplyByScalar(t,1-r,i),a.add(o,i,i)};var u=new a,f=new a;a.angleBetween=function(t,n){e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),a.normalize(t,u),a.normalize(n,f);var r=a.dot(u,f),i=a.magnitude(a.cross(u,f,u));return Math.atan2(i,r)};var c=new a;a.mostOrthogonalAxis=function(t,n){e.n.typeOf.object("cartesian",t),e.n.typeOf.object("result",n);var r=a.normalize(t,c);return a.abs(r,r),n=r.x<=r.y?r.x<=r.z?a.clone(a.UNIT_X,n):a.clone(a.UNIT_Z,n):r.y<=r.z?a.clone(a.UNIT_Y,n):a.clone(a.UNIT_Z,n)},a.projectVector=function(t,n,r){e.n.defined("a",t),e.n.defined("b",n),e.n.defined("result",r);var i=a.dot(t,n)/a.dot(n,n);return a.multiplyByScalar(n,i,r)},a.equals=function(t,e){return t===e||n.t(t)&&n.t(e)&&t.x===e.x&&t.y===e.y&&t.z===e.z},a.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]&&t.z===e[n+2]},a.equalsEpsilon=function(t,e,a,i){return t===e||n.t(t)&&n.t(e)&&r.n.equalsEpsilon(t.x,e.x,a,i)&&r.n.equalsEpsilon(t.y,e.y,a,i)&&r.n.equalsEpsilon(t.z,e.z,a,i)},a.cross=function(t,n,r){e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r);var a=t.x,i=t.y,o=t.z,u=n.x,f=n.y,c=n.z,l=i*c-o*f,s=o*u-a*c,y=a*f-i*u;return r.x=l,r.y=s,r.z=y,r},a.midpoint=function(t,n,r){return e.n.typeOf.object("left",t),e.n.typeOf.object("right",n),e.n.typeOf.object("result",r),r.x=.5*(t.x+n.x),r.y=.5*(t.y+n.y),r.z=.5*(t.z+n.z),r},a.fromDegrees=function(t,n,i,o,u){return e.n.typeOf.number("longitude",t),e.n.typeOf.number("latitude",n),t=r.n.toRadians(t),n=r.n.toRadians(n),a.fromRadians(t,n,i,o,u)};var l=new a,s=new a,y=new a(40680631590769,40680631590769,40408299984661.445),d=new a(40680631590769,40680631590769,40680631590769);a.fromRadians=function(t,i,o,u,f){e.n.typeOf.number("longitude",t),e.n.typeOf.number("latitude",i),o=n.e(o,0);var c=n.t(u)?u.radiiSquared:d;r.n.equalsEpsilon(r.n.Radius,6356752.314245179,r.n.EPSILON10)&&(c=n.t(u)?u.radiiSquared:y);var h=Math.cos(i);l.x=h*Math.cos(t),l.y=h*Math.sin(t),l.z=Math.sin(i),l=a.normalize(l,l),a.multiplyComponents(c,l,s);var p=Math.sqrt(a.dot(l,s));return s=a.divideByScalar(s,p,s),l=a.multiplyByScalar(l,o,l),n.t(f)||(f=new a),a.add(s,l,f)},a.fromDegreesArray=function(t,r,i){if(e.n.defined("coordinates",t),t.length<2||t.length%2!=0)throw new e.t("the number of coordinates must be a multiple of 2 and at least 2");var o=t.length;n.t(i)?i.length=o/2:i=new Array(o/2);for(var u=0;u<o;u+=2){var f=t[u],c=t[u+1],l=u/2;i[l]=a.fromDegrees(f,c,0,r,i[l])}return i},a.fromRadiansArray=function(t,r,i){if(e.n.defined("coordinates",t),t.length<2||t.length%2!=0)throw new e.t("the number of coordinates must be a multiple of 2 and at least 2");var o=t.length;n.t(i)?i.length=o/2:i=new Array(o/2);for(var u=0;u<o;u+=2){var f=t[u],c=t[u+1],l=u/2;i[l]=a.fromRadians(f,c,0,r,i[l])}return i},a.fromDegreesArrayHeights=function(t,r,i){if(e.n.defined("coordinates",t),t.length<3||t.length%3!=0)throw new e.t("the number of coordinates must be a multiple of 3 and at least 3");var o=t.length;n.t(i)?i.length=o/3:i=new Array(o/3);for(var u=0;u<o;u+=3){var f=t[u],c=t[u+1],l=t[u+2],s=u/3;i[s]=a.fromDegrees(f,c,l,r,i[s])}return i},a.fromRadiansArrayHeights=function(t,r,i){if(e.n.defined("coordinates",t),t.length<3||t.length%3!=0)throw new e.t("the number of coordinates must be a multiple of 3 and at least 3");var o=t.length;n.t(i)?i.length=o/3:i=new Array(o/3);for(var u=0;u<o;u+=3){var f=t[u],c=t[u+1],l=t[u+2],s=u/3;i[s]=a.fromRadians(f,c,l,r,i[s])}return i},a.ZERO=Object.freeze(new a(0,0,0)),a.UNIT_X=Object.freeze(new a(1,0,0)),a.UNIT_Y=Object.freeze(new a(0,1,0)),a.UNIT_Z=Object.freeze(new a(0,0,1)),a.UNIT_XYZ=Object.freeze(new a(1,1,1)),a.prototype.clone=function(t){return a.clone(this,t)},a.prototype.equals=function(t){return a.equals(this,t)},a.prototype.equalsEpsilon=function(t,e,n){return a.equalsEpsilon(this,t,e,n)},a.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+")"},a.globalOffset=new a(0,0,0);var h=new a,p=new a;function m(t,i,o,u,f){if(!n.t(t))throw new e.t("cartesian is required.");if(!n.t(i))throw new e.t("oneOverRadii is required.");if(!n.t(o))throw new e.t("oneOverRadiiSquared is required.");if(!n.t(u))throw new e.t("centerToleranceSquared is required.");var c=t.x,l=t.y,s=t.z,y=i.x,d=i.y,m=i.z,b=c*c*y*y,g=l*l*d*d,O=s*s*m*m,z=b+g+O,x=Math.sqrt(1/z),w=a.multiplyByScalar(t,x,h);if(z<u)return isFinite(x)?a.clone(w,f):void 0;var j=o.x,v=o.y,q=o.z,M=p;M.x=w.x*j*2,M.y=w.y*v*2,M.z=w.z*q*2;var R,S,E,N,A,B,C,I=(1-x)*a.magnitude(t)/(.5*a.magnitude(M)),T=0;do{T=(R=b*(A=(S=1/(1+(I-=T)*j))*S)+g*(B=(E=1/(1+I*v))*E)+O*(C=(N=1/(1+I*q))*N)-1)/(-2*(b*(A*S)*j+g*(B*E)*v+O*(C*N)*q))}while(Math.abs(R)>r.n.EPSILON12);return n.t(f)?(f.x=c*S,f.y=l*E,f.z=s*N,f):new a(c*S,l*E,s*N)}function b(t,e,r){this.longitude=n.e(t,0),this.latitude=n.e(e,0),this.height=n.e(r,0)}b.fromRadians=function(t,r,a,i){return e.n.typeOf.number("longitude",t),e.n.typeOf.number("latitude",r),a=n.e(a,0),n.t(i)?(i.longitude=t,i.latitude=r,i.height=a,i):new b(t,r,a)},b.fromDegrees=function(t,n,a,i){return e.n.typeOf.number("longitude",t),e.n.typeOf.number("latitude",n),t=r.n.toRadians(t),n=r.n.toRadians(n),b.fromRadians(t,n,a,i)};var g=new a,O=new a,z=new a,x=new a(1/6378137,1/6378137,1/6356752.314245179),w=new a(1/6378137,1/6378137,1/6378137),j=new a(1/40680631590769,1/40680631590769,1/40408299984661.445),v=new a(1/40680631590769,1/40680631590769,1/40680631590769),q=r.n.EPSILON1;b.fromCartesian=function(t,e,i){var o=n.t(e)?e.oneOverRadii:w,u=n.t(e)?e.oneOverRadiiSquared:v,f=n.t(e)?e._centerToleranceSquared:q;r.n.equalsEpsilon(r.n.Radius,6356752.314245179,r.n.EPSILON10)&&(o=n.t(e)?e.oneOverRadii:x,u=n.t(e)?e.oneOverRadiiSquared:j);var c=m(t,o,u,f,O);if(n.t(c)){var l=a.multiplyComponents(c,u,g);l=a.normalize(l,l);var s=a.subtract(t,c,z),y=Math.atan2(l.y,l.x),d=Math.asin(l.z),h=r.n.sign(a.dot(s,t))*a.magnitude(s);return n.t(i)?(i.longitude=y,i.latitude=d,i.height=h,i):new b(y,d,h)}},b.toCartesian=function(t,n,r){return e.n.defined("cartographic",t),a.fromRadians(t.longitude,t.latitude,t.height,n,r)},b.sphericalDistance=function(t,n,a,i){if(e.n.defined("longitudeA",t),e.n.defined("longitudeB",a),e.n.defined("latitudeA",n),e.n.defined("latitudeB",i),t===a&&n===i)return 0;var o=r.n.toRadians(n),u=r.n.toRadians(i),f=r.n.toRadians(t),c=r.n.toRadians(a),l=f*f+o*o,s=c*c+u*u,y=(l+s-((f-c)*(f-c)+(o-u)*(o-u)))/(2*Math.sqrt(l)*Math.sqrt(s));return y=r.n.clamp(y,-1,1),Math.acos(y)*r.n.Radius},b.clone=function(t,e){if(n.t(t))return n.t(e)?(e.longitude=t.longitude,e.latitude=t.latitude,e.height=t.height,e):new b(t.longitude,t.latitude,t.height)},b.equals=function(t,e){return t===e||n.t(t)&&n.t(e)&&t.longitude===e.longitude&&t.latitude===e.latitude&&t.height===e.height},b.equalsEpsilon=function(t,r,a){return e.n.typeOf.number("epsilon",a),t===r||n.t(t)&&n.t(r)&&Math.abs(t.longitude-r.longitude)<=a&&Math.abs(t.latitude-r.latitude)<=a&&Math.abs(t.height-r.height)<=a},b.ZERO=Object.freeze(new b(0,0,0)),b.prototype.clone=function(t){return b.clone(this,t)},b.prototype.equals=function(t){return b.equals(this,t)},b.prototype.equalsEpsilon=function(t,e){return b.equalsEpsilon(this,t,e)},b.prototype.toString=function(){return"("+this.longitude+", "+this.latitude+", "+this.height+")"},t.a=a,t.b=m,t.i=b}));
