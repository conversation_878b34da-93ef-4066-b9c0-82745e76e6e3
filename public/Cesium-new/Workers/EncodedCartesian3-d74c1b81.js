define(["exports","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295"],(function(e,n,o,r){"use strict";function t(){this.high=n.a.clone(n.a.ZERO),this.low=n.a.clone(n.a.ZERO)}t.encode=function(e,n){var t;return o.n.typeOf.number("value",e),r.t(n)||(n={high:0,low:0}),e>=0?(t=65536*Math.floor(e/65536),n.high=t,n.low=e-t):(t=65536*Math.floor(-e/65536),n.high=-t,n.low=e+t),n};var a={high:0,low:0};t.fromCartesian=function(e,n){o.n.typeOf.object("cartesian",e),r.t(n)||(n=new t);var h=n.high,i=n.low;return t.encode(e.x,a),h.x=a.high,i.x=a.low,t.encode(e.y,a),h.y=a.high,i.y=a.low,t.encode(e.z,a),h.z=a.high,i.z=a.low,n};var h=new t;t.writeElements=function(e,n,r){o.n.defined("cartesianArray",n),o.n.typeOf.number("index",r),o.n.typeOf.number.greaterThanOrEquals("index",r,0),t.fromCartesian(e,h);var a=h.high,i=h.low;n[r]=a.x,n[r+1]=a.y,n[r+2]=a.z,n[r+3]=i.x,n[r+4]=i.y,n[r+5]=i.z},e.t=t}));
