define(["./when-515d5295","./Rectangle-e170be8b","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryInstance-c11993d9","./GeometryOffsetAttribute-800f7650","./GeometryPipeline-137aa28e","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./PolygonPipeline-b8b35011","./RectangleGeometryLibrary-a52b9128","./VertexFormat-e844760b","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./IntersectionTests-5fa33dbd","./Plane-92c15089","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6"],(function(t,e,a,n,r,o,i,s,l,u,c,p,d,g,m,v,f,y,h,b,_,w,A,x,E,D,F,P,R,T,L){"use strict";var O=new o.a,C=new o.a,N=new o.a,I=new o.a,k=new e.s,S=new r.r,G=new n.c,M=new n.c;function H(t,e){var a=new l.T({attributes:new u.t,primitiveType:v._0x38df4a.TRIANGLES});return a.attributes.position=new l.r({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e.positions}),t.normal&&(a.attributes.normal=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.normals})),t.tangent&&(a.attributes.tangent=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.tangents})),t.bitangent&&(a.attributes.bitangent=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.bitangents})),a}var z=new o.a,V=new o.a;function U(t,e){var a=t._vertexFormat,n=t._ellipsoid,r=e.height,i=e.width,u=e.northCap,c=e.southCap,p=0,d=r,m=r,f=0;u&&(p=1,m-=1,f+=1),c&&(d-=1,m-=1,f+=1),f+=i*m;for(var h=a.position?new Float64Array(3*f):void 0,b=a.st?new Float32Array(2*f):void 0,_=0,w=0,A=O,x=S,E=Number.MAX_VALUE,D=Number.MAX_VALUE,F=-Number.MAX_VALUE,P=-Number.MAX_VALUE,R=p;R<d;++R)for(var T=0;T<i;++T)y.W.computePosition(e,n,a.st,R,T,A,x),h[_++]=A.x,h[_++]=A.y,h[_++]=A.z,a.st&&(b[w++]=x.x,b[w++]=x.y,E=Math.min(E,x.x),D=Math.min(D,x.y),F=Math.max(F,x.x),P=Math.max(P,x.y));if(u&&(y.W.computePosition(e,n,a.st,0,0,A,x),h[_++]=A.x,h[_++]=A.y,h[_++]=A.z,a.st&&(b[w++]=x.x,b[w++]=x.y,E=x.x,D=x.y,F=x.x,P=x.y)),c&&(y.W.computePosition(e,n,a.st,r-1,0,A,x),h[_++]=A.x,h[_++]=A.y,h[_]=A.z,a.st&&(b[w++]=x.x,b[w]=x.y,E=Math.min(E,x.x),D=Math.min(D,x.y),F=Math.max(F,x.x),P=Math.max(P,x.y))),a.st&&(E<0||D<0||F>1||P>1))for(var L=0;L<b.length;L+=2)b[L]=(b[L]-E)/(F-E),b[L+1]=(b[L+1]-D)/(P-D);var k=function(t,e,a,n){var r=t.length,i=e.normal?new Float32Array(r):void 0,s=e.tangent?new Float32Array(r):void 0,l=e.bitangent?new Float32Array(r):void 0,u=0,c=I,p=N,d=C;if(e.normal||e.tangent||e.bitangent)for(var g=0;g<r;g+=3){var m=o.a.fromArray(t,g,O),f=u+1,y=u+2;d=a.geodeticSurfaceNormal(m,d),(e.tangent||e.bitangent)&&(o.a.cross(o.a.UNIT_Z,d,p),v.r.multiplyByVector(n,p,p),o.a.normalize(p,p),e.bitangent&&o.a.normalize(o.a.cross(d,p,c),c)),e.normal&&(i[u]=d.x,i[f]=d.y,i[y]=d.z),e.tangent&&(s[u]=p.x,s[f]=p.y,s[y]=p.z),e.bitangent&&(l[u]=c.x,l[f]=c.y,l[y]=c.z),u+=3}return H(e,{positions:t,normals:i,tangents:s,bitangents:l})}(h,a,n,e.tangentRotationMatrix),G=6*(i-1)*(m-1);u&&(G+=3*(i-1)),c&&(G+=3*(i-1));var M,z=g.IndexDatatype.createTypedArray(f,G),V=0,U=0;for(M=0;M<m-1;++M){for(var W=0;W<i-1;++W){var B=V,q=B+i,Y=q+1,j=B+1;z[U++]=B,z[U++]=q,z[U++]=j,z[U++]=j,z[U++]=q,z[U++]=Y,++V}++V}if(u||c){var X,J,Q=f-1,Z=f-1;if(u&&c&&(Q=f-2),V=0,u)for(M=0;M<i-1;M++)J=(X=V)+1,z[U++]=Q,z[U++]=X,z[U++]=J,++V;if(c)for(V=(m-1)*i,M=0;M<i-1;M++)J=(X=V)+1,z[U++]=X,z[U++]=Z,z[U++]=J,++V}return k.indices=z,a.st&&(k.attributes.st=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:b})),k}function W(t,e,a,n,r){return t[e++]=n[a],t[e++]=n[a+1],t[e++]=n[a+2],t[e++]=r[a],t[e++]=r[a+1],t[e]=r[a+2],t}function B(t,e,a,n){return t[e++]=n[a],t[e++]=n[a+1],t[e++]=n[a],t[e]=n[a+1],t}var q=new h.n;function Y(e,n){var r,i=e._shadowVolume,u=e._offsetAttribute,v=e._vertexFormat,y=e._extrudedHeight,b=e._surfaceHeight,_=e._ellipsoid,w=n.height,A=n.width;if(i){var x=h.n.clone(v,q);x.normal=!0,e._vertexFormat=x}var E=U(e,n);i&&(e._vertexFormat=v);var D=f.T.scaleToGeodeticHeight(E.attributes.position.values,b,_,!1),F=(D=new Float64Array(D)).length,P=2*F,R=new Float64Array(P);R.set(D);var T=f.T.scaleToGeodeticHeight(E.attributes.position.values,y,_);R.set(T,F),E.attributes.position.values=R;var L,k,S=v.normal?new Float32Array(P):void 0,G=v.tangent?new Float32Array(P):void 0,M=v.bitangent?new Float32Array(P):void 0,Y=v.st?new Float32Array(P/3*2):void 0;if(v.normal){for(k=E.attributes.normal.values,S.set(k),r=0;r<F;r++)k[r]=-k[r];S.set(k,F),E.attributes.normal.values=S}if(i){k=E.attributes.normal.values,v.normal||(E.attributes.normal=void 0);var j=new Float32Array(P);for(r=0;r<F;r++)k[r]=-k[r];j.set(k,F),E.attributes.extrudeDirection=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})}var X,J=t.t(u);if(J){var Q=F/3*2,Z=new Uint8Array(Q);u===p.I.TOP?Z=a.d(Z,1,0,Q/2):(X=u===p.I.NONE?0:1,Z=a.d(Z,X)),E.attributes.applyOffset=new l.r({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:Z})}if(v.tangent){var K=E.attributes.tangent.values;for(G.set(K),r=0;r<F;r++)K[r]=-K[r];G.set(K,F),E.attributes.tangent.values=G}if(v.bitangent){var $=E.attributes.bitangent.values;M.set($),M.set($,F),E.attributes.bitangent.values=M}v.st&&(L=E.attributes.st.values,Y.set(L),Y.set(L,F/3*2),E.attributes.st.values=Y);var tt=E.indices,et=tt.length,at=F/3,nt=g.IndexDatatype.createTypedArray(P/3,2*et);for(nt.set(tt),r=0;r<et;r+=3)nt[r+et]=tt[r+2]+at,nt[r+1+et]=tt[r+1]+at,nt[r+2+et]=tt[r]+at;E.indices=nt;var rt=n.northCap,ot=n.southCap,it=w,st=2,lt=0,ut=4,ct=4;rt&&(st-=1,it-=1,lt+=1,ut-=2,ct-=1),ot&&(st-=1,it-=1,lt+=1,ut-=2,ct-=1);var pt=2*((lt+=st*A+2*it-ut)+ct),dt=new Float64Array(3*pt),gt=i?new Float32Array(3*pt):void 0,mt=J?new Uint8Array(pt):void 0,vt=v.st?new Float32Array(2*pt):void 0,ft=u===p.I.TOP;J&&!ft&&(X=u===p.I.ALL?1:0,mt=a.d(mt,X));var yt,ht=0,bt=0,_t=0,wt=0,At=A*it;for(r=0;r<At;r+=A)dt=W(dt,ht,yt=3*r,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*r,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1);if(ot){var xt=rt?At+1:At;for(yt=3*xt,r=0;r<2;r++)dt=W(dt,ht,yt,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*xt,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1)}else for(r=At-A;r<At;r++)dt=W(dt,ht,yt=3*r,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*r,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1);for(r=At-1;r>0;r-=A)dt=W(dt,ht,yt=3*r,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*r,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1);if(rt){var Et=At;for(yt=3*Et,r=0;r<2;r++)dt=W(dt,ht,yt,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*Et,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1)}else for(r=A-1;r>=0;r--)dt=W(dt,ht,yt=3*r,D,T),ht+=6,v.st&&(vt=B(vt,bt,2*r,L),bt+=4),i&&(_t+=3,gt[_t++]=k[yt],gt[_t++]=k[yt+1],gt[_t++]=k[yt+2]),ft&&(mt[wt++]=1,wt+=1);var Dt=function(t,e,a){var n=t.length,r=e.normal?new Float32Array(n):void 0,i=e.tangent?new Float32Array(n):void 0,s=e.bitangent?new Float32Array(n):void 0,l=0,u=0,c=0,p=!0,d=I,g=N,v=C;if(e.normal||e.tangent||e.bitangent)for(var f=0;f<n;f+=6){var y=o.a.fromArray(t,f,O),h=o.a.fromArray(t,(f+6)%n,z);if(p){var b=o.a.fromArray(t,(f+3)%n,V);o.a.subtract(h,y,h),o.a.subtract(b,y,b),v=o.a.normalize(o.a.cross(b,h,v),v),p=!1}o.a.equalsEpsilon(h,y,m.n.EPSILON10)&&(p=!0),(e.tangent||e.bitangent)&&(d=a.geodeticSurfaceNormal(y,d),e.tangent&&(g=o.a.normalize(o.a.cross(d,v,g),g))),e.normal&&(r[l++]=v.x,r[l++]=v.y,r[l++]=v.z,r[l++]=v.x,r[l++]=v.y,r[l++]=v.z),e.tangent&&(i[u++]=g.x,i[u++]=g.y,i[u++]=g.z,i[u++]=g.x,i[u++]=g.y,i[u++]=g.z),e.bitangent&&(s[c++]=d.x,s[c++]=d.y,s[c++]=d.z,s[c++]=d.x,s[c++]=d.y,s[c++]=d.z)}return H(e,{positions:t,normals:r,tangents:i,bitangents:s})}(dt,v,_);v.st&&(Dt.attributes.st=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:vt})),i&&(Dt.attributes.extrudeDirection=new l.r({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:gt})),J&&(Dt.attributes.applyOffset=new l.r({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:mt}));var Ft,Pt,Rt,Tt,Lt=g.IndexDatatype.createTypedArray(pt,6*lt);F=dt.length/3;var Ot=0;for(r=0;r<F-1;r+=2){Tt=((Ft=r)+2)%F;var Ct=o.a.fromArray(dt,3*Ft,z),Nt=o.a.fromArray(dt,3*Tt,V);o.a.equalsEpsilon(Ct,Nt,m.n.EPSILON10)||(Rt=((Pt=(Ft+1)%F)+2)%F,Lt[Ot++]=Ft,Lt[Ot++]=Pt,Lt[Ot++]=Tt,Lt[Ot++]=Tt,Lt[Ot++]=Pt,Lt[Ot++]=Rt)}return Dt.indices=Lt,(Dt=d.F.combineInstances([new c.m({geometry:E}),new c.m({geometry:Dt})]))[0]}var j=[new o.a,new o.a,new o.a,new o.a],X=new o.i,J=new o.i;function Q(t,a,n,r,o){if(0===n)return e.s.clone(t,o);var i=y.W.computeOptions(t,a,n,0,k,X),s=i.height,l=i.width,u=j;return y.W.computePosition(i,r,!1,0,0,u[0]),y.W.computePosition(i,r,!1,0,l-1,u[1]),y.W.computePosition(i,r,!1,s-1,0,u[2]),y.W.computePosition(i,r,!1,s-1,l-1,u[3]),e.s.fromCartesianArray(u,r,o)}function Z(a){var n=(a=t.e(a,t.e.EMPTY_OBJECT)).rectangle;if(i.n.typeOf.object("rectangle",n),e.s.validate(n),n.north<n.south)throw new i.t("options.rectangle.north must be greater than or equal to options.rectangle.south");var r=t.e(a.height,0),o=t.e(a.extrudedHeight,r);this._rectangle=e.s.clone(n),this._granularity=t.e(a.granularity,m.n.RADIANS_PER_DEGREE),this._ellipsoid=e.n.clone(t.e(a.ellipsoid,e.n.WGS84)),this._surfaceHeight=Math.max(r,o),this._rotation=t.e(a.rotation,0),this._stRotation=t.e(a.stRotation,0),this._vertexFormat=h.n.clone(t.e(a.vertexFormat,h.n.DEFAULT)),this._extrudedHeight=Math.min(r,o),this._shadowVolume=t.e(a.shadowVolume,!1),this._workerName="createRectangleGeometry",this._offsetAttribute=a.offsetAttribute,this._rotatedRectangle=void 0,this._textureCoordinateRotationPoints=void 0}Z.packedLength=e.s.packedLength+e.n.packedLength+h.n.packedLength+7,Z.pack=function(a,n,r){return i.n.typeOf.object("value",a),i.n.defined("array",n),r=t.e(r,0),e.s.pack(a._rectangle,n,r),r+=e.s.packedLength,e.n.pack(a._ellipsoid,n,r),r+=e.n.packedLength,h.n.pack(a._vertexFormat,n,r),r+=h.n.packedLength,n[r++]=a._granularity,n[r++]=a._surfaceHeight,n[r++]=a._rotation,n[r++]=a._stRotation,n[r++]=a._extrudedHeight,n[r++]=a._shadowVolume?1:0,n[r]=t.e(a._offsetAttribute,-1),n};var K=new e.s,$=e.n.clone(e.n.UNIT_SPHERE),tt={rectangle:K,ellipsoid:$,vertexFormat:q,granularity:void 0,height:void 0,rotation:void 0,stRotation:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};Z.unpack=function(a,n,r){i.n.defined("array",a),n=t.e(n,0);var o=e.s.unpack(a,n,K);n+=e.s.packedLength;var s=e.n.unpack(a,n,$);n+=e.n.packedLength;var l=h.n.unpack(a,n,q);n+=h.n.packedLength;var u=a[n++],c=a[n++],p=a[n++],d=a[n++],g=a[n++],m=1===a[n++],v=a[n];return t.t(r)?(r._rectangle=e.s.clone(o,r._rectangle),r._ellipsoid=e.n.clone(s,r._ellipsoid),r._vertexFormat=h.n.clone(l,r._vertexFormat),r._granularity=u,r._surfaceHeight=c,r._rotation=p,r._stRotation=d,r._extrudedHeight=g,r._shadowVolume=m,r._offsetAttribute=-1===v?void 0:v,r):(tt.granularity=u,tt.height=c,tt.rotation=p,tt.stRotation=d,tt.extrudedHeight=g,tt.shadowVolume=m,tt.offsetAttribute=-1===v?void 0:v,new Z(tt))},Z.computeRectangle=function(a,n){var r=(a=t.e(a,t.e.EMPTY_OBJECT)).rectangle;if(i.n.typeOf.object("rectangle",r),e.s.validate(r),r.north<r.south)throw new i.t("options.rectangle.north must be greater than or equal to options.rectangle.south");var o=t.e(a.granularity,m.n.RADIANS_PER_DEGREE),s=t.e(a.ellipsoid,e.n.WGS84);return Q(r,o,t.e(a.rotation,0),s,n)};var et=new v.r,at=new l.a,nt=new o.i;Z.createGeometry=function(r){if(!m.n.equalsEpsilon(r._rectangle.north,r._rectangle.south,m.n.EPSILON10)&&!m.n.equalsEpsilon(r._rectangle.east,r._rectangle.west,m.n.EPSILON10)){var o=r._rectangle,i=r._ellipsoid,u=r._rotation,c=r._stRotation,d=r._vertexFormat,g=y.W.computeOptions(o,r._granularity,u,c,k,X,J),h=et;if(0!==c||0!==u){var b=e.s.center(o,nt),_=i.geodeticSurfaceNormalCartographic(b,z);l.a.fromAxisAngle(_,-c,at),v.r.fromQuaternion(at,h)}else v.r.clone(v.r.IDENTITY,h);var w,A,x=r._surfaceHeight,E=r._extrudedHeight,D=!m.n.equalsEpsilon(x,E,0,m.n.EPSILON2);if(g.lonScalar=1/r._rectangle.width,g.latScalar=1/r._rectangle.height,g.tangentRotationMatrix=h,o=r._rectangle,D){w=Y(r,g);var F=n.c.fromRectangle3D(o,i,x,M),P=n.c.fromRectangle3D(o,i,E,G);A=n.c.union(F,P)}else{if((w=U(r,g)).attributes.position.values=f.T.scaleToGeodeticHeight(w.attributes.position.values,x,i,!1),t.t(r._offsetAttribute)){var R=w.attributes.position.values.length,T=new Uint8Array(R/3),L=r._offsetAttribute===p.I.NONE?0:1;a.d(T,L),w.attributes.applyOffset=new l.r({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:T})}A=n.c.fromRectangle3D(o,i,x)}return d.position||delete w.attributes.position,new l.T({attributes:w.attributes,indices:w.indices,primitiveType:w.primitiveType,boundingSphere:A,offsetAttribute:r._offsetAttribute})}},Z.createShadowVolume=function(t,e,a){var n=t._granularity,r=t._ellipsoid,o=e(n,r),i=a(n,r);return new Z({rectangle:t._rectangle,rotation:t._rotation,ellipsoid:r,stRotation:t._stRotation,granularity:n,extrudedHeight:i,height:o,vertexFormat:h.n.POSITION_ONLY,shadowVolume:!0})};var rt=new e.s,ot=[new r.r,new r.r,new r.r],it=new l.u,st=new o.i;return Object.defineProperties(Z.prototype,{rectangle:{get:function(){return t.t(this._rotatedRectangle)||(this._rotatedRectangle=Q(this._rectangle,this._granularity,this._rotation,this._ellipsoid)),this._rotatedRectangle}},textureCoordinateRotationPoints:{get:function(){return t.t(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){if(0===t._stRotation)return[0,0,0,1,1,0];var a=e.s.clone(t._rectangle,rt),n=t._granularity,o=t._ellipsoid,i=Q(a,n,t._rotation-t._stRotation,o,rt),s=ot;s[0].x=i.west,s[0].y=i.south,s[1].x=i.west,s[1].y=i.north,s[2].x=i.east,s[2].y=i.south;for(var u=t.rectangle,c=l.u.fromRotation(t._stRotation,it),p=e.s.center(u,st),d=0;d<3;++d){var g=s[d];g.x-=p.longitude,g.y-=p.latitude,l.u.multiplyByVector(c,g,g),g.x+=p.longitude,g.y+=p.latitude,g.x=(g.x-u.west)/u.width,g.y=(g.y-u.south)/u.height}var m=s[0],v=s[1],f=s[2],y=new Array(6);return r.r.pack(m,y),r.r.pack(v,y,2),r.r.pack(f,y,4),y}(this)),this._textureCoordinateRotationPoints}}}),function(a,n){return t.t(n)&&(a=Z.unpack(a,n)),a._ellipsoid=e.n.clone(a._ellipsoid),a._rectangle=e.s.clone(a._rectangle),Z.createGeometry(a)}}));
