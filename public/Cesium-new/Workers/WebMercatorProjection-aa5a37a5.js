define(["exports","./Cartographic-1bbcab04","./when-515d5295","./Check-3aa71481","./Rectangle-e170be8b","./Math-5e38123d"],(function(t,e,i,a,o,r){"use strict";function n(t){this._ellipsoid=i.e(t,o.n.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(n.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),n.mercatorAngleToGeodeticLatitude=function(t){return r.n.PI_OVER_TWO-2*Math.atan(Math.exp(-t))},n.geodeticLatitudeToMercatorAngle=function(t){t>n.MaximumLatitude?t=n.MaximumLatitude:t<-n.MaximumLatitude&&(t=-n.MaximumLatitude);var e=Math.sin(t);return.5*Math.log((1+e)/(1-e))},n.MaximumLatitude=n.mercatorAngleToGeodeticLatitude(Math.PI),n.prototype.project=function(t,a){var o=this._semimajorAxis,r=t.longitude*o,u=n.geodeticLatitudeToMercatorAngle(t.latitude)*o,d=t.height;return i.t(a)?(a.x=r,a.y=u,a.z=d,a):new e.a(r,u,d)},n.prototype.unproject=function(t,o){if(!i.t(t))throw new a.t("cartesian is required");var r=this._oneOverSemimajorAxis,u=t.x*r,d=n.mercatorAngleToGeodeticLatitude(t.y*r),s=t.z;return i.t(o)?(o.longitude=u,o.latitude=d,o.height=s,o):new e.i(u,d,s)},t.e=n}));
