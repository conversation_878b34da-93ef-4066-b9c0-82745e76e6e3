define(["./Rectangle-e170be8b","./EllipsoidTangentPlane-fd839d7b","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./when-515d5295","./Check-3aa71481","./TerrainEncoding-29e3257b","./Math-5e38123d","./PrimitiveType-b38a4004","./OrientedBoundingBox-57407e6e","./GeometryAttribute-9bc31a7f","./WebMercatorProjection-aa5a37a5","./RuntimeError-350acae3","./createTaskProcessorWorker","./Intersect-53434a77","./Cartesian4-034d54d5","./IntersectionTests-5fa33dbd","./Plane-92c15089","./Event-9821f5d9","./AttributeCompression-f9ee669b","./ComponentDatatype-d430c7f7","./WebGLConstants-77a84876","./PolygonPipeline-b8b35011","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6","./FeatureDetection-7fae0d5a"],(function(e,t,i,a,r,n,s,l,o,f,u,c,h,d,m,g,p,w,x,k,y,b,I,v,U,T,M){"use strict";var A=Object.freeze({NONE:0,LERC:1}),V={};V.DEFAULT_STRUCTURE=Object.freeze({heightScale:1,heightOffset:0,elementsPerHeight:1,stride:1,elementMultiplier:256,isBigEndian:!1});var D=new r.a,B=new f.c,S=new r.a,P=new r.a;V.computeVertices=function(d){if(!n.t(d)||!n.t(d.heightmap))throw new s.t("options.heightmap is required.");if(!n.t(d.width)||!n.t(d.height))throw new s.t("options.width and options.height are required.");if(!n.t(d.nativeRectangle))throw new s.t("options.nativeRectangle is required.");if(!n.t(d.skirtHeight))throw new s.t("options.skirtHeight is required.");var m,g,p,w,x=Math.cos,k=Math.sin,y=Math.sqrt,b=Math.atan,I=Math.exp,v=o.n.PI_OVER_TWO,U=o.n.toRadians,T=d.heightmap,M=d.width,A=d.height,E=d.skirtHeight,F=n.e(d.isGeographic,!0),L=n.e(d.ellipsoid,e.n.WGS84),C=1/L.maximumRadius,N=d.nativeRectangle,O=d.rectangle;n.t(O)?(m=O.west,g=O.south,p=O.east,w=O.north):F?(m=U(N.west),g=U(N.south),p=U(N.east),w=U(N.north)):(m=N.west*C,g=v-2*b(I(-N.south*C)),p=N.east*C,w=v-2*b(I(-N.north*C)));var z=d.relativeToCenter,R=n.t(z);z=R?z:r.a.ZERO;var H=n.e(d.exaggeration,1),_=n.e(d.includeWebMercatorT,!1),Y=n.e(d.structure,V.DEFAULT_STRUCTURE),X=n.e(Y.heightScale,V.DEFAULT_STRUCTURE.heightScale),W=n.e(Y.heightOffset,V.DEFAULT_STRUCTURE.heightOffset),Z=n.e(Y.elementsPerHeight,V.DEFAULT_STRUCTURE.elementsPerHeight),G=n.e(Y.stride,V.DEFAULT_STRUCTURE.stride),q=n.e(Y.elementMultiplier,V.DEFAULT_STRUCTURE.elementMultiplier),j=n.e(Y.isBigEndian,V.DEFAULT_STRUCTURE.isBigEndian),Q=e.s.computeWidth(N),J=e.s.computeHeight(N),K=Q/(M-1),$=J/(A-1);F||(Q*=C,J*=C);var ee,te,ie=L.radiiSquared,ae=ie.x,re=ie.y,ne=ie.z,se=65536,le=-65536,oe=c.m.eastNorthUpToFixedFrame(z,L),fe=f.c.inverseTransformation(oe,B);_&&(ee=h.e.geodeticLatitudeToMercatorAngle(g),te=1/(h.e.geodeticLatitudeToMercatorAngle(w)-ee));var ue=S;ue.x=Number.POSITIVE_INFINITY,ue.y=Number.POSITIVE_INFINITY,ue.z=Number.POSITIVE_INFINITY;var ce=P;ce.x=Number.NEGATIVE_INFINITY,ce.y=Number.NEGATIVE_INFINITY,ce.z=Number.NEGATIVE_INFINITY;var he=Number.POSITIVE_INFINITY,de=M*A,me=de+(E>0?2*M+2*A:0),ge=new Array(me),pe=new Array(me),we=new Array(me),xe=_?new Array(me):[],ke=0,ye=A,be=0,Ie=M;E>0&&(--ke,++ye,--be,++Ie);for(var ve=1e-5,Ue=ke;Ue<ye;++Ue){var Te=Ue;Te<0&&(Te=0),Te>=A&&(Te=A-1);var Me=N.north-$*Te,Ae=((Me=F?U(Me):v-2*b(I(-Me*C)))-g)/(w-g);Ae=o.n.clamp(Ae,0,1);var Ve=Ue===ke,De=Ue===ye-1;E>0&&(Ve?Me+=ve*J:De&&(Me-=ve*J));var Be,Se=x(Me),Pe=k(Me),Ee=ne*Pe;_&&(Be=(h.e.geodeticLatitudeToMercatorAngle(Me)-ee)*te);for(var Fe=be;Fe<Ie;++Fe){var Le=Fe;Le<0&&(Le=0),Le>=M&&(Le=M-1);var Ce,Ne,Oe=Te*(M*G)+Le*G;if(1===Z)Ce=T[Oe];else if(Ce=0,j)for(Ne=0;Ne<Z;++Ne)Ce=Ce*q+T[Oe+Ne];else for(Ne=Z-1;Ne>=0;--Ne)Ce=Ce*q+T[Oe+Ne];Ce=(Ce*X+W)*H,le=Math.max(le,Ce),se=Math.min(se,Ce);var ze=N.west+K*Le;F?ze=U(ze):ze*=C;var Re=(ze-m)/(p-m);Re=o.n.clamp(Re,0,1);var He=Te*M+Le;if(E>0){var _e=Fe===be,Ye=Fe===Ie-1,Xe=Ve||De||_e||Ye;if((Ve||De)&&(_e||Ye))continue;Xe&&(Ce-=E,_e?(He=de+(A-Te-1),ze-=ve*Q):De?He=de+A+(M-Le-1):Ye?(He=de+A+M+Te,ze+=ve*Q):Ve&&(He=de+A+M+A+Le))}var We=Se*x(ze),Ze=Se*k(ze),Ge=ae*We,qe=re*Ze,je=1/y(Ge*We+qe*Ze+Ee*Pe),Qe=Ge*je,Je=qe*je,Ke=Ee*je,$e=new r.a;$e.x=Qe+We*Ce,$e.y=Je+Ze*Ce,$e.z=Ke+Pe*Ce,ge[He]=$e,pe[He]=Ce,we[He]=new a.r(Re,Ae),_&&(xe[He]=Be),f.c.multiplyByPoint(fe,$e,D),r.a.minimumByComponent(D,ue,ue),r.a.maximumByComponent(D,ce,ce),he=Math.min(he,Ce)}}var et,tt,it=i.c.fromPoints(ge);(n.t(O)&&(et=u.b.fromRectangle(O,se,le,L)),R)&&(tt=new l.c(L).computeHorizonCullingPointPossiblyUnderEllipsoid(z,ge,se));for(var at=new t.e(ue,ce,z),rt=new l.u(at,he,le,oe,!1,_),nt=new Float32Array(me*rt.getStride()),st=0,lt=0;lt<me;++lt)st=rt.encode(nt,st,ge[lt],we[lt],pe[lt],void 0,xe[lt]);return{vertices:nt,maximumHeight:le,minimumHeight:se,encoding:rt,boundingSphere3D:it,orientedBoundingBox:et,occludeePointInScaledSpace:tt}};
/* Copyright 2015-2018 Esri. Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 @preserve */var E,F,L,C,N,O,z,R,H,_,Y,X,W,Z={};_=function(){var e={defaultNoDataValue:-34027999387901484e22,decode:function(n,s){var l=(s=s||{}).encodedMaskData||null===s.encodedMaskData,o=r(n,s.inputOffset||0,l),f=null!==s.noDataValue?s.noDataValue:e.defaultNoDataValue,u=t(o,s.pixelType||Float32Array,s.encodedMaskData,f,s.returnMask),c={width:o.width,height:o.height,pixelData:u.resultPixels,minValue:u.minValue,maxValue:o.pixels.maxValue,noDataValue:f};return u.resultMask&&(c.maskData=u.resultMask),s.returnEncodedMask&&o.mask&&(c.encodedMaskData=o.mask.bitset?o.mask.bitset:null),s.returnFileInfo&&(c.fileInfo=i(o),s.computeUsedBitDepths&&(c.fileInfo.bitDepths=a(o))),c}},t=function(e,t,i,a,r){var s,l,o,f=0,u=e.pixels.numBlocksX,c=e.pixels.numBlocksY,h=Math.floor(e.width/u),d=Math.floor(e.height/c),m=2*e.maxZError,g=Number.MAX_VALUE;i=i||(e.mask?e.mask.bitset:null),l=new t(e.width*e.height),r&&i&&(o=new Uint8Array(e.width*e.height));for(var p,w,x=new Float32Array(h*d),k=0;k<=c;k++){var y=k!==c?d:e.height%c;if(0!==y)for(var b=0;b<=u;b++){var I=b!==u?h:e.width%u;if(0!==I){var v,U,T,M,A=k*e.width*d+b*h,V=e.width-I,D=e.pixels.blocks[f];if(D.encoding<2?(0===D.encoding?v=D.rawData:(n(D.stuffedData,D.bitsPerPixel,D.numValidPixels,D.offset,m,x,e.pixels.maxValue),v=x),U=0):T=2===D.encoding?0:D.offset,i)for(w=0;w<y;w++){for(7&A&&(M=i[A>>3],M<<=7&A),p=0;p<I;p++)7&A||(M=i[A>>3]),128&M?(o&&(o[A]=1),g=g>(s=D.encoding<2?v[U++]:T)?s:g,l[A++]=s):(o&&(o[A]=0),l[A++]=a),M<<=1;A+=V}else if(D.encoding<2)for(w=0;w<y;w++){for(p=0;p<I;p++)g=g>(s=v[U++])?s:g,l[A++]=s;A+=V}else for(g=g>T?T:g,w=0;w<y;w++){for(p=0;p<I;p++)l[A++]=T;A+=V}if(1===D.encoding&&U!==D.numValidPixels)throw"Block and Mask do not match";f++}}}return{resultPixels:l,resultMask:o,minValue:g}},i=function(e){return{fileIdentifierString:e.fileIdentifierString,fileVersion:e.fileVersion,imageType:e.imageType,height:e.height,width:e.width,maxZError:e.maxZError,eofOffset:e.eofOffset,mask:e.mask?{numBlocksX:e.mask.numBlocksX,numBlocksY:e.mask.numBlocksY,numBytes:e.mask.numBytes,maxValue:e.mask.maxValue}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,numBytes:e.pixels.numBytes,maxValue:e.pixels.maxValue,noDataValue:e.noDataValue}}},a=function(e){for(var t=e.pixels.numBlocksX*e.pixels.numBlocksY,i={},a=0;a<t;a++){var r=e.pixels.blocks[a];0===r.encoding?i.float32=!0:1===r.encoding?i[r.bitsPerPixel]=!0:i[0]=!0}return Object.keys(i)},r=function(e,t,i){var a={},r=new Uint8Array(e,t,10);if(a.fileIdentifierString=String.fromCharCode.apply(null,r),"CntZImage"!==a.fileIdentifierString.trim())throw"Unexpected file identifier string: "+a.fileIdentifierString;t+=10;var n=new DataView(e,t,24);if(a.fileVersion=n.getInt32(0,!0),a.imageType=n.getInt32(4,!0),a.height=n.getUint32(8,!0),a.width=n.getUint32(12,!0),a.maxZError=n.getFloat64(16,!0),t+=24,!i)if(n=new DataView(e,t,16),a.mask={},a.mask.numBlocksY=n.getUint32(0,!0),a.mask.numBlocksX=n.getUint32(4,!0),a.mask.numBytes=n.getUint32(8,!0),a.mask.maxValue=n.getFloat32(12,!0),t+=16,a.mask.numBytes>0){var s=new Uint8Array(Math.ceil(a.width*a.height/8)),l=(n=new DataView(e,t,a.mask.numBytes)).getInt16(0,!0),o=2,f=0;do{if(l>0)for(;l--;)s[f++]=n.getUint8(o++);else{var u=n.getUint8(o++);for(l=-l;l--;)s[f++]=u}l=n.getInt16(o,!0),o+=2}while(o<a.mask.numBytes);if(-32768!==l||f<s.length)throw"Unexpected end of mask RLE encoding";a.mask.bitset=s,t+=a.mask.numBytes}else 0==(a.mask.numBytes|a.mask.numBlocksY|a.mask.maxValue)&&(a.mask.bitset=new Uint8Array(Math.ceil(a.width*a.height/8)));n=new DataView(e,t,16),a.pixels={},a.pixels.numBlocksY=n.getUint32(0,!0),a.pixels.numBlocksX=n.getUint32(4,!0),a.pixels.numBytes=n.getUint32(8,!0),a.pixels.maxValue=n.getFloat32(12,!0),t+=16;var c=a.pixels.numBlocksX,h=a.pixels.numBlocksY,d=c+(a.width%c>0?1:0),m=h+(a.height%h>0?1:0);a.pixels.blocks=new Array(d*m);for(var g=0,p=0;p<m;p++)for(var w=0;w<d;w++){var x=0,k=e.byteLength-t;n=new DataView(e,t,Math.min(10,k));var y={};a.pixels.blocks[g++]=y;var b=n.getUint8(0);if(x++,y.encoding=63&b,y.encoding>3)throw"Invalid block encoding ("+y.encoding+")";if(2!==y.encoding){if(0!==b&&2!==b){if(b>>=6,y.offsetType=b,2===b)y.offset=n.getInt8(1),x++;else if(1===b)y.offset=n.getInt16(1,!0),x+=2;else{if(0!==b)throw"Invalid block offset type";y.offset=n.getFloat32(1,!0),x+=4}if(1===y.encoding)if(b=n.getUint8(x),x++,y.bitsPerPixel=63&b,b>>=6,y.numValidPixelsType=b,2===b)y.numValidPixels=n.getUint8(x),x++;else if(1===b)y.numValidPixels=n.getUint16(x,!0),x+=2;else{if(0!==b)throw"Invalid valid pixel count type";y.numValidPixels=n.getUint32(x,!0),x+=4}}var I;if(t+=x,3!==y.encoding)if(0===y.encoding){var v=(a.pixels.numBytes-1)/4;if(v!==Math.floor(v))throw"uncompressed block has invalid length";I=new ArrayBuffer(4*v),new Uint8Array(I).set(new Uint8Array(e,t,4*v));var U=new Float32Array(I);y.rawData=U,t+=4*v}else if(1===y.encoding){var T=Math.ceil(y.numValidPixels*y.bitsPerPixel/8),M=Math.ceil(T/4);I=new ArrayBuffer(4*M),new Uint8Array(I).set(new Uint8Array(e,t,T)),y.stuffedData=new Uint32Array(I),t+=T}}else t++}return a.eofOffset=t,a},n=function(e,t,i,a,r,n,s){var l,o,f,u=(1<<t)-1,c=0,h=0,d=Math.ceil((s-a)/r),m=4*e.length-Math.ceil(t*i/8);for(e[e.length-1]<<=8*m,l=0;l<i;l++){if(0===h&&(f=e[c++],h=32),h>=t)o=f>>>h-t&u,h-=t;else{var g=t-h;o=(f&u)<<g&u,o+=(f=e[c++])>>>(h=32-g)}n[l]=o<d?a+o*r:s}return n};return e}(),E=function(e,t,i,a,r,n,s,l){var o,f,u,c,h,d=(1<<i)-1,m=0,g=0,p=4*e.length-Math.ceil(i*a/8);if(e[e.length-1]<<=8*p,r)for(o=0;o<a;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&d,g-=i):(f=(u&d)<<(c=i-g)&d,f+=(u=e[m++])>>>(g=32-c)),t[o]=r[f];else for(h=Math.ceil((l-n)/s),o=0;o<a;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&d,g-=i):(f=(u&d)<<(c=i-g)&d,f+=(u=e[m++])>>>(g=32-c)),t[o]=f<h?n+f*s:l},F=function(e,t,i,a,r,n){var s,l=(1<<t)-1,o=0,f=0,u=0,c=0,h=0,d=[],m=4*e.length-Math.ceil(t*i/8);e[e.length-1]<<=8*m;var g=Math.ceil((n-a)/r);for(f=0;f<i;f++)0===c&&(s=e[o++],c=32),c>=t?(h=s>>>c-t&l,c-=t):(h=(s&l)<<(u=t-c)&l,h+=(s=e[o++])>>>(c=32-u)),d[f]=h<g?a+h*r:n;return d.unshift(a),d},L=function(e,t,i,a,r,n,s,l){var o,f,u,c,h=(1<<i)-1,d=0,m=0,g=0;if(r)for(o=0;o<a;o++)0===m&&(u=e[d++],m=32,g=0),m>=i?(f=u>>>g&h,m-=i,g+=i):(f=u>>>g&h,m=32-(c=i-m),f|=((u=e[d++])&(1<<c)-1)<<i-c,g=c),t[o]=r[f];else{var p=Math.ceil((l-n)/s);for(o=0;o<a;o++)0===m&&(u=e[d++],m=32,g=0),m>=i?(f=u>>>g&h,m-=i,g+=i):(f=u>>>g&h,m=32-(c=i-m),f|=((u=e[d++])&(1<<c)-1)<<i-c,g=c),t[o]=f<p?n+f*s:l}return t},C=function(e,t,i,a,r,n){var s,l=(1<<t)-1,o=0,f=0,u=0,c=0,h=0,d=0,m=[],g=Math.ceil((n-a)/r);for(f=0;f<i;f++)0===c&&(s=e[o++],c=32,d=0),c>=t?(h=s>>>d&l,c-=t,d+=t):(h=s>>>d&l,c=32-(u=t-c),h|=((s=e[o++])&(1<<u)-1)<<t-u,d=u),m[f]=h<g?a+h*r:n;return m.unshift(a),m},N=function(e,t,i,a){var r,n,s,l,o=(1<<i)-1,f=0,u=0,c=4*e.length-Math.ceil(i*a/8);for(e[e.length-1]<<=8*c,r=0;r<a;r++)0===u&&(s=e[f++],u=32),u>=i?(n=s>>>u-i&o,u-=i):(n=(s&o)<<(l=i-u)&o,n+=(s=e[f++])>>>(u=32-l)),t[r]=n;return t},O=function(e,t,i,a){var r,n,s,l,o=(1<<i)-1,f=0,u=0,c=0;for(r=0;r<a;r++)0===u&&(s=e[f++],u=32,c=0),u>=i?(n=s>>>c&o,u-=i,c+=i):(n=s>>>c&o,u=32-(l=i-u),n|=((s=e[f++])&(1<<l)-1)<<i-l,c=l),t[r]=n;return t},z={HUFFMAN_LUT_BITS_MAX:12,computeChecksumFletcher32:function(e){for(var t=65535,i=65535,a=e.length,r=Math.floor(a/2),n=0;r;){var s=r>=359?359:r;r-=s;do{t+=e[n++]<<8,i+=t+=e[n++]}while(--s);t=(65535&t)+(t>>>16),i=(65535&i)+(i>>>16)}return 1&a&&(i+=t+=e[n]<<8),((i=(65535&i)+(i>>>16))<<16|(t=(65535&t)+(t>>>16)))>>>0},readHeaderInfo:function(e,t){var i=t.ptr,a=new Uint8Array(e,i,6),r={};if(r.fileIdentifierString=String.fromCharCode.apply(null,a),0!==r.fileIdentifierString.lastIndexOf("Lerc2",0))throw"Unexpected file identifier string (expect Lerc2 ): "+r.fileIdentifierString;i+=6;var n,s=new DataView(e,i,8),l=s.getInt32(0,!0);if(r.fileVersion=l,i+=4,l>=3&&(r.checksum=s.getUint32(4,!0),i+=4),s=new DataView(e,i,12),r.height=s.getUint32(0,!0),r.width=s.getUint32(4,!0),i+=8,l>=4?(r.numDims=s.getUint32(8,!0),i+=4):r.numDims=1,s=new DataView(e,i,40),r.numValidPixel=s.getUint32(0,!0),r.microBlockSize=s.getInt32(4,!0),r.blobSize=s.getInt32(8,!0),r.imageType=s.getInt32(12,!0),r.maxZError=s.getFloat64(16,!0),r.zMin=s.getFloat64(24,!0),r.zMax=s.getFloat64(32,!0),i+=40,t.headerInfo=r,t.ptr=i,l>=3&&(n=l>=4?52:48,this.computeChecksumFletcher32(new Uint8Array(e,i-n,r.blobSize-14))!==r.checksum))throw"Checksum failed.";return!0},checkMinMaxRanges:function(e,t){var i=t.headerInfo,a=this.getDataTypeArray(i.imageType),r=i.numDims*this.getDataTypeSize(i.imageType),n=this.readSubArray(e,t.ptr,a,r),s=this.readSubArray(e,t.ptr+r,a,r);t.ptr+=2*r;var l,o=!0;for(l=0;l<i.numDims;l++)if(n[l]!==s[l]){o=!1;break}return i.minValues=n,i.maxValues=s,o},readSubArray:function(e,t,i,a){var r;if(i===Uint8Array)r=new Uint8Array(e,t,a);else{var n=new ArrayBuffer(a);new Uint8Array(n).set(new Uint8Array(e,t,a)),r=new i(n)}return r},readMask:function(e,t){var i,a,r=t.ptr,n=t.headerInfo,s=n.width*n.height,l=n.numValidPixel,o=new DataView(e,r,4),f={};if(f.numBytes=o.getUint32(0,!0),r+=4,(0===l||s===l)&&0!==f.numBytes)throw"invalid mask";if(0===l)i=new Uint8Array(Math.ceil(s/8)),f.bitset=i,a=new Uint8Array(s),t.pixels.resultMask=a,r+=f.numBytes;else if(f.numBytes>0){i=new Uint8Array(Math.ceil(s/8));var u=(o=new DataView(e,r,f.numBytes)).getInt16(0,!0),c=2,h=0,d=0;do{if(u>0)for(;u--;)i[h++]=o.getUint8(c++);else for(d=o.getUint8(c++),u=-u;u--;)i[h++]=d;u=o.getInt16(c,!0),c+=2}while(c<f.numBytes);if(-32768!==u||h<i.length)throw"Unexpected end of mask RLE encoding";a=new Uint8Array(s);var m=0,g=0;for(g=0;g<s;g++)7&g?(m=i[g>>3],m<<=7&g):m=i[g>>3],128&m&&(a[g]=1);t.pixels.resultMask=a,f.bitset=i,r+=f.numBytes}return t.ptr=r,t.mask=f,!0},readDataOneSweep:function(e,t,i){var a,r=t.ptr,n=t.headerInfo,s=n.numDims,l=n.width*n.height,o=n.imageType,f=n.numValidPixel*z.getDataTypeSize(o)*s,u=t.pixels.resultMask;if(i===Uint8Array)a=new Uint8Array(e,r,f);else{var c=new ArrayBuffer(f);new Uint8Array(c).set(new Uint8Array(e,r,f)),a=new i(c)}if(a.length===l*s)t.pixels.resultPixels=a;else{t.pixels.resultPixels=new i(l*s);var h=0,d=0,m=0,g=0;if(s>1)for(m=0;m<s;m++)for(g=m*l,d=0;d<l;d++)u[d]&&(t.pixels.resultPixels[g+d]=a[h++]);else for(d=0;d<l;d++)u[d]&&(t.pixels.resultPixels[d]=a[h++])}return r+=f,t.ptr=r,!0},readHuffmanTree:function(e,t){var i=this.HUFFMAN_LUT_BITS_MAX,a=new DataView(e,t.ptr,16);if(t.ptr+=16,a.getInt32(0,!0)<2)throw"unsupported Huffman version";var r=a.getInt32(4,!0),n=a.getInt32(8,!0),s=a.getInt32(12,!0);if(n>=s)return!1;var l=new Uint32Array(s-n);z.decodeBits(e,t,l);var o,f,u,c,h=[];for(o=n;o<s;o++)h[f=o-(o<r?0:r)]={first:l[o-n],second:null};var d=e.byteLength-t.ptr,m=Math.ceil(d/4),g=new ArrayBuffer(4*m);new Uint8Array(g).set(new Uint8Array(e,t.ptr,d));var p,w=new Uint32Array(g),x=0,k=0;for(p=w[0],o=n;o<s;o++)(c=h[f=o-(o<r?0:r)].first)>0&&(h[f].second=p<<x>>>32-c,32-x>=c?32===(x+=c)&&(x=0,p=w[++k]):(x+=c-32,p=w[++k],h[f].second|=p>>>32-x));var y=0,b=0,I=new R;for(o=0;o<h.length;o++)void 0!==h[o]&&(y=Math.max(y,h[o].first));b=y>=i?i:y,y>=30&&console.log("WARning, large NUM LUT BITS IS "+y);var v,U,T,M,A,V=[];for(o=n;o<s;o++)if((c=h[f=o-(o<r?0:r)].first)>0)if(v=[c,f],c<=b)for(U=h[f].second<<b-c,T=1<<b-c,u=0;u<T;u++)V[U|u]=v;else for(U=h[f].second,A=I,M=c-1;M>=0;M--)U>>>M&1?(A.right||(A.right=new R),A=A.right):(A.left||(A.left=new R),A=A.left),0===M&&!A.val&&(A.val=v[1]);return{decodeLut:V,numBitsLUTQick:b,numBitsLUT:y,tree:I,stuffedData:w,srcPtr:k,bitPos:x}},readHuffman:function(e,t,i){var a,r,n,s,l,o,f,u,c,h=t.headerInfo,d=h.numDims,m=t.headerInfo.height,g=t.headerInfo.width,p=g*m,w=this.readHuffmanTree(e,t),x=w.decodeLut,k=w.tree,y=w.stuffedData,b=w.srcPtr,I=w.bitPos,v=w.numBitsLUTQick,U=w.numBitsLUT,T=0===t.headerInfo.imageType?128:0,M=t.pixels.resultMask,A=0;I>0&&(b++,I=0);var V,D=y[b],B=1===t.encodeMode,S=new i(p*d),P=S;for(V=0;V<h.numDims;V++){if(d>1&&(P=new i(S.buffer,p*V,p),A=0),t.headerInfo.numValidPixel===g*m)for(u=0,o=0;o<m;o++)for(f=0;f<g;f++,u++){if(r=0,l=s=D<<I>>>32-v,32-I<v&&(l=s|=y[b+1]>>>64-I-v),x[l])r=x[l][1],I+=x[l][0];else for(l=s=D<<I>>>32-U,32-I<U&&(l=s|=y[b+1]>>>64-I-U),a=k,c=0;c<U;c++)if(!(a=s>>>U-c-1&1?a.right:a.left).left&&!a.right){r=a.val,I=I+c+1;break}I>=32&&(I-=32,D=y[++b]),n=r-T,B?(n+=f>0?A:o>0?P[u-g]:A,n&=255,P[u]=n,A=n):P[u]=n}else for(u=0,o=0;o<m;o++)for(f=0;f<g;f++,u++)if(M[u]){if(r=0,l=s=D<<I>>>32-v,32-I<v&&(l=s|=y[b+1]>>>64-I-v),x[l])r=x[l][1],I+=x[l][0];else for(l=s=D<<I>>>32-U,32-I<U&&(l=s|=y[b+1]>>>64-I-U),a=k,c=0;c<U;c++)if(!(a=s>>>U-c-1&1?a.right:a.left).left&&!a.right){r=a.val,I=I+c+1;break}I>=32&&(I-=32,D=y[++b]),n=r-T,B?(f>0&&M[u-1]?n+=A:o>0&&M[u-g]?n+=P[u-g]:n+=A,n&=255,P[u]=n,A=n):P[u]=n}t.ptr=t.ptr+4*(b+1)+(I>0?4:0)}t.pixels.resultPixels=S},decodeBits:function(e,t,i,a,r){var n=t.headerInfo,s=n.fileVersion,l=0,o=e.byteLength-t.ptr>=5?5:e.byteLength-t.ptr,f=new DataView(e,t.ptr,o),u=f.getUint8(0);l++;var c=u>>6,h=0===c?4:3-c,d=(32&u)>0,m=31&u,g=0;if(1===h)g=f.getUint8(l),l++;else if(2===h)g=f.getUint16(l,!0),l+=2;else{if(4!==h)throw"Invalid valid pixel count type";g=f.getUint32(l,!0),l+=4}var p,w,x,k,y,b,I,v,U,T=2*n.maxZError,M=n.numDims>1?n.maxValues[r]:n.zMax;if(d){for(t.counter.lut++,v=f.getUint8(l),l++,k=Math.ceil((v-1)*m/8),y=Math.ceil(k/4),w=new ArrayBuffer(4*y),x=new Uint8Array(w),t.ptr+=l,x.set(new Uint8Array(e,t.ptr,k)),I=new Uint32Array(w),t.ptr+=k,U=0;v-1>>>U;)U++;k=Math.ceil(g*U/8),y=Math.ceil(k/4),w=new ArrayBuffer(4*y),(x=new Uint8Array(w)).set(new Uint8Array(e,t.ptr,k)),p=new Uint32Array(w),t.ptr+=k,b=s>=3?C(I,m,v-1,a,T,M):F(I,m,v-1,a,T,M),s>=3?L(p,i,U,g,b):E(p,i,U,g,b)}else t.counter.bitstuffer++,U=m,t.ptr+=l,U>0&&(k=Math.ceil(g*U/8),y=Math.ceil(k/4),w=new ArrayBuffer(4*y),(x=new Uint8Array(w)).set(new Uint8Array(e,t.ptr,k)),p=new Uint32Array(w),t.ptr+=k,s>=3?null===a?O(p,i,U,g):L(p,i,U,g,!1,a,T,M):null===a?N(p,i,U,g):E(p,i,U,g,!1,a,T,M))},readTiles:function(e,t,i){var a=t.headerInfo,r=a.width,n=a.height,s=a.microBlockSize,l=a.imageType,o=z.getDataTypeSize(l),f=Math.ceil(r/s),u=Math.ceil(n/s);t.pixels.numBlocksY=u,t.pixels.numBlocksX=f,t.pixels.ptr=0;var c,h,d,m,g,p,w,x,k=0,y=0,b=0,I=0,v=0,U=0,T=0,M=0,A=0,V=0,D=0,B=0,S=0,P=0,E=0,F=new i(s*s),L=n%s||s,C=r%s||s,N=a.numDims,O=t.pixels.resultMask,R=t.pixels.resultPixels;for(b=0;b<u;b++)for(v=b!==u-1?s:L,I=0;I<f;I++)for(V=b*r*s+I*s,D=r-(U=I!==f-1?s:C),x=0;x<N;x++){if(N>1&&(R=new i(t.pixels.resultPixels.buffer,r*n*x*o,r*n)),T=e.byteLength-t.ptr,h={},E=0,E++,A=(M=(c=new DataView(e,t.ptr,Math.min(10,T))).getUint8(0))>>6&255,(M>>2&15)!=(I*s>>3&15))throw"integrity issue";if((g=3&M)>3)throw t.ptr+=E,"Invalid block encoding ("+g+")";if(2!==g)if(0===g){if(t.counter.uncompressed++,t.ptr+=E,B=(B=v*U*o)<(S=e.byteLength-t.ptr)?B:S,d=new ArrayBuffer(B%o==0?B:B+o-B%o),new Uint8Array(d).set(new Uint8Array(e,t.ptr,B)),m=new i(d),P=0,O)for(k=0;k<v;k++){for(y=0;y<U;y++)O[V]&&(R[V]=m[P++]),V++;V+=D}else for(k=0;k<v;k++){for(y=0;y<U;y++)R[V++]=m[P++];V+=D}t.ptr+=P*o}else if(p=z.getDataTypeUsed(l,A),w=z.getOnePixel(h,E,p,c),E+=z.getDataTypeSize(p),3===g)if(t.ptr+=E,t.counter.constantoffset++,O)for(k=0;k<v;k++){for(y=0;y<U;y++)O[V]&&(R[V]=w),V++;V+=D}else for(k=0;k<v;k++){for(y=0;y<U;y++)R[V++]=w;V+=D}else if(t.ptr+=E,z.decodeBits(e,t,F,w,x),E=0,O)for(k=0;k<v;k++){for(y=0;y<U;y++)O[V]&&(R[V]=F[E++]),V++;V+=D}else for(k=0;k<v;k++){for(y=0;y<U;y++)R[V++]=F[E++];V+=D}else t.counter.constant++,t.ptr+=E}},formatFileInfo:function(e){return{fileIdentifierString:e.headerInfo.fileIdentifierString,fileVersion:e.headerInfo.fileVersion,imageType:e.headerInfo.imageType,height:e.headerInfo.height,width:e.headerInfo.width,numValidPixel:e.headerInfo.numValidPixel,microBlockSize:e.headerInfo.microBlockSize,blobSize:e.headerInfo.blobSize,maxZError:e.headerInfo.maxZError,pixelType:z.getPixelType(e.headerInfo.imageType),eofOffset:e.eofOffset,mask:e.mask?{numBytes:e.mask.numBytes}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,maxValue:e.headerInfo.zMax,minValue:e.headerInfo.zMin,noDataValue:e.noDataValue}}},constructConstantSurface:function(e){var t=e.headerInfo.zMax,i=e.headerInfo.numDims,a=e.headerInfo.height*e.headerInfo.width,r=a*i,n=0,s=0,l=0,o=e.pixels.resultMask;if(o)if(i>1)for(n=0;n<i;n++)for(l=n*a,s=0;s<a;s++)o[s]&&(e.pixels.resultPixels[l+s]=t);else for(s=0;s<a;s++)o[s]&&(e.pixels.resultPixels[s]=t);else if(e.pixels.resultPixels.fill)e.pixels.resultPixels.fill(t);else for(s=0;s<r;s++)e.pixels.resultPixels[s]=t},getDataTypeArray:function(e){var t;switch(e){case 0:t=Int8Array;break;case 1:t=Uint8Array;break;case 2:t=Int16Array;break;case 3:t=Uint16Array;break;case 4:t=Int32Array;break;case 5:t=Uint32Array;break;case 6:default:t=Float32Array;break;case 7:t=Float64Array}return t},getPixelType:function(e){var t;switch(e){case 0:t="S8";break;case 1:t="U8";break;case 2:t="S16";break;case 3:t="U16";break;case 4:t="S32";break;case 5:t="U32";break;case 6:default:t="F32";break;case 7:t="F64"}return t},isValidPixelValue:function(e,t){if(null===t)return!1;var i;switch(e){case 0:i=t>=-128&&t<=127;break;case 1:i=t>=0&&t<=255;break;case 2:i=t>=-32768&&t<=32767;break;case 3:i=t>=0&&t<=65536;break;case 4:i=t>=-2147483648&&t<=2147483647;break;case 5:i=t>=0&&t<=4294967296;break;case 6:i=t>=-34027999387901484e22&&t<=34027999387901484e22;break;case 7:i=t>=5e-324&&t<=17976931348623157e292;break;default:i=!1}return i},getDataTypeSize:function(e){var t=0;switch(e){case 0:case 1:t=1;break;case 2:case 3:t=2;break;case 4:case 5:case 6:t=4;break;case 7:t=8;break;default:t=e}return t},getDataTypeUsed:function(e,t){var i=e;switch(e){case 2:case 4:i=e-t;break;case 3:case 5:i=e-2*t;break;case 6:i=0===t?e:1===t?2:1;break;case 7:i=0===t?e:e-2*t+1;break;default:i=e}return i},getOnePixel:function(e,t,i,a){var r=0;switch(i){case 0:r=a.getInt8(t);break;case 1:r=a.getUint8(t);break;case 2:r=a.getInt16(t,!0);break;case 3:r=a.getUint16(t,!0);break;case 4:r=a.getInt32(t,!0);break;case 5:r=a.getUInt32(t,!0);break;case 6:r=a.getFloat32(t,!0);break;case 7:r=a.getFloat64(t,!0);break;default:throw"the decoder does not understand this pixel type"}return r}},R=function(e,t,i){this.val=e,this.left=t,this.right=i},H={decode:function(e,t){var i=(t=t||{}).noDataValue,a=0,r={};if(r.ptr=t.inputOffset||0,r.pixels={},z.readHeaderInfo(e,r)){var n=r.headerInfo,s=n.fileVersion,l=z.getDataTypeArray(n.imageType);z.readMask(e,r),n.numValidPixel!==n.width*n.height&&!r.pixels.resultMask&&(r.pixels.resultMask=t.maskData);var o,f=n.width*n.height;if(r.pixels.resultPixels=new l(f*n.numDims),r.counter={onesweep:0,uncompressed:0,lut:0,bitstuffer:0,constant:0,constantoffset:0},0!==n.numValidPixel)if(n.zMax===n.zMin)z.constructConstantSurface(r);else if(s>=4&&z.checkMinMaxRanges(e,r))z.constructConstantSurface(r);else{var u=new DataView(e,r.ptr,2),c=u.getUint8(0);if(r.ptr++,c)z.readDataOneSweep(e,r,l);else if(s>1&&n.imageType<=1&&Math.abs(n.maxZError-.5)<1e-5){var h=u.getUint8(1);if(r.ptr++,r.encodeMode=h,h>2||s<4&&h>1)throw"Invalid Huffman flag "+h;h?z.readHuffman(e,r,l):z.readTiles(e,r,l)}else z.readTiles(e,r,l)}r.eofOffset=r.ptr,t.inputOffset?(o=r.headerInfo.blobSize+t.inputOffset-r.ptr,Math.abs(o)>=1&&(r.eofOffset=t.inputOffset+r.headerInfo.blobSize)):(o=r.headerInfo.blobSize-r.ptr,Math.abs(o)>=1&&(r.eofOffset=r.headerInfo.blobSize));var d={width:n.width,height:n.height,pixelData:r.pixels.resultPixels,minValue:n.zMin,maxValue:n.zMax,validPixelCount:n.numValidPixel,dimCount:n.numDims,dimStats:{minValues:n.minValues,maxValues:n.maxValues},maskData:r.pixels.resultMask};if(r.pixels.resultMask&&z.isValidPixelValue(n.imageType,i)){var m=r.pixels.resultMask;for(a=0;a<f;a++)m[a]||(d.pixelData[a]=i);d.noDataValue=i}return r.noDataValue=i,t.returnFileInfo&&(d.fileInfo=z.formatFileInfo(r)),d}},getBandCount:function(e){for(var t=0,i=0,a={ptr:0,pixels:{}};i<e.byteLength-58;)z.readHeaderInfo(e,a),i+=a.headerInfo.blobSize,t++,a.ptr=i;return t}},Y=H,X=function(){var e=new ArrayBuffer(4),t=new Uint8Array(e);return new Uint32Array(e)[0]=1,1===t[0]}(),W={decode:function(e,t){if(!X)throw"Big endian system is not supported.";var i,a,r,n,s,l=(t=t||{}).inputOffset||0,o=new Uint8Array(e,l,10),f=String.fromCharCode.apply(null,o);if("CntZImage"===f.trim())i=_,a=1;else{if("Lerc2"!==f.substring(0,5))throw"Unexpected file identifier string: "+f;i=Y,a=2}for(var u,c,h,d=0,m=e.byteLength-10,g=[],p={width:0,height:0,pixels:[],pixelType:t.pixelType,mask:null,statistics:[]};l<m;){var w=i.decode(e,{inputOffset:l,encodedMaskData:u,maskData:h,returnMask:0===d,returnEncodedMask:0===d,returnFileInfo:!0,pixelType:t.pixelType||null,noDataValue:t.noDataValue||null});l=w.fileInfo.eofOffset,0===d&&(u=w.encodedMaskData,h=w.maskData,p.width=w.width,p.height=w.height,p.dimCount=w.dimCount||1,p.pixelType=w.pixelType||w.fileInfo.pixelType,p.mask=w.maskData),a>1&&w.fileInfo.mask&&w.fileInfo.mask.numBytes>0&&g.push(w.maskData),d++,p.pixels.push(w.pixelData),p.statistics.push({minValue:w.minValue,maxValue:w.maxValue,noDataValue:w.noDataValue,dimStats:w.dimStats})}if(a>1&&g.length>1){for(s=p.width*p.height,p.bandMasks=g,(h=new Uint8Array(s)).set(g[0]),r=1;r<g.length;r++)for(c=g[r],n=0;n<s;n++)h[n]=h[n]&c[n];p.maskData=h}return p}},Z.Lerc=W;var G=Z.Lerc;return m((function(t,i){if(t.encoding===A.LERC){var a;try{a=G.decode(t.heightmap)}catch(e){throw new d.t(e)}if(a.statistics[0].minValue===Number.MAX_VALUE)throw new d.t("Invalid tile data");t.heightmap=a.pixels[0],t.width=a.width,t.height=a.height}t.ellipsoid=e.n.clone(t.ellipsoid),t.rectangle=e.s.clone(t.rectangle);var r=V.computeVertices(t),n=r.vertices;return i.push(n.buffer),{vertices:n.buffer,numberOfAttributes:r.encoding.getStride(),minimumHeight:r.minimumHeight,maximumHeight:r.maximumHeight,gridWidth:t.width,gridHeight:t.height,boundingSphere3D:r.boundingSphere3D,orientedBoundingBox:r.orientedBoundingBox,occludeePointInScaledSpace:r.occludeePointInScaledSpace,encoding:r.encoding,westIndicesSouthToNorth:r.westIndicesSouthToNorth,southIndicesEastToWest:r.southIndicesEastToWest,eastIndicesNorthToSouth:r.eastIndicesNorthToSouth,northIndicesWestToEast:r.northIndicesWestToEast}}))}));
