define(["exports","./when-515d5295"],(function(e,r){"use strict";var n,t,u,s,l,o,i,c,a,f,p,d,m,A,v,g,y,F,b,h,E,x={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},w={};function C(e){for(var r=e.split("."),n=0,t=r.length;n<t;++n)r[n]=parseInt(r[n],10);return r}function S(){if(!r.t(u)&&(u=!1,!V())){var e=/ Chrome\/([\.0-9]+)/.exec(t.userAgent);null!==e&&(u=!0,s=C(e[1]))}return u}function I(){if(!r.t(l)&&(l=!1,!S()&&!V()&&/ Safari\/[\.0-9]+/.test(t.userAgent))){var e=/ Version\/([\.0-9]+)/.exec(t.userAgent);null!==e&&(l=!0,o=C(e[1]))}return l}function W(){if(!r.t(i)){i=!1;var e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(t.userAgent);null!==e&&(i=!0,(c=C(e[1])).isNightly=!!e[2])}return i}function q(){var e;r.t(a)||(a=!1,"Microsoft Internet Explorer"===t.appName?null!==(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(t.userAgent))&&(a=!0,f=C(e[1])):"Netscape"===t.appName&&(null!==(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(t.userAgent))&&(a=!0,f=C(e[1]))));return a}function V(){if(!r.t(p)){p=!1;var e=/ Edge\/([\.0-9]+)/.exec(t.userAgent);null!==e&&(p=!0,d=C(e[1]))}return p}function P(){if(!r.t(m)){m=!1;var e=/Firefox\/([\.0-9]+)/.exec(t.userAgent);null!==e&&(m=!0,A=C(e[1]))}return m}function k(){if(!r.t(b)){var e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");var n=e.style.imageRendering;(b=r.t(n)&&""!==n)&&(F=n)}return b}function N(){if(r.t(E))return E.promise;E=r.c.defer(),V()&&(h=!1,E.resolve(h));var e=new Image;return e.onload=function(){h=e.width>0&&e.height>0,E.resolve(h)},e.onerror=function(){h=!1,E.resolve(h)},e.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",E.promise}Object.defineProperties(w,{element:{get:function(){if(w.supportsFullscreen())return document[x.fullscreenElement]}},changeEventName:{get:function(){if(w.supportsFullscreen())return x.fullscreenchange}},errorEventName:{get:function(){if(w.supportsFullscreen())return x.fullscreenerror}},enabled:{get:function(){if(w.supportsFullscreen())return document[x.fullscreenEnabled]}},fullscreen:{get:function(){if(w.supportsFullscreen())return null!==w.element}}}),w.supportsFullscreen=function(){if(r.t(n))return n;n=!1;var e=document.body;if("function"==typeof e.requestFullscreen)return x.requestFullscreen="requestFullscreen",x.exitFullscreen="exitFullscreen",x.fullscreenEnabled="fullscreenEnabled",x.fullscreenElement="fullscreenElement",x.fullscreenchange="fullscreenchange",x.fullscreenerror="fullscreenerror",n=!0;for(var t,u=["webkit","moz","o","ms","khtml"],s=0,l=u.length;s<l;++s){var o=u[s];"function"==typeof e[t=o+"RequestFullscreen"]?(x.requestFullscreen=t,n=!0):"function"==typeof e[t=o+"RequestFullScreen"]&&(x.requestFullscreen=t,n=!0),t=o+"ExitFullscreen","function"==typeof document[t]?x.exitFullscreen=t:(t=o+"CancelFullScreen","function"==typeof document[t]&&(x.exitFullscreen=t)),t=o+"FullscreenEnabled",void 0!==document[t]?x.fullscreenEnabled=t:(t=o+"FullScreenEnabled",void 0!==document[t]&&(x.fullscreenEnabled=t)),t=o+"FullscreenElement",void 0!==document[t]?x.fullscreenElement=t:(t=o+"FullScreenElement",void 0!==document[t]&&(x.fullscreenElement=t)),t=o+"fullscreenchange",void 0!==document["on"+t]&&("ms"===o&&(t="MSFullscreenChange"),x.fullscreenchange=t),t=o+"fullscreenerror",void 0!==document["on"+t]&&("ms"===o&&(t="MSFullscreenError"),x.fullscreenerror=t)}return n},w.requestFullscreen=function(e,r){!w.supportsFullscreen()||e[x.requestFullscreen]({vrDisplay:r})},w.exitFullscreen=function(){!w.supportsFullscreen()||document[x.exitFullscreen]()},w._names=x,t=typeof navigator<"u"?navigator:{};var R=[];typeof ArrayBuffer<"u"&&(R.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),typeof Uint8ClampedArray<"u"&&R.push(Uint8ClampedArray),typeof CanvasPixelArray<"u"&&R.push(CanvasPixelArray));var U={isChrome:S,chromeVersion:function(){return S()&&s},isSafari:I,safariVersion:function(){return I()&&o},isWebkit:W,webkitVersion:function(){return W()&&c},isInternetExplorer:q,internetExplorerVersion:function(){return q()&&f},isEdge:V,edgeVersion:function(){return V()&&d},isFirefox:P,firefoxVersion:function(){return P()&&A},isWindows:function(){return r.t(v)||(v=/Windows/i.test(t.appVersion)),v},isNodeJs:function(){return r.t(g)||(g="object"==typeof process&&"[object process]"===Object.prototype.toString.call(process)),g},hardwareConcurrency:r.e(t.hardwareConcurrency,3),supportsPointerEvents:function(){return r.t(y)||(y=!P()&&typeof PointerEvent<"u"&&(!r.t(t.pointerEnabled)||t.pointerEnabled)),y},supportsImageRenderingPixelated:k,supportsWebP:N,supportsWebPSync:function(){return r.t(E)||N(),h},imageRenderingValue:function(){return k()?F:void 0},typedArrayTypes:R,isPCBroswer:function(){var e=window.navigator.userAgent.toLowerCase(),r="ipad"==e.match(/ipad/i),n="iphone os"==e.match(/iphone os/i),t="midp"==e.match(/midp/i),u="rv:*******"==e.match(/rv:*******/i),s="ucweb"==e.match(/ucweb/i),l="android"==e.match(/android/i),o="windows ce"==e.match(/windows ce/i),i="windows mobile"==e.match(/windows mobile/i);return!(r||n||t||u||s||l||o||i)}};U.supportsFullscreen=function(){return w.supportsFullscreen()},U.supportsTypedArrays=function(){return typeof ArrayBuffer<"u"},U.supportsWebWorkers=function(){return typeof Worker<"u"},U.supportsWebAssembly=function(){return typeof WebAssembly<"u"&&!U.isEdge()},U.supportsOffscreenCanvas=function(){return typeof OffscreenCanvas<"u"&&!U.isEdge()},e.o=U}));
