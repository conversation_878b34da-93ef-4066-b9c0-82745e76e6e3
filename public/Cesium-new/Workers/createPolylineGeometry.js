define(["./when-515d5295","./Rectangle-e170be8b","./ArcType-98a7a011","./arrayRemoveDuplicates-a4c6347e","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Color-39e7bd91","./ComponentDatatype-d430c7f7","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./IndexDatatype-eefd5922","./Math-5e38123d","./PolylinePipeline-bf1462fc","./PrimitiveType-b38a4004","./VertexFormat-e844760b","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./FeatureDetection-7fae0d5a","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./Cartesian4-034d54d5","./EllipsoidGeodesic-e5406761","./EllipsoidRhumbLine-f50fdea6","./IntersectionTests-5fa33dbd","./Plane-92c15089"],(function(e,t,r,a,n,o,i,l,s,c,p,d,h,u,f,y,v,m,w,_,g,b,D,E,A,P,x){"use strict";var T=[];function k(e,t,r,a,n){var o=T;o.length=n;var l,s=r.red,c=r.green,p=r.blue,d=r.alpha,h=a.red,u=a.green,f=a.blue,y=a.alpha;if(i.e.equals(r,a)){for(l=0;l<n;l++)o[l]=i.e.clone(r);return o}var v=(h-s)/n,m=(u-c)/n,w=(f-p)/n,_=(y-d)/n;for(l=0;l<n;l++)o[l]=new i.e(s+l*v,c+l*m,p+l*w,d+l*_);return o}function L(a){var l=(a=e.e(a,e.e.EMPTY_OBJECT)).positions,c=a.colors,p=e.e(a.width,1),d=e.e(a.hMax,-1),u=e.e(a.colorsPerVertex,!1);if(!e.t(l)||l.length<2)throw new s.t("At least two positions are required.");if("number"!=typeof p)throw new s.t("width must be a number");if(e.t(c)&&(u&&c.length<l.length||!u&&c.length<l.length-1))throw new s.t("colors has an invalid length.");this._positions=l,this._colors=c,this._width=p,this._hMax=d,this._colorsPerVertex=u,this._dist=a.dist,this._period=a.period,this._vertexFormat=y.n.clone(e.e(a.vertexFormat,y.n.DEFAULT)),this._followSurface=e.e(a.followSurface,!0),e.t(a.followSurface)&&(function(t,r){if(!e.t(t)||!e.t(r))throw new s.t("identifier and message are required.");n.e(t,r)}("PolylineGeometry.followSurface","PolylineGeometry.followSurface is deprecated and will be removed in Cesium 1.55. Use PolylineGeometry.arcType instead."),a.arcType=a.followSurface?r.D.GEODESIC:r.D.NONE),this._arcType=e.e(a.arcType,r.D.GEODESIC),this._followSurface=this._arcType!==r.D.NONE,this._granularity=e.e(a.granularity,h.n.RADIANS_PER_DEGREE),this._ellipsoid=t.n.clone(e.e(a.ellipsoid,t.n.WGS84)),this._workerName="createPolylineGeometry";var f=1+l.length*o.a.packedLength;f+=e.t(c)?1+c.length*i.e.packedLength:1,this.packedLength=f+t.n.packedLength+y.n.packedLength+4+2}L.pack=function(r,a,n){if(!e.t(r))throw new s.t("value is required");if(!e.t(a))throw new s.t("array is required");n=e.e(n,0);var l,c=r._positions,p=c.length;for(a[n++]=p,l=0;l<p;++l,n+=o.a.packedLength)o.a.pack(c[l],a,n);var d=r._colors;for(p=e.t(d)?d.length:0,a[n++]=p,l=0;l<p;++l,n+=i.e.packedLength)i.e.pack(d[l],a,n);return t.n.pack(r._ellipsoid,a,n),n+=t.n.packedLength,y.n.pack(r._vertexFormat,a,n),n+=y.n.packedLength,a[n++]=r._width,a[n++]=r._colorsPerVertex?1:0,a[n++]=r._arcType,a[n++]=r._granularity,a[n++]=r._hMax,a[n++]=r._dist,a[n]=r._period,a};var C=t.n.clone(t.n.UNIT_SPHERE),S=new y.n,F={positions:void 0,colors:void 0,ellipsoid:C,vertexFormat:S,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};L.unpack=function(r,a,n){if(!e.t(r))throw new s.t("array is required");a=e.e(a,0);var l,c=r[a++],p=new Array(c);for(l=0;l<c;++l,a+=o.a.packedLength)p[l]=o.a.unpack(r,a);var d=(c=r[a++])>0?new Array(c):void 0;for(l=0;l<c;++l,a+=i.e.packedLength)d[l]=i.e.unpack(r,a);var h=t.n.unpack(r,a,C);a+=t.n.packedLength;var u=y.n.unpack(r,a,S);a+=y.n.packedLength;var f=r[a++],v=1===r[a++],m=r[a++],w=r[a++],_=r[a++],g=1==r[a++],b=r[a];return e.t(n)?(n._positions=p,n._colors=d,n._ellipsoid=t.n.clone(h,n._ellipsoid),n._vertexFormat=y.n.clone(u,n._vertexFormat),n._width=f,n._colorsPerVertex=v,n._arcType=m,n._granularity=w,n._hMax=_,n._dist=g,n._period=b,n):(F.positions=p,F.colors=d,F.width=f,F.colorsPerVertex=v,F.arcType=m,F.granularity=w,F.hMax=_,F.dist=g,F.period=b,new L(F))};var G=new o.a,O=new o.a,I=new o.a,M=new o.a;return L.createGeometry=function(t){var s,y,v,m=t._width,w=t._hMax,_=t._vertexFormat,g=t._colors,b=t._colorsPerVertex,D=t._arcType,E=t._granularity,A=t._ellipsoid,P=t._dist,x=t._period,L=a.u(t._positions,o.a.equalsEpsilon),C=L.length;if(!(C<2||m<=0)){if(D===r.D.GEODESIC||D===r.D.RHUMB){var S,F;D===r.D.GEODESIC?(S=h.n.chordLength(E,A.maximumRadius),F=u.v.numberOfPoints):(S=E,F=u.v.numberOfPointsRhumbLine);var R=u.v.extractHeights(L,A);if(e.t(g)){var N=1;for(s=0;s<C-1;++s)N+=F(L[s],L[s+1],S);var B=new Array(N),U=0;for(s=0;s<C-1;++s){var V=L[s],q=L[s+1],H=g[s],W=F(V,q,S);if(b&&s<N){var Y=k(0,0,H,g[s+1],W),z=Y.length;for(y=0;y<z;++y)B[U++]=Y[y]}else for(y=0;y<W;++y)B[U++]=i.e.clone(H)}B[U]=i.e.clone(g[g.length-1]),g=B,T.length=0}L=D===r.D.GEODESIC?u.v.generateCartesianArc({positions:L,minDistance:S,ellipsoid:A,height:R,hMax:w}):u.v.generateCartesianRhumbArc({positions:L,granularity:S,ellipsoid:A,height:R})}var J,j=4*(C=L.length)-4,K=new Float64Array(3*j),Q=new Float64Array(3*j),X=new Float64Array(3*j),Z=new Float32Array(2*j),$=_.st?new Float32Array(2*j):void 0,ee=e.t(g)?new Uint8Array(4*j):void 0,te=P?new Float32Array(3*j):void 0,re=0,ae=0,ne=0,oe=0,ie=0,le=0;for(y=0;y<C;++y){var se,ce;0===y?(J=G,o.a.subtract(L[0],L[1],J),o.a.add(L[0],J,J)):J=L[y-1],o.a.clone(J,I),o.a.clone(L[y],O),y===C-1?(J=G,o.a.subtract(L[C-1],L[C-2],J),o.a.add(L[C-1],J,J)):J=L[y+1],o.a.clone(J,M),e.t(ee)&&(se=0===y||b?g[y]:g[y-1],y!==C-1&&(ce=g[y]));var pe=y===C-1?2:4;for(v=0===y?2:0;v<pe;++v){o.a.pack(O,K,re),o.a.pack(I,Q,re),o.a.pack(M,X,re),re+=3;var de=v-2<0?-1:1,he=v%2*2-1,ue=he*y/C;if(Z[ae++]=w>0?ue:he,Z[ae++]=de*m,_.st&&($[ne++]=y/(C-1),$[ne++]=Math.max(Z[ae-2],0)),e.t(ee)){var fe=v<2?se:ce;ee[oe++]=i.e.floatToByte(fe.red),ee[oe++]=i.e.floatToByte(fe.green),ee[oe++]=i.e.floatToByte(fe.blue),ee[oe++]=i.e.floatToByte(fe.alpha)}P&&(te[3*ie]=le,ie++)}le+=o.a.distance(J,L[y])}if(P){var ye=le,ve=Math.random()*(x>0?x:ye);for(y=0;y<j;y++)te[3*y+1]=ye,te[3*y+2]=ve}var me=new p.t;me.position=new c.r({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:K}),me.prevPosition=new c.r({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:Q}),me.nextPosition=new c.r({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:X}),me.expandAndWidth=new c.r({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:Z}),_.st&&(me.st=new c.r({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:$})),e.t(ee)&&(me.color=new c.r({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:ee,normalize:!0})),P&&(me.dist=new c.r({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:te}));var we=d.IndexDatatype.createTypedArray(j,6*C-6),_e=0,ge=0,be=C-1;for(y=0;y<be;++y)we[ge++]=_e,we[ge++]=_e+2,we[ge++]=_e+1,we[ge++]=_e+1,we[ge++]=_e+2,we[ge++]=_e+3,_e+=4;return new c.T({attributes:me,indices:we,primitiveType:f._0x38df4a.TRIANGLES,boundingSphere:n.c.fromPoints(L),geometryType:c.Sr.POLYLINES})}},function(r,a){return e.t(a)&&(r=L.unpack(r,a)),r._ellipsoid=t.n.clone(r._ellipsoid),L.createGeometry(r)}}));
