define(["exports","./Cartographic-1bbcab04","./Math-5e38123d","./PrimitiveType-b38a4004","./GeometryAttribute-9bc31a7f"],(function(a,r,e,t,n){"use strict";var i={},o=new r.a,l=new r.a,y=new n.a,s=new t.r;function c(a,e,i,c,u,x,m,h,z,f){var v=a+e;r.a.multiplyByScalar(c,Math.cos(v),o),r.a.multiplyByScalar(i,Math.sin(v),l),r.a.add(o,l,o);var _=Math.cos(a);_*=_;var d=Math.sin(a);d*=d;var O=x/Math.sqrt(m*_+u*d)/h;return n.a.fromAxisAngle(o,O,y),t.r.fromQuaternion(y,s),t.r.multiplyByVector(s,z,f),r.a.normalize(f,f),r.a.multiplyByScalar(f,h,f),f}var u=new r.a,x=new r.a,m=new r.a,h=new r.a;i.raisePositionsToHeight=function(a,e,t){for(var n=e.ellipsoid,i=e.height,o=e.extrudedHeight,l=t?a.length/3*2:a.length/3,y=new Float64Array(3*l),s=a.length,c=t?s:0,z=0;z<s;z+=3){var f=z+1,v=z+2,_=r.a.fromArray(a,z,u);n.scaleToGeodeticSurface(_,_);var d=r.a.clone(_,x),O=n.geodeticSurfaceNormal(_,h),p=r.a.multiplyByScalar(O,i,m);r.a.add(_,p,_),t&&(r.a.multiplyByScalar(O,o,p),r.a.add(d,p,d),y[z+c]=d.x,y[f+c]=d.y,y[v+c]=d.z),y[z]=_.x,y[f]=_.y,y[v]=_.z}return y};var z=new r.a,f=new r.a,v=new r.a;i.computeEllipsePositions=function(a,t,n){var i=a.semiMinorAxis,o=a.semiMajorAxis,l=a.rotation,y=a.center,s=8*a.granularity,h=i*i,_=o*o,d=o*i,O=r.a.magnitude(y),p=r.a.normalize(y,z),P=r.a.cross(r.a.UNIT_Z,y,f);P=r.a.normalize(P,P);var w=r.a.cross(p,P,v),M=1+Math.ceil(e.n.PI_OVER_TWO/s),T=e.n.PI_OVER_TWO/(M-1),g=e.n.PI_OVER_TWO-M*T;g<0&&(M-=Math.ceil(Math.abs(g)/T));var I,A,E,V,R,W=t?new Array(3*(M*(M+2)*2)):void 0,b=0,S=u,B=x,N=4*M*3,G=N-1,H=0,j=n?new Array(N):void 0;for(S=c(g=e.n.PI_OVER_TWO,l,w,P,h,d,_,O,p,S),t&&(W[b++]=S.x,W[b++]=S.y,W[b++]=S.z),n&&(j[G--]=S.z,j[G--]=S.y,j[G--]=S.x),g=e.n.PI_OVER_TWO-T,I=1;I<M+1;++I){if(S=c(g,l,w,P,h,d,_,O,p,S),B=c(Math.PI-g,l,w,P,h,d,_,O,p,B),t){for(W[b++]=S.x,W[b++]=S.y,W[b++]=S.z,E=2*I+2,A=1;A<E-1;++A)V=A/(E-1),R=r.a.lerp(S,B,V,m),W[b++]=R.x,W[b++]=R.y,W[b++]=R.z;W[b++]=B.x,W[b++]=B.y,W[b++]=B.z}n&&(j[G--]=S.z,j[G--]=S.y,j[G--]=S.x,j[H++]=B.x,j[H++]=B.y,j[H++]=B.z),g=e.n.PI_OVER_TWO-(I+1)*T}for(I=M;I>1;--I){if(S=c(-(g=e.n.PI_OVER_TWO-(I-1)*T),l,w,P,h,d,_,O,p,S),B=c(g+Math.PI,l,w,P,h,d,_,O,p,B),t){for(W[b++]=S.x,W[b++]=S.y,W[b++]=S.z,E=2*(I-1)+2,A=1;A<E-1;++A)V=A/(E-1),R=r.a.lerp(S,B,V,m),W[b++]=R.x,W[b++]=R.y,W[b++]=R.z;W[b++]=B.x,W[b++]=B.y,W[b++]=B.z}n&&(j[G--]=S.z,j[G--]=S.y,j[G--]=S.x,j[H++]=B.x,j[H++]=B.y,j[H++]=B.z)}S=c(-(g=e.n.PI_OVER_TWO),l,w,P,h,d,_,O,p,S);var q={};return t&&(W[b++]=S.x,W[b++]=S.y,W[b++]=S.z,q.positions=W,q.numPts=M),n&&(j[G--]=S.z,j[G--]=S.y,j[G--]=S.x,q.outerPositions=j),q},a.N=i}));
