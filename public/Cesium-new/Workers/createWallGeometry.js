define(["./when-515d5295","./Rectangle-e170be8b","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./VertexFormat-e844760b","./WallGeometryLibrary-240d09b5","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5","./arrayRemoveDuplicates-a4c6347e","./PolylinePipeline-bf1462fc","./EllipsoidGeodesic-e5406761","./EllipsoidRhumbLine-f50fdea6","./IntersectionTests-5fa33dbd","./Plane-92c15089"],(function(e,t,n,a,i,o,r,s,m,p,l,u,c,h,d,g,v,y,f,w,b,_,A,x,k,C){"use strict";var L=new a.a,E=new a.a,F=new a.a,P=new a.a,H=new a.a,D=new a.a,T=new a.a,G=new a.a;function O(n){var i=(n=e.e(n,e.e.EMPTY_OBJECT)).positions,r=n.maximumHeights,s=n.minimumHeights;if(!e.t(i))throw new o.t("options.positions is required.");if(e.t(r)&&r.length!==i.length)throw new o.t("options.positions and options.maximumHeights must have the same length.");if(e.t(s)&&s.length!==i.length)throw new o.t("options.positions and options.minimumHeights must have the same length.");var m=e.e(n.vertexFormat,u.n.DEFAULT),l=e.e(n.granularity,p.n.RADIANS_PER_DEGREE),c=e.e(n.ellipsoid,t.n.WGS84);this._positions=i,this._minimumHeights=s,this._maximumHeights=r,this._vertexFormat=u.n.clone(m),this._granularity=l,this._ellipsoid=t.n.clone(c),this._enuCenter=n.enuCenter,this._workerName="createWallGeometry";var h=1+i.length*a.a.packedLength+2;e.t(s)&&(h+=s.length),e.t(r)&&(h+=r.length),this.packedLength=h+t.n.packedLength+u.n.packedLength+1,this.packedLength+=a.a.packedLength}O.pack=function(n,i,r){if(!e.t(n))throw new o.t("value is required");if(!e.t(i))throw new o.t("array is required");r=e.e(r,0);var s,m=n._positions,p=m.length;for(i[r++]=p,s=0;s<p;++s,r+=a.a.packedLength)a.a.pack(m[s],i,r);var l=n._minimumHeights;if(p=e.t(l)?l.length:0,i[r++]=p,e.t(l))for(s=0;s<p;++s)i[r++]=l[s];var c=n._maximumHeights;if(p=e.t(c)?c.length:0,i[r++]=p,e.t(c))for(s=0;s<p;++s)i[r++]=c[s];return t.n.pack(n._ellipsoid,i,r),r+=t.n.packedLength,u.n.pack(n._vertexFormat,i,r),r+=u.n.packedLength,i[r++]=n._granularity,e.t(n._enuCenter)?a.a.pack(n._enuCenter,i,r):a.a.pack(a.a.ZERO,i,r),i};var z=t.n.clone(t.n.UNIT_SPHERE),R=new u.n,I={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:z,vertexFormat:R,granularity:void 0,enuCenter:void 0};return O.unpack=function(n,i,r){if(!e.t(n))throw new o.t("array is required");i=e.e(i,0);var s,m,p,l=n[i++],c=new Array(l);for(s=0;s<l;++s,i+=a.a.packedLength)c[s]=a.a.unpack(n,i);if((l=n[i++])>0)for(m=new Array(l),s=0;s<l;++s)m[s]=n[i++];if((l=n[i++])>0)for(p=new Array(l),s=0;s<l;++s)p[s]=n[i++];var h=t.n.unpack(n,i,z);i+=t.n.packedLength;var d=u.n.unpack(n,i,R);i+=u.n.packedLength;var g=n[i++],v=a.a.unpack(n,i);return a.a.equals(v,a.a.ZERO)&&(v=void 0),e.t(r)?(r._positions=c,r._minimumHeights=m,r._maximumHeights=p,r._ellipsoid=t.n.clone(h,r._ellipsoid),r._vertexFormat=u.n.clone(d,r._vertexFormat),r._granularity=g,r._enuCenter=v,r):(I.positions=c,I.minimumHeights=m,I.maximumHeights=p,I.granularity=g,I.enuCenter=v,new O(I))},O.fromConstantHeights=function(t){var n=(t=e.e(t,e.e.EMPTY_OBJECT)).positions;if(!e.t(n))throw new o.t("options.positions is required.");var a,i,r=t.minimumHeight,s=t.maximumHeight,m=e.t(r),p=e.t(s);if(m||p){var l=n.length;a=m?new Array(l):void 0,i=p?new Array(l):void 0;for(var u=0;u<l;++u)m&&(a[u]=r),p&&(i[u]=s)}return new O({positions:n,maximumHeights:i,minimumHeights:a,ellipsoid:t.ellipsoid,vertexFormat:t.vertexFormat})},O.createGeometry=function(t){var o=t._positions,u=t._minimumHeights,h=t._maximumHeights,d=t._vertexFormat,g=t._granularity,v=t._ellipsoid,y=t._enuCenter,f=c.B.computePositions(v,o,h,u,g,!0,y);if(e.t(f.pos)){var w;e.t(y)&&(w=r.m.eastNorthUpToFixedFrame(y));var b,_=f.pos.bottomPositions,A=f.pos.topPositions,x=f.pos.numCorners,k=A.length,C=2*k,O=d.position?new Float64Array(C):void 0,z=d.normal?new Float32Array(C):void 0,R=d.tangent?new Float32Array(C):void 0,I=d.bitangent?new Float32Array(C):void 0,S=d.st?new Float32Array(C/3*2):void 0,q=0,N=0,B=0,U=0,M=0,W=G,Z=T,J=D,V=!0,Y=0,j=1/((k/=3)-o.length+1);for(b=0;b<k;++b){var K=3*b,Q=a.a.fromArray(A,K,L),X=a.a.fromArray(_,K,E);if(d.position&&(O[q++]=X.x,O[q++]=X.y,O[q++]=X.z,O[q++]=Q.x,O[q++]=Q.y,O[q++]=Q.z),d.st&&(S[M++]=Y,S[M++]=0,S[M++]=Y,S[M++]=1),d.normal||d.tangent||d.bitangent){var $,ee=a.a.clone(a.a.ZERO,H),te=v.scaleToGeodeticSurface(a.a.fromArray(A,K,E),E);if(b+1<k&&($=v.scaleToGeodeticSurface(a.a.fromArray(A,K+3,F),F),ee=a.a.fromArray(A,K+3,H)),V){var ne=a.a.subtract(ee,Q,P),ae=a.a.subtract(te,Q,L);W=a.a.normalize(a.a.cross(ae,ne,W),W),V=!1}a.a.equalsEpsilon($,te,p.n.EPSILON10)?V=!0:(Y+=j,d.tangent&&(Z=a.a.normalize(a.a.subtract($,te,Z),Z)),d.bitangent&&(J=a.a.normalize(a.a.cross(W,Z,J),J))),d.normal&&(e.t(y)&&(l.c.multiplyByPoint(w,W,W),a.a.normalize(W,W)),z[N++]=W.x,z[N++]=W.y,z[N++]=W.z,z[N++]=W.x,z[N++]=W.y,z[N++]=W.z),d.tangent&&(R[U++]=Z.x,R[U++]=Z.y,R[U++]=Z.z,R[U++]=Z.x,R[U++]=Z.y,R[U++]=Z.z),d.bitangent&&(I[B++]=J.x,I[B++]=J.y,I[B++]=J.z,I[B++]=J.x,I[B++]=J.y,I[B++]=J.z)}}var ie=new s.t;d.position&&(ie.position=new r.r({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:O})),d.normal&&(ie.normal=new r.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:z})),d.tangent&&(ie.tangent=new r.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),d.bitangent&&(ie.bitangent=new r.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:I})),d.st&&(ie.st=new r.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:S}));var oe=C/3;C-=6*(x+1);var re=m.IndexDatatype.createTypedArray(oe,C),se=0;for(b=0;b<oe-2;b+=2){var me=b,pe=b+2,le=a.a.fromArray(O,3*me,L),ue=a.a.fromArray(O,3*pe,E);if(!a.a.equalsEpsilon(le,ue,p.n.EPSILON10)){var ce=b+1,he=b+3;re[se++]=ce,re[se++]=me,re[se++]=he,re[se++]=he,re[se++]=me,re[se++]=pe}}var de=new r.T({attributes:ie,indices:re,primitiveType:l._0x38df4a.TRIANGLES,boundingSphere:new n.c.fromVertices(O)});return e.t(t._enuCenter)&&(de.attributes.position.values.set(f.localPos.topPositions,0),de.attributes.position.values.set(f.localPos.bottomPositions,de.attributes.position.values.length/2),de.attributes.position.componentDatatype=i.ComponentDatatype.FLOAT),de}},function(n,a){return e.t(a)&&(n=O.unpack(n,a)),n._ellipsoid=t.n.clone(n._ellipsoid),O.createGeometry(n)}}));
