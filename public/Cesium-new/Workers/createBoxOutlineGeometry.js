define(["./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./when-515d5295","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./PrimitiveType-b38a4004","./Rectangle-e170be8b","./Math-5e38123d","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5"],(function(e,t,n,a,r,i,o,m,u,f,s,c,b,p,d,y,x,A,l){"use strict";var O=new n.a;function h(e){var t=(e=i.e(e,i.e.EMPTY_OBJECT)).minimum,r=e.maximum;if(a.n.typeOf.object("min",t),a.n.typeOf.object("max",r),i.t(e.offsetAttribute)&&e.offsetAttribute===u.I.TOP)throw new a.t("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._min=n.a.clone(t),this._max=n.a.clone(r),this._offsetAttribute=e.offsetAttribute,this._workerName="createBoxOutlineGeometry"}h.fromDimensions=function(e){var t=(e=i.e(e,i.e.EMPTY_OBJECT)).dimensions;a.n.typeOf.object("dimensions",t),a.n.typeOf.number.greaterThanOrEquals("dimensions.x",t.x,0),a.n.typeOf.number.greaterThanOrEquals("dimensions.y",t.y,0),a.n.typeOf.number.greaterThanOrEquals("dimensions.z",t.z,0);var r=n.a.multiplyByScalar(t,.5,new n.a);return new h({minimum:n.a.negate(r,new n.a),maximum:r,offsetAttribute:e.offsetAttribute})},h.fromAxisAlignedBoundingBox=function(e){return a.n.typeOf.object("boundindBox",e),new h({minimum:e.minimum,maximum:e.maximum})},h.packedLength=2*n.a.packedLength+1,h.pack=function(e,t,r){return a.n.typeOf.object("value",e),a.n.defined("array",t),r=i.e(r,0),n.a.pack(e._min,t,r),n.a.pack(e._max,t,r+n.a.packedLength),t[r+2*n.a.packedLength]=i.e(e._offsetAttribute,-1),t};var _=new n.a,w=new n.a,v={minimum:_,maximum:w,offsetAttribute:void 0};return h.unpack=function(e,t,r){a.n.defined("array",e),t=i.e(t,0);var o=n.a.unpack(e,t,_),m=n.a.unpack(e,t+n.a.packedLength,w),u=e[t+2*n.a.packedLength];return i.t(r)?(r._min=n.a.clone(o,r._min),r._max=n.a.clone(m,r._max),r._offsetAttribute=-1===u?void 0:u,r):(v.offsetAttribute=-1===u?void 0:u,new h(v))},h.createGeometry=function(a){var s=a._min,c=a._max;if(!n.a.equals(s,c)){var b=new m.t,p=new Uint16Array(24),d=new Float64Array(24);d[0]=s.x,d[1]=s.y,d[2]=s.z,d[3]=c.x,d[4]=s.y,d[5]=s.z,d[6]=c.x,d[7]=c.y,d[8]=s.z,d[9]=s.x,d[10]=c.y,d[11]=s.z,d[12]=s.x,d[13]=s.y,d[14]=c.z,d[15]=c.x,d[16]=s.y,d[17]=c.z,d[18]=c.x,d[19]=c.y,d[20]=c.z,d[21]=s.x,d[22]=c.y,d[23]=c.z,b.position=new o.r({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d}),p[0]=4,p[1]=5,p[2]=5,p[3]=6,p[4]=6,p[5]=7,p[6]=7,p[7]=4,p[8]=0,p[9]=1,p[10]=1,p[11]=2,p[12]=2,p[13]=3,p[14]=3,p[15]=0,p[16]=0,p[17]=4,p[18]=1,p[19]=5,p[20]=2,p[21]=6,p[22]=3,p[23]=7;var y=n.a.subtract(c,s,O),x=.5*n.a.magnitude(y);if(i.t(a._offsetAttribute)){var A=d.length,l=new Uint8Array(A/3),h=a._offsetAttribute===u.I.NONE?0:1;e.d(l,h),b.applyOffset=new o.r({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:l})}return new o.T({attributes:b,indices:p,primitiveType:f._0x38df4a.LINES,boundingSphere:new t.c(n.a.ZERO,x),offsetAttribute:a._offsetAttribute})}},function(e,t){return i.t(t)&&(e=h.unpack(e,t)),h.createGeometry(e)}}));
