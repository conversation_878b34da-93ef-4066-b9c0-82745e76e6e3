define(["./arrayRemoveDuplicates-a4c6347e","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./CoplanarPolygonGeometryLibrary-929a67df","./when-515d5295","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryInstance-c11993d9","./GeometryPipeline-137aa28e","./IndexDatatype-eefd5922","./PolygonGeometryLibrary-e3bb7139","./PrimitiveType-b38a4004","./Rectangle-e170be8b","./Math-5e38123d","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./OrientedBoundingBox-57407e6e","./Cartesian4-034d54d5","./EllipsoidTangentPlane-fd839d7b","./IntersectionTests-5fa33dbd","./Plane-92c15089","./PolygonPipeline-b8b35011","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6","./FeatureDetection-7fae0d5a","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./ArcType-98a7a011"],(function(e,n,t,r,a,i,o,c,p,s,y,d,l,u,g,f,b,h,m,v,P,T,C,E,H,k,w,G,L,_,I,O,D){"use strict";function x(e){for(var n=e.length,t=new Float64Array(3*n),r=d.IndexDatatype.createTypedArray(n,2*n),i=0,o=0,s=0;s<n;s++){var y=e[s];t[i++]=y.x,t[i++]=y.y,t[i++]=y.z,r[o++]=s,r[o++]=(s+1)%n}var l=new p.t({position:new c.r({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})});return new c.T({attributes:l,indices:r,primitiveType:u._0x38df4a.LINES})}function A(e){var n=(e=o.e(e,o.e.EMPTY_OBJECT)).polygonHierarchy;r.n.defined("options.polygonHierarchy",n),this._polygonHierarchy=n,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=l.g.computeHierarchyPackedLength(n)+1}A.fromPositions=function(e){return e=o.e(e,o.e.EMPTY_OBJECT),r.n.defined("options.positions",e.positions),new A({polygonHierarchy:{positions:e.positions}})},A.pack=function(e,n,t){return r.n.typeOf.object("value",e),r.n.defined("array",n),t=o.e(t,0),n[t=l.g.packPolygonHierarchy(e._polygonHierarchy,n,t)]=e.packedLength,n};var B={polygonHierarchy:{}};return A.unpack=function(e,n,t){r.n.defined("array",e),n=o.e(n,0);var a=l.g.unpackPolygonHierarchy(e,n);n=a.startingIndex,delete a.startingIndex;var i=e[n];return o.t(t)||(t=new A(B)),t._polygonHierarchy=a,t.packedLength=i,t},A.createGeometry=function(r){var a=r._polygonHierarchy,o=a.positions;if(!((o=e.u(o,t.a.equalsEpsilon,!0)).length<3)&&i.p.validOutline(o)){var p=l.g.polygonOutlinesFromHierarchy(a,!1);if(0!==p.length){for(var d=[],u=0;u<p.length;u++){var g=new s.m({geometry:x(p[u])});d.push(g)}var f=y.F.combineInstances(d)[0],b=n.c.fromPoints(a.positions);return new c.T({attributes:f.attributes,indices:f.indices,primitiveType:f.primitiveType,boundingSphere:b})}}},function(e,n){return o.t(n)&&(e=A.unpack(e,n)),e._ellipsoid=g.n.clone(e._ellipsoid),A.createGeometry(e)}}));
