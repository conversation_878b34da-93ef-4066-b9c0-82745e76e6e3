define(["./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./EllipseOutlineGeometry-c1a1f716","./Rectangle-e170be8b","./Math-5e38123d","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./EllipseGeometryLibrary-497ff3d7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922"],(function(e,i,t,r,n,a,o,l,s,c,d,u,m,p,y,f,_,b,h,g,G,x){"use strict";function w(e){var n=(e=t.e(e,t.e.EMPTY_OBJECT)).radius;i.n.typeOf.number("radius",n);var a={center:e.center,semiMajorAxis:n,semiMinorAxis:n,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,numberOfVerticalLines:e.numberOfVerticalLines};this._ellipseGeometry=new r.w(a),this._workerName="createCircleOutlineGeometry"}w.packedLength=r.w.packedLength,w.pack=function(e,t,n){return i.n.typeOf.object("value",e),r.w.pack(e._ellipseGeometry,t,n)};var v=new r.w({center:new e.a,semiMajorAxis:1,semiMinorAxis:1}),A={center:new e.a,radius:void 0,ellipsoid:n.n.clone(n.n.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,numberOfVerticalLines:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0};return w.unpack=function(i,a,o){var l=r.w.unpack(i,a,v);return A.center=e.a.clone(l._center,A.center),A.ellipsoid=n.n.clone(l._ellipsoid,A.ellipsoid),A.height=l._height,A.extrudedHeight=l._extrudedHeight,A.granularity=l._granularity,A.numberOfVerticalLines=l._numberOfVerticalLines,t.t(o)?(A.semiMajorAxis=l._semiMajorAxis,A.semiMinorAxis=l._semiMinorAxis,o._ellipseGeometry=new r.w(A),o):(A.radius=l._semiMajorAxis,new w(A))},w.createGeometry=function(e){return r.w.createGeometry(e._ellipseGeometry)},function(i,r){return t.t(r)&&(i=w.unpack(i,r)),i._ellipseGeometry._center=e.a.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=n.n.clone(i._ellipseGeometry._ellipsoid),w.createGeometry(i)}}));
