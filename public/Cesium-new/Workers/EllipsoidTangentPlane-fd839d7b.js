define(["exports","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Intersect-53434a77","./Cartesian2-1b9b0d8a","./Cartesian4-034d54d5","./Rectangle-e170be8b","./IntersectionTests-5fa33dbd","./PrimitiveType-b38a4004","./Plane-92c15089","./GeometryAttribute-9bc31a7f"],(function(t,n,e,i,a,r,o,s,m,c,u,l){"use strict";function h(t,e,a){this.minimum=n.a.clone(i.e(t,n.a.ZERO)),this.maximum=n.a.clone(i.e(e,n.a.ZERO)),a=i.t(a)?n.a.clone(a):n.a.midpoint(this.minimum,this.maximum,new n.a),this.center=a}h.fromPoints=function(t,e){if(i.t(e)||(e=new h),!i.t(t)||0===t.length)return e.minimum=n.a.clone(n.a.ZERO,e.minimum),e.maximum=n.a.clone(n.a.ZERO,e.maximum),e.center=n.a.clone(n.a.ZERO,e.center),e;for(var a=t[0].x,r=t[0].y,o=t[0].z,s=t[0].x,m=t[0].y,c=t[0].z,u=t.length,l=1;l<u;l++){var p=t[l],d=p.x,f=p.y,x=p.z;a=Math.min(d,a),s=Math.max(d,s),r=Math.min(f,r),m=Math.max(f,m),o=Math.min(x,o),c=Math.max(x,c)}var y=e.minimum;y.x=a,y.y=r,y.z=o;var g=e.maximum;return g.x=s,g.y=m,g.z=c,e.center=n.a.midpoint(y,g,e.center),e},h.clone=function(t,e){if(i.t(t))return i.t(e)?(e.minimum=n.a.clone(t.minimum,e.minimum),e.maximum=n.a.clone(t.maximum,e.maximum),e.center=n.a.clone(t.center,e.center),e):new h(t.minimum,t.maximum,t.center)},h.equals=function(t,e){return t===e||i.t(t)&&i.t(e)&&n.a.equals(t.center,e.center)&&n.a.equals(t.minimum,e.minimum)&&n.a.equals(t.maximum,e.maximum)};var p=new n.a;h.intersectPlane=function(t,i){e.n.defined("box",t),e.n.defined("plane",i),p=n.a.subtract(t.maximum,t.minimum,p);var r=n.a.multiplyByScalar(p,.5,p),o=i.normal,s=r.x*Math.abs(o.x)+r.y*Math.abs(o.y)+r.z*Math.abs(o.z),m=n.a.dot(t.center,o)+i.distance;return m-s>0?a.S.INSIDE:m+s<0?a.S.OUTSIDE:a.S.INTERSECTING},h.prototype.clone=function(t){return h.clone(this,t)},h.prototype.intersectPlane=function(t){return h.intersectPlane(this,t)},h.prototype.equals=function(t){return h.equals(this,t)};var d=new o.a;function f(t,a){if(e.n.defined("origin",t),t=(a=i.e(a,s.n.WGS84)).scaleToGeodeticSurface(t),!i.t(t))throw new e.t("origin must not be at the center of the ellipsoid.");var r=l.m.eastNorthUpToFixedFrame(t,a);this._ellipsoid=a,this._origin=t,this._xAxis=n.a.fromCartesian4(c.c.getColumn(r,0,d)),this._yAxis=n.a.fromCartesian4(c.c.getColumn(r,1,d));var o=n.a.fromCartesian4(c.c.getColumn(r,2,d));this._plane=u.n.fromPointNormal(t,o)}Object.defineProperties(f.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});var x=new h;f.fromPoints=function(t,n){return e.n.defined("cartesians",t),new f(h.fromPoints(t,x).center,n)};var y=new m.n,g=new n.a;f.prototype.projectPointOntoPlane=function(t,a){e.n.defined("cartesian",t);var o=y;o.origin=t,n.a.normalize(t,o.direction);var s=m.h.rayPlane(o,this._plane,g);if(i.t(s)||(n.a.negate(o.direction,o.direction),s=m.h.rayPlane(o,this._plane,g)),i.t(s)){var c=n.a.subtract(s,this._origin,s),u=n.a.dot(this._xAxis,c),l=n.a.dot(this._yAxis,c);return i.t(a)?(a.x=u,a.y=l,a):new r.r(u,l)}},f.prototype.projectPointsOntoPlane=function(t,n){e.n.defined("cartesians",t),i.t(n)||(n=[]);for(var a=0,r=t.length,o=0;o<r;o++){var s=this.projectPointOntoPlane(t[o],n[a]);i.t(s)&&(n[a]=s,a++)}return n.length=a,n},f.prototype.projectPointToNearestOnPlane=function(t,a){e.n.defined("cartesian",t),i.t(a)||(a=new r.r);var o=y;o.origin=t,n.a.clone(this._plane.normal,o.direction);var s=m.h.rayPlane(o,this._plane,g);i.t(s)||(n.a.negate(o.direction,o.direction),s=m.h.rayPlane(o,this._plane,g));var c=n.a.subtract(s,this._origin,s),u=n.a.dot(this._xAxis,c),l=n.a.dot(this._yAxis,c);return a.x=u,a.y=l,a},f.prototype.projectPointsToNearestOnPlane=function(t,n){e.n.defined("cartesians",t),i.t(n)||(n=[]);var a=t.length;n.length=a;for(var r=0;r<a;r++)n[r]=this.projectPointToNearestOnPlane(t[r],n[r]);return n};var P=new n.a;f.prototype.projectPointOntoEllipsoid=function(t,a){e.n.defined("cartesian",t),i.t(a)||(a=new n.a);var r=this._ellipsoid,o=this._origin,s=this._xAxis,m=this._yAxis,c=P;return n.a.multiplyByScalar(s,t.x,c),a=n.a.add(o,c,a),n.a.multiplyByScalar(m,t.y,c),n.a.add(a,c,a),r.scaleToGeocentricSurface(a,a),a},f.prototype.projectPointsOntoEllipsoid=function(t,n){e.n.defined("cartesians",t);var a=t.length;i.t(n)?n.length=a:n=new Array(a);for(var r=0;r<a;++r)n[r]=this.projectPointOntoEllipsoid(t[r],n[r]);return n},t.e=h,t.s=f}));
