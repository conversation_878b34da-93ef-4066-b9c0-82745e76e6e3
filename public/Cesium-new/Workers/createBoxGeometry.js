define(["./BoxGeometry-995be07a","./when-515d5295","./arrayFill-4d3cc415","./Check-3aa71481","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Math-5e38123d","./Rectangle-e170be8b","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./VertexFormat-e844760b"],(function(e,t,a,r,n,c,b,i,o,d,u,f,m,s,y,l,C,G,p,h,A){"use strict";return function(a,r){return t.t(r)&&(a=e.c.unpack(a,r)),e.c.createGeometry(a)}}));
