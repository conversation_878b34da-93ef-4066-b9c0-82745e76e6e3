define(["./Cartographic-1bbcab04","./when-515d5295","./EllipseGeometry-2d9feab2","./Rectangle-e170be8b","./Check-3aa71481","./Math-5e38123d","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./Cartesian2-1b9b0d8a","./ComponentDatatype-d430c7f7","./EllipseGeometryLibrary-497ff3d7","./GeometryAttribute-9bc31a7f","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryInstance-c11993d9","./GeometryOffsetAttribute-800f7650","./GeometryPipeline-137aa28e","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./IndexDatatype-eefd5922","./IntersectionTests-5fa33dbd","./Plane-92c15089","./VertexFormat-e844760b"],(function(e,t,a,r,n,i,c,d,o,b,s,f,l,u,m,y,p,G,C,E,h,A,I,_,D,F,P,g,k){"use strict";return function(n,i){return t.t(i)&&(n=a.H.unpack(n,i)),n._center=e.a.clone(n._center),n._ellipsoid=r.n.clone(n._ellipsoid),a.H.createGeometry(n)}}));
