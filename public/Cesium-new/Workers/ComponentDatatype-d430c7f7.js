define(["exports","./when-515d5295","./Check-3aa71481","./WebGLConstants-77a84876"],(function(r,e,t,n){"use strict";var a={BYTE:n.t.BYTE,UNSIGNED_BYTE:n.t.UNSIGNED_BYTE,SHORT:n.t.SHORT,UNSIGNED_SHORT:n.t.UNSIGNED_SHORT,INT:n.t.INT,UNSIGNED_INT:n.t.UNSIGNED_INT,FLOAT:n.t.FLOAT,DOUBLE:n.t.DOUBLE,getSizeInBytes:function(r){if(!e.t(r))throw new t.t("value is required.");switch(r){case a.BYTE:return Int8Array.BYTES_PER_ELEMENT;case a.UNSIGNED_BYTE:return Uint8Array.BYTES_PER_ELEMENT;case a.SHORT:return Int16Array.BYTES_PER_ELEMENT;case a.UNSIGNED_SHORT:return Uint16Array.BYTES_PER_ELEMENT;case a.INT:return Int32Array.BYTES_PER_ELEMENT;case a.UNSIGNED_INT:return Uint32Array.BYTES_PER_ELEMENT;case a.FLOAT:return Float32Array.BYTES_PER_ELEMENT;case a.DOUBLE:return Float64Array.BYTES_PER_ELEMENT;default:throw new t.t("componentDatatype is not a valid value.")}},fromTypedArray:function(r){return r instanceof Int8Array?a.BYTE:r instanceof Uint8Array?a.UNSIGNED_BYTE:r instanceof Int16Array?a.SHORT:r instanceof Uint16Array?a.UNSIGNED_SHORT:r instanceof Int32Array?a.INT:r instanceof Uint32Array?a.UNSIGNED_INT:r instanceof Float32Array?a.FLOAT:r instanceof Float64Array?a.DOUBLE:void 0},validate:function(r){return e.t(r)&&(r===a.BYTE||r===a.UNSIGNED_BYTE||r===a.SHORT||r===a.UNSIGNED_SHORT||r===a.INT||r===a.UNSIGNED_INT||r===a.FLOAT||r===a.DOUBLE)},createTypedArray:function(r,n){if(!e.t(r))throw new t.t("componentDatatype is required.");if(!e.t(n))throw new t.t("valuesOrLength is required.");switch(r){case a.BYTE:return new Int8Array(n);case a.UNSIGNED_BYTE:return new Uint8Array(n);case a.SHORT:return new Int16Array(n);case a.UNSIGNED_SHORT:return new Uint16Array(n);case a.INT:return new Int32Array(n);case a.UNSIGNED_INT:return new Uint32Array(n);case a.FLOAT:return new Float32Array(n);case a.DOUBLE:return new Float64Array(n);default:throw new t.t("componentDatatype is not a valid value.")}},createArrayBufferView:function(r,n,E,N){if(!e.t(r))throw new t.t("componentDatatype is required.");if(!e.t(n))throw new t.t("buffer is required.");switch(E=e.e(E,0),N=e.e(N,(n.byteLength-E)/a.getSizeInBytes(r)),r){case a.BYTE:return new Int8Array(n,E,N);case a.UNSIGNED_BYTE:return new Uint8Array(n,E,N);case a.SHORT:return new Int16Array(n,E,N);case a.UNSIGNED_SHORT:return new Uint16Array(n,E,N);case a.INT:return new Int32Array(n,E,N);case a.UNSIGNED_INT:return new Uint32Array(n,E,N);case a.FLOAT:return new Float32Array(n,E,N);case a.DOUBLE:return new Float64Array(n,E,N);default:throw new t.t("componentDatatype is not a valid value.")}},fromName:function(r){switch(r){case"BYTE":return a.BYTE;case"UNSIGNED_BYTE":return a.UNSIGNED_BYTE;case"SHORT":return a.SHORT;case"UNSIGNED_SHORT":return a.UNSIGNED_SHORT;case"INT":return a.INT;case"UNSIGNED_INT":return a.UNSIGNED_INT;case"FLOAT":return a.FLOAT;case"DOUBLE":return a.DOUBLE;default:throw new t.t("name is not a valid value.")}}},E=Object.freeze(a);r.ComponentDatatype=E}));
