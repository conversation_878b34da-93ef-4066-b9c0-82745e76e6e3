define(["exports","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./Math-5e38123d"],(function(t,i,n,e,a,s){"use strict";function h(t,i,n){if(0===t)return i*n;var e=t*t,a=e*e,s=a*e,h=s*e,r=h*e,d=r*e,u=n;return i*((1-e/4-3*a/64-5*s/256-175*h/16384-441*r/65536-4851*d/1048576)*u-(3*e/8+3*a/32+45*s/1024+105*h/4096+2205*r/131072+6237*d/524288)*Math.sin(2*u)+(15*a/256+45*s/1024+525*h/16384+1575*r/65536+155925*d/8388608)*Math.sin(4*u)-(35*s/3072+175*h/12288+3675*r/262144+13475*d/1048576)*Math.sin(6*u)+(315*h/131072+2205*r/524288+43659*d/8388608)*Math.sin(8*u)-(693*r/1310720+6237*d/5242880)*Math.sin(10*u)+1001*d/8388608*Math.sin(12*u))}function r(t,i){if(0===t)return Math.log(Math.tan(.5*(s.n.PI_OVER_TWO+i)));var n=t*Math.sin(i);return Math.log(Math.tan(.5*(s.n.PI_OVER_TWO+i)))-t/2*Math.log((1+n)/(1-n))}var d=new i.a,u=new i.a;function o(t,e,a,o){var l=i.a.normalize(o.cartographicToCartesian(e,u),d),c=i.a.normalize(o.cartographicToCartesian(a,u),u);n.n.typeOf.number.greaterThanOrEquals("value",Math.abs(Math.abs(i.a.angleBetween(l,c))-Math.PI),.0125);var _=o.maximumRadius,g=o.minimumRadius,M=_*_,p=g*g;t._ellipticitySquared=(M-p)/M,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=i.i.clone(e,t._start),t._start.height=0,t._end=i.i.clone(a,t._end),t._end.height=0,t._heading=function(t,i,n,e,a){var h=r(t._ellipticity,n),d=r(t._ellipticity,a);return Math.atan2(s.n.negativePiToPi(e-i),d-h)}(t,e.longitude,e.latitude,a.longitude,a.latitude),t._distance=function(t,i,n,e,a,r,d){var u=t._heading,o=r-e,l=0;if(s.n.equalsEpsilon(Math.abs(u),s.n.PI_OVER_TWO,s.n.EPSILON8))if(i===n)l=i*Math.cos(a)*s.n.negativePiToPi(o);else{var c=Math.sin(a);l=i*Math.cos(a)*s.n.negativePiToPi(o)/Math.sqrt(1-t._ellipticitySquared*c*c)}else{var _=h(t._ellipticity,i,a);l=(h(t._ellipticity,i,d)-_)/Math.cos(u)}return Math.abs(l)}(t,o.maximumRadius,o.minimumRadius,e.longitude,e.latitude,a.longitude,a.latitude)}function l(t,n,a,d,u,o){var l,c,_,g=u*u;if(Math.abs(s.n.PI_OVER_TWO-Math.abs(n))>s.n.EPSILON8){c=function(t,i,n){var e=t/n;if(0===i)return e;var a=e*e,s=a*e,h=s*e,r=i*i,d=r*r,u=d*r,o=u*r,l=o*r,c=l*r,_=Math.sin(2*e),g=Math.cos(2*e),M=Math.sin(4*e),p=Math.cos(4*e),f=Math.sin(6*e),P=Math.cos(6*e),v=Math.sin(8*e),O=Math.cos(8*e),m=Math.sin(10*e);return e+e*r/4+7*e*d/64+15*e*u/256+579*e*o/16384+1515*e*l/65536+16837*e*c/1048576+(3*e*d/16+45*e*u/256-e*(32*a-561)*o/4096-e*(232*a-1677)*l/16384+e*(399985-90560*a+512*h)*c/5242880)*g+(21*e*u/256+483*e*o/4096-e*(224*a-1969)*l/16384-e*(33152*a-112599)*c/1048576)*p+(151*e*o/4096+4681*e*l/65536+1479*e*c/16384-453*s*c/32768)*P+(1097*e*l/65536+42783*e*c/1048576)*O+8011*e*c/1048576*Math.cos(10*e)+(3*r/8+3*d/16+213*u/2048-3*a*u/64+255*o/4096-33*a*o/512+20861*l/524288-33*a*l/512+h*l/1024+28273*c/1048576-471*a*c/8192+9*h*c/4096)*_+(21*d/256+21*u/256+533*o/8192-21*a*o/512+197*l/4096-315*a*l/4096+584039*c/16777216-12517*a*c/131072+7*h*c/2048)*M+(151*u/6144+151*o/4096+5019*l/131072-453*a*l/16384+26965*c/786432-8607*a*c/131072)*f+(1097*o/131072+1097*l/65536+225797*c/10485760-1097*a*c/65536)*v+(8011*l/2621440+8011*c/1048576)*m+293393*c/251658240*Math.sin(12*e)}(h(u,d,t.latitude)+a*Math.cos(n),u,d);var M=r(u,t.latitude),p=r(u,c);_=Math.tan(n)*(p-M),l=s.n.negativePiToPi(t.longitude+_)}else{var f;if(c=t.latitude,0===u)f=d*Math.cos(t.latitude);else{var P=Math.sin(t.latitude);f=d*Math.cos(t.latitude)/Math.sqrt(1-g*P*P)}_=a/f,l=n>0?s.n.negativePiToPi(t.longitude+_):s.n.negativePiToPi(t.longitude-_)}return e.t(o)?(o.longitude=l,o.latitude=c,o.height=0,o):new i.i(l,c,0)}function c(t,n,s){var h=e.e(s,a.n.WGS84);this._ellipsoid=h,this._start=new i.i,this._end=new i.i,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,e.t(t)&&e.t(n)&&o(this,t,n,h)}Object.defineProperties(c.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return n.n.defined("distance",this._distance),this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return n.n.defined("distance",this._distance),this._heading}}}),c.fromStartHeadingDistance=function(t,i,h,r,d){n.n.defined("start",t),n.n.defined("heading",i),n.n.defined("distance",h),n.n.typeOf.number.greaterThan("distance",h,0);var u=e.e(r,a.n.WGS84),o=u.maximumRadius,_=u.minimumRadius,g=o*o,M=_*_,p=Math.sqrt((g-M)/g),f=l(t,i=s.n.negativePiToPi(i),h,u.maximumRadius,p);return!e.t(d)||e.t(r)&&!r.equals(d.ellipsoid)?new c(t,f,u):(d.setEndPoints(t,f),d)},c.prototype.setEndPoints=function(t,i){n.n.defined("start",t),n.n.defined("end",i),o(this,t,i,this._ellipsoid)},c.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},c.prototype.interpolateUsingSurfaceDistance=function(t,i){if(n.n.typeOf.number("distance",t),!e.t(this._distance)||0===this._distance)throw new n.t("EllipsoidRhumbLine must have distinct start and end set.");return l(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,i)},c.prototype.findIntersectionWithLongitude=function(t,a){if(n.n.typeOf.number("intersectionLongitude",t),!e.t(this._distance)||0===this._distance)throw new n.t("EllipsoidRhumbLine must have distinct start and end set.");var h=this._ellipticity,r=this._heading,d=Math.abs(r),u=this._start;if(t=s.n.negativePiToPi(t),s.n.equalsEpsilon(Math.abs(t),Math.PI,s.n.EPSILON14)&&(t=s.n.sign(u.longitude)*Math.PI),e.t(a)||(a=new i.i),Math.abs(s.n.PI_OVER_TWO-d)<=s.n.EPSILON8)return a.longitude=t,a.latitude=u.latitude,a.height=0,a;if(s.n.equalsEpsilon(Math.abs(s.n.PI_OVER_TWO-d),s.n.PI_OVER_TWO,s.n.EPSILON8))return s.n.equalsEpsilon(t,u.longitude,s.n.EPSILON12)?void 0:(a.longitude=t,a.latitude=s.n.PI_OVER_TWO*s.n.sign(s.n.PI_OVER_TWO-r),a.height=0,a);var o,l=u.latitude,c=h*Math.sin(l),_=Math.tan(.5*(s.n.PI_OVER_TWO+l))*Math.exp((t-u.longitude)/Math.tan(r)),g=(1+c)/(1-c),M=u.latitude;do{o=M;var p=h*Math.sin(o),f=(1+p)/(1-p);M=2*Math.atan(_*Math.pow(f/g,h/2))-s.n.PI_OVER_TWO}while(!s.n.equalsEpsilon(M,o,s.n.EPSILON12));return a.longitude=t,a.latitude=M,a.height=0,a},c.prototype.findIntersectionWithLatitude=function(t,a){if(n.n.typeOf.number("intersectionLatitude",t),!e.t(this._distance)||0===this._distance)throw new n.t("EllipsoidRhumbLine must have distinct start and end set.");var h=this._ellipticity,d=this._heading,u=this._start;if(!s.n.equalsEpsilon(Math.abs(d),s.n.PI_OVER_TWO,s.n.EPSILON8)){var o=r(h,u.latitude),l=r(h,t),c=Math.tan(d)*(l-o),_=s.n.negativePiToPi(u.longitude+c);return e.t(a)?(a.longitude=_,a.latitude=t,a.height=0,a):new i.i(_,t,0)}},t.M=c}));
