define(["exports","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Rectangle-e170be8b","./AttributeCompression-f9ee669b","./Cartesian2-1b9b0d8a","./ComponentDatatype-d430c7f7","./Math-5e38123d","./PrimitiveType-b38a4004"],(function(t,e,i,r,n,o,a,s,c,m,u){"use strict";function d(t,e){r.n.typeOf.object("ellipsoid",t),this._ellipsoid=t,this._cameraPosition=new i.a,this._cameraPositionInScaledSpace=new i.a,this._distanceToLimbInScaledSpaceSquared=0,n.t(e)&&(this.cameraPosition=e)}Object.defineProperties(d.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(t){var e=this._ellipsoid.transformPositionToScaledSpace(t,this._cameraPositionInScaledSpace),r=i.a.magnitudeSquared(e)-1;i.a.clone(t,this._cameraPosition),this._cameraPositionInScaledSpace=e,this._distanceToLimbInScaledSpaceSquared=r}}});var l=new i.a;d.prototype.isPointVisible=function(t){return T(this._ellipsoid.transformPositionToScaledSpace(t,l),this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)},d.prototype.isScaledSpacePointVisible=function(t){return T(t,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};var p=new i.a;d.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(t,e){var i,r,o=this._ellipsoid;return n.t(e)&&e<0&&o.minimumRadius>-e?((r=p).x=this._cameraPosition.x/(o.radii.x+e),r.y=this._cameraPosition.y/(o.radii.y+e),r.z=this._cameraPosition.z/(o.radii.z+e),i=r.x*r.x+r.y*r.y+r.z*r.z-1):(r=this._cameraPositionInScaledSpace,i=this._distanceToLimbInScaledSpaceSquared),T(t,r,i)},d.prototype.computeHorizonCullingPoint=function(t,e,i){return x(this._ellipsoid,t,e,i)};var h=o.n.clone(o.n.UNIT_SPHERE);d.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(t,e,i,r){return x(S(this._ellipsoid,i,h),t,e,r)},d.prototype.computeHorizonCullingPointFromVertices=function(t,e,i,r,n){return v(this._ellipsoid,t,e,i,r,n)},d.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(t,e,i,r,n,o){return v(S(this._ellipsoid,n,h),t,e,i,r,o)};var f=[];d.prototype.computeHorizonCullingPointFromRectangle=function(t,n,a){r.n.typeOf.object("rectangle",t);var s=o.s.subsample(t,n,0,f),c=e.c.fromPoints(s);if(!(i.a.magnitude(c.center)<.1*n.minimumRadius))return this.computeHorizonCullingPoint(c.center,s,a)};var y=new i.a;function S(t,e,r){if(n.t(e)&&e<0&&t.minimumRadius>-e){var a=i.a.fromElements(t.radii.x+e,t.radii.y+e,t.radii.z+e,y);t=o.n.fromCartesian3(a,r)}return t}function x(t,e,o,a){r.n.typeOf.object("directionToPoint",e),r.n.defined("positions",o),n.t(a)||(a=new i.a);for(var s=I(t,e),c=0,m=0,u=o.length;m<u;++m){var d=z(t,o[m],s);if(d<0)return;c=Math.max(c,d)}return E(s,c,a)}var b=new i.a;function v(t,e,o,a,s,c){r.n.typeOf.object("directionToPoint",e),r.n.defined("vertices",o),r.n.typeOf.number("stride",a),n.t(c)||(c=new i.a),a=n.e(a,3),s=n.e(s,i.a.ZERO);for(var m=I(t,e),u=0,d=0,l=o.length;d<l;d+=a){b.x=o[d]+s.x,b.y=o[d+1]+s.y,b.z=o[d+2]+s.z;var p=z(t,b,m);if(p<0)return;u=Math.max(u,p)}return E(m,u,c)}function T(t,e,r){var n=e,o=r,a=i.a.subtract(t,n,l),s=-i.a.dot(a,n);return!(o<0?s>0:s>o&&s*s/i.a.magnitudeSquared(a)>o)}var g=new i.a,P=new i.a;function z(t,e,r){var n=t.transformPositionToScaledSpace(e,g),o=i.a.magnitudeSquared(n),a=Math.sqrt(o),s=i.a.divideByScalar(n,a,P);o=Math.max(1,o);var c=1/(a=Math.max(1,a));return 1/(i.a.dot(s,r)*c-i.a.magnitude(i.a.cross(s,r,s))*(Math.sqrt(o-1)*c))}function E(t,e,r){if(!(e<=0||e===1/0||e!=e))return i.a.multiplyByScalar(t,e,r)}var N=new i.a;function I(t,e){return i.a.equals(e,i.a.ZERO)?e:(t.transformPositionToScaledSpace(e,N),i.a.normalize(N,N))}var C=Object.freeze({NONE:0,BITS12:1}),_=new i.a,B=new i.a,w=new s.r,q=new u.c,H=new u.c,M=Math.pow(2,12);function O(t,e,r,o,a,s){var c,m,d,l=C.NONE;if(n.t(t)&&n.t(e)&&n.t(r)&&n.t(o)){var p=t.minimum,h=t.maximum,f=i.a.subtract(h,p,B),y=r-e;l=Math.max(i.a.maximumComponent(f),y)<M-1?C.BITS12:C.NONE,l=C.NONE,c=t.center,m=u.c.inverseTransformation(o,new u.c);var S=i.a.negate(p,_);u.c.multiply(u.c.fromTranslation(S,q),m,m);var x=_;x.x=1/f.x,x.y=1/f.y,x.z=1/f.z,u.c.multiply(u.c.fromScale(x,q),m,m),d=u.c.clone(o),u.c.setTranslation(d,i.a.ZERO,d),o=u.c.clone(o,new u.c);var b=u.c.fromTranslation(p,q),v=u.c.fromScale(f,H),T=u.c.multiply(b,v,q);u.c.multiply(o,T,o),u.c.multiply(d,T,d)}this.quantization=l,this.minimumHeight=e,this.maximumHeight=r,this.center=c,this.toScaledENU=m,this.fromScaledENU=o,this.matrix=d,this.hasVertexNormals=a,this.hasWebMercatorT=n.e(s,!1)}O.prototype.encode=function(t,e,r,n,o,c,d){var l=n.x,p=n.y;if(this.quantization===C.BITS12){(r=u.c.multiplyByPoint(this.toScaledENU,r,_)).x=m.n.clamp(r.x,0,1),r.y=m.n.clamp(r.y,0,1),r.z=m.n.clamp(r.z,0,1);var h=this.maximumHeight-this.minimumHeight,f=m.n.clamp((o-this.minimumHeight)/h,0,1);s.r.fromElements(r.x,r.y,w);var y=a.r.compressTextureCoordinates(w);s.r.fromElements(r.z,f,w);var S=a.r.compressTextureCoordinates(w);s.r.fromElements(l,p,w);var x=a.r.compressTextureCoordinates(w);if(t[e++]=y,t[e++]=S,t[e++]=x,this.hasWebMercatorT){s.r.fromElements(d,0,w);var b=a.r.compressTextureCoordinates(w);t[e++]=b}}else i.a.subtract(r,this.center,_),t[e++]=_.x,t[e++]=_.y,t[e++]=_.z,t[e++]=o,t[e++]=l,t[e++]=p,this.hasWebMercatorT&&(t[e++]=d);return this.hasVertexNormals&&(t[e++]=a.r.octPackFloat(c)),e},O.prototype.decodePosition=function(t,e,r){if(n.t(r)||(r=new i.a),e*=this.getStride(),this.quantization===C.BITS12){var o=a.r.decompressTextureCoordinates(t[e],w);r.x=o.x,r.y=o.y;var s=a.r.decompressTextureCoordinates(t[e+1],w);return r.z=s.x,u.c.multiplyByPoint(this.fromScaledENU,r,r)}return r.x=t[e],r.y=t[e+1],r.z=t[e+2],i.a.add(r,this.center,r)},O.prototype.decodeTextureCoordinates=function(t,e,i){return n.t(i)||(i=new s.r),e*=this.getStride(),this.quantization===C.BITS12?a.r.decompressTextureCoordinates(t[e+2],i):s.r.fromElements(t[e+4],t[e+5],i)},O.prototype.decodeHeight=function(t,e){return e*=this.getStride(),this.quantization===C.BITS12?a.r.decompressTextureCoordinates(t[e+1],w).y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight:t[e+3]},O.prototype.decodeWebMercatorT=function(t,e){return e*=this.getStride(),this.quantization===C.BITS12?a.r.decompressTextureCoordinates(t[e+3],w).x:t[e+6]},O.prototype.getOctEncodedNormal=function(t,e,i){var r=t[e=(e+1)*this.getStride()-1]/256,n=Math.floor(r),o=256*(r-n);return s.r.fromElements(n,o,i)},O.prototype.getStride=function(){var t;if(this.quantization===C.BITS12)t=3;else t=6;return this.hasWebMercatorT&&++t,this.hasVertexNormals&&++t,t};var A={position3DAndHeight:0,textureCoordAndEncodedNormals:1},U={compressed0:0,compressed1:1};O.prototype.getAttributes=function(t){var e,i=c.ComponentDatatype.FLOAT,r=c.ComponentDatatype.getSizeInBytes(i);if(this.quantization===C.NONE){var n=2;this.hasWebMercatorT&&++n,this.hasVertexNormals&&++n;var o=[{index:A.position3DAndHeight,vertexBuffer:t,componentDatatype:i,componentsPerAttribute:4,offsetInBytes:0,strideInBytes:e=(4+n)*r},{index:A.textureCoordAndEncodedNormals,vertexBuffer:t,componentDatatype:i,componentsPerAttribute:n,offsetInBytes:4*r,strideInBytes:e}];return o}var a=3,s=0;return(this.hasWebMercatorT||this.hasVertexNormals)&&++a,this.hasWebMercatorT&&this.hasVertexNormals?(e=(a+ ++s)*r,[{index:o.compressed0,vertexBuffer:t,componentDatatype:i,componentsPerAttribute:a,offsetInBytes:0,strideInBytes:e},{index:o.compressed1,vertexBuffer:t,componentDatatype:i,componentsPerAttribute:s,offsetInBytes:a*r,strideInBytes:e}]):[{index:o.compressed0,vertexBuffer:t,componentDatatype:i,componentsPerAttribute:a}]},O.prototype.getAttributeLocations=function(){return this.quantization===C.NONE?A:U},O.clone=function(t,e){return n.t(e)||(e=new O),e.quantization=t.quantization,e.minimumHeight=t.minimumHeight,e.maximumHeight=t.maximumHeight,e.center=i.a.clone(t.center),e.toScaledENU=u.c.clone(t.toScaledENU),e.fromScaledENU=u.c.clone(t.fromScaledENU),e.matrix=u.c.clone(t.matrix),e.hasVertexNormals=t.hasVertexNormals,e.hasWebMercatorT=t.hasWebMercatorT,e},t.c=d,t.u=O}));
