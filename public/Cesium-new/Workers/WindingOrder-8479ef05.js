define(["exports","./WebGLConstants-77a84876"],(function(e,n){"use strict";function t(e,n,t){t=t||2;var u,v,f,y,a,l,s,c=n&&n.length,Z=c?n[0]*t:e.length,d=r(e,0,Z,t,!0),g=[];if(!d||d.next===d.prev)return g;if(c&&(d=function(e,n,t,i){var u,v,f,y,a,l=[];for(u=0,v=n.length;u<v;u++)f=n[u]*i,y=u<v-1?n[u+1]*i:e.length,(a=r(e,f,y,i,!1))===a.next&&(a.steiner=!0),l.push(h(a));for(l.sort(o),u=0;u<l.length;u++)p(l[u],t),t=x(t,t.next);return t}(e,n,d,t)),e.length>80*t){u=f=e[0],v=y=e[1];for(var C=t;C<Z;C+=t)(a=e[C])<u&&(u=a),(l=e[C+1])<v&&(v=l),a>f&&(f=a),l>y&&(y=l);s=0!==(s=Math.max(f-u,y-v))?1/s:0}return i(d,g,t,u,v,s),g}function r(e,n,t,r,x){var i,u;if(x===m(e,n,t,r)>0)for(i=n;i<t;i+=r)u=b(i,e[i],e[i+1],u);else for(i=t-r;i>=n;i-=r)u=b(i,e[i],e[i+1],u);return u&&d(u,u.next)&&(O(u),u=u.next),u}function x(e,n){if(!e)return e;n||(n=e);var t,r=e;do{if(t=!1,r.steiner||!d(r,r.next)&&0!==Z(r.prev,r,r.next))r=r.next;else{if(O(r),(r=n=r.prev)===r.next)break;t=!0}}while(t||r!==n);return n}function i(e,n,t,r,o,p,a){if(e){!a&&p&&function(e,n,t,r){var x=e;do{null===x.z&&(x.z=l(x.x,x.y,n,t,r)),x.prevZ=x.prev,x.nextZ=x.next,x=x.next}while(x!==e);x.prevZ.nextZ=null,x.prevZ=null,function(e){var n,t,r,x,i,u,v,f,y=1;do{for(t=e,e=null,i=null,u=0;t;){for(u++,r=t,v=0,n=0;n<y&&(v++,r=r.nextZ);n++);for(f=y;v>0||f>0&&r;)0!==v&&(0===f||!r||t.z<=r.z)?(x=t,t=t.nextZ,v--):(x=r,r=r.nextZ,f--),i?i.nextZ=x:e=x,x.prevZ=i,i=x;t=r}i.nextZ=null,y*=2}while(u>1)}(x)}(e,r,o,p);for(var h,s,c=e;e.prev!==e.next;)if(h=e.prev,s=e.next,p?v(e,r,o,p):u(e))n.push(h.i/t),n.push(e.i/t),n.push(s.i/t),O(e),e=s.next,c=s.next;else if((e=s)===c){a?1===a?i(e=f(x(e),n,t),n,t,r,o,p,2):2===a&&y(e,n,t,r,o,p):i(x(e),n,t,r,o,p,1);break}}}function u(e){var n=e.prev,t=e,r=e.next;if(Z(n,t,r)>=0)return!1;for(var x=e.next.next;x!==e.prev;){if(s(n.x,n.y,t.x,t.y,r.x,r.y,x.x,x.y)&&Z(x.prev,x,x.next)>=0)return!1;x=x.next}return!0}function v(e,n,t,r){var x=e.prev,i=e,u=e.next;if(Z(x,i,u)>=0)return!1;for(var v=x.x<i.x?x.x<u.x?x.x:u.x:i.x<u.x?i.x:u.x,f=x.y<i.y?x.y<u.y?x.y:u.y:i.y<u.y?i.y:u.y,y=x.x>i.x?x.x>u.x?x.x:u.x:i.x>u.x?i.x:u.x,o=x.y>i.y?x.y>u.y?x.y:u.y:i.y>u.y?i.y:u.y,p=l(v,f,n,t,r),a=l(y,o,n,t,r),h=e.prevZ,c=e.nextZ;h&&h.z>=p&&c&&c.z<=a;){if(h!==e.prev&&h!==e.next&&s(x.x,x.y,i.x,i.y,u.x,u.y,h.x,h.y)&&Z(h.prev,h,h.next)>=0||(h=h.prevZ,c!==e.prev&&c!==e.next&&s(x.x,x.y,i.x,i.y,u.x,u.y,c.x,c.y)&&Z(c.prev,c,c.next)>=0))return!1;c=c.nextZ}for(;h&&h.z>=p;){if(h!==e.prev&&h!==e.next&&s(x.x,x.y,i.x,i.y,u.x,u.y,h.x,h.y)&&Z(h.prev,h,h.next)>=0)return!1;h=h.prevZ}for(;c&&c.z<=a;){if(c!==e.prev&&c!==e.next&&s(x.x,x.y,i.x,i.y,u.x,u.y,c.x,c.y)&&Z(c.prev,c,c.next)>=0)return!1;c=c.nextZ}return!0}function f(e,n,t){var r=e;do{var i=r.prev,u=r.next.next;!d(i,u)&&g(i,r,r.next,u)&&z(i,u)&&z(u,i)&&(n.push(i.i/t),n.push(r.i/t),n.push(u.i/t),O(r),O(r.next),r=e=u),r=r.next}while(r!==e);return x(r)}function y(e,n,t,r,u,v){var f=e;do{for(var y=f.next.next;y!==f.prev;){if(f.i!==y.i&&c(f,y)){var o=M(f,y);return f=x(f,f.next),o=x(o,o.next),i(f,n,t,r,u,v),void i(o,n,t,r,u,v)}y=y.next}f=f.next}while(f!==e)}function o(e,n){return e.x-n.x}function p(e,n){if(n=function(e,n){var t,r=n,x=e.x,i=e.y,u=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var v=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(v<=x&&v>u){if(u=v,v===x){if(i===r.y)return r;if(i===r.next.y)return r.next}t=r.x<r.next.x?r:r.next}}r=r.next}while(r!==n);if(!t)return null;if(x===u)return t;var f,y=t,o=t.x,p=t.y,l=1/0;r=t;do{x>=r.x&&r.x>=o&&x!==r.x&&s(i<p?x:u,i,o,p,i<p?u:x,i,r.x,r.y)&&(f=Math.abs(i-r.y)/(x-r.x),z(r,e)&&(f<l||f===l&&(r.x>t.x||r.x===t.x&&a(t,r)))&&(t=r,l=f)),r=r.next}while(r!==y);return t}(e,n),n){var t=M(n,e);x(t,t.next)}}function a(e,n){return Z(e.prev,e,n.prev)<0&&Z(n.next,e,e.next)<0}function l(e,n,t,r,x){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=32767*(e-t)*x)|e<<8))|e<<4))|e<<2))|e<<1))|(n=1431655765&((n=858993459&((n=252645135&((n=16711935&((n=32767*(n-r)*x)|n<<8))|n<<4))|n<<2))|n<<1))<<1}function h(e){var n=e,t=e;do{(n.x<t.x||n.x===t.x&&n.y<t.y)&&(t=n),n=n.next}while(n!==e);return t}function s(e,n,t,r,x,i,u,v){return(x-u)*(n-v)-(e-u)*(i-v)>=0&&(e-u)*(r-v)-(t-u)*(n-v)>=0&&(t-u)*(i-v)-(x-u)*(r-v)>=0}function c(e,n){return e.next.i!==n.i&&e.prev.i!==n.i&&!function(e,n){var t=e;do{if(t.i!==e.i&&t.next.i!==e.i&&t.i!==n.i&&t.next.i!==n.i&&g(t,t.next,e,n))return!0;t=t.next}while(t!==e);return!1}(e,n)&&(z(e,n)&&z(n,e)&&function(e,n){var t=e,r=!1,x=(e.x+n.x)/2,i=(e.y+n.y)/2;do{t.y>i!=t.next.y>i&&t.next.y!==t.y&&x<(t.next.x-t.x)*(i-t.y)/(t.next.y-t.y)+t.x&&(r=!r),t=t.next}while(t!==e);return r}(e,n)&&(Z(e.prev,e,n.prev)||Z(e,n.prev,n))||d(e,n)&&Z(e.prev,e,e.next)>0&&Z(n.prev,n,n.next)>0)}function Z(e,n,t){return(n.y-e.y)*(t.x-n.x)-(n.x-e.x)*(t.y-n.y)}function d(e,n){return e.x===n.x&&e.y===n.y}function g(e,n,t,r){var x=w(Z(e,n,t)),i=w(Z(e,n,r)),u=w(Z(t,r,e)),v=w(Z(t,r,n));return!!(x!==i&&u!==v||0===x&&C(e,t,n)||0===i&&C(e,r,n)||0===u&&C(t,e,r)||0===v&&C(t,n,r))}function C(e,n,t){return n.x<=Math.max(e.x,t.x)&&n.x>=Math.min(e.x,t.x)&&n.y<=Math.max(e.y,t.y)&&n.y>=Math.min(e.y,t.y)}function w(e){return e>0?1:e<0?-1:0}function z(e,n){return Z(e.prev,e,e.next)<0?Z(e,n,e.next)>=0&&Z(e,e.prev,n)>=0:Z(e,n,e.prev)<0||Z(e,e.next,n)<0}function M(e,n){var t=new E(e.i,e.x,e.y),r=new E(n.i,n.x,n.y),x=e.next,i=n.prev;return e.next=n,n.prev=e,t.next=x,x.prev=t,r.next=t,t.prev=r,i.next=r,r.prev=i,r}function b(e,n,t,r){var x=new E(e,n,t);return r?(x.next=r.next,x.prev=r,r.next.prev=x,r.next=x):(x.prev=x,x.next=x),x}function O(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function E(e,n,t){this.i=e,this.x=n,this.y=t,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function m(e,n,t,r){for(var x=0,i=n,u=t-r;i<t;i+=r)x+=(e[u]-e[i])*(e[i+1]+e[u+1]),u=i;return x}t.deviation=function(e,n,t,r){var x=n&&n.length,i=x?n[0]*t:e.length,u=Math.abs(m(e,0,i,t));if(x)for(var v=0,f=n.length;v<f;v++){var y=n[v]*t,o=v<f-1?n[v+1]*t:e.length;u-=Math.abs(m(e,y,o,t))}var p=0;for(v=0;v<r.length;v+=3){var a=r[v]*t,l=r[v+1]*t,h=r[v+2]*t;p+=Math.abs((e[a]-e[h])*(e[l+1]-e[a+1])-(e[a]-e[l])*(e[h+1]-e[a+1]))}return 0===u&&0===p?0:Math.abs((p-u)/u)},t.flatten=function(e){for(var n=e[0][0].length,t={vertices:[],holes:[],dimensions:n},r=0,x=0;x<e.length;x++){for(var i=0;i<e[x].length;i++)for(var u=0;u<n;u++)t.vertices.push(e[x][i][u]);x>0&&(r+=e[x-1].length,t.holes.push(r))}return t};var W={CLOCKWISE:n.t.CW,COUNTER_CLOCKWISE:n.t.CCW,NONE:n.t.NONE,validate:function(e){return e===W.CLOCKWISE||e===W.COUNTER_CLOCKWISE}},N=Object.freeze(W);e.F=N,e.m=t}));
