define(["./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./CylinderGeometryLibrary-4c16342b","./when-515d5295","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922","./PrimitiveType-b38a4004","./Rectangle-e170be8b","./Math-5e38123d","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5"],(function(t,e,i,r,n,a,o,s,u,f,d,b,c,p,m,l,y,_,h,v,O){"use strict";var A=new i.r;function R(t){var e=(t=s.e(t,s.e.EMPTY_OBJECT)).length,i=t.topRadius,r=t.bottomRadius,a=s.e(t.slices,128),o=Math.max(s.e(t.numberOfVerticalLines,16),0);if(n.n.typeOf.number("options.positions",e),n.n.typeOf.number("options.topRadius",i),n.n.typeOf.number("options.bottomRadius",r),n.n.typeOf.number.greaterThanOrEquals("options.slices",a,3),s.t(t.offsetAttribute)&&t.offsetAttribute===d.I.TOP)throw new n.t("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._length=e,this._topRadius=i,this._bottomRadius=r,this._slices=a,this._numberOfVerticalLines=o,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}R.packedLength=6,R.pack=function(t,e,i){return n.n.typeOf.object("value",t),n.n.defined("array",e),i=s.e(i,0),e[i++]=t._length,e[i++]=t._topRadius,e[i++]=t._bottomRadius,e[i++]=t._slices,e[i++]=t._numberOfVerticalLines,e[i]=s.e(t._offsetAttribute,-1),e};var g={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};return R.unpack=function(t,e,i){n.n.defined("array",t),e=s.e(e,0);var r=t[e++],a=t[e++],o=t[e++],u=t[e++],f=t[e++],d=t[e];return s.t(i)?(i._length=r,i._topRadius=a,i._bottomRadius=o,i._slices=u,i._numberOfVerticalLines=f,i._offsetAttribute=-1===d?void 0:d,i):(g.length=r,g.topRadius=a,g.bottomRadius=o,g.slices=u,g.numberOfVerticalLines=f,g.offsetAttribute=-1===d?void 0:d,new R(g))},R.createGeometry=function(n){var p=n._length,m=n._topRadius,l=n._bottomRadius,y=n._slices,_=n._numberOfVerticalLines;if(!(p<=0||m<0||l<0||0===m&&0===l)){var h,v=2*y,O=o.x.computePositions(p,m,l,y,!1),R=2*y;if(_>0){var g=Math.min(_,y);h=Math.round(y/g),R+=g}var w,L=b.IndexDatatype.createTypedArray(v,2*R),C=0;for(w=0;w<y-1;w++)L[C++]=w,L[C++]=w+1,L[C++]=w+y,L[C++]=w+1+y;if(L[C++]=y-1,L[C++]=0,L[C++]=y+y-1,L[C++]=y,_>0)for(w=0;w<y;w+=h)L[C++]=w,L[C++]=w+y;var E=new f.t;E.position=new u.r({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:O}),A.x=.5*p,A.y=Math.max(l,m);var D=new e.c(r.a.ZERO,i.r.magnitude(A));if(s.t(n._offsetAttribute)){p=O.length;var G=new Uint8Array(p/3),T=n._offsetAttribute===d.I.NONE?0:1;t.d(G,T),E.applyOffset=new u.r({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:G})}return new u.T({attributes:E,indices:L,primitiveType:c._0x38df4a.LINES,boundingSphere:D,offsetAttribute:n._offsetAttribute})}},function(t,e){return s.t(e)&&(t=R.unpack(t,e)),R.createGeometry(t)}}));
