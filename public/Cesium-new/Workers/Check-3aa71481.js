define(["exports","./when-515d5295"],(function(t,e){"use strict";function n(t){var e;this.name="DeveloperError",this.message=t;try{throw new Error}catch(t){e=t.stack}this.stack=e}e.t(Object.create)&&(n.prototype=Object.create(Error.prototype),n.prototype.constructor=n),n.prototype.toString=function(){var t=this.name+": "+this.message;return e.t(this.stack)&&(t+="\n"+this.stack.toString()),t},n.throwInstantiationError=function(){throw new n("This function defines an interface and should not be called directly.")};var o={};function r(t,e,n){return"Expected "+n+" to be typeof "+e+", actual typeof was "+t}o.typeOf={},o.defined=function(t,o){if(!e.t(o))throw new n(function(t){return t+" is required, actual value was undefined"}(t))},o.typeOf.func=function(t,e){if("function"!=typeof e)throw new n(r(typeof e,"function",t))},o.typeOf.string=function(t,e){if("string"!=typeof e)throw new n(r(typeof e,"string",t))},o.typeOf.number=function(t,e){if("number"!=typeof e)throw new n(r(typeof e,"number",t))},o.typeOf.number.lessThan=function(t,e,r){if(o.typeOf.number(t,e),e>=r)throw new n("Expected "+t+" to be less than "+r+", actual value was "+e)},o.typeOf.number.lessThanOrEquals=function(t,e,r){if(o.typeOf.number(t,e),e>r)throw new n("Expected "+t+" to be less than or equal to "+r+", actual value was "+e)},o.typeOf.number.greaterThan=function(t,e,r){if(o.typeOf.number(t,e),e<=r)throw new n("Expected "+t+" to be greater than "+r+", actual value was "+e)},o.typeOf.number.greaterThanOrEquals=function(t,e,r){if(o.typeOf.number(t,e),e<r)throw new n("Expected "+t+" to be greater than or equal to"+r+", actual value was "+e)},o.typeOf.object=function(t,e){if("object"!=typeof e)throw new n(r(typeof e,"object",t))},o.typeOf.bool=function(t,e){if("boolean"!=typeof e)throw new n(r(typeof e,"boolean",t))},o.typeOf.number.equals=function(t,e,r,a){if(o.typeOf.number(t,r),o.typeOf.number(e,a),r!==a)throw new n(t+" must be equal to "+e+", the actual values are "+r+" and "+a)},t.n=o,t.t=n}));
