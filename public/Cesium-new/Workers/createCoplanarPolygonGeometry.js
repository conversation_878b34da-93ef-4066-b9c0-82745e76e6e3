define(["./arrayRemoveDuplicates-a4c6347e","./BoundingRectangle-409afd17","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./Check-3aa71481","./ComponentDatatype-d430c7f7","./CoplanarPolygonGeometryLibrary-929a67df","./when-515d5295","./Rectangle-e170be8b","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryInstance-c11993d9","./GeometryPipeline-137aa28e","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./PolygonGeometryLibrary-e3bb7139","./PolygonPipeline-b8b35011","./VertexFormat-e844760b","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./OrientedBoundingBox-57407e6e","./Cartesian4-034d54d5","./EllipsoidTangentPlane-fd839d7b","./IntersectionTests-5fa33dbd","./Plane-92c15089","./FeatureDetection-7fae0d5a","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./ArcType-98a7a011","./EllipsoidRhumbLine-f50fdea6","./WindingOrder-8479ef05"],(function(e,t,n,a,o,r,i,s,p,c,l,y,d,g,u,m,v,b,h,f,w,A,P,T,x,F,D,_,L,k,E,C,R,I,H){"use strict";var G=new o.a,O=new t.n,B=new a.r,z=new a.r,N=new o.a,S=new o.a,j=new o.a,M=new o.a,U=new o.a,V=new o.a,W=new l.a,Y=new v.r,q=new v.r,J=new o.a;function Q(e,t,n,r,s,p,c,d){var g=e.positions,b=h.T.triangulate(e.positions2D,e.holes);b.length<3&&(b=[0,1,2]);var f=u.IndexDatatype.createTypedArray(g.length,b.length);f.set(b);var w=Y;if(0!==r){var A=l.a.fromAxisAngle(p,r,W);if(w=v.r.fromQuaternion(A,w),t.tangent||t.bitangent){A=l.a.fromAxisAngle(p,-r,W);var P=v.r.fromQuaternion(A,q);c=o.a.normalize(v.r.multiplyByVector(P,c,c),c),t.bitangent&&(d=o.a.normalize(o.a.cross(p,c,d),d))}}else w=v.r.clone(v.r.IDENTITY,w);var T=z;t.st&&(T.x=n.x,T.y=n.y);for(var x=g.length,F=3*x,D=new Float64Array(F),_=t.normal?new Float32Array(F):void 0,L=t.tangent?new Float32Array(F):void 0,k=t.bitangent?new Float32Array(F):void 0,E=t.st?new Float32Array(2*x):void 0,C=0,R=0,I=0,H=0,O=0,N=0;N<x;N++){var S=g[N];if(D[C++]=S.x,D[C++]=S.y,D[C++]=S.z,t.st){var j=s(v.r.multiplyByVector(w,S,G),B);a.r.subtract(j,T,j);var M=m.n.clamp(j.x/n.width,0,1),U=m.n.clamp(j.y/n.height,0,1);E[O++]=M,E[O++]=U}t.normal&&(_[R++]=p.x,_[R++]=p.y,_[R++]=p.z),t.tangent&&(L[H++]=c.x,L[H++]=c.y,L[H++]=c.z),t.bitangent&&(k[I++]=d.x,k[I++]=d.y,k[I++]=d.z)}var V=new y.t;return t.position&&(V.position=new l.r({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:D})),t.normal&&(V.normal=new l.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:_})),t.tangent&&(V.tangent=new l.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),t.bitangent&&(V.bitangent=new l.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:k})),t.st&&(V.st=new l.r({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:E})),new l.T({attributes:V,indices:f,primitiveType:v._0x38df4a.TRIANGLES})}function Z(e){var t=(e=p.e(e,p.e.EMPTY_OBJECT)).polygonHierarchy;r.n.defined("options.polygonHierarchy",t);var n=p.e(e.vertexFormat,f.n.DEFAULT);this._vertexFormat=f.n.clone(n),this._polygonHierarchy=t,this._stRotation=p.e(e.stRotation,0),this._ellipsoid=c.n.clone(p.e(e.ellipsoid,c.n.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=b.g.computeHierarchyPackedLength(t)+f.n.packedLength+c.n.packedLength+2}Z.fromPositions=function(e){return e=p.e(e,p.e.EMPTY_OBJECT),r.n.defined("options.positions",e.positions),new Z({polygonHierarchy:{positions:e.positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},Z.pack=function(e,t,n){return r.n.typeOf.object("value",e),r.n.defined("array",t),n=p.e(n,0),n=b.g.packPolygonHierarchy(e._polygonHierarchy,t,n),c.n.pack(e._ellipsoid,t,n),n+=c.n.packedLength,f.n.pack(e._vertexFormat,t,n),n+=f.n.packedLength,t[n++]=e._stRotation,t[n]=e.packedLength,t};var K=c.n.clone(c.n.UNIT_SPHERE),X=new f.n,$={polygonHierarchy:{}};return Z.unpack=function(e,t,n){r.n.defined("array",e),t=p.e(t,0);var a=b.g.unpackPolygonHierarchy(e,t);t=a.startingIndex,delete a.startingIndex;var o=c.n.unpack(e,t,K);t+=c.n.packedLength;var i=f.n.unpack(e,t,X);t+=f.n.packedLength;var s=e[t++],l=e[t];return p.t(n)||(n=new Z($)),n._polygonHierarchy=a,n._ellipsoid=c.n.clone(o,n._ellipsoid),n._vertexFormat=f.n.clone(i,n._vertexFormat),n._stRotation=s,n.packedLength=l,n},Z.createGeometry=function(t){var a=t._vertexFormat,r=t._polygonHierarchy,i=t._stRotation,p=r.positions;if(!((p=e.u(p,o.a.equalsEpsilon,!0)).length<3)){var c=N,y=S,v=j,h=U,f=V;if(s.p.computeProjectTo2DArguments(p,M,h,f)){if(c=o.a.cross(h,f,c),c=o.a.normalize(c,c),!o.a.equalsEpsilon(M,o.a.ZERO,m.n.EPSILON6)){var w=t._ellipsoid.geodeticSurfaceNormal(M,J);o.a.dot(c,w)<0&&(c=o.a.negate(c,c),h=o.a.negate(h,h))}var A=s.p.createProjectPointsTo2DFunction(M,h,f),P=s.p.createProjectPointTo2DFunction(M,h,f);a.tangent&&(y=o.a.clone(h,y)),a.bitangent&&(v=o.a.clone(f,v));var T=b.g.polygonsFromHierarchy(r,A,!1),x=T.hierarchy,F=T.polygons;if(0!==x.length){p=x[0].outerRing;for(var D=n.c.fromPoints(p),_=b.g.computeBoundingRectangle(c,P,p,i,O),L=[],k=0;k<F.length;k++){var E=new d.m({geometry:Q(F[k],a,_,i,P,c,y,v)});L.push(E)}var C=g.F.combineInstances(L)[0];C.attributes.position.values=new Float64Array(C.attributes.position.values),C.indices=u.IndexDatatype.createTypedArray(C.attributes.position.values.length/3,C.indices);var R=C.attributes;return a.position||delete R.position,new l.T({attributes:R,indices:C.indices,primitiveType:C.primitiveType,boundingSphere:D})}}}},function(e,t){return p.t(t)&&(e=Z.unpack(e,t)),Z.createGeometry(e)}}));
