define(["./PrimitivePipeline-67f346ce","./createTaskProcessorWorker","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./Check-3aa71481","./when-515d5295","./Math-5e38123d","./Rectangle-e170be8b","./Intersect-53434a77","./PrimitiveType-b38a4004","./Cartesian4-034d54d5","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Event-9821f5d9","./ComponentDatatype-d430c7f7","./GeometryAttribute-9bc31a7f","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./GeometryAttributes-7d904f0f","./GeometryPipeline-137aa28e","./AttributeCompression-f9ee669b","./EncodedCartesian3-d74c1b81","./IndexDatatype-eefd5922","./IntersectionTests-5fa33dbd","./Plane-92c15089","./WebMercatorProjection-aa5a37a5"],(function(e,t,a,r,n,i,o,c,b,d,s,m,u,f,p,C,y,l,P,G,k,h,v,A,D,E){"use strict";return t((function(t,a){var r=e.S.unpackCombineGeometryParameters(t),n=e.S.combineGeometry(r);return e.S.packCombineGeometryResults(n,a)}))}));
