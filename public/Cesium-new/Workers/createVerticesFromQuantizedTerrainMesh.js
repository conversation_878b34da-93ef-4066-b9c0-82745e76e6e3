define(["./AttributeCompression-f9ee669b","./EllipsoidTangentPlane-fd839d7b","./buildModuleUrl-dba4ec07","./Cartesian2-1b9b0d8a","./Cartographic-1bbcab04","./when-515d5295","./Rectangle-e170be8b","./TerrainEncoding-29e3257b","./IndexDatatype-eefd5922","./Math-5e38123d","./PrimitiveType-b38a4004","./OrientedBoundingBox-57407e6e","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./WebMercatorProjection-aa5a37a5","./createTaskProcessorWorker","./Intersect-53434a77","./Cartesian4-034d54d5","./IntersectionTests-5fa33dbd","./Plane-92c15089","./Event-9821f5d9","./RuntimeError-350acae3","./ComponentDatatype-d430c7f7","./WebGLConstants-77a84876","./PolygonPipeline-b8b35011","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6","./FeatureDetection-7fae0d5a"],(function(t,e,r,n,i,o,a,s,c,h,d,u,I,l,m,g,T,f,v,y,w,E,p,b,N,S,A,x){"use strict";function P(){I.t.throwInstantiationError()}Object.defineProperties(P.prototype,{errorEvent:{get:I.t.throwInstantiationError},credit:{get:I.t.throwInstantiationError},tilingScheme:{get:I.t.throwInstantiationError},ready:{get:I.t.throwInstantiationError},readyPromise:{get:I.t.throwInstantiationError},hasWaterMask:{get:I.t.throwInstantiationError},hasVertexNormals:{get:I.t.throwInstantiationError},availability:{get:I.t.throwInstantiationError}});var _=[];P.getRegularGridIndices=function(t,e){if(t*e>=h.n.FOUR_GIGABYTES)throw new I.t("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=_[t];o.t(r)||(_[t]=r=[]);var n=r[e];return o.t(n)||W(t,e,n=t*e<h.n.SIXTY_FOUR_KILOBYTES?r[e]=new Uint16Array((t-1)*(e-1)*6+3*(t+e-2)):r[e]=new Uint32Array((t-1)*(e-1)*6+3*(t+e-2)),0),n},P.getRegularGridIndicesForReproject=function(t,e){if(t*e>=h.n.FOUR_GIGABYTES)throw new I.t("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=_[t];o.t(r)||(_[t]=r=[]);var n=r[e];return o.t(n)||W(t,e,n=t*e<h.n.SIXTY_FOUR_KILOBYTES?r[e]=new Uint16Array((t-1)*(e-1)*6):r[e]=new Uint32Array((t-1)*(e-1)*6),0),n};var F=[];P.getRegularGridIndicesAndEdgeIndices=function(t,e){if(t*e>=h.n.FOUR_GIGABYTES)throw new I.t("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=F[t];o.t(r)||(F[t]=r=[]);var n=r[e];if(!o.t(n)){var i=P.getRegularGridIndices(t,e),a=G(t,e),s=a.westIndicesSouthToNorth,c=a.southIndicesEastToWest,d=a.eastIndicesNorthToSouth,u=a.northIndicesWestToEast;n=r[e]={indices:i,westIndicesSouthToNorth:s,southIndicesEastToWest:c,eastIndicesNorthToSouth:d,northIndicesWestToEast:u}}return n};var M=[];function G(t,e){var r,n=new Array(e),i=new Array(t),o=new Array(e),a=new Array(t);for(r=0;r<t;++r)a[r]=r,i[r]=t*e-1-r;for(r=0;r<e;++r)o[r]=(r+1)*t-1,n[r]=(e-r-1)*t;return{westIndicesSouthToNorth:n,southIndicesEastToWest:i,eastIndicesNorthToSouth:o,northIndicesWestToEast:a}}function W(t,e,r,n){for(var i=0,o=0;o<e-1;++o){for(var a=0;a<t-1;++a){var s=i,c=s+t,h=c+1,d=s+1;r[n++]=s,r[n++]=c,r[n++]=d,r[n++]=d,r[n++]=c,r[n++]=h,++i}++i}var u=(e-1)/2,I=(t-1)/2;i=0;for(a=0;a<I;a++)r[n++]=i,r[n++]=i+1,r[n++]=i+2,i+=2;i=t*(e-1);for(a=0;a<I;a++)r[n++]=i+1,r[n++]=i,r[n++]=i+2,i+=2;i=0;for(a=0;a<u;a++)r[n++]=i+t,r[n++]=i,r[n++]=i+2*t,i+=2*t;i=t-1;for(a=0;a<u;a++)r[n++]=i,r[n++]=i+t,r[n++]=i+2*t,i+=2*t}function B(t,e,r,n,i){for(var a=o.t(i),s=t[0],c=t.length,h=1;h<c;++h){var d=t[h];!a||i[s+"_"+d]?(r[n++]=s,r[n++]=d,r[n++]=e,r[n++]=e,r[n++]=d,r[n++]=e+1,s=d,++e):(s=d,++e)}return n}P.getRegularGridAndSkirtIndicesAndEdgeIndices=function(t,e){if(t*e>=h.n.FOUR_GIGABYTES)throw new I.t("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=M[t];o.t(r)||(M[t]=r=[]);var n=r[e];if(!o.t(n)){var i=t*e,a=(t-1)*(e-1)*6,s=2*t+2*e,d=i+s,u=3*(t+e-2),l=a+6*Math.max(0,s-4)+u,m=G(t,e),g=m.westIndicesSouthToNorth,T=m.southIndicesEastToWest,f=m.eastIndicesNorthToSouth,v=m.northIndicesWestToEast,y=c.IndexDatatype.createTypedArray(d,l);W(t,e,y,0),P.addSkirtIndices(g,T,f,v,i,y,a+u),n=r[e]={indices:y,westIndicesSouthToNorth:g,southIndicesEastToWest:T,eastIndicesNorthToSouth:f,northIndicesWestToEast:v,indexCountWithoutSkirts:a}}return n},P.addSkirtIndices=function(t,e,r,n,i,o,a,s){var c=i;a=B(t,c,o,a,s),a=B(e,c+=t.length,o,a,s),a=B(r,c+=e.length,o,a,s),B(n,c+=r.length,o,a,s)},P.heightmapTerrainQuality=.25,P.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(t,e,r){return 2*t.maximumRadius*Math.PI*P.heightmapTerrainQuality/(e*r)},P.prototype.requestTileGeometry=I.t.throwInstantiationError,P.prototype.getLevelMaximumGeometricError=I.t.throwInstantiationError,P.prototype.getTileDataAvailable=I.t.throwInstantiationError,P.prototype.loadTileDataAvailability=I.t.throwInstantiationError;var O=32767,Y=new i.a,k=new i.a,C=new i.a,H=new i.i,R=new n.r,V=new i.a,U=new d.c,z=new d.c;function L(t,e,r,n,o,a,s,c,u){var I=Number.POSITIVE_INFINITY,l=o.north,m=o.south,g=o.east,T=o.west;g<T&&(g+=h.n.TWO_PI);for(var f=t.length,v=0;v<f;++v){var y=t[v],w=r[y],E=n[y];H.longitude=h.n.lerp(T,g,E.x),H.latitude=h.n.lerp(m,l,E.y),H.height=w-e;var p=a.cartographicToCartesian(H,Y);d.c.multiplyByPoint(s,p,p),i.a.minimumByComponent(p,c,c),i.a.maximumByComponent(p,u,u),I=Math.min(I,H.height)}return I}function D(e,r,n,a,s,c,u,I,g,T,f,v,y,w,E){var p=o.t(u),b=g.north,N=g.south,S=g.east,A=g.west;S<A&&(S+=h.n.TWO_PI);for(var x=n.length,P=0;P<x;++P){var _=n[P],F=s[_],M=c[_];H.longitude=h.n.lerp(A,S,M.x)+w,H.latitude=h.n.lerp(N,b,M.y)+E,H.height=F-T;var G,W=I.cartographicToCartesian(H,Y);if(p){var B=2*_;if(R.x=u[B],R.y=u[B+1],1!==f){var O=t.r.octDecode(R.x,R.y,V),k=l.m.eastNorthUpToFixedFrame(Y,I,z),C=d.c.inverseTransformation(k,U);d.c.multiplyByPointAsVector(C,O,O),O.z*=f,i.a.normalize(O,O),d.c.multiplyByPointAsVector(k,O,O),i.a.normalize(O,O),t.r.octEncode(O,R)}}a.hasWebMercatorT&&(G=(m.e.geodeticLatitudeToMercatorAngle(H.latitude)-v)*y),r=a.encode(e,r,W,M,H.height,R,G)}}function j(t,e){var r;return"function"==typeof t.slice&&"function"!=typeof(r=t.slice()).sort&&(r=void 0),o.t(r)||(r=Array.prototype.slice.call(t)),r.sort(e),r}var q=g((function(I,g){var T,f,v=I.quantizedVertices,y=v.length/3,w=I.octEncodedNormals,E=I.westIndices.length+I.eastIndices.length+I.southIndices.length+I.northIndices.length,p=I.includeWebMercatorT,b=a.s.clone(I.rectangle),N=b.west,S=b.south,A=b.east,x=b.north,_=a.n.clone(I.ellipsoid),F=I.exaggeration,M=I.minimumHeight*F,G=I.maximumHeight*F,W=o.t(I.validMinimumHeight)?I.validMinimumHeight*F:M*F,B=o.t(I.validMaximumHeight)?I.validMaximumHeight*F:G*F,q=I.relativeToCenter,K=l.m.eastNorthUpToFixedFrame(q,_),Q=d.c.inverseTransformation(K,new d.c);p&&(T=m.e.geodeticLatitudeToMercatorAngle(S),f=1/(m.e.geodeticLatitudeToMercatorAngle(x)-T));var X=v.subarray(0,y),Z=v.subarray(y,2*y),J=v.subarray(2*y,3*y),$=o.t(w),tt=new Array(y),et=new Array(y),rt=new Array(y),nt=p?new Array(y):[],it=k;it.x=Number.POSITIVE_INFINITY,it.y=Number.POSITIVE_INFINITY,it.z=Number.POSITIVE_INFINITY;var ot=C;ot.x=Number.NEGATIVE_INFINITY,ot.y=Number.NEGATIVE_INFINITY,ot.z=Number.NEGATIVE_INFINITY;for(var at=Number.POSITIVE_INFINITY,st=Number.NEGATIVE_INFINITY,ct=Number.POSITIVE_INFINITY,ht=Number.NEGATIVE_INFINITY,dt=0;dt<y;++dt){var ut=X[dt],It=Z[dt],lt=ut/O,mt=It/O,gt=h.n.lerp(M,G,J[dt]/O);H.longitude=h.n.lerp(N,A,lt),H.latitude=h.n.lerp(S,x,mt),H.height=gt,at=Math.min(H.longitude,at),st=Math.max(H.longitude,st),ct=Math.min(H.latitude,ct),ht=Math.max(H.latitude,ht);var Tt=_.cartographicToCartesian(H);tt[dt]=new n.r(lt,mt),et[dt]=gt,rt[dt]=Tt,p&&(nt[dt]=(m.e.geodeticLatitudeToMercatorAngle(H.latitude)-T)*f),d.c.multiplyByPoint(Q,Tt,Y),i.a.minimumByComponent(Y,it,it),i.a.maximumByComponent(Y,ot,ot)}var ft,vt,yt=j(I.westIndices,(function(t,e){return tt[t].y-tt[e].y})),wt=j(I.eastIndices,(function(t,e){return tt[e].y-tt[t].y})),Et=j(I.southIndices,(function(t,e){return tt[e].x-tt[t].x})),pt=j(I.northIndices,(function(t,e){return tt[t].x-tt[e].x}));vt=r.c.fromPoints(rt),ft=u.b.fromRectangle(b,M,G,_);var bt,Nt=u.b.fromRectangle(b,W,B,_);(1!==F||M<0)&&(bt=new s.c(_).computeHorizonCullingPointPossiblyUnderEllipsoid(q,rt,M));var St=M;St=Math.min(St,L(I.westIndices,I.westSkirtHeight,et,tt,b,_,Q,it,ot)),St=Math.min(St,L(I.southIndices,I.southSkirtHeight,et,tt,b,_,Q,it,ot)),St=Math.min(St,L(I.eastIndices,I.eastSkirtHeight,et,tt,b,_,Q,it,ot)),St=Math.min(St,L(I.northIndices,I.northSkirtHeight,et,tt,b,_,Q,it,ot));for(var At=new e.e(it,ot,q),xt=new s.u(At,St,G,K,$,p),Pt=xt.getStride(),_t=new Float32Array(y*Pt+E*Pt),Ft=0,Mt=0;Mt<y;++Mt){if($){var Gt=2*Mt;if(R.x=w[Gt],R.y=w[Gt+1],1!==F){var Wt=t.r.octDecode(R.x,R.y,V),Bt=l.m.eastNorthUpToFixedFrame(rt[Mt],_,z),Ot=d.c.inverseTransformation(Bt,U);d.c.multiplyByPointAsVector(Ot,Wt,Wt),Wt.z*=F,i.a.normalize(Wt,Wt),d.c.multiplyByPointAsVector(Bt,Wt,Wt),i.a.normalize(Wt,Wt),t.r.octEncode(Wt,R)}}Ft=xt.encode(_t,Ft,rt[Mt],tt[Mt],et[Mt],R,nt[Mt])}var Yt=Math.max(0,2*(E-4)),kt=I.indices.length+3*Yt,Ct=c.IndexDatatype.createTypedArray(y+E,kt);Ct.set(I.indices,0);var Ht=1e-4,Rt=(st-at)*Ht,Vt=(ht-ct)*Ht,Ut=-Rt,zt=Rt,Lt=Vt,Dt=-Vt,jt=y*Pt;D(_t,jt,yt,xt,et,tt,w,_,b,I.westSkirtHeight,F,T,f,Ut,0),D(_t,jt+=I.westIndices.length*Pt,Et,xt,et,tt,w,_,b,I.southSkirtHeight,F,T,f,0,Dt),D(_t,jt+=I.southIndices.length*Pt,wt,xt,et,tt,w,_,b,I.eastSkirtHeight,F,T,f,zt,0),D(_t,jt+=I.eastIndices.length*Pt,pt,xt,et,tt,w,_,b,I.northSkirtHeight,F,T,f,0,Lt);var qt=function(t,e,r,n){if(n<12)return;for(var i={},o=t.length,a=0;a<o;a+=3){var s=t[a],c=t[a+1],h=t[a+2];(e[s]===O&&e[c]===O||0===e[s]&&0===e[c]||r[s]===O&&r[c]===O||0===r[s]&&0===r[c])&&(i[s+"_"+c]=1,i[c+"_"+s]=1),(e[c]===O&&e[h]===O||0===e[c]&&0===e[h]||r[c]===O&&r[h]===O||0===r[c]&&0===r[h])&&(i[c+"_"+h]=1,i[h+"_"+c]=1),(e[h]===O&&e[s]===O||0===e[h]&&0===e[s]||r[h]===O&&r[s]===O||0===r[h]&&0===r[s])&&(i[h+"_"+s]=1,i[s+"_"+h]=1)}return i}(I.indices,X,Z,I.level);return P.addSkirtIndices(yt,Et,wt,pt,y,Ct,I.indices.length,qt),g.push(_t.buffer,Ct.buffer),{vertices:_t.buffer,indices:Ct.buffer,westIndicesSouthToNorth:yt,southIndicesEastToWest:Et,eastIndicesNorthToSouth:wt,northIndicesWestToEast:pt,vertexStride:Pt,center:q,minimumHeight:M,maximumHeight:G,boundingSphere:vt,orientedBoundingBox:ft,validOrientedBoundingBox:Nt,occludeePointInScaledSpace:bt,encoding:xt,indexCountWithoutSkirts:I.indices.length}}));return q}));
