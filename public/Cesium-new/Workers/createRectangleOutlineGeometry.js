define(["./when-515d5295","./Rectangle-e170be8b","./arrayFill-4d3cc415","./buildModuleUrl-dba4ec07","./Cartographic-1bbcab04","./ComponentDatatype-d430c7f7","./Check-3aa71481","./GeometryAttribute-9bc31a7f","./GeometryAttributes-7d904f0f","./GeometryOffsetAttribute-800f7650","./IndexDatatype-eefd5922","./Math-5e38123d","./PolygonPipeline-b8b35011","./PrimitiveType-b38a4004","./RectangleGeometryLibrary-a52b9128","./Intersect-53434a77","./Event-9821f5d9","./RuntimeError-350acae3","./WebGLConstants-77a84876","./Cartesian2-1b9b0d8a","./FeatureDetection-7fae0d5a","./Cartesian4-034d54d5","./WindingOrder-8479ef05","./EllipsoidRhumbLine-f50fdea6"],(function(e,t,i,r,n,a,o,s,u,l,c,d,p,f,h,g,b,_,v,y,m,w,A,E){"use strict";var x=new r.c,D=new r.c,T=new n.a,P=new t.s;function H(e,t){var i=e._ellipsoid,r=t.height,n=t.width,o=t.northCap,l=t.southCap,d=r,p=2,g=0,b=4;o&&(p-=1,d-=1,g+=1,b-=2),l&&(p-=1,d-=1,g+=1,b-=2),g+=p*n+2*d-b;var _,v=new Float64Array(3*g),y=0,m=0,w=T;if(o)h.W.computePosition(t,i,!1,m,0,w),v[y++]=w.x,v[y++]=w.y,v[y++]=w.z;else for(_=0;_<n;_++)h.W.computePosition(t,i,!1,m,_,w),v[y++]=w.x,v[y++]=w.y,v[y++]=w.z;for(_=n-1,m=1;m<r;m++)h.W.computePosition(t,i,!1,m,_,w),v[y++]=w.x,v[y++]=w.y,v[y++]=w.z;if(m=r-1,!l)for(_=n-2;_>=0;_--)h.W.computePosition(t,i,!1,m,_,w),v[y++]=w.x,v[y++]=w.y,v[y++]=w.z;for(_=0,m=r-2;m>0;m--)h.W.computePosition(t,i,!1,m,_,w),v[y++]=w.x,v[y++]=w.y,v[y++]=w.z;for(var A=v.length/3*2,E=c.IndexDatatype.createTypedArray(v.length/3,A),x=0,D=0;D<v.length/3-1;D++)E[x++]=D,E[x++]=D+1;E[x++]=v.length/3-1,E[x++]=0;var P=new s.T({attributes:new u.t,primitiveType:f._0x38df4a.LINES});return P.attributes.position=new s.r({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v}),P.indices=E,P}function k(i){var r=(i=e.e(i,e.e.EMPTY_OBJECT)).rectangle,n=e.e(i.granularity,d.n.RADIANS_PER_DEGREE),a=e.e(i.ellipsoid,t.n.WGS84),s=e.e(i.rotation,0);if(!e.t(r))throw new o.t("rectangle is required.");if(t.s.validate(r),r.north<r.south)throw new o.t("options.rectangle.north must be greater than options.rectangle.south");var u=e.e(i.height,0),l=e.e(i.extrudedHeight,u);this._rectangle=t.s.clone(r),this._granularity=n,this._ellipsoid=a,this._surfaceHeight=Math.max(u,l),this._rotation=s,this._extrudedHeight=Math.min(u,l),this._offsetAttribute=i.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}k.packedLength=t.s.packedLength+t.n.packedLength+5,k.pack=function(i,r,n){if(!e.t(i))throw new o.t("value is required");if(!e.t(r))throw new o.t("array is required");return n=e.e(n,0),t.s.pack(i._rectangle,r,n),n+=t.s.packedLength,t.n.pack(i._ellipsoid,r,n),n+=t.n.packedLength,r[n++]=i._granularity,r[n++]=i._surfaceHeight,r[n++]=i._rotation,r[n++]=i._extrudedHeight,r[n]=e.e(i._offsetAttribute,-1),r};var I=new t.s,L=t.n.clone(t.n.UNIT_SPHERE),N={rectangle:I,ellipsoid:L,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};k.unpack=function(i,r,n){if(!e.t(i))throw new o.t("array is required");r=e.e(r,0);var a=t.s.unpack(i,r,I);r+=t.s.packedLength;var s=t.n.unpack(i,r,L);r+=t.n.packedLength;var u=i[r++],l=i[r++],c=i[r++],d=i[r++],p=i[r];return e.t(n)?(n._rectangle=t.s.clone(a,n._rectangle),n._ellipsoid=t.n.clone(s,n._ellipsoid),n._surfaceHeight=l,n._rotation=c,n._extrudedHeight=d,n._offsetAttribute=-1===p?void 0:p,n):(N.granularity=u,N.height=l,N.rotation=c,N.extrudedHeight=d,N.offsetAttribute=-1===p?void 0:p,new k(N))};var G=new n.i;return k.createGeometry=function(t){var n,o,u=t._rectangle,g=t._ellipsoid,b=h.W.computeOptions(u,t._granularity,t._rotation,0,P,G);if(!d.n.equalsEpsilon(u.north,u.south,d.n.EPSILON10)&&!d.n.equalsEpsilon(u.east,u.west,d.n.EPSILON10)){var _,v=t._surfaceHeight,y=t._extrudedHeight;if(!d.n.equalsEpsilon(v,y,0,d.n.EPSILON2)){if(n=function(e,t){var i=e._surfaceHeight,r=e._extrudedHeight,n=e._ellipsoid,a=r,o=i,s=H(e,t),u=t.height,l=t.width,d=p.T.scaleToGeodeticHeight(s.attributes.position.values,o,n,!1),f=d.length,h=new Float64Array(2*f);h.set(d);var g=p.T.scaleToGeodeticHeight(s.attributes.position.values,a,n);h.set(g,f),s.attributes.position.values=h;var b=t.northCap,_=t.southCap,v=4;b&&(v-=1),_&&(v-=1);var y,m=2*(h.length/3+v),w=c.IndexDatatype.createTypedArray(h.length/3,m);f=h.length/6;for(var A=0,E=0;E<f-1;E++)w[A++]=E,w[A++]=E+1,w[A++]=E+f,w[A++]=E+f+1;if(w[A++]=f-1,w[A++]=0,w[A++]=f+f-1,w[A++]=f,w[A++]=0,w[A++]=f,b)y=u-1;else{var x=l-1;w[A++]=x,w[A++]=x+f,y=l+u-2}if(w[A++]=y,w[A++]=y+f,!_){var D=l+y-1;w[A++]=D,w[A]=D+f}return s.indices=w,s}(t,b),e.t(t._offsetAttribute)){var m=n.attributes.position.values.length/3,w=new Uint8Array(m);t._offsetAttribute===l.I.TOP?w=i.d(w,1,0,m/2):(_=t._offsetAttribute===l.I.NONE?0:1,w=i.d(w,_)),n.attributes.applyOffset=new s.r({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:w})}var A=r.c.fromRectangle3D(u,g,v,D),E=r.c.fromRectangle3D(u,g,y,x);o=r.c.union(A,E)}else{if((n=H(t,b)).attributes.position.values=p.T.scaleToGeodeticHeight(n.attributes.position.values,v,g,!1),e.t(t._offsetAttribute)){var T=n.attributes.position.values.length,k=new Uint8Array(T/3);_=t._offsetAttribute===l.I.NONE?0:1,i.d(k,_),n.attributes.applyOffset=new s.r({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:k})}o=r.c.fromRectangle3D(u,g,v)}return new s.T({attributes:n.attributes,indices:n.indices,primitiveType:f._0x38df4a.LINES,boundingSphere:o,offsetAttribute:t._offsetAttribute})}},function(i,r){return e.t(r)&&(i=k.unpack(i,r)),i._ellipsoid=t.n.clone(i._ellipsoid),i._rectangle=t.s.clone(i._rectangle),k.createGeometry(i)}}));
