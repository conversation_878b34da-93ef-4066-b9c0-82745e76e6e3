define(["exports","./Cartographic-1bbcab04","./PolylineVolumeGeometryLibrary-ae5f3405","./when-515d5295","./Math-5e38123d","./PrimitiveType-b38a4004","./PolylinePipeline-bf1462fc","./GeometryAttribute-9bc31a7f"],(function(a,e,r,n,t,i,o,l){"use strict";var s={},u=new e.a,c=new e.a,y=new e.a,d=new e.a,m=[new e.a,new e.a],p=new e.a,g=new e.a,f=new e.a,h=new e.a,w=new e.a,v=new e.a,z=new e.a,x=new e.a,A=new e.a,b=new e.a,B=new l.a,E=new i.r;function S(a,n,o,s,y){var d,m=e.a.angleBetween(e.a.subtract(n,a,u),e.a.subtract(o,a,c)),p=s===r.O.BEVELED?1:Math.ceil(m/t.n.toRadians(5))+1,g=3*p,f=new Array(g);f[g-3]=o.x,f[g-2]=o.y,f[g-1]=o.z,d=y?i.r.fromQuaternion(l.a.fromAxisAngle(e.a.negate(a,u),m/p,B),E):i.r.fromQuaternion(l.a.fromAxisAngle(a,m/p,B),E);var h=0;n=e.a.clone(n,u);for(var w=0;w<p;w++)n=i.r.multiplyByVector(d,n,n),f[h++]=n.x,f[h++]=n.y,f[h++]=n.z;return f}function O(a,r,n,t){var i=u;return t||(r=e.a.negate(r,r)),[(i=e.a.add(a,r,i)).x,i.y,i.z,n.x,n.y,n.z]}function D(a,r,n,t){for(var i=new Array(a.length),o=new Array(a.length),l=e.a.multiplyByScalar(r,n,u),s=e.a.negate(l,c),m=0,p=a.length-1,g=0;g<a.length;g+=3){var f=e.a.fromArray(a,g,y),h=e.a.add(f,s,d);i[m++]=h.x,i[m++]=h.y,i[m++]=h.z;var w=e.a.add(f,l,d);o[p--]=w.z,o[p--]=w.y,o[p--]=w.x}return t.push(i,o),t}s.addAttribute=function(a,e,r,t){var i=e.x,o=e.y,l=e.z;n.t(r)&&(a[r]=i,a[r+1]=o,a[r+2]=l),n.t(t)&&(a[t]=l,a[t-1]=o,a[t-2]=i)};var P=new e.a,N=new e.a;s.computePositions=function(a){var n=a.granularity,i=a.positions,l=a.ellipsoid,s=a.width/2,c=a.cornerType,y=a.saveAttributes,d=p,B=g,E=f,R=h,L=w,U=v,V=z,M=x,T=A,G=b,I=[],Q=y?[]:void 0,q=y?[]:void 0,C=i[0],K=i[1];B=e.a.normalize(e.a.subtract(K,C,B),B),d=l.geodeticSurfaceNormal(C,d),R=e.a.normalize(e.a.cross(d,B,R),R),y&&(Q.push(R.x,R.y,R.z),q.push(d.x,d.y,d.z)),V=e.a.clone(C,V),C=K,E=e.a.negate(B,E);var j,k,F=[],H=i.length;for(j=1;j<H-1;j++){d=l.geodeticSurfaceNormal(C,d),K=i[j+1],B=e.a.normalize(e.a.subtract(K,C,B),B),L=e.a.normalize(e.a.add(B,E,L),L);var J=e.a.multiplyByScalar(d,e.a.dot(B,d),P);e.a.subtract(B,J,J),e.a.normalize(J,J);var W=e.a.multiplyByScalar(d,e.a.dot(E,d),N);if(e.a.subtract(E,W,W),e.a.normalize(W,W),!t.n.equalsEpsilon(Math.abs(e.a.dot(J,W)),1,t.n.EPSILON7)){L=e.a.cross(L,d,L),L=e.a.cross(d,L,L),L=e.a.normalize(L,L);var X=s/Math.max(.25,e.a.magnitude(e.a.cross(L,E,u))),Y=r.K.angleIsGreaterThanPi(B,E,C,l);L=e.a.multiplyByScalar(L,X,L),Y?(M=e.a.add(C,L,M),G=e.a.add(M,e.a.multiplyByScalar(R,s,G),G),T=e.a.add(M,e.a.multiplyByScalar(R,2*s,T),T),m[0]=e.a.clone(V,m[0]),m[1]=e.a.clone(G,m[1]),I=D(o.v.generateArc({positions:m,granularity:n,ellipsoid:l}),R,s,I),y&&(Q.push(R.x,R.y,R.z),q.push(d.x,d.y,d.z)),U=e.a.clone(T,U),R=e.a.normalize(e.a.cross(d,B,R),R),T=e.a.add(M,e.a.multiplyByScalar(R,2*s,T),T),V=e.a.add(M,e.a.multiplyByScalar(R,s,V),V),c===r.O.ROUNDED||c===r.O.BEVELED?F.push({leftPositions:S(M,U,T,c,Y)}):F.push({leftPositions:O(C,e.a.negate(L,L),T,Y)})):(T=e.a.add(C,L,T),G=e.a.add(T,e.a.negate(e.a.multiplyByScalar(R,s,G),G),G),M=e.a.add(T,e.a.negate(e.a.multiplyByScalar(R,2*s,M),M),M),m[0]=e.a.clone(V,m[0]),m[1]=e.a.clone(G,m[1]),I=D(o.v.generateArc({positions:m,granularity:n,ellipsoid:l}),R,s,I),y&&(Q.push(R.x,R.y,R.z),q.push(d.x,d.y,d.z)),U=e.a.clone(M,U),R=e.a.normalize(e.a.cross(d,B,R),R),M=e.a.add(T,e.a.negate(e.a.multiplyByScalar(R,2*s,M),M),M),V=e.a.add(T,e.a.negate(e.a.multiplyByScalar(R,s,V),V),V),c===r.O.ROUNDED||c===r.O.BEVELED?F.push({rightPositions:S(T,U,M,c,Y)}):F.push({rightPositions:O(C,L,M,Y)})),E=e.a.negate(B,E)}C=K}return d=l.geodeticSurfaceNormal(C,d),m[0]=e.a.clone(V,m[0]),m[1]=e.a.clone(C,m[1]),I=D(o.v.generateArc({positions:m,granularity:n,ellipsoid:l}),R,s,I),y&&(Q.push(R.x,R.y,R.z),q.push(d.x,d.y,d.z)),c===r.O.ROUNDED&&(k=function(a){var n=p,t=g,i=f,o=a[1];t=e.a.fromArray(a[1],o.length-3,t),i=e.a.fromArray(a[0],0,i);var l=S(n=e.a.midpoint(t,i,n),t,i,r.O.ROUNDED,!1),s=a.length-1,u=a[s-1];return o=a[s],t=e.a.fromArray(u,u.length-3,t),i=e.a.fromArray(o,0,i),[l,S(n=e.a.midpoint(t,i,n),t,i,r.O.ROUNDED,!1)]}(I)),{positions:I,corners:F,lefts:Q,normals:q,endPositions:k}},a.T=s}));
