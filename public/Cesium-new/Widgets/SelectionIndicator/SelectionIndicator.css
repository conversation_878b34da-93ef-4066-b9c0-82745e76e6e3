.cesium-selection-wrapper {
    position: absolute;
    width: 160px;
    height: 160px;
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s 0.2s, opacity 0.2s ease-in;
}

.cesium-selection-wrapper-visible {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-out;
}

.cesium-selection-wrapper svg {
    fill: #2e2;
    stroke: #000;
    stroke-width: 1.1px;
}
