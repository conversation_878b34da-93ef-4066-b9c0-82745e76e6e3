server {
    listen       80;
    server_name  localhost;
    client_max_body_size  100m;
    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    gzip on;
    gzip_static on;
    gzip_min_length 1;
    gzip_comp_level 4;
    gzip_vary on;
    gzip_types text/plain application/javascript text/css application/xml text/javascript application/x-http-php image/jpeg image/gif image/png;
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        # try_files $uri $uri/ /index.html; history mode
    }

    location /smartworkPlugin/{
        alias /usr/shar/nginx/smartwork/;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location /yx-gis/v2/api-docs {
        return 403;
    }

    location  /smartworkImg/{
       proxy_pass http://*************:6027/;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header Accept-Encoding "";
       sub_filter_types *;
       sub_filter_once off;
       sub_filter 'indexTips' 'indexTips1';
    }

        location /tianditu/t0/ {
        proxy_pass https://t0.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t1/ {
        proxy_pass https://t1.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t2/ {
        proxy_pass https://t2.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t3/ {
        proxy_pass https://t3.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t4/ {
        proxy_pass https://t4.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t5/ {
        proxy_pass https://t5.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t6/ {
        proxy_pass https://t6.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /tianditu/t7/ {
        proxy_pass https://t7.tianditu.gov.cn/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /cache/ {
        proxy_pass http://only-office-server:80/cache/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forward-For $proxy_add_x_forwarded_for;
    }

    location /only-office-server/ {
        proxy_pass http://only-office-server:80/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forward-For $proxy_add_x_forwarded_for;
    }


    location /filepreview/ {
        proxy_pass http://file-preview:8012/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

   location /api/YXSZY/ {
        proxy_pass http://***************:8901/YXSZY/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /api/ {
        proxy_pass http://sys-gateway:11312/;
		    proxy_buffering off;
		    proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /SimpleViewApp/ {
        proxy_pass http://sys-gateway:11312/;
		    proxy_buffering off;
		    proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /yx-gis/ {
        proxy_pass http://yx-gis:15002/;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /iserver/ {
        proxy_pass http://*************:80;
        proxy_redirect     off;
        proxy_set_header   Host                 $host;
        proxy_set_header   X-Real-IP            $remote_addr;
        proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto    $scheme;
       # Enables WS support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /iserver2/ {
        proxy_pass http://*************:80;           #web正式*************:80  sim测试***************:8080
        proxy_redirect     off;
        proxy_set_header   Host                 $host;
        proxy_set_header   X-Real-IP            $remote_addr;
        proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto    $scheme;
       # Enables WS support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /yuxiBasemap/ {
        proxy_pass http://*************:80;           #web正式*************:80  sim测试***************:8080
        proxy_redirect     off;
        proxy_set_header   Host                 $host;
        proxy_set_header   X-Real-IP            $remote_addr;
        proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto    $scheme;
       # Enables WS support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET,POST,DELETE';
        add_header 'Access-Control-Allow-Header' 'Content-Type,*';
    }

    location /yuxi_agent/ {
        proxy_pass http://*************:80;           #web正式*************:80  sim测试***************:8080
        proxy_redirect     off;
        proxy_set_header   Host                 $host;
        proxy_set_header   X-Real-IP            $remote_addr;
        proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto    $scheme;
       # Enables WS support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /sysperfix/ {
	    proxy_pass http://*************:8000/; # web正式*************:8000 sim测试***************:8080
        proxy_redirect     off;
        proxy_set_header   Host                 $host;
        proxy_set_header   X-Real-IP            $remote_addr;
        proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto    $scheme;
       # Enables WS support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
	}

    #     location /iserver/ {
    #        proxy_pass http://***************:8080;
    #        proxy_buffering off;
    #        proxy_request_buffering off;
    #    }


    # location /DataServer/ {
    #     proxy_pass http://t6.tianditu.gov.cn/DataServer/;
    #     proxy_buffering off;
		#     proxy_request_buffering off;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    # }

    #location /dev/ {
    #        proxy_pass http://flowable-designer:80/;
    #		    proxy_buffering off;
    #		    proxy_request_buffering off;
    #        proxy_http_version 1.1;
    #        proxy_set_header Upgrade $http_upgrade;
    #        proxy_set_header Connection "upgrade";
    #    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
