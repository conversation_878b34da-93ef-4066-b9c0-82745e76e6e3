{"name": "central-system", "version": "1.7.1", "private": true, "scripts": {"postinstall": "patch-package", "fix-memory-limit": "cross-env LIMIT=9216 increase-memory-limit", "dev": "node --max_old_space_size=24576 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build:prod": "node --max_old_space_size=9216 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "build:local": "node --max_old_space_size=9216 node_modules/@vue/cli-service/bin/vue-cli-service.js build --mode local", "build:stage": "node --max_old_space_size=32768 node_modules/@vue/cli-service/bin/vue-cli-service.js build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/g6": "4.6.15", "@antv/x6": "2.11.4", "@antv/x6-plugin-clipboard": "^2.1.4", "@antv/x6-plugin-dnd": "^2.0.4", "@antv/x6-plugin-export": "^2.1.5", "@antv/x6-plugin-history": "^2.2.0", "@antv/x6-plugin-keyboard": "^2.2.0", "@antv/x6-plugin-scroller": "^2.0.9", "@antv/x6-plugin-selection": "^2.1.5", "@antv/x6-plugin-snapline": "^2.1.6", "@antv/x6-plugin-stencil": "^2.0.2", "@antv/x6-plugin-transform": "^2.1.5", "@antv/x6-vue-shape": "^2.0.9", "@bentley/bentleyjs-core": "2.12.0", "@bentley/frontend-authorization-client": "2.12.0", "@bentley/frontend-devtools": "2.12.0", "@bentley/geometry-core": "2.12.0", "@bentley/imodelhub-client": "2.12.0", "@bentley/imodeljs-common": "2.12.0", "@bentley/imodeljs-frontend": "2.12.0", "@bentley/imodeljs-i18n": "2.12.0", "@bentley/imodeljs-markup": "2.12.0", "@bentley/imodeljs-quantity": "2.12.0", "@bentley/itwin-client": "2.12.0", "@bentley/orbitgt-core": "2.12.0", "@bentley/presentation-common": "2.12.0", "@bentley/presentation-components": "2.12.0", "@bentley/presentation-frontend": "^2.12.0", "@bentley/product-settings-client": "2.12.0", "@bentley/rbac-client": "2.12.0", "@bentley/telemetry-client": "^2.12.0", "@bentley/ui-abstract": "2.12.0", "@bentley/webgl-compatibility": "2.12.0", "@d2-projects/vue-filename-injector": "^1.0.0", "@handsontable-pro/vue": "^3.0.0", "@handsontable/vue": "^4.0.0", "@mapbox/mapbox-gl-geocoder": "^5.0.0", "@mapbox/mapbox-gl-language": "^1.0.0", "@turf/turf": "^6.5.0", "@vue/composition-api": "^1.7.1", "@vueuse/core": "^10.9.0", "axios": "^0.21.1", "better-scroll": "^2.0.5", "bpmn-js": "^7.3.0", "d3": "^5.7.0", "d3-collection": "^1.0.7", "d3-selection": "^1.3.2", "d3-selection-multi": "^1.0.1", "d3-tip": "^0.9.1", "dayjs": "^1.10.4", "decimal.js": "^10.2.1", "downloadjs": "^1.4.7", "draggable": "^4.2.0", "echarts": "^4.9.0", "echarts-liquidfill": "^2.0.5", "el-tree-transfer": "^2.4.7", "element-resize-detector": "^1.2.2", "exceljs": " ^4.3.0", "fawkes-lib": "0.2.8", "fecha": "^4.2.1", "handsontable": "^7.0.0", "handsontable-pro": "^6.2.0", "hdec-transform": "^1.1.1", "hls.js": "^0.14.17", "html2canvas": "^1.0.0-rc.7", "insert-css": "^2.0.0", "js-audio-recorder": "^1.0.7", "js-export-excel": "^1.1.4", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "jspdf": "^2.3.1", "konva": "^4.2.2", "lodash": "^4.17.21", "mapbox-gl": "2.10.0", "nprogress": "^0.2.0", "ol": "6.9.0", "pinyin-match": "^1.2.1", "proj4": "^2.7.5", "qs": "^6.11.2", "sm-crypto": "^0.3.7", "sockjs-client": "^1.5.0", "sockjs-client-noxhr": "^1.0.0", "sortablejs": "^1.14.0", "spark-md5": "^3.0.1", "stompjs": "^2.3.3", "svg-pan-zoom": "^3.6.1", "throttle-debounce": "^1.1.0", "tooltip.js": "^1.3.3", "tsparticles": "^2.1.4", "v-calendar": "^2.3.2", "vue": "^2.6.10", "vue-clipboard2": "^0.3.1", "vue-cropper": "^0.5.2", "vue-echarts": "4.1.0", "vue-i18n": "^5.0.3", "vue-konva": "^2.1.7", "vue-multipane": "^0.9.5", "vue-router": "^2.8.1", "vue-seamless-scroll": "^1.1.23", "vue-socket.io": "^3.0.7", "vue-splitpane": "^1.0.6", "vue-video-player": "^5.0.2", "vue2-particles": "^2.1.4", "vuedraggable": "^2.24.3", "vuex": "^3.1.1", "wangeditor": "^3.1.1", "webstomp-client": "^1.2.6", "webuploader": "^0.1.8", "xgplayer": "^2.30.1", "xgplayer-hls.js": "^2.5.2", "xlsx": "^0.16.9", "ztree": "^3.5.24"}, "devDependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-preset-jsx": "^1.2.4", "@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-service": "^4.0.0", "babel-eslint": "^7.2.3", "babel-plugin-dynamic-import-node": "^2.3.3", "compression-webpack-plugin": "^6.1.1", "core-js": "^3.3.2", "cross-env": "^7.0.3", "eslint": "^4.3.0", "eslint-config-standard": "^10.2.1", "eslint-loader": "2.0.0", "eslint-plugin-html": "^3.1.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "html-webpack-plugin": "3.2.0", "increase-memory-limit": "^1.0.7", "node-sass": "^4.13.0", "patch-package": "^6.4.7", "postinstall-postinstall": "^2.1.0", "sass-loader": "^8.0.0", "style-loader": "^2.0.0", "svg-sprite-loader": "^5.0.0", "terser-webpack-plugin": "^4.2.3", "vue-loader": "^15.9.3", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.9.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "engines": {"node": ">=8.10.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}