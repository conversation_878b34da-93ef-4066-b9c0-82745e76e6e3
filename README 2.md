## 1.5.0.RELEASE

**发布日期：** 2020-10-13

1-组件库升级0.2.x

2-新增系统配置模块，支持对系统界面各项参数进行配置

3-新增对于多门户多级管控的支持

4-新增基于WebScoket实时推送能力

5-新增用户连续登录失败进行账号锁定

6-新增岗位管理模块

7-新增用户绑定岗位功能

8-新增基于岗位的授权方式

9-新增人员的导入与导出

10-新增内容管理模块

11-优化前端框架，将项目拆解成各个子模块

12-优化菜单按钮和数据权限的授权与交互

13-优化人力资源中组织和人员的交互

14-优化应用和租户的配置与交互

15-优化系统登录页

16-优化权限配置，支持签名，登录，URL三种级别的细粒度配置

### 配置说明

配置文件 src/settings.js

##### CLIENT/CLIENT_SECRET/AUTH_TOKEN

应用信息，决定访问的权限与租户

##### prefix

缓存前缀，根据应用名称修改

##### localRoute

是否加载本地路由，默认true，登录后获取所有本地路由

##### localSvg

加载本地svg，默认为true，svg目录为src/assets/iconfont/svg；加载线上svg需配合anser-wiki服务

##### multiPortal

开启多门户，门户等级在字典 portal_type 中维护；单门户请置为false
