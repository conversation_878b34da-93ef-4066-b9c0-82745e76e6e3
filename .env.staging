# just a flag
ENV = 'stage'
NODE_ENV = 'production'
VUE_APP_REAL_ENV = 'test'
VUE_APP_BASE_API = '../api'

#svg api
VUE_APP_SVG_URL = '../api/anser-wiki/icon'

#upload api
VUE_APP_UPLOAD_API = '../api/sys-storage/upload'

#download api
VUE_APP_DOWNLOAD_API = '../api/sys-storage/download_image'

#socket api
#ipAddress is only runnable in http protocols
#use '../api/sys-socket' in official product
VUE_APP_SOCKET_API = '/api/sys-socket'

#previewHttp
VUE_APP_PREVIEW_URL = 'http://***************:18200'

#actionHttp
VUE_APP_POSTS_URL = 'https://jsonplaceholder.typicode.com'

#mapboxHttp
VUE_APP_MAP_BOX_URL = 'https://api.mapbox.com'

#bimHttp
VUE_APP_BIM_URL = 'http://**************:5566'

#rasterHttp
VUE_APP_RASTER_URL = 'http://t6.tianditu.gov.cn'

VUE_APP_GIS_CT_URL = 'http://gisct.ecidi.com'

VUE_APP_TIANDITU_URL = 'https://api.tianditu.gov.cn'

VUE_APP_APIHUBS_URL = 'https://api.apihubs.cn'

VUE_APP_AMAP_URL = 'https://restapi.amap.com'

VUE_APP_AMAP_URL = 'https://restapi.amap.com'
VUE_APP_YXYWWEB_URL = 'https://yxywweb.hdec.com'
VUE_APP_CESHI_URL = 'http://**************'
VUE_APP_AMAP_WEB_KEY = 'b7b4069549da19098064c44d6be0a1fa'
