'use strict';
const path = require('path');
const VueFilenameInjector = require('@d2-projects/vue-filename-injector');
const defaultSettings = require('./src/settings.js');
const TerserPlugin = require('terser-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const HtmlWebpackPlugin = require('html-webpack-plugin');
// 准备从chunk-vendor.js中提取的依赖
const libArr = ['fawkes-lib', '@bentley', 'exceljs', 'xlsx', 'js-export-excel', 'echarts', 'handsontable', 'mapbox-gl', 'jspdf'];
const splitThirdPartyLib = (libArr) => {
  const basePriority = 20;
  return libArr.reduce((acc, prev, index) => {
    acc[prev] = {
      test: new RegExp(`[\\\\/]node_modules[\\\\/]_?${prev}(.*)`), // in order to adapt to cnpm
      name: `chunk~${prev}`,
      chunks: 'all',
      enforce: true,
      priority: basePriority + index
    };
    return acc;
  }, {});
};

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || 'Fawkes Template'; // page title
// If your port is set to 80,
// use administrator privileges to execute the command line.
// For Example, Mac: sudo npm run
const port = 9528; // dev port
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
	 * You will need to set publicPath if you plan to deploy your site under a sub path,
	 * for Example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
	 * then publicPath should be set to "/bar/".
	 * In most cases please use '/' !!!
	 * Detail: https://cli.vuejs.org/config/#publicpath
	 */
  publicPath: './',
  outputDir: process.env.ENV === 'production' ? 'dist-production' : 'dist',
  assetsDir: '',
  lintOnSave: false,
  productionSourceMap: false,

  devServer: {
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: defaultSettings.proxy
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    devtool: process.env.NODE_ENV === 'development' ? 'eval-source-map' : '',
    resolve: {
      extensions: ['.js', '.vue'],
      alias: {
        '@': resolve('./src'),
        '#': resolve('./public')
      }
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          use: ['thread-loader']
        },
        {test: /\.fpf$/, use: ['xml-loader']}
      ]
    },
    optimization: {
      concatenateModules: true,
      minimizer: [
        new TerserPlugin({
          // 采用多进程打包
          parallel: 4,
          terserOptions: {
            compress: {
              // 去除debug、console
              warnings: true,
              drop_debugger: true,
              drop_console: true
            }
          }
        })
      ]
    },
    plugins: (() => {
      const plugins = [];
      if (process.env.NODE_ENV !== 'development') {
        plugins.push(new CompressionWebpackPlugin({
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: new RegExp('\\.(' + ['js', 'css'].join('|') + ')$'),
          threshold: 10240,
          minRatio: 0.8
        }));
      }
      // content="upgrade-insecure-requests" http-equiv="Content-Security-Policy"
      (process.env.ENV === 'local') && plugins.push(new BundleAnalyzerPlugin());
      if (process.env.ENV === 'production') {
        plugins.push(new HtmlWebpackPlugin({
          template: './public/index.html',
          meta: {
            xxx: {
              content: 'upgrade-insecure-requests',
              'http-equiv': 'Content-Security-Policy'
            }
          }
        }));
      }
      return plugins;
    })()
  },

  chainWebpack(config) {
    config.plugins.delete('preload'); // TODO: need test
    config.plugins.delete('prefetch'); // TODO: need test
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/iconfont/svg'))
      .end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include
      .add(resolve('src/assets/iconfont/svg')) // 处理svg目录
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      });

    // style - loader覆盖默认loader实现样式动态加载可控
    const css = config.module.rule('css').toConfig();
    const useable = {...css.oneOf[3], test: /index_(.+)\.css$/i};
    useable.use = [...useable.use];
    useable.use[0] = {loader: 'style-loader', options: {injectType: 'lazySingletonStyleTag'}};
    config.module.rule('css').merge({oneOf: [useable]});

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config
    // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === 'development',
        config => {
          config.devtool('eval-source-map');
        }
      );

    VueFilenameInjector(config, {
      propName: '__source' // default
    });

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                vendor: {
                  name: 'chunk-vendor',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  enforce: true,
                  chunks: 'all' // only package third parties that are initially dependent
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  enforce: true
                },
                ...splitThirdPartyLib(libArr)
              }
            });
          config.optimization.runtimeChunk('single');
          config.optimization.minimize(true); // 代码压缩
        }
      );
  }
};
