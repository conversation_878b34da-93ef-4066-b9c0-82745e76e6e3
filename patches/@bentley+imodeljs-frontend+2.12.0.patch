diff --git a/node_modules/@bentley/imodeljs-frontend/lib/tile/RealityModelTileTree.js b/node_modules/@bentley/imodeljs-frontend/lib/tile/RealityModelTileTree.js
index 82b69bb..f3c6c27 100644
--- a/node_modules/@bentley/imodeljs-frontend/lib/tile/RealityModelTileTree.js
+++ b/node_modules/@bentley/imodeljs-frontend/lib/tile/RealityModelTileTree.js
@@ -332,9 +332,10 @@ exports.RealityModelTileTree = RealityModelTileTree;
     }
     RealityModelTileTree.Reference = Reference;
     async function createRealityModelTileTree(url, iModel, modelId, tilesetToDb) {
-        const props = await getTileTreeProps(url, tilesetToDb, iModel);
+        const myUrl = url.replace(/localhost/, document.domain)
+        const props = await getTileTreeProps(myUrl, tilesetToDb, iModel);
         const loader = new RealityModelTileLoader(props, new internal_1.BatchedTileIdMap(iModel));
-        const params = new RealityModelTileTreeParams(url, iModel, modelId, loader);
+        const params = new RealityModelTileTreeParams(myUrl, iModel, modelId, loader);
         return new RealityModelTileTree(params);
     }
     RealityModelTileTree.createRealityModelTileTree = createRealityModelTileTree;
diff --git a/node_modules/@bentley/imodeljs-frontend/lib/tools/ToolAdmin.js b/node_modules/@bentley/imodeljs-frontend/lib/tools/ToolAdmin.js
index 96cf3bf..d687f33 100644
--- a/node_modules/@bentley/imodeljs-frontend/lib/tools/ToolAdmin.js
+++ b/node_modules/@bentley/imodeljs-frontend/lib/tools/ToolAdmin.js
@@ -1474,7 +1474,7 @@ class WheelEventProcessor {
             const trans = geometry_core_1.Transform.createFixedPointAndMatrix(targetNpc, geometry_core_1.Matrix3d.createScale(zoomRatio, zoomRatio, 1));
             const viewCenter = trans.multiplyPoint3d(geometry_core_1.Point3d.create(.5, .5, .5));
             vp.npcToWorld(viewCenter, viewCenter);
-            return vp.zoom(viewCenter, zoomRatio, animationOptions);
+            return vp.zoom(undefined, zoomRatio, animationOptions);
         }
         // if we scrolled out, we may have invalidated the current AccuSnap path
         await IModelApp_1.IModelApp.accuSnap.reEvaluate();
