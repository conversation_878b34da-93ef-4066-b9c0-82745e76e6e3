diff --git a/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js b/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
index fcd4ccb..bb102bb 100644
--- a/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
+++ b/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
@@ -13,5 +13,5 @@ exports.isElectronRenderer = typeof navigator === "object" && navigator.userAgen
  * Set to true if the process is running in Electron main process
  * @internal
  */
-exports.isElectronMain = typeof process === "object" && process.versions.hasOwnProperty("electron");
+// exports.isElectronMain = typeof process === "object" && process.versions.hasOwnProperty("electron");
 //# sourceMappingURL=ElectronUtils.js.map
\ No newline at end of file
