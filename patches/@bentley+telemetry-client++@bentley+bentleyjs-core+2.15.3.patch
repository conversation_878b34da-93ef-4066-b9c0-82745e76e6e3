diff --git a/node_modules/@bentley/telemetry-client/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js b/node_modules/@bentley/telemetry-client/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
index 6c8a0ab..fd02031 100644
--- a/node_modules/@bentley/telemetry-client/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
+++ b/node_modules/@bentley/telemetry-client/node_modules/@bentley/bentleyjs-core/lib/ElectronUtils.js
@@ -16,5 +16,5 @@ exports.isElectronRenderer = typeof navigator === "object" && navigator.userAgen
  * @internal
  * @deprecated use ProcessDetector.isElectronAppBackend
  */
-exports.isElectronMain = typeof process === "object" && process.versions.hasOwnProperty("electron");
+// exports.isElectronMain = typeof process === "object" && process.versions.hasOwnProperty("electron");
 //# sourceMappingURL=ElectronUtils.js.map
